<html><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8"><meta name="CreationDate" content="D:20061229174159-08'00'"><meta name="Creator" content="easyPDF SDK 4.3"><meta name="Producer" content="BCL easyPDF 4.30 (0615)"><meta name="ModDate" content="D:20061230135334+01'00'"><title>Life beyond Distributed Transactions:  an Apostate’s Opinion</title></head><body bgcolor="#ffffff" link="blue" vlink="blue"><div style="border: 1px solid rgb(153, 153, 153); margin: -1px -1px 0pt; padding: 0pt; background: rgb(255, 255, 255) none repeat scroll 0% 0%; -moz-background-clip: border; -moz-background-origin: padding; -moz-background-inline-policy: continuous;"><div style="border: 1px solid rgb(153, 153, 153); margin: 12px; padding: 8px; background: rgb(221, 221, 221) none repeat scroll 0% 0%; -moz-background-clip: border; -moz-background-origin: padding; -moz-background-inline-policy: continuous; font-family: arial,sans-serif; font-style: normal; font-variant: normal; font-size: 13px; line-height: normal; font-size-adjust: none; font-stretch: normal; -x-system-font: none; color: rgb(0, 0, 0); font-weight: normal; text-align: left;">This is the html version of the file <a href="http://www.cidrdb.org/cidr2007/papers/cidr07p15.pdf" style="text-decoration: underline; color: rgb(0, 0, 204);">http://www.cidrdb.org/cidr2007/papers/cidr07p15.pdf</a>.<br><b>Google</b> automatically generates html versions of documents as we crawl the web.</div></div><div style="position: relative;">









<table border="0" width="100%"><tbody><tr><td align="right" bgcolor="#eeeeee"><font face="arial,sans-serif"><a name="1"><b>Page 1</b></a></font></td></tr></tbody></table><font face="Times" size="4"><span style="font-size: 25px; font-family: Times;">
<div style="position: absolute; top: 288px; left: 216px;"><nobr><b>Life beyond Distributed Transactions: </b></nobr></div>
<div style="position: absolute; top: 323px; left: 316px;"><nobr><b>an Apostate’s Opinion</b></nobr></div>
</span></font>
<font face="Times" size="3"><span style="font-size: 19px; font-family: Times;">
<div style="position: absolute; top: 356px; left: 391px;"><nobr>Position Paper</nobr></div>
</span></font>
<font face="Times" size="3"><span style="font-size: 16px; font-family: Times;">
<div style="position: absolute; top: 414px; left: 413px;"><nobr>Pat Helland</nobr></div>
<div style="position: absolute; top: 444px; left: 404px;"><nobr>Amazon.Com</nobr></div>
<div style="position: absolute; top: 465px; left: 380px;"><nobr>705 Fifth Ave South</nobr></div>
<div style="position: absolute; top: 486px; left: 384px;"><nobr>Seattle, WA 98104</nobr></div>
<div style="position: absolute; top: 506px; left: 441px;"><nobr>USA</nobr></div>
</span></font>
<font color="#0000ff" face="Times" size="3"><span style="font-size: 16px; font-family: Times; color: rgb(0, 0, 255);">
<div style="position: absolute; top: 527px; left: 361px;"><nobr><EMAIL></nobr></div>
</span></font>
<font face="Times" size="3"><span style="font-size: 12px; font-family: Times;">
<div style="position: absolute; top: 576px; left: 122px;"><nobr>The  positions  expressed  in  this  paper  are </nobr></div>
<div style="position: absolute; top: 593px; left: 122px;"><nobr>personal opinions and do not in any way reflect</nobr></div>
<div style="position: absolute; top: 610px; left: 122px;"><nobr>the positions of my employer Amazon.com.</nobr></div>
</span></font>
<font face="Times" size="3"><span style="font-size: 16px; font-family: Times;">
<div style="position: absolute; top: 642px; left: 121px;"><nobr><b>ABSTRACT</b></nobr></div>
</span></font>
<font face="Times" size="3"><span style="font-size: 12px; font-family: Times;">
<div style="position: absolute; top: 676px; left: 122px;"><nobr>Many decades of work have been invested in the </nobr></div>
<div style="position: absolute; top: 694px; left: 122px;"><nobr>area  of  distributed  transactions  including </nobr></div>
<div style="position: absolute; top: 711px; left: 122px;"><nobr>protocols  such  as  2PC,  Paxos,  and  various </nobr></div>
<div style="position: absolute; top: 728px; left: 122px;"><nobr>approaches to quorum.  These protocols provide </nobr></div>
<div style="position: absolute; top: 745px; left: 122px;"><nobr>the  application programmer  a  façade  of  global </nobr></div>
<div style="position: absolute; top: 763px; left: 122px;"><nobr>serializability.  Personally, I have invested a non-</nobr></div>
<div style="position: absolute; top: 780px; left: 122px;"><nobr>trivial portion of my career as a strong advocate</nobr></div>
<div style="position: absolute; top: 797px; left: 122px;"><nobr>for  the  implementation  and  use  of  platforms </nobr></div>
<div style="position: absolute; top: 814px; left: 122px;"><nobr>providing guarantees of global serializability.</nobr></div>
<div style="position: absolute; top: 836px; left: 122px;"><nobr>My experience over the last decade has led me to </nobr></div>
<div style="position: absolute; top: 853px; left: 122px;"><nobr>liken these  platforms  to  the  Maginot  Line</nobr></div>
</span></font>
<font face="Times" size="2"><span style="font-size: 7px; font-family: Times;">
<div style="position: absolute; top: 850px; left: 385px;"><nobr>1</nobr></div>
</span></font>
<font face="Times" size="3"><span style="font-size: 12px; font-family: Times;">
<div style="position: absolute; top: 853px; left: 390px;"><nobr>.  In </nobr></div>
<div style="position: absolute; top: 871px; left: 122px;"><nobr>general,  application  developers  simply  do  not </nobr></div>
<div style="position: absolute; top: 888px; left: 122px;"><nobr>implement  large  scalable  applications  assuming </nobr></div>
<div style="position: absolute; top: 905px; left: 122px;"><nobr>distributed  transactions.  When  they  attempt  to </nobr></div>
<div style="position: absolute; top: 922px; left: 122px;"><nobr>use distributed transactions, the projects founder </nobr></div>
<div style="position: absolute; top: 940px; left: 122px;"><nobr>because the performance costs and fragility make </nobr></div>
<div style="position: absolute; top: 957px; left: 122px;"><nobr>them impractical.  Natural selection kicks in…</nobr></div>
</span></font>
<font face="Times" size="2"><span style="font-size: 7px; font-family: Times;">
<div style="position: absolute; top: 1001px; left: 95px;"><nobr>1</nobr></div>
</span></font>
<font face="Times" size="3"><span style="font-size: 12px; font-family: Times;">
<div style="position: absolute; top: 1005px; left: 103px;"><nobr>The Maginot Line was a huge fortress that ran the length </nobr></div>
<div style="position: absolute; top: 1022px; left: 95px;"><nobr>of the Franco-German border and was constructed at great</nobr></div>
<div style="position: absolute; top: 1039px; left: 95px;"><nobr>expense  between  World War  I  and  World  War  II.  It </nobr></div>
<div style="position: absolute; top: 1056px; left: 95px;"><nobr>successfully kept the German army from directly crossing </nobr></div>
<div style="position: absolute; top: 1074px; left: 94px;"><nobr>the border between France and Germany.  It was quickly </nobr></div>
<div style="position: absolute; top: 1091px; left: 94px;"><nobr>bypassed by the Germans  in 1940  who invaded  through </nobr></div>
<div style="position: absolute; top: 1108px; left: 94px;"><nobr>Belgium.</nobr></div>
</span></font>
<font face="Times" size="3"><span style="font-size: 10px; font-family: Times;">
<div style="position: absolute; top: 1145px; left: 95px;"><nobr>This article is published under a Creative Commons License Agreement </nobr></div>
<div style="position: absolute; top: 1159px; left: 95px;"><nobr>(http://creativecommons.org/licenses/by/2.5/).</nobr></div>
<div style="position: absolute; top: 1173px; left: 95px;"><nobr>You may copy, distribute, display, and perform the work, make </nobr></div>
<div style="position: absolute; top: 1186px; left: 95px;"><nobr>derivative works and make commercial use of the work, but you must </nobr></div>
<div style="position: absolute; top: 1200px; left: 95px;"><nobr>attribute the work to the author and CIDR  2007.</nobr></div>
<div style="position: absolute; top: 1217px; left: 95px;"><nobr>3</nobr></div>
</span></font>
<font face="Times" size="2"><span style="font-size: 5px; font-family: Times;">
<div style="position: absolute; top: 1215px; left: 100px;"><nobr>rd</nobr></div>
</span></font>
<font face="Times" size="3"><span style="font-size: 10px; font-family: Times;">
<div style="position: absolute; top: 1217px; left: 110px;"><nobr>Biennial Conference on Innovative DataSystems Research (CIDR)</nobr></div>
<div style="position: absolute; top: 1231px; left: 94px;"><nobr>January 7-10, Asilomar, California USA.</nobr></div>
</span></font>
<font face="Times" size="3"><span style="font-size: 12px; font-family: Times;">
<div style="position: absolute; top: 573px; left: 500px;"><nobr>Instead,  applications  are  built  using  different </nobr></div>
<div style="position: absolute; top: 590px; left: 500px;"><nobr>techniques  which  do  not  provide  the  same </nobr></div>
<div style="position: absolute; top: 607px; left: 500px;"><nobr>transactional guarantees but still meet the needs </nobr></div>
<div style="position: absolute; top: 624px; left: 500px;"><nobr>of their businesses.</nobr></div>
<div style="position: absolute; top: 646px; left: 500px;"><nobr>This  paper  explores  and  names  some  of  the </nobr></div>
<div style="position: absolute; top: 663px; left: 500px;"><nobr>practical approaches used in the implementations </nobr></div>
<div style="position: absolute; top: 681px; left: 500px;"><nobr>of  large-scale  mission-critical  applications  in  a </nobr></div>
<div style="position: absolute; top: 698px; left: 500px;"><nobr>world which rejects distributed transactions.  We </nobr></div>
<div style="position: absolute; top: 715px; left: 500px;"><nobr>discuss the management of fine-grained pieces of </nobr></div>
<div style="position: absolute; top: 732px; left: 500px;"><nobr>application data which may be repartitioned over </nobr></div>
<div style="position: absolute; top: 750px; left: 500px;"><nobr>time as the application grows.  We also discuss </nobr></div>
<div style="position: absolute; top: 767px; left: 500px;"><nobr>the  design  patterns  used  in  sending  messages </nobr></div>
<div style="position: absolute; top: 784px; left: 500px;"><nobr>between these repartitionable pieces of data.</nobr></div>
<div style="position: absolute; top: 806px; left: 500px;"><nobr>The reason for starting this discussion is to raise </nobr></div>
<div style="position: absolute; top: 823px; left: 500px;"><nobr>awareness of new patterns for two reasons.  First, </nobr></div>
<div style="position: absolute; top: 840px; left: 500px;"><nobr>it is  my belief  that this  awareness  can ease  the </nobr></div>
<div style="position: absolute; top: 858px; left: 500px;"><nobr>challenges  of  people  hand-crafting  very  large </nobr></div>
<div style="position: absolute; top: 875px; left: 500px;"><nobr>scalable applications.  Second, by observing the </nobr></div>
<div style="position: absolute; top: 892px; left: 500px;"><nobr>patterns,  hopefully  the  industry  can  work </nobr></div>
<div style="position: absolute; top: 909px; left: 500px;"><nobr>towards  the  creation  of  platforms  that  make  it </nobr></div>
<div style="position: absolute; top: 927px; left: 500px;"><nobr>easier to build these very large applications.</nobr></div>
</span></font>
<font face="Times" size="3"><span style="font-size: 16px; font-family: Times;">
<div style="position: absolute; top: 949px; left: 473px;"><nobr><b>1. INTRODUCTION</b></nobr></div>
</span></font>
<font face="Times" size="3"><span style="font-size: 12px; font-family: Times;">
<div style="position: absolute; top: 974px; left: 494px;"><nobr>Let’s  examine  some  goals  for  this  paper, some </nobr></div>
<div style="position: absolute; top: 991px; left: 472px;"><nobr>assumptions  that  I  am  making  for  this  discussion,  and </nobr></div>
<div style="position: absolute; top: 1008px; left: 472px;"><nobr>then some opinions derived from the assumptions.  While </nobr></div>
<div style="position: absolute; top: 1025px; left: 472px;"><nobr>I am keenly interested in high availability, this paper will </nobr></div>
<div style="position: absolute; top: 1043px; left: 472px;"><nobr>ignore  that  issue  and  focus  on  scalability  alone.  In </nobr></div>
<div style="position: absolute; top: 1060px; left: 472px;"><nobr>particular,  we  focus  on  the  implications  that  fall  out  of </nobr></div>
<div style="position: absolute; top: 1077px; left: 472px;"><nobr>assuming  we  cannot  have  large-scale  distributed </nobr></div>
<div style="position: absolute; top: 1094px; left: 472px;"><nobr>transactions.</nobr></div>
<div style="position: absolute; top: 1118px; left: 473px;"><nobr><b>Goals</b></nobr></div>
<div style="position: absolute; top: 1139px; left: 473px;"><nobr>This paper has three broad goals:</nobr></div>
<div style="position: absolute; top: 1160px; left: 473px;"><nobr> Discuss Scalable Applications</nobr></div>
<div style="position: absolute; top: 1182px; left: 489px;"><nobr>Many  of  the  requirements  for  the  design  of  scalable </nobr></div>
<div style="position: absolute; top: 1199px; left: 489px;"><nobr>systems are understood implicitly by many application</nobr></div>
<div style="position: absolute; top: 1216px; left: 489px;"><nobr>designers who build large systems.  </nobr></div>
</span></font>
<font face="Times" size="3"><span style="font-size: 10px; font-family: Times;">
<div style="position: absolute; top: 1293px; left: 449px;"><nobr>132</nobr></div>
</span></font>

<div style="position: absolute; top: 1363px; left: 0pt;"><hr><table border="0" width="100%"><tbody><tr><td align="right" bgcolor="#eeeeee"><font face="arial,sans-serif"><a name="2"><b>Page 2</b></a></font></td></tr></tbody></table></div><font face="Times" size="3"><span style="font-size: 12px; font-family: Times;">
<div style="position: absolute; top: 1447px; left: 111px;"><nobr>The  problem  is  that  the  issues,  concepts,  and </nobr></div>
<div style="position: absolute; top: 1464px; left: 111px;"><nobr>abstractions  for  the  interaction  of  transactions  and </nobr></div>
<div style="position: absolute; top: 1482px; left: 111px;"><nobr>scalable  systems  have  no  names  and  are  not  crisply </nobr></div>
<div style="position: absolute; top: 1499px; left: 111px;"><nobr>understood. </nobr></div>
<div style="position: absolute; top: 1499px; left: 208px;"><nobr>When  they  get  applied,  they  are </nobr></div>
<div style="position: absolute; top: 1516px; left: 111px;"><nobr>inconsistently applied and sometimes come back to bite</nobr></div>
<div style="position: absolute; top: 1533px; left: 111px;"><nobr>us.  One  goal  of  this  paper  is  to  launch  a  discussion </nobr></div>
<div style="position: absolute; top: 1551px; left: 111px;"><nobr>which  can  increase  awareness  of  these  concepts  and, </nobr></div>
<div style="position: absolute; top: 1568px; left: 111px;"><nobr>hopefully, drive towards a common set of terms and an </nobr></div>
<div style="position: absolute; top: 1585px; left: 111px;"><nobr>agreed approach to scalable programs.  </nobr></div>
<div style="position: absolute; top: 1607px; left: 111px;"><nobr>This  paper  attempts  to  name  and  formalize  some </nobr></div>
<div style="position: absolute; top: 1624px; left: 111px;"><nobr>abstractions  implicitly  in  use  for  years  to  implement</nobr></div>
<div style="position: absolute; top: 1641px; left: 111px;"><nobr>scalable systems.</nobr></div>
<div style="position: absolute; top: 1659px; left: 95px;"><nobr> Think about Almost-Infinite Scaling of Applications</nobr></div>
<div style="position: absolute; top: 1681px; left: 111px;"><nobr>To frame the discussion on scaling, this paper presents </nobr></div>
<div style="position: absolute; top: 1699px; left: 111px;"><nobr>an  informal  thought  experiment  on  the  impact  of</nobr></div>
<div style="position: absolute; top: 1716px; left: 111px;"><nobr>almost-infinite  scaling.  I  assume  the  number  of </nobr></div>
<div style="position: absolute; top: 1733px; left: 111px;"><nobr>customers,  purchasable  entities,  orders,  shipments, </nobr></div>
<div style="position: absolute; top: 1750px; left: 111px;"><nobr>health-care-patients, taxpayers, bank accounts, and  all </nobr></div>
<div style="position: absolute; top: 1768px; left: 111px;"><nobr>other business concepts manipulated by the application</nobr></div>
<div style="position: absolute; top: 1785px; left: 111px;"><nobr>grow  significantly  larger  over  time.  Typically,  the </nobr></div>
<div style="position: absolute; top: 1802px; left: 111px;"><nobr>individual  things  do  not  get  significantly  larger;  we </nobr></div>
<div style="position: absolute; top: 1819px; left: 111px;"><nobr>simply get more and  more of them.  It really doesn’t </nobr></div>
<div style="position: absolute; top: 1837px; left: 111px;"><nobr>matter what resource on the computer is saturated first, </nobr></div>
<div style="position: absolute; top: 1854px; left: 111px;"><nobr>the  increase  in  demand  will  drive  us  to  spread  what </nobr></div>
<div style="position: absolute; top: 1871px; left: 111px;"><nobr>formerly ran on a small set of machines to run over a </nobr></div>
<div style="position: absolute; top: 1888px; left: 111px;"><nobr>larger set of machines… </nobr></div>
</span></font>
<font face="Times" size="2"><span style="font-size: 7px; font-family: Times;">
<div style="position: absolute; top: 1885px; left: 262px;"><nobr>2</nobr></div>
</span></font>
<font face="Times" size="3"><span style="font-size: 12px; font-family: Times;">
<div style="position: absolute; top: 1910px; left: 111px;"><nobr>Almost-infinite  scaling  is  a  loose,  imprecise,  and </nobr></div>
<div style="position: absolute; top: 1927px; left: 111px;"><nobr>deliberately amorphous way to motivate the need to be </nobr></div>
<div style="position: absolute; top: 1945px; left: 111px;"><nobr>very  clear  about  when  and  where  we  can  know</nobr></div>
<div style="position: absolute; top: 1962px; left: 111px;"><nobr>something  fits on  one  machine and  what to  do  if  we </nobr></div>
<div style="position: absolute; top: 1979px; left: 111px;"><nobr>cannot ensure it does fit on one machine.  Furthermore, </nobr></div>
<div style="position: absolute; top: 1996px; left: 111px;"><nobr>we want to  scale almost linearly</nobr></div>
</span></font>
<font face="Times" size="2"><span style="font-size: 7px; font-family: Times;">
<div style="position: absolute; top: 1993px; left: 315px;"><nobr>3</nobr></div>
</span></font>
<font face="Times" size="3"><span style="font-size: 12px; font-family: Times;">
<div style="position: absolute; top: 1996px; left: 326px;"><nobr>with the load (both </nobr></div>
<div style="position: absolute; top: 2014px; left: 111px;"><nobr>data and computation).  </nobr></div>
<div style="position: absolute; top: 2032px; left: 95px;"><nobr> Describe a Few Common Patterns for Scalable Apps</nobr></div>
<div style="position: absolute; top: 2054px; left: 111px;"><nobr>What are the impacts of almost-infinite scaling on the </nobr></div>
<div style="position: absolute; top: 2071px; left: 111px;"><nobr>business  logic? I  am  asserting  that  scaling  implies </nobr></div>
<div style="position: absolute; top: 2088px; left: 111px;"><nobr>using a new abstraction called an “entity” as you write </nobr></div>
<div style="position: absolute; top: 2105px; left: 111px;"><nobr>your program.  An entity lives on a single machine at a </nobr></div>
<div style="position: absolute; top: 2123px; left: 111px;"><nobr>time and the application can only manipulate one entity </nobr></div>
<div style="position: absolute; top: 2140px; left: 111px;"><nobr>at a time.  A consequence of almost-infinite scaling is </nobr></div>
<div style="position: absolute; top: 2157px; left: 111px;"><nobr>that this programmatic abstraction must be exposed to </nobr></div>
<div style="position: absolute; top: 2175px; left: 111px;"><nobr>the developer of business logic.</nobr></div>
<div style="position: absolute; top: 2196px; left: 111px;"><nobr>By  naming  and  discussing  this  as-yet-unnamed </nobr></div>
<div style="position: absolute; top: 2213px; left: 111px;"><nobr>concept, it is hoped that we can agree on a consistent</nobr></div>
<div style="position: absolute; top: 2231px; left: 111px;"><nobr>programmatic approach and a consistent understanding </nobr></div>
<div style="position: absolute; top: 2248px; left: 111px;"><nobr>of the issues involved in building scalable systems.</nobr></div>
<div style="position: absolute; top: 2270px; left: 111px;"><nobr>Furthermore, the use of entities has implications on the </nobr></div>
<div style="position: absolute; top: 2287px; left: 111px;"><nobr>messaging patterns used to connect the entities.  These </nobr></div>
<div style="position: absolute; top: 2304px; left: 111px;"><nobr>lead to the creation of state machines that cope with the </nobr></div>
</span></font>
<font face="Times" size="2"><span style="font-size: 7px; font-family: Times;">
<div style="position: absolute; top: 2344px; left: 95px;"><nobr>2</nobr></div>
</span></font>
<font face="Times" size="3"><span style="font-size: 12px; font-family: Times;">
<div style="position: absolute; top: 2347px; left: 108px;"><nobr>To be  clear,  this  is  conceptually  assuming  tens  of </nobr></div>
<div style="position: absolute; top: 2364px; left: 94px;"><nobr>thousands  or  hundreds  of  thousands  of  machines.  Too </nobr></div>
<div style="position: absolute; top: 2382px; left: 94px;"><nobr>many to make them behave like one “big” machine.</nobr></div>
</span></font>
<font face="Times" size="2"><span style="font-size: 7px; font-family: Times;">
<div style="position: absolute; top: 2396px; left: 95px;"><nobr>3</nobr></div>
</span></font>
<font face="Times" size="3"><span style="font-size: 12px; font-family: Times;">
<div style="position: absolute; top: 2399px; left: 106px;"><nobr>Scaling  at  N  log  N  for  some big  log  would  be  really </nobr></div>
<div style="position: absolute; top: 2416px; left: 95px;"><nobr>nice…</nobr></div>
<div style="position: absolute; top: 1447px; left: 489px;"><nobr>message  delivery  inconsistencies  foisted  upon  the </nobr></div>
<div style="position: absolute; top: 1464px; left: 489px;"><nobr>innocent application developer as they attempt to build </nobr></div>
<div style="position: absolute; top: 1482px; left: 489px;"><nobr>scalable solutions to business problems.</nobr></div>
<div style="position: absolute; top: 1510px; left: 473px;"><nobr><b>Assumptions</b></nobr></div>
<div style="position: absolute; top: 1531px; left: 494px;"><nobr>Let’s  start  out  with  three  assumptions  which  are </nobr></div>
<div style="position: absolute; top: 1548px; left: 472px;"><nobr>asserted and  not justified.  We simply assume these  are </nobr></div>
<div style="position: absolute; top: 1566px; left: 472px;"><nobr>true based on experience.</nobr></div>
<div style="position: absolute; top: 1584px; left: 473px;"><nobr> Layers of the Application and Scale-Agnosticism</nobr></div>
<div style="position: absolute; top: 1606px; left: 489px;"><nobr>Let’s start by presuming  (at  least)  two layers  in each </nobr></div>
<div style="position: absolute; top: 1624px; left: 489px;"><nobr>scalable  application.  These layers  differ  in  their</nobr></div>
<div style="position: absolute; top: 1641px; left: 489px;"><nobr>perception of scaling.  They may have other differences </nobr></div>
<div style="position: absolute; top: 1658px; left: 489px;"><nobr>but that is not relevant to this discussion. </nobr></div>
<div style="position: absolute; top: 1680px; left: 489px;"><nobr>The lower layer of the application understands the fact </nobr></div>
<div style="position: absolute; top: 1697px; left: 489px;"><nobr>that  more  computers  get  added  to  make  the  system </nobr></div>
<div style="position: absolute; top: 1714px; left: 489px;"><nobr>scale.  In  addition  to  other  work,  it  manages  the </nobr></div>
<div style="position: absolute; top: 1732px; left: 489px;"><nobr>mapping  of  the  upper  layer’s  code  to  the  physical </nobr></div>
<div style="position: absolute; top: 1749px; left: 489px;"><nobr>machines and their locations.  The lower layer is <i>scale-</i></nobr></div>
<div style="position: absolute; top: 1766px; left: 489px;"><nobr><i>aware </i>in  that  it  understands  this  mapping.  We  are </nobr></div>
<div style="position: absolute; top: 1783px; left: 489px;"><nobr>presuming  that  the  lower  layer  provides  a  <i>scale-</i></nobr></div>
<div style="position: absolute; top: 1800px; left: 489px;"><nobr><i>agnostic programming abstraction </i>to the upper layer</nobr></div>
</span></font>
<font face="Times" size="2"><span style="font-size: 7px; font-family: Times;">
<div style="position: absolute; top: 1797px; left: 807px;"><nobr>4</nobr></div>
</span></font>
<font face="Times" size="3"><span style="font-size: 12px; font-family: Times;">
<div style="position: absolute; top: 1800px; left: 812px;"><nobr>.  </nobr></div>
<div style="position: absolute; top: 1822px; left: 489px;"><nobr>Using this scale-agnostic programming abstraction, the </nobr></div>
<div style="position: absolute; top: 1840px; left: 489px;"><nobr>upper  layer of  application  code  is  written  without </nobr></div>
<div style="position: absolute; top: 1857px; left: 489px;"><nobr>worrying about scaling issues.  By sticking to the scale-</nobr></div>
<div style="position: absolute; top: 1874px; left: 489px;"><nobr>agnostic  programming  abstraction,  we  can  write </nobr></div>
<div style="position: absolute; top: 1891px; left: 489px;"><nobr>application code that is not worried about the changes </nobr></div>
<div style="position: absolute; top: 1908px; left: 489px;"><nobr>happening  when  the  application  is  deployed  against</nobr></div>
<div style="position: absolute; top: 1926px; left: 489px;"><nobr>ever increasing load.</nobr></div>
<div style="position: absolute; top: 1948px; left: 489px;"><nobr>Over  time,  the  lower  layer  of  these  applications  may </nobr></div>
<div style="position: absolute; top: 2127px; left: 489px;"><nobr>evolve to become new platforms or middleware which </nobr></div>
<div style="position: absolute; top: 2144px; left: 489px;"><nobr>simplify  the  creation  of  scale-agnostic applications</nobr></div>
<div style="position: absolute; top: 2161px; left: 489px;"><nobr>(similar to the past scenarios when CICS and other TP-</nobr></div>
<div style="position: absolute; top: 2178px; left: 489px;"><nobr>Monitors  evolved  to  simplify  the  creation  of </nobr></div>
<div style="position: absolute; top: 2196px; left: 489px;"><nobr>applications for block-mode terminals).  </nobr></div>
<div style="position: absolute; top: 2218px; left: 489px;"><nobr>The  focus  of  this  discussion  is  on  the  possibilities</nobr></div>
<div style="position: absolute; top: 2235px; left: 489px;"><nobr>posed by these nascent scale-agnostic APIs.</nobr></div>
<div style="position: absolute; top: 2253px; left: 473px;"><nobr> Scopes of Transactional Serializability</nobr></div>
<div style="position: absolute; top: 2275px; left: 489px;"><nobr>Lots of academic work has been done on the notion of </nobr></div>
<div style="position: absolute; top: 2292px; left: 489px;"><nobr>providing transactional serializability across distributed</nobr></div>
<div style="position: absolute; top: 2309px; left: 489px;"><nobr>systems.  This includes 2PC (two phase commit) which </nobr></div>
<div style="position: absolute; top: 2327px; left: 489px;"><nobr>can easily block when nodes are unavailable and other </nobr></div>
<div style="position: absolute; top: 2344px; left: 489px;"><nobr>protocols  which  do  not  block  in  the  face  of  node </nobr></div>
<div style="position: absolute; top: 2361px; left: 489px;"><nobr>failures such as the Paxos algorithm.  </nobr></div>
</span></font>
<font face="Times" size="2"><span style="font-size: 7px; font-family: Times;">
<div style="position: absolute; top: 2396px; left: 473px;"><nobr>4</nobr></div>
</span></font>
<font face="Times" size="3"><span style="font-size: 12px; font-family: Times;">
<div style="position: absolute; top: 2399px; left: 482px;"><nobr>Google’s MapReduce is an example of a scale-agnostic </nobr></div>
<div style="position: absolute; top: 2416px; left: 472px;"><nobr>programming abstraction.</nobr></div>
<div style="position: absolute; top: 1973px; left: 661px;"><nobr>Scale Agnostic Code</nobr></div>
<div style="position: absolute; top: 2046px; left: 666px;"><nobr>Scale-Aware  Code </nobr></div>
<div style="position: absolute; top: 2064px; left: 649px;"><nobr>Implementing Support </nobr></div>
<div style="position: absolute; top: 2082px; left: 653px;"><nobr>for the Scale-Agnostic</nobr></div>
<div style="position: absolute; top: 2100px; left: 631px;"><nobr>Programming Abstraction</nobr></div>
<div style="position: absolute; top: 1976px; left: 516px;"><nobr><b>Application</b></nobr></div>
<div style="position: absolute; top: 2004px; left: 508px;"><nobr>Upper Layer</nobr></div>
<div style="position: absolute; top: 2022px; left: 523px;"><nobr>of Code</nobr></div>
<div style="position: absolute; top: 2063px; left: 508px;"><nobr>Lower Layer</nobr></div>
<div style="position: absolute; top: 2081px; left: 523px;"><nobr>of Code</nobr></div>
<div style="position: absolute; top: 1998px; left: 677px;"><nobr>Scale Agnostic</nobr></div>
<div style="position: absolute; top: 2016px; left: 642px;"><nobr>Programming Abstraction</nobr></div>
<div style="position: absolute; top: 1973px; left: 661px;"><nobr>Scale Agnostic Code</nobr></div>
<div style="position: absolute; top: 2046px; left: 666px;"><nobr>Scale-Aware  Code </nobr></div>
<div style="position: absolute; top: 2064px; left: 649px;"><nobr>Implementing Support </nobr></div>
<div style="position: absolute; top: 2082px; left: 653px;"><nobr>for the Scale-Agnostic</nobr></div>
<div style="position: absolute; top: 2100px; left: 631px;"><nobr>Programming Abstraction</nobr></div>
<div style="position: absolute; top: 1976px; left: 516px;"><nobr><b>Application</b></nobr></div>
<div style="position: absolute; top: 2004px; left: 508px;"><nobr>Upper Layer</nobr></div>
<div style="position: absolute; top: 2022px; left: 523px;"><nobr>of Code</nobr></div>
<div style="position: absolute; top: 2063px; left: 508px;"><nobr>Lower Layer</nobr></div>
<div style="position: absolute; top: 2081px; left: 523px;"><nobr>of Code</nobr></div>
<div style="position: absolute; top: 2004px; left: 508px;"><nobr>Upper Layer</nobr></div>
<div style="position: absolute; top: 2022px; left: 523px;"><nobr>of Code</nobr></div>
<div style="position: absolute; top: 2063px; left: 508px;"><nobr>Lower Layer</nobr></div>
<div style="position: absolute; top: 2081px; left: 523px;"><nobr>of Code</nobr></div>
<div style="position: absolute; top: 1998px; left: 677px;"><nobr>Scale Agnostic</nobr></div>
<div style="position: absolute; top: 2016px; left: 642px;"><nobr>Programming Abstraction</nobr></div>
</span></font>
<font face="Times" size="3"><span style="font-size: 10px; font-family: Times;">
<div style="position: absolute; top: 2481px; left: 449px;"><nobr>133</nobr></div>
</span></font>

<div style="position: absolute; top: 2551px; left: 0pt;"><hr><table border="0" width="100%"><tbody><tr><td align="right" bgcolor="#eeeeee"><font face="arial,sans-serif"><a name="3"><b>Page 3</b></a></font></td></tr></tbody></table></div><font face="Times" size="3"><span style="font-size: 12px; font-family: Times;">
<div style="position: absolute; top: 2635px; left: 111px;"><nobr>Let’s describe these algorithms as ones which provide </nobr></div>
<div style="position: absolute; top: 2652px; left: 111px;"><nobr><i>global  transactional  serializability</i></nobr></div>
</span></font>
<font face="Times" size="2"><span style="font-size: 7px; font-family: Times;">
<div style="position: absolute; top: 2649px; left: 325px;"><nobr><i>5</i></nobr></div>
</span></font>
<font face="Times" size="3"><span style="font-size: 12px; font-family: Times;">
<div style="position: absolute; top: 2652px; left: 329px;"><nobr>.  Their  goal  is  to </nobr></div>
<div style="position: absolute; top: 2670px; left: 111px;"><nobr>allow  arbitrary  atomic  updates  across  data  spread </nobr></div>
<div style="position: absolute; top: 2687px; left: 111px;"><nobr>across  a  set  of  machines.  These  algorithms  allow </nobr></div>
<div style="position: absolute; top: 2704px; left: 111px;"><nobr>updates  to  exist  in  a  single  scope  of  serializability </nobr></div>
<div style="position: absolute; top: 2721px; left: 111px;"><nobr>across this set of machines.</nobr></div>
<div style="position: absolute; top: 2743px; left: 111px;"><nobr>We  are  going  to  consider  what  happens  when  you </nobr></div>
<div style="position: absolute; top: 2760px; left: 111px;"><nobr>simply don’t do this.  Real system developers and real </nobr></div>
<div style="position: absolute; top: 2778px; left: 111px;"><nobr>systems as we see them deployed today rarely even try </nobr></div>
<div style="position: absolute; top: 2795px; left: 111px;"><nobr>to achieve transactional serializability across machines </nobr></div>
<div style="position: absolute; top: 2812px; left: 111px;"><nobr>or,  if  they  do,  it  is  within  a  small  number  of  tightly </nobr></div>
<div style="position: absolute; top: 2829px; left: 111px;"><nobr>connected  machines  functioning  as  a  cluster.  Put </nobr></div>
<div style="position: absolute; top: 2847px; left: 111px;"><nobr>simply,  we  aren’t  doing  transactions  across  machines </nobr></div>
<div style="position: absolute; top: 2864px; left: 111px;"><nobr>except <i>perhaps </i>in the simple case where there is a tight </nobr></div>
<div style="position: absolute; top: 2881px; left: 111px;"><nobr>cluster which looks like one machine.</nobr></div>
<div style="position: absolute; top: 2903px; left: 111px;"><nobr>Instead,  we  assume  <i>multiple  disjoint  scopes  of </i></nobr></div>
<div style="position: absolute; top: 2920px; left: 111px;"><nobr><i>transactional  serializability</i>.  Consider  each computer</nobr></div>
<div style="position: absolute; top: 2937px; left: 111px;"><nobr>to be a separate scope of transactional serializability</nobr></div>
</span></font>
<font face="Times" size="2"><span style="font-size: 7px; font-family: Times;">
<div style="position: absolute; top: 2934px; left: 424px;"><nobr>6</nobr></div>
</span></font>
<font face="Times" size="3"><span style="font-size: 12px; font-family: Times;">
<div style="position: absolute; top: 2937px; left: 428px;"><nobr>.</nobr></div>
<div style="position: absolute; top: 2959px; left: 111px;"><nobr>Each data item resides in a single computer or cluster</nobr></div>
</span></font>
<font face="Times" size="2"><span style="font-size: 7px; font-family: Times;">
<div style="position: absolute; top: 2956px; left: 437px;"><nobr>7</nobr></div>
</span></font>
<font face="Times" size="3"><span style="font-size: 12px; font-family: Times;">
<div style="position: absolute; top: 2959px; left: 442px;"><nobr>.  </nobr></div>
<div style="position: absolute; top: 2976px; left: 111px;"><nobr>Atomic  transactions  may  include  any  data  residing </nobr></div>
<div style="position: absolute; top: 2994px; left: 111px;"><nobr>within that single scope of transactional serializability </nobr></div>
<div style="position: absolute; top: 3011px; left: 111px;"><nobr>(i.e. within the single computer or cluster).  You cannot</nobr></div>
<div style="position: absolute; top: 3028px; left: 111px;"><nobr>perform  atomic  transactions  across  these  disjoint</nobr></div>
<div style="position: absolute; top: 3045px; left: 111px;"><nobr>scopes  of  transactional  serializability.  That’s  what </nobr></div>
<div style="position: absolute; top: 3063px; left: 111px;"><nobr>makes them disjoint!</nobr></div>
<div style="position: absolute; top: 3080px; left: 95px;"><nobr> Most Applications Use “At-Least-Once” Messaging</nobr></div>
<div style="position: absolute; top: 3103px; left: 111px;"><nobr>TCP-IP  is  great  if  you  are  a  short-lived  Unix-style </nobr></div>
<div style="position: absolute; top: 3120px; left: 111px;"><nobr>process.  But let’s  consider the  dilemma  faced  by an </nobr></div>
<div style="position: absolute; top: 3137px; left: 111px;"><nobr>application  developer  whose  job is  to  process  a </nobr></div>
<div style="position: absolute; top: 3154px; left: 111px;"><nobr>message and modify some data durably represented on </nobr></div>
<div style="position: absolute; top: 3172px; left: 111px;"><nobr>disk (either  in a SQL database or some other durable </nobr></div>
<div style="position: absolute; top: 3189px; left: 111px;"><nobr>store). </nobr></div>
<div style="position: absolute; top: 3189px; left: 172px;"><nobr>The  message  is  consumed  but  not  yet </nobr></div>
<div style="position: absolute; top: 3206px; left: 111px;"><nobr>acknowledged. The database is updated and then the </nobr></div>
<div style="position: absolute; top: 3223px; left: 111px;"><nobr>message is acknowledged.  In a failure, this is restarted</nobr></div>
<div style="position: absolute; top: 3241px; left: 111px;"><nobr>and the message is processed again.</nobr></div>
<div style="position: absolute; top: 3262px; left: 111px;"><nobr>The  dilemma  derives  from  the  fact  that  the  message </nobr></div>
<div style="position: absolute; top: 3280px; left: 111px;"><nobr>delivery  is  not  directly  coupled  to  the  update  of  the </nobr></div>
<div style="position: absolute; top: 3297px; left: 111px;"><nobr>durable  data  other  than  through  application  action.  </nobr></div>
<div style="position: absolute; top: 3314px; left: 111px;"><nobr>While  it  is  possible  to  couple  the  consumption  of </nobr></div>
<div style="position: absolute; top: 3331px; left: 111px;"><nobr>messages to the update of the durable data, this is not </nobr></div>
<div style="position: absolute; top: 3349px; left: 111px;"><nobr>commonly  available.  The  absence of  this  coupling </nobr></div>
<div style="position: absolute; top: 3366px; left: 111px;"><nobr>leads  to  failure  windows  in  which  the  message  is </nobr></div>
<div style="position: absolute; top: 3383px; left: 111px;"><nobr>delivered  more  than  once.  The  messaging  plumbing </nobr></div>
</span></font>
<font face="Times" size="2"><span style="font-size: 7px; font-family: Times;">
<div style="position: absolute; top: 3428px; left: 95px;"><nobr>5</nobr></div>
</span></font>
<font face="Times" size="3"><span style="font-size: 12px; font-family: Times;">
<div style="position: absolute; top: 3431px; left: 104px;"><nobr>I am deliberately conflating strict serializability and the </nobr></div>
<div style="position: absolute; top: 3449px; left: 94px;"><nobr>weaker locking modes.  The issue is the scope of the data </nobr></div>
<div style="position: absolute; top: 3466px; left: 94px;"><nobr>participating in the transactions visible to the application.</nobr></div>
</span></font>
<font face="Times" size="2"><span style="font-size: 7px; font-family: Times;">
<div style="position: absolute; top: 3480px; left: 95px;"><nobr>6</nobr></div>
</span></font>
<font face="Times" size="3"><span style="font-size: 12px; font-family: Times;">
<div style="position: absolute; top: 3483px; left: 106px;"><nobr>This  is  not  intended to  preclude  a  small  collection  of </nobr></div>
<div style="position: absolute; top: 3501px; left: 95px;"><nobr>computers functioning in a cluster to behave as if they are </nobr></div>
<div style="position: absolute; top: 3518px; left: 95px;"><nobr>one machine.  This IS intended to formally state that we </nobr></div>
<div style="position: absolute; top: 3535px; left: 94px;"><nobr>assume many computers and the likelihood that we must </nobr></div>
<div style="position: absolute; top: 3552px; left: 94px;"><nobr>consider work which cannot be atomically committed.</nobr></div>
</span></font>
<font face="Times" size="2"><span style="font-size: 7px; font-family: Times;">
<div style="position: absolute; top: 3566px; left: 95px;"><nobr>7</nobr></div>
</span></font>
<font face="Times" size="3"><span style="font-size: 12px; font-family: Times;">
<div style="position: absolute; top: 3569px; left: 104px;"><nobr>This is excluding replication for high-availability which </nobr></div>
<div style="position: absolute; top: 3587px; left: 95px;"><nobr>will  not  change  the  presumption  of  disjoint  scopes  of</nobr></div>
<div style="position: absolute; top: 3604px; left: 95px;"><nobr>tra`nsactional serializability.</nobr></div>
<div style="position: absolute; top: 2635px; left: 489px;"><nobr>does  this  because  its  only  other  recourse is  to </nobr></div>
<div style="position: absolute; top: 2652px; left: 489px;"><nobr>occasionally </nobr></div>
<div style="position: absolute; top: 2652px; left: 591px;"><nobr>lose </nobr></div>
<div style="position: absolute; top: 2652px; left: 643px;"><nobr>messages </nobr></div>
<div style="position: absolute; top: 2652px; left: 727px;"><nobr>(“at-most-once”</nobr></div>
<div style="position: absolute; top: 2670px; left: 489px;"><nobr>messaging) and that is even more onerous to deal with</nobr></div>
</span></font>
<font face="Times" size="2"><span style="font-size: 7px; font-family: Times;">
<div style="position: absolute; top: 2666px; left: 815px;"><nobr>8</nobr></div>
</span></font>
<font face="Times" size="3"><span style="font-size: 12px; font-family: Times;">
<div style="position: absolute; top: 2669px; left: 820px;"><nobr>.</nobr></div>
<div style="position: absolute; top: 2691px; left: 489px;"><nobr>A  consequence  of  this  behavior  from  the  messaging </nobr></div>
<div style="position: absolute; top: 2709px; left: 489px;"><nobr>plumbing is that the application must tolerate message </nobr></div>
<div style="position: absolute; top: 2726px; left: 489px;"><nobr>retries and the out-of-order arrival of some messages. </nobr></div>
<div style="position: absolute; top: 2743px; left: 489px;"><nobr>This  paper  considers  the  application  patterns  arising </nobr></div>
<div style="position: absolute; top: 2760px; left: 489px;"><nobr>when business-logic programmers must deal with this </nobr></div>
<div style="position: absolute; top: 2778px; left: 489px;"><nobr>burden in almost-infinitely large applications.</nobr></div>
<div style="position: absolute; top: 2806px; left: 473px;"><nobr><b>Opinions to Be Justified</b></nobr></div>
<div style="position: absolute; top: 2827px; left: 494px;"><nobr>The nice thing about  writing a position paper  is that </nobr></div>
<div style="position: absolute; top: 2844px; left: 472px;"><nobr>you can express wild opinions.  Here are a few that we </nobr></div>
<div style="position: absolute; top: 2862px; left: 472px;"><nobr>will be arguing in the corpus of this position paper</nobr></div>
</span></font>
<font face="Times" size="2"><span style="font-size: 7px; font-family: Times;">
<div style="position: absolute; top: 2858px; left: 775px;"><nobr>9</nobr></div>
</span></font>
<font face="Times" size="3"><span style="font-size: 12px; font-family: Times;">
<div style="position: absolute; top: 2862px; left: 780px;"><nobr>:</nobr></div>
<div style="position: absolute; top: 2880px; left: 473px;"><nobr> Scalable Apps Use Uniquely Identified “Entities”</nobr></div>
<div style="position: absolute; top: 2902px; left: 489px;"><nobr>This paper will argue that the upper layer code for each </nobr></div>
<div style="position: absolute; top: 2920px; left: 489px;"><nobr>application must manipulate a single collection of data </nobr></div>
<div style="position: absolute; top: 2937px; left: 489px;"><nobr>we are calling an <i>entity</i>.  There are no restrictions on </nobr></div>
<div style="position: absolute; top: 2954px; left: 489px;"><nobr>the size of an entity except  that it  must live  within a </nobr></div>
<div style="position: absolute; top: 2971px; left: 489px;"><nobr>single  scope  of  serializability  (i.e.  one  machine  or </nobr></div>
<div style="position: absolute; top: 2988px; left: 489px;"><nobr>cluster).  </nobr></div>
<div style="position: absolute; top: 3010px; left: 489px;"><nobr>Each entity has a unique identifier or key.  An entity-</nobr></div>
<div style="position: absolute; top: 3028px; left: 489px;"><nobr>key  may  be  of  any  shape,  form,  or  flavor  but  it </nobr></div>
<div style="position: absolute; top: 3045px; left: 489px;"><nobr>somehow uniquely identifies exactly one entity and the </nobr></div>
<div style="position: absolute; top: 3062px; left: 489px;"><nobr>data contained within that entity.</nobr></div>
<div style="position: absolute; top: 3297px; left: 489px;"><nobr>There  are  no  constraints  on  the  representation  of  the </nobr></div>
<div style="position: absolute; top: 3314px; left: 489px;"><nobr>entity.  It  may  be  stored  as  SQL  records,  XML </nobr></div>
<div style="position: absolute; top: 3332px; left: 489px;"><nobr>documents, files, data contained within file systems, as </nobr></div>
<div style="position: absolute; top: 3349px; left: 489px;"><nobr>blobs, or anything else that is convenient or appropriate </nobr></div>
<div style="position: absolute; top: 3366px; left: 489px;"><nobr>for the app’s needs.  One possible representation is as a </nobr></div>
<div style="position: absolute; top: 3383px; left: 489px;"><nobr>collection  of  SQL  records  (potentially  across  many </nobr></div>
<div style="position: absolute; top: 3401px; left: 489px;"><nobr>tables) whose primary key begins with the entity-key.</nobr></div>
</span></font>
<font face="Times" size="2"><span style="font-size: 7px; font-family: Times;">
<div style="position: absolute; top: 3480px; left: 473px;"><nobr>8</nobr></div>
</span></font>
<font face="Times" size="3"><span style="font-size: 12px; font-family: Times;">
<div style="position: absolute; top: 3483px; left: 482px;"><nobr>I am a big fan of “exactly-once in-order” messaging but </nobr></div>
<div style="position: absolute; top: 3501px; left: 473px;"><nobr>to  provide  it  for  durable  data  requires  a  long-lived</nobr></div>
<div style="position: absolute; top: 3518px; left: 473px;"><nobr>programmatic  abstraction  similar  to  a  TCP connection.  </nobr></div>
<div style="position: absolute; top: 3535px; left: 473px;"><nobr>The  assertion  here  is  that  these  facilities  are  rarely </nobr></div>
<div style="position: absolute; top: 3552px; left: 473px;"><nobr>available</nobr></div>
<div style="position: absolute; top: 3552px; left: 545px;"><nobr>to  the  programmer  building  scalable </nobr></div>
<div style="position: absolute; top: 3570px; left: 473px;"><nobr>applications.  Hence,  we  are  considering  cases  dealing </nobr></div>
<div style="position: absolute; top: 3587px; left: 473px;"><nobr>with “at-least-once”.</nobr></div>
</span></font>
<font face="Times" size="2"><span style="font-size: 7px; font-family: Times;">
<div style="position: absolute; top: 3601px; left: 473px;"><nobr>9</nobr></div>
</span></font>
<font face="Times" size="3"><span style="font-size: 12px; font-family: Times;">
<div style="position: absolute; top: 3604px; left: 481px;"><nobr>Note that these topics will be discussed in more detail.</nobr></div>
<div style="position: absolute; top: 3102px; left: 612px;"><nobr>Data for an app comprises </nobr></div>
<div style="position: absolute; top: 3118px; left: 604px;"><nobr>many different entities.       </nobr></div>
<div style="position: absolute; top: 3241px; left: 521px;"><nobr>Key = “UNW”</nobr></div>
<div style="position: absolute; top: 3240px; left: 669px;"><nobr>Entity</nobr></div>
<div style="position: absolute; top: 3212px; left: 670px;"><nobr>Entity</nobr></div>
<div style="position: absolute; top: 3185px; left: 670px;"><nobr>Entity</nobr></div>
<div style="position: absolute; top: 3158px; left: 669px;"><nobr>Entity</nobr></div>
<div style="position: absolute; top: 3159px; left: 523px;"><nobr>Key = “ABC”</nobr></div>
<div style="position: absolute; top: 3186px; left: 522px;"><nobr>Key = “WBP”</nobr></div>
<div style="position: absolute; top: 3214px; left: 524px;"><nobr>Key = “QLA”</nobr></div>
<div style="position: absolute; top: 3102px; left: 612px;"><nobr>Data for an app comprises </nobr></div>
<div style="position: absolute; top: 3118px; left: 604px;"><nobr>many different entities.       </nobr></div>
<div style="position: absolute; top: 3241px; left: 521px;"><nobr>Key = “UNW”</nobr></div>
<div style="position: absolute; top: 3240px; left: 669px;"><nobr>Entity</nobr></div>
<div style="position: absolute; top: 3241px; left: 521px;"><nobr>Key = “UNW”</nobr></div>
<div style="position: absolute; top: 3241px; left: 521px;"><nobr>Key = “UNW”</nobr></div>
<div style="position: absolute; top: 3240px; left: 669px;"><nobr>Entity</nobr></div>
<div style="position: absolute; top: 3212px; left: 670px;"><nobr>Entity</nobr></div>
<div style="position: absolute; top: 3185px; left: 670px;"><nobr>Entity</nobr></div>
<div style="position: absolute; top: 3158px; left: 669px;"><nobr>Entity</nobr></div>
<div style="position: absolute; top: 3159px; left: 523px;"><nobr>Key = “ABC”</nobr></div>
<div style="position: absolute; top: 3186px; left: 522px;"><nobr>Key = “WBP”</nobr></div>
<div style="position: absolute; top: 3214px; left: 524px;"><nobr>Key = “QLA”</nobr></div>
</span></font>
<font face="Times" size="3"><span style="font-size: 10px; font-family: Times;">
<div style="position: absolute; top: 3669px; left: 449px;"><nobr>134</nobr></div>
</span></font>

<div style="position: absolute; top: 3739px; left: 0pt;"><hr><table border="0" width="100%"><tbody><tr><td align="right" bgcolor="#eeeeee"><font face="arial,sans-serif"><a name="4"><b>Page 4</b></a></font></td></tr></tbody></table></div><font face="Times" size="3"><span style="font-size: 12px; font-family: Times;">
<div style="position: absolute; top: 3823px; left: 111px;"><nobr>Entities  represent disjoint  sets  of  data.  Each  datum </nobr></div>
<div style="position: absolute; top: 3840px; left: 111px;"><nobr>resides  in  exactly  one  entity. The  data  of  an  entity</nobr></div>
<div style="position: absolute; top: 3858px; left: 111px;"><nobr>never overlaps the data of another entity.</nobr></div>
<div style="position: absolute; top: 3879px; left: 111px;"><nobr>An application comprises many entities.  For example,</nobr></div>
<div style="position: absolute; top: 3897px; left: 111px;"><nobr>an  “order-processing” application  encapsulates  many </nobr></div>
<div style="position: absolute; top: 3914px; left: 111px;"><nobr>orders.  Each order is identified by a unique Order-ID.</nobr></div>
<div style="position: absolute; top: 3931px; left: 111px;"><nobr>To  be  a  scalable  “order-processing” application,  data </nobr></div>
<div style="position: absolute; top: 3948px; left: 111px;"><nobr>for one order must be disjoint from the data for other </nobr></div>
<div style="position: absolute; top: 3966px; left: 111px;"><nobr>orders.</nobr></div>
<div style="position: absolute; top: 3984px; left: 95px;"><nobr> Atomic Transactions Cannot Span Entities</nobr></div>
<div style="position: absolute; top: 4006px; left: 111px;"><nobr>We  will  argue  below why  we  conclude  that  atomic</nobr></div>
<div style="position: absolute; top: 4023px; left: 111px;"><nobr>transactions  cannot  span  entities.  The  programmer </nobr></div>
<div style="position: absolute; top: 4040px; left: 111px;"><nobr>must always stick to the data contained inside a single </nobr></div>
<div style="position: absolute; top: 4057px; left: 111px;"><nobr>entity for each transaction.  This restriction is true for </nobr></div>
<div style="position: absolute; top: 4075px; left: 111px;"><nobr>entities  within  the  same  application  and  for  entities</nobr></div>
<div style="position: absolute; top: 4092px; left: 111px;"><nobr>within different applications.</nobr></div>
<div style="position: absolute; top: 4114px; left: 111px;"><nobr>From  the  programmer’s  perspective,  <i>the  uniquely </i></nobr></div>
<div style="position: absolute; top: 4131px; left: 111px;"><nobr><i>identified  entity  is  the  scope  of  serializability</i>. This </nobr></div>
<div style="position: absolute; top: 4148px; left: 111px;"><nobr>concept  has  a  powerful  impact  on  the  behavior  of </nobr></div>
<div style="position: absolute; top: 4165px; left: 111px;"><nobr>applications  designed  for  scaling.  An  implication  of </nobr></div>
<div style="position: absolute; top: 4183px; left: 111px;"><nobr>this we will explore is that alternate indices cannot be </nobr></div>
<div style="position: absolute; top: 4200px; left: 111px;"><nobr>kept  transactionally  consistent  when  designing  for </nobr></div>
<div style="position: absolute; top: 4217px; left: 111px;"><nobr>almost-infinite scaling.</nobr></div>
<div style="position: absolute; top: 4235px; left: 95px;"><nobr> Messages Are Addressed to Entities</nobr></div>
<div style="position: absolute; top: 4257px; left: 111px;"><nobr>Most  messaging  systems  do  not  consider the </nobr></div>
<div style="position: absolute; top: 4274px; left: 111px;"><nobr>partitioning key for the data but rather target a queue</nobr></div>
<div style="position: absolute; top: 4292px; left: 111px;"><nobr>which is then consumed by a stateless process.</nobr></div>
<div style="position: absolute; top: 4313px; left: 111px;"><nobr>Standard  practice  is  to  include  some  data  in  the </nobr></div>
<div style="position: absolute; top: 4331px; left: 111px;"><nobr>message  that  informs the  stateless  application  code </nobr></div>
<div style="position: absolute; top: 4348px; left: 111px;"><nobr>where to get the data it needs.  This is the entity-key</nobr></div>
<div style="position: absolute; top: 4365px; left: 111px;"><nobr>described above.  The  data  for  the  entity  is  fetched </nobr></div>
<div style="position: absolute; top: 4382px; left: 111px;"><nobr>from  some  database  or  other  durable  store  by  the </nobr></div>
<div style="position: absolute; top: 4400px; left: 111px;"><nobr>application.</nobr></div>
<div style="position: absolute; top: 4421px; left: 111px;"><nobr>A couple of interesting trends are already happening in </nobr></div>
<div style="position: absolute; top: 4439px; left: 111px;"><nobr>the industry.  First, the size of the set of entities within </nobr></div>
<div style="position: absolute; top: 4456px; left: 111px;"><nobr>a single application is growing larger than can fit in a </nobr></div>
<div style="position: absolute; top: 4473px; left: 111px;"><nobr>single data-store. Each individual entity fits in a store </nobr></div>
<div style="position: absolute; top: 4490px; left: 111px;"><nobr>but  the  set  of  them  all  does  not.  Increasingly,  the </nobr></div>
<div style="position: absolute; top: 4508px; left: 111px;"><nobr>stateless application is routing to fetch the entity based </nobr></div>
<div style="position: absolute; top: 4525px; left: 111px;"><nobr>upon some partitioning scheme.  Second, the fetching </nobr></div>
<div style="position: absolute; top: 4542px; left: 111px;"><nobr>and  partitioning  scheme  is  being  separated  into  the </nobr></div>
<div style="position: absolute; top: 4560px; left: 111px;"><nobr>lower-layers of the application and deliberately isolated </nobr></div>
<div style="position: absolute; top: 4577px; left: 111px;"><nobr>from the upper-layers of the application responsible for </nobr></div>
<div style="position: absolute; top: 4594px; left: 111px;"><nobr>business logic.</nobr></div>
<div style="position: absolute; top: 4616px; left: 111px;"><nobr>This  is  effectively  driving  towards  the  message </nobr></div>
<div style="position: absolute; top: 4633px; left: 111px;"><nobr>destination  being  the  entity  key.  Both  the  stateless </nobr></div>
<div style="position: absolute; top: 4650px; left: 111px;"><nobr>Unix-style  process  and  the  lower-layers  of  the </nobr></div>
<div style="position: absolute; top: 4667px; left: 111px;"><nobr>application are simply part of the implementation of the </nobr></div>
<div style="position: absolute; top: 4685px; left: 111px;"><nobr>scale-agnostic API for the business-logic.  The upper-</nobr></div>
<div style="position: absolute; top: 4702px; left: 111px;"><nobr>layer scale-agnostic business logic simply addresses the </nobr></div>
<div style="position: absolute; top: 4719px; left: 111px;"><nobr>message  to  the  entity-key  that  identifies  the  durable </nobr></div>
<div style="position: absolute; top: 4736px; left: 111px;"><nobr>state known as the entity.</nobr></div>
<div style="position: absolute; top: 3819px; left: 473px;"><nobr> Entities Manage Per-Partner State (“Activities”)</nobr></div>
<div style="position: absolute; top: 3841px; left: 489px;"><nobr>Scale-agnostic messaging is effectively entity-to-entity</nobr></div>
<div style="position: absolute; top: 3859px; left: 489px;"><nobr>messaging.  The  sending  entity  (as  manifest  by  its </nobr></div>
<div style="position: absolute; top: 3876px; left: 489px;"><nobr>durable  state and  identified  by its  entity-key) sends  a </nobr></div>
<div style="position: absolute; top: 3893px; left: 489px;"><nobr>message  which  is  addressed  to  another  entity.  The </nobr></div>
<div style="position: absolute; top: 3910px; left: 489px;"><nobr>recipient  entity  comprises  both  upper-layer  (scale-</nobr></div>
<div style="position: absolute; top: 3928px; left: 489px;"><nobr>agnostic)  business  logic  and  the  durable  data </nobr></div>
<div style="position: absolute; top: 3945px; left: 489px;"><nobr>representing its state which is stored and accessed by </nobr></div>
<div style="position: absolute; top: 3962px; left: 489px;"><nobr>the entity-key.</nobr></div>
<div style="position: absolute; top: 3984px; left: 489px;"><nobr>Recall the assumption that messages are delivered “at-</nobr></div>
<div style="position: absolute; top: 4001px; left: 489px;"><nobr>least-once”.  This means that the recipient entity must </nobr></div>
<div style="position: absolute; top: 4018px; left: 489px;"><nobr>be  prepared  in  its  durable  state  to  be  assailed  with </nobr></div>
<div style="position: absolute; top: 4036px; left: 489px;"><nobr>redundant messages that must be ignored.  In practice, </nobr></div>
<div style="position: absolute; top: 4053px; left: 489px;"><nobr>messages  fall  into  one  of  two  categories:  those  that </nobr></div>
<div style="position: absolute; top: 4070px; left: 489px;"><nobr>affect the state of the recipient entity and those that do </nobr></div>
<div style="position: absolute; top: 4087px; left: 489px;"><nobr>not.  Messages  that  do  not  cause  change  to  the </nobr></div>
<div style="position: absolute; top: 4105px; left: 489px;"><nobr>processing  entity  are  easy…  They  are  trivially </nobr></div>
<div style="position: absolute; top: 4122px; left: 489px;"><nobr>idempotent.  It is those making changes to the recipient </nobr></div>
<div style="position: absolute; top: 4139px; left: 489px;"><nobr>that pose design challenges.</nobr></div>
<div style="position: absolute; top: 4161px; left: 489px;"><nobr>To  ensure  idempotence  (i.e.  guarantee  the  processing </nobr></div>
<div style="position: absolute; top: 4178px; left: 489px;"><nobr>of retried messages is harmless), the recipient entity is </nobr></div>
<div style="position: absolute; top: 4195px; left: 489px;"><nobr>typically  designed  to  remember  that  the  message  has </nobr></div>
<div style="position: absolute; top: 4213px; left: 489px;"><nobr>been  processed.</nobr></div>
<div style="position: absolute; top: 4213px; left: 611px;"><nobr>Once  it  has  been,  the  repeated</nobr></div>
<div style="position: absolute; top: 4230px; left: 489px;"><nobr>message  will  typically  generate  a  new  response  (or </nobr></div>
<div style="position: absolute; top: 4247px; left: 489px;"><nobr>outgoing  message)  which  mimics the  behavior  of  the </nobr></div>
<div style="position: absolute; top: 4264px; left: 489px;"><nobr>earlier processed message.</nobr></div>
<div style="position: absolute; top: 4286px; left: 489px;"><nobr>The knowledge of the received messages creates state </nobr></div>
<div style="position: absolute; top: 4303px; left: 489px;"><nobr>which is wrapped up on a per-partner basis.  The key </nobr></div>
<div style="position: absolute; top: 4321px; left: 489px;"><nobr>observation  here  is  that  the  state  gets  organized  by </nobr></div>
<div style="position: absolute; top: 4338px; left: 489px;"><nobr>partner and the partner is an entity.</nobr></div>
<div style="position: absolute; top: 4360px; left: 489px;"><nobr>We  are  applying  the  term  activity to  the  state  which </nobr></div>
<div style="position: absolute; top: 4377px; left: 489px;"><nobr>manages the per-party messaging on each side of this </nobr></div>
<div style="position: absolute; top: 4394px; left: 489px;"><nobr>two-party  relationship.  Each  activity  lives  is  exactly </nobr></div>
<div style="position: absolute; top: 4411px; left: 489px;"><nobr>one  entity.  An  entity  will  have  an  activity  for  each </nobr></div>
<div style="position: absolute; top: 4429px; left: 489px;"><nobr>partner entity from which it receives messages. </nobr></div>
<div style="position: absolute; top: 4450px; left: 489px;"><nobr>In addition to managing message melees, activities are </nobr></div>
<div style="position: absolute; top: 4468px; left: 489px;"><nobr>used to manage loosely-coupled agreement.  In a world </nobr></div>
<div style="position: absolute; top: 4485px; left: 489px;"><nobr>where  atomic  transactions  are  not  a  possibility, </nobr></div>
<div style="position: absolute; top: 4502px; left: 489px;"><nobr>tentative  operations  are  used  to  negotiate  a  shared </nobr></div>
<div style="position: absolute; top: 4519px; left: 489px;"><nobr>outcome.  These  are  performed  between  entities  and </nobr></div>
<div style="position: absolute; top: 4537px; left: 489px;"><nobr>are managed by activities.</nobr></div>
<div style="position: absolute; top: 4558px; left: 489px;"><nobr>This paper is not asserting that activities can solve the </nobr></div>
<div style="position: absolute; top: 4576px; left: 489px;"><nobr>well  known  challenges  to  reaching  agreement </nobr></div>
<div style="position: absolute; top: 4593px; left: 489px;"><nobr>described so thoroughly in workflow discussions.  We </nobr></div>
<div style="position: absolute; top: 4610px; left: 489px;"><nobr>are, however, pointing out that almost-infinite scaling</nobr></div>
<div style="position: absolute; top: 4627px; left: 489px;"><nobr>leads  to  surprisingly  fine-grained  workflow-style </nobr></div>
<div style="position: absolute; top: 4645px; left: 489px;"><nobr>solutions.  The participants are entities and each entity </nobr></div>
<div style="position: absolute; top: 4662px; left: 489px;"><nobr>manages its workflow using specific knowledge about </nobr></div>
<div style="position: absolute; top: 4679px; left: 489px;"><nobr>the other entities involved.  That two-party knowledge </nobr></div>
<div style="position: absolute; top: 4696px; left: 489px;"><nobr>maintained inside an entity is what we call an activity.</nobr></div>
<div style="position: absolute; top: 4718px; left: 489px;"><nobr>Examples of activities are sometimes subtle.  An order </nobr></div>
<div style="position: absolute; top: 4735px; left: 489px;"><nobr>application  will  send  messages  to  the  shipping </nobr></div>
<div style="position: absolute; top: 4753px; left: 489px;"><nobr>application and include the shipping-id and the sending </nobr></div>
<div style="position: absolute; top: 4770px; left: 489px;"><nobr>order-id.  The message-type may be used to stimulate </nobr></div>
<div style="position: absolute; top: 4787px; left: 489px;"><nobr>the state changes in the shipping application to record </nobr></div>
</span></font>
<font face="Times" size="3"><span style="font-size: 10px; font-family: Times;">
<div style="position: absolute; top: 4857px; left: 449px;"><nobr>135</nobr></div>
</span></font>

<div style="position: absolute; top: 4927px; left: 0pt;"><hr><table border="0" width="100%"><tbody><tr><td align="right" bgcolor="#eeeeee"><font face="arial,sans-serif"><a name="5"><b>Page 5</b></a></font></td></tr></tbody></table></div><font face="Times" size="3"><span style="font-size: 12px; font-family: Times;">
<div style="position: absolute; top: 5011px; left: 111px;"><nobr>that  the  specified  order is  ready-to-ship.  Frequently, </nobr></div>
<div style="position: absolute; top: 5028px; left: 111px;"><nobr>implementers  don’t  design  for  retries  until  a  bug </nobr></div>
<div style="position: absolute; top: 5046px; left: 111px;"><nobr>appears.  Rarely  but  occasionally,  the  application </nobr></div>
<div style="position: absolute; top: 5063px; left: 111px;"><nobr>designers think about and plan the design for activities.</nobr></div>
<div style="position: absolute; top: 5085px; left: 95px;"><nobr>The  remaining  part  of  this  paper  will  examine  these </nobr></div>
<div style="position: absolute; top: 5102px; left: 95px;"><nobr>assertions  in  greater  depth  and  propose  arguments  and </nobr></div>
<div style="position: absolute; top: 5119px; left: 95px;"><nobr>explanations for these opinions.</nobr></div>
</span></font>
<font face="Times" size="3"><span style="font-size: 16px; font-family: Times;">
<div style="position: absolute; top: 5152px; left: 95px;"><nobr><b>2. ENTITIES</b></nobr></div>
</span></font>
<font face="Times" size="3"><span style="font-size: 12px; font-family: Times;">
<div style="position: absolute; top: 5177px; left: 116px;"><nobr>This section examines the nature of entities in greater </nobr></div>
<div style="position: absolute; top: 5194px; left: 94px;"><nobr>depth. </nobr></div>
<div style="position: absolute; top: 5194px; left: 154px;"><nobr>We  first  consider the  guarantee  of  atomic </nobr></div>
<div style="position: absolute; top: 5211px; left: 94px;"><nobr>transactions <i>within </i>a single entity.  Next, we consider the </nobr></div>
<div style="position: absolute; top: 5228px; left: 94px;"><nobr>use of a unique key to access the entity and how this can </nobr></div>
<div style="position: absolute; top: 5245px; left: 94px;"><nobr>empower  the  lower-level (scale-aware)  part  of  the </nobr></div>
<div style="position: absolute; top: 5263px; left: 94px;"><nobr>application to relocate entities when repartitioning.  After </nobr></div>
<div style="position: absolute; top: 5280px; left: 94px;"><nobr>this,  we consider what  may be  accessed  within  a  single </nobr></div>
<div style="position: absolute; top: 5297px; left: 94px;"><nobr>atomic transaction and, finally, examine the implications </nobr></div>
<div style="position: absolute; top: 5314px; left: 94px;"><nobr>of almost-infinite scaling on alternate indices.</nobr></div>
<div style="position: absolute; top: 5338px; left: 95px;"><nobr><b>Disjoint Scopes of Serializability</b></nobr></div>
<div style="position: absolute; top: 5359px; left: 116px;"><nobr>Each entity  is  defined  as  a  collection of  data  with a </nobr></div>
<div style="position: absolute; top: 5377px; left: 94px;"><nobr>unique  key  known  to  live  within  a  single  scope  of </nobr></div>
<div style="position: absolute; top: 5394px; left: 94px;"><nobr>serializability.  Because it lives  within a single scope of </nobr></div>
<div style="position: absolute; top: 5411px; left: 94px;"><nobr>serializability,  we  are  ensured  that  we  may  always  do </nobr></div>
<div style="position: absolute; top: 5429px; left: 94px;"><nobr>atomic transactions <i>within a single entity</i>.</nobr></div>
<div style="position: absolute; top: 5446px; left: 116px;"><nobr>It  is  this  aspect  that  warrants  giving  the  “entity”  a </nobr></div>
<div style="position: absolute; top: 5463px; left: 94px;"><nobr>different name then an “object”.  Objects may or may not </nobr></div>
<div style="position: absolute; top: 5480px; left: 94px;"><nobr>share  transactional  scopes. </nobr></div>
<div style="position: absolute; top: 5480px; left: 305px;"><nobr>Entities  never  share </nobr></div>
<div style="position: absolute; top: 5498px; left: 94px;"><nobr>transactional scopes because repartitioning may put them</nobr></div>
<div style="position: absolute; top: 5515px; left: 94px;"><nobr>on different machines.</nobr></div>
<div style="position: absolute; top: 5539px; left: 95px;"><nobr><b>Uniquely Keyed Entities</b></nobr></div>
<div style="position: absolute; top: 5560px; left: 116px;"><nobr>Code for the upper layer of an application is naturally</nobr></div>
<div style="position: absolute; top: 5577px; left: 94px;"><nobr>designed  around  collections  of  data  with  a  unique  key.  </nobr></div>
<div style="position: absolute; top: 5594px; left: 94px;"><nobr>We  see  customer-ids,  social-security-numbers,  product-</nobr></div>
<div style="position: absolute; top: 5611px; left: 94px;"><nobr>SKUs,  and  other  unique  identifiers  all  the  time  within </nobr></div>
<div style="position: absolute; top: 5629px; left: 94px;"><nobr>applications.  They  are  used  as  keys  to  locate  the  data </nobr></div>
<div style="position: absolute; top: 5646px; left: 94px;"><nobr>implementing  the  applications. </nobr></div>
<div style="position: absolute; top: 5646px; left: 323px;"><nobr>This  is  a  natural </nobr></div>
<div style="position: absolute; top: 5663px; left: 94px;"><nobr>paradigm.  We observe that the boundary of the disjoint </nobr></div>
<div style="position: absolute; top: 5681px; left: 94px;"><nobr>scope  of  serializability  (i.e. the  “entity”)  is  always </nobr></div>
<div style="position: absolute; top: 5698px; left: 94px;"><nobr>identified by a unique key in practice.    </nobr></div>
<div style="position: absolute; top: 5721px; left: 95px;"><nobr><b>Repartitioning and Entities</b></nobr></div>
<div style="position: absolute; top: 5743px; left: 116px;"><nobr>One  of  our  assumptions  is  that  the  emerging  upper-</nobr></div>
<div style="position: absolute; top: 5760px; left: 94px;"><nobr>layer  is  scale-agnostic  and  the  lower-layer  decides  how </nobr></div>
<div style="position: absolute; top: 5777px; left: 94px;"><nobr>the deployment evolves as requirements for scale change.  </nobr></div>
<div style="position: absolute; top: 5794px; left: 94px;"><nobr>This means that the location of a specific entity is likely to </nobr></div>
<div style="position: absolute; top: 5011px; left: 472px;"><nobr>change as the deployment evolves.  The upper-layer of the </nobr></div>
<div style="position: absolute; top: 5028px; left: 472px;"><nobr>application  cannot  make  assumptions  about  the  location </nobr></div>
<div style="position: absolute; top: 5046px; left: 472px;"><nobr>of the entity because that would not be scale-agnostic.</nobr></div>
<div style="position: absolute; top: 5069px; left: 473px;"><nobr><b>Atomic Transactions and Entities</b></nobr></div>
<div style="position: absolute; top: 5090px; left: 494px;"><nobr>In scalable systems, <i>you can’t assume transactions for </i></nobr></div>
<div style="position: absolute; top: 5108px; left: 473px;"><nobr><i>updates across these different entities</i>.  Each entity has a </nobr></div>
<div style="position: absolute; top: 5125px; left: 473px;"><nobr>unique key and each entity is easily placed into one scope </nobr></div>
<div style="position: absolute; top: 5142px; left: 473px;"><nobr>of serializability</nobr></div>
</span></font>
<font face="Times" size="2"><span style="font-size: 7px; font-family: Times;">
<div style="position: absolute; top: 5139px; left: 572px;"><nobr>10</nobr></div>
</span></font>
<font face="Times" size="3"><span style="font-size: 12px; font-family: Times;">
<div style="position: absolute; top: 5142px; left: 581px;"><nobr>.  How can you know that two separate </nobr></div>
<div style="position: absolute; top: 5160px; left: 473px;"><nobr>entities  are  guaranteed  to  be  within  the  same  scope  of </nobr></div>
<div style="position: absolute; top: 5177px; left: 473px;"><nobr>serializability (and, hence, atomically updateable)?  You </nobr></div>
<div style="position: absolute; top: 5194px; left: 473px;"><nobr>only know when there is a single unique key that unifies </nobr></div>
<div style="position: absolute; top: 5211px; left: 473px;"><nobr>both.  Now it is really one entity!  </nobr></div>
<div style="position: absolute; top: 5229px; left: 494px;"><nobr>If  we  use  hashing  for  partitioning  by  entity-key, </nobr></div>
<div style="position: absolute; top: 5246px; left: 473px;"><nobr>there’s  no  telling  when  two  entities  with  different  keys </nobr></div>
<div style="position: absolute; top: 5263px; left: 473px;"><nobr>land on the same box.  If we use key-range partitioning </nobr></div>
<div style="position: absolute; top: 5280px; left: 473px;"><nobr>for  the  entity-keys,  most  of  the  time  the  adjacent  key-</nobr></div>
<div style="position: absolute; top: 5298px; left: 473px;"><nobr>values resides on the same machine but once in a while </nobr></div>
<div style="position: absolute; top: 5315px; left: 473px;"><nobr>you will get unlucky and your neighbor will be on another </nobr></div>
<div style="position: absolute; top: 5332px; left: 473px;"><nobr>machine.  A simple test-case  which counts on atomicity </nobr></div>
<div style="position: absolute; top: 5349px; left: 473px;"><nobr>with  a  neighbor  in  a  key-range  partitioning  will  very </nobr></div>
<div style="position: absolute; top: 5367px; left: 473px;"><nobr>likely  experience  that  atomicity  during  the  test </nobr></div>
<div style="position: absolute; top: 5384px; left: 473px;"><nobr>deployment.  Only  later  when  redeployment  moves  the </nobr></div>
<div style="position: absolute; top: 5401px; left: 473px;"><nobr>entities  across  different  scopes  of  serializability  will the </nobr></div>
<div style="position: absolute; top: 5418px; left: 473px;"><nobr>latent bug emerge as the updates can no longer be atomic.  </nobr></div>
<div style="position: absolute; top: 5436px; left: 473px;"><nobr>You  can never  count  on different  entity-key-values </nobr></div>
<div style="position: absolute; top: 5453px; left: 473px;"><nobr>residing in the same place!  </nobr></div>
<div style="position: absolute; top: 5470px; left: 494px;"><nobr>Put  more  simply,  the  lower-layer  of  the  application</nobr></div>
<div style="position: absolute; top: 5487px; left: 473px;"><nobr>will  ensure  each  entity-key (and  its  entity)  reside on  a </nobr></div>
<div style="position: absolute; top: 5505px; left: 473px;"><nobr>single machine (or small cluster).  Different entities may </nobr></div>
<div style="position: absolute; top: 5522px; left: 473px;"><nobr>be anywhere.  </nobr></div>
<div style="position: absolute; top: 5539px; left: 494px;"><nobr><i>A scale-agnostic programming abstraction must have </i></nobr></div>
<div style="position: absolute; top: 5556px; left: 473px;"><nobr><i>the  notion  of  entity  as  the  boundary  of  atomicity. </i>The </nobr></div>
<div style="position: absolute; top: 5573px; left: 473px;"><nobr>understanding  of  the  existence  of  the  entity  as  a </nobr></div>
<div style="position: absolute; top: 5591px; left: 473px;"><nobr>programmatic  abstraction, the  use of the entity-key, and </nobr></div>
<div style="position: absolute; top: 5608px; left: 473px;"><nobr>the  clear  commitment  to  assuming  a  lack  of  atomicity </nobr></div>
<div style="position: absolute; top: 5625px; left: 473px;"><nobr>across entities are essential to providing a scale-agnostic</nobr></div>
<div style="position: absolute; top: 5643px; left: 473px;"><nobr>upper layer to the application.</nobr></div>
<div style="position: absolute; top: 5660px; left: 494px;"><nobr>Large-scale  applications  implicitly  do  this  in  the </nobr></div>
<div style="position: absolute; top: 5677px; left: 473px;"><nobr>industry today; we just don’t have a name for the concept </nobr></div>
<div style="position: absolute; top: 5694px; left: 473px;"><nobr>of  an  entity.  From  an  upper-layer  app’s  perspective,  it </nobr></div>
<div style="position: absolute; top: 5712px; left: 473px;"><nobr>must assume that the entity is the scope of serializability.  </nobr></div>
<div style="position: absolute; top: 5729px; left: 473px;"><nobr>Assuming more will break when the deployment changes.</nobr></div>
<div style="position: absolute; top: 5753px; left: 473px;"><nobr><b>Considering Alternate Indices</b></nobr></div>
<div style="position: absolute; top: 5774px; left: 494px;"><nobr>We are accustomed to the ability to address data with </nobr></div>
<div style="position: absolute; top: 5791px; left: 472px;"><nobr>multiple  keys  or  indices.  For  example,  sometimes  we </nobr></div>
<div style="position: absolute; top: 5808px; left: 472px;"><nobr>reference  a  customer  by social  security  number, </nobr></div>
<div style="position: absolute; top: 5825px; left: 472px;"><nobr>sometimes  by  credit-card  number,  and  sometimes  by </nobr></div>
<div style="position: absolute; top: 5843px; left: 472px;"><nobr>street address.  If we assume extreme amounts of scaling, </nobr></div>
<div style="position: absolute; top: 5860px; left: 472px;"><nobr>these  indices  cannot  reside  on  the  same  machine  or  a </nobr></div>
<div style="position: absolute; top: 5877px; left: 472px;"><nobr>single  large  cluster.  <i>The  data  about  a  single  customer </i></nobr></div>
</span></font>
<font face="Times" size="2"><span style="font-size: 7px; font-family: Times;">
<div style="position: absolute; top: 5925px; left: 473px;"><nobr>10</nobr></div>
</span></font>
<font face="Times" size="3"><span style="font-size: 12px; font-family: Times;">
<div style="position: absolute; top: 5928px; left: 489px;"><nobr>Recall  the  premise  that  almost-infinite  scaling  causes </nobr></div>
<div style="position: absolute; top: 5945px; left: 473px;"><nobr><i>the number of entitys </i>to inexorably increase but <i>size of the </i></nobr></div>
<div style="position: absolute; top: 5963px; left: 473px;"><nobr><i>individual entity </i>remains small enough to fit in one scope </nobr></div>
<div style="position: absolute; top: 5980px; left: 472px;"><nobr>of serializability (i.e. one computer or small cluster).</nobr></div>
<div style="position: absolute; top: 5955px; left: 131px;"><nobr>Entity</nobr></div>
<div style="position: absolute; top: 5934px; left: 109px;"><nobr>Key= “ABC”</nobr></div>
<div style="position: absolute; top: 5829px; left: 100px;"><nobr>Entities are spread across scopes of serializability </nobr></div>
<div style="position: absolute; top: 5845px; left: 100px;"><nobr>using either hashing or key-range partitioning.</nobr></div>
<div style="position: absolute; top: 5998px; left: 249px;"><nobr>Entity</nobr></div>
<div style="position: absolute; top: 5978px; left: 227px;"><nobr>Key= “QLA”</nobr></div>
<div style="position: absolute; top: 6001px; left: 367px;"><nobr>Entity</nobr></div>
<div style="position: absolute; top: 5980px; left: 344px;"><nobr>Key= “UNV”</nobr></div>
<div style="position: absolute; top: 5942px; left: 367px;"><nobr>Entity</nobr></div>
<div style="position: absolute; top: 5921px; left: 343px;"><nobr>Key= “WBP”</nobr></div>
<div style="position: absolute; top: 5955px; left: 131px;"><nobr>Entity</nobr></div>
<div style="position: absolute; top: 5934px; left: 109px;"><nobr>Key= “ABC”</nobr></div>
<div style="position: absolute; top: 5955px; left: 131px;"><nobr>Entity</nobr></div>
<div style="position: absolute; top: 5934px; left: 109px;"><nobr>Key= “ABC”</nobr></div>
<div style="position: absolute; top: 5829px; left: 100px;"><nobr>Entities are spread across scopes of serializability </nobr></div>
<div style="position: absolute; top: 5845px; left: 100px;"><nobr>using either hashing or key-range partitioning.</nobr></div>
<div style="position: absolute; top: 5998px; left: 249px;"><nobr>Entity</nobr></div>
<div style="position: absolute; top: 5978px; left: 227px;"><nobr>Key= “QLA”</nobr></div>
<div style="position: absolute; top: 5998px; left: 249px;"><nobr>Entity</nobr></div>
<div style="position: absolute; top: 5978px; left: 227px;"><nobr>Key= “QLA”</nobr></div>
<div style="position: absolute; top: 6001px; left: 367px;"><nobr>Entity</nobr></div>
<div style="position: absolute; top: 5980px; left: 344px;"><nobr>Key= “UNV”</nobr></div>
<div style="position: absolute; top: 5942px; left: 367px;"><nobr>Entity</nobr></div>
<div style="position: absolute; top: 5921px; left: 343px;"><nobr>Key= “WBP”</nobr></div>
<div style="position: absolute; top: 6001px; left: 367px;"><nobr>Entity</nobr></div>
<div style="position: absolute; top: 5980px; left: 344px;"><nobr>Key= “UNV”</nobr></div>
<div style="position: absolute; top: 6001px; left: 367px;"><nobr>Entity</nobr></div>
<div style="position: absolute; top: 5980px; left: 344px;"><nobr>Key= “UNV”</nobr></div>
<div style="position: absolute; top: 5942px; left: 367px;"><nobr>Entity</nobr></div>
<div style="position: absolute; top: 5921px; left: 343px;"><nobr>Key= “WBP”</nobr></div>
<div style="position: absolute; top: 5942px; left: 367px;"><nobr>Entity</nobr></div>
<div style="position: absolute; top: 5921px; left: 343px;"><nobr>Key= “WBP”</nobr></div>
</span></font>
<font face="Times" size="3"><span style="font-size: 10px; font-family: Times;">
<div style="position: absolute; top: 6045px; left: 449px;"><nobr>136</nobr></div>
</span></font>

<div style="position: absolute; top: 6115px; left: 0pt;"><hr><table border="0" width="100%"><tbody><tr><td align="right" bgcolor="#eeeeee"><font face="arial,sans-serif"><a name="6"><b>Page 6</b></a></font></td></tr></tbody></table></div><font face="Times" size="3"><span style="font-size: 12px; font-family: Times;">
<div style="position: absolute; top: 6199px; left: 95px;"><nobr><i>cannot  be  known  to  reside  within  a  single  scope  of </i></nobr></div>
<div style="position: absolute; top: 6216px; left: 95px;"><nobr><i>serializability! </i>The entity itself can reside within a single </nobr></div>
<div style="position: absolute; top: 6234px; left: 94px;"><nobr>scope of serializability.  The challenge is that the copies</nobr></div>
<div style="position: absolute; top: 6251px; left: 94px;"><nobr>of the information used to create an alternate index must </nobr></div>
<div style="position: absolute; top: 6268px; left: 94px;"><nobr>be assumed to reside in a different scope of serializability!  </nobr></div>
<div style="position: absolute; top: 6285px; left: 116px;"><nobr>Consider  guaranteeing  the  alternate  index  resides  in </nobr></div>
<div style="position: absolute; top: 6303px; left: 94px;"><nobr>the  same  scope  of  serializability.  When  almost-infinite </nobr></div>
<div style="position: absolute; top: 6320px; left: 94px;"><nobr>scaling kicks in and the set of entities is smeared across </nobr></div>
<div style="position: absolute; top: 6337px; left: 94px;"><nobr>gigantic  numbers  of  machines,  the  primary  index  and </nobr></div>
<div style="position: absolute; top: 6354px; left: 94px;"><nobr>alternate  index  information  must reside within the  same </nobr></div>
<div style="position: absolute; top: 6372px; left: 94px;"><nobr>scope  of  serializability.  How  do  we  ensure  that? The </nobr></div>
<div style="position: absolute; top: 6389px; left: 94px;"><nobr>only way to ensure they both live within the same scope is </nobr></div>
<div style="position: absolute; top: 6406px; left: 94px;"><nobr>to  begin  locating  the  alternate  index  using  the  primary </nobr></div>
<div style="position: absolute; top: 6423px; left: 94px;"><nobr>index!  That takes us to the same scope of serializability.  </nobr></div>
<div style="position: absolute; top: 6441px; left: 94px;"><nobr>If we start without the primary index and have to search </nobr></div>
<div style="position: absolute; top: 6458px; left: 94px;"><nobr>all  of  the  scopes  of  serializability,  each  alternate  index </nobr></div>
<div style="position: absolute; top: 6475px; left: 94px;"><nobr>lookup must examine an almost-infinite number of scopes </nobr></div>
<div style="position: absolute; top: 6492px; left: 94px;"><nobr>as it looks for the match to the alternate key!  This will </nobr></div>
<div style="position: absolute; top: 6510px; left: 94px;"><nobr>eventually become untenable! </nobr></div>
<div style="position: absolute; top: 6681px; left: 116px;"><nobr>The only logical alternative is to do a two step lookup.  </nobr></div>
<div style="position: absolute; top: 6698px; left: 94px;"><nobr>First,  we  lookup  the  alternate  key  and  that  yields  the </nobr></div>
<div style="position: absolute; top: 6715px; left: 94px;"><nobr>entity-key.  Second, we access the entity using the entity-</nobr></div>
<div style="position: absolute; top: 6733px; left: 94px;"><nobr>key.  This is very much like inside a relational database as </nobr></div>
<div style="position: absolute; top: 6750px; left: 94px;"><nobr>it uses two steps to access a record via an alternate key.  </nobr></div>
<div style="position: absolute; top: 6767px; left: 94px;"><nobr>But our premise of almost-infinite scaling means the two </nobr></div>
<div style="position: absolute; top: 6785px; left: 94px;"><nobr>indices (primary and alternate) cannot be known to reside </nobr></div>
<div style="position: absolute; top: 6802px; left: 94px;"><nobr>in the same scope of serializability!</nobr></div>
<div style="position: absolute; top: 6821px; left: 95px;"><nobr><i>The scale-agnostic application program can’t atomically </i></nobr></div>
<div style="position: absolute; top: 6838px; left: 95px;"><nobr><i>update an entity and its alternate index! </i>The upper-layer </nobr></div>
<div style="position: absolute; top: 6856px; left: 95px;"><nobr>scale-agnostic application must be designed to understand </nobr></div>
<div style="position: absolute; top: 6873px; left: 95px;"><nobr>that alternate indices may be out of sync with the entity</nobr></div>
<div style="position: absolute; top: 6890px; left: 95px;"><nobr>accessed with its primary index (i.e. entity-key).</nobr></div>
<div style="position: absolute; top: 6910px; left: 116px;"><nobr>What in the past  has been managed automatically as</nobr></div>
<div style="position: absolute; top: 6927px; left: 94px;"><nobr>alternate indices must now be managed manually by the </nobr></div>
<div style="position: absolute; top: 6944px; left: 94px;"><nobr>application.  Workflow-style  updates  via  asynchronous </nobr></div>
<div style="position: absolute; top: 6961px; left: 94px;"><nobr>messaging are all that is left  to the almost-infinite  scale </nobr></div>
<div style="position: absolute; top: 6979px; left: 94px;"><nobr>application.  Reading of the data that was previously kept </nobr></div>
<div style="position: absolute; top: 6996px; left: 94px;"><nobr>as  alternate  indices  must  now  be  done  with  an </nobr></div>
<div style="position: absolute; top: 7013px; left: 94px;"><nobr>understanding that this is potentially out of sync with the </nobr></div>
<div style="position: absolute; top: 7030px; left: 94px;"><nobr>entity  implementing  the  primary  representation  of  the </nobr></div>
<div style="position: absolute; top: 7048px; left: 94px;"><nobr>data. </nobr></div>
<div style="position: absolute; top: 7048px; left: 146px;"><nobr>The  functionality  previously  implemented  as </nobr></div>
<div style="position: absolute; top: 7065px; left: 94px;"><nobr>alternate indices is now harder.  It is a fact of life in the </nobr></div>
<div style="position: absolute; top: 7082px; left: 94px;"><nobr>big cruel world of huge systems!</nobr></div>
</span></font>
<font face="Times" size="3"><span style="font-size: 16px; font-family: Times;">
<div style="position: absolute; top: 6200px; left: 473px;"><nobr><b>3. MESSAGING ACROSS ENTITIES</b></nobr></div>
</span></font>
<font face="Times" size="3"><span style="font-size: 12px; font-family: Times;">
<div style="position: absolute; top: 6224px; left: 494px;"><nobr>In  this  section,  we  consider the  means  to  connect</nobr></div>
<div style="position: absolute; top: 6242px; left: 472px;"><nobr>independent  entities  using  messages. </nobr></div>
<div style="position: absolute; top: 6242px; left: 740px;"><nobr>We  examine </nobr></div>
<div style="position: absolute; top: 6259px; left: 472px;"><nobr>naming,  transactions  and  messages,  look  at  message </nobr></div>
<div style="position: absolute; top: 6276px; left: 472px;"><nobr>delivery  semantics,  and  consider  the  impact  of </nobr></div>
<div style="position: absolute; top: 6293px; left: 472px;"><nobr>repartioning  the  location  of  entities  on  these  message</nobr></div>
<div style="position: absolute; top: 6311px; left: 472px;"><nobr>delivery semantics.</nobr></div>
<div style="position: absolute; top: 6334px; left: 473px;"><nobr><b>Messages to Communicate across Entities</b></nobr></div>
<div style="position: absolute; top: 6356px; left: 494px;"><nobr>If you can’t update the data across two entities in the </nobr></div>
<div style="position: absolute; top: 6373px; left: 472px;"><nobr>same transaction,  you  need  a  mechanism  to  update  the </nobr></div>
<div style="position: absolute; top: 6390px; left: 473px;"><nobr>data in different transactions.  The connection between the </nobr></div>
<div style="position: absolute; top: 6407px; left: 473px;"><nobr>entities is via a message.</nobr></div>
<div style="position: absolute; top: 6431px; left: 473px;"><nobr><b>Asynchronous with Respect to Sending Transactions</b></nobr></div>
<div style="position: absolute; top: 6452px; left: 494px;"><nobr>Since messages are across entities, the data associated </nobr></div>
<div style="position: absolute; top: 6469px; left: 472px;"><nobr>with the decision to send the message is in one entity and </nobr></div>
<div style="position: absolute; top: 6487px; left: 472px;"><nobr>the destination of the message in another entity.  By the </nobr></div>
<div style="position: absolute; top: 6504px; left: 472px;"><nobr>definition of an entity, we must assume that they cannot </nobr></div>
<div style="position: absolute; top: 6521px; left: 472px;"><nobr>be atomically updated.   </nobr></div>
<div style="position: absolute; top: 6538px; left: 494px;"><nobr>It  would  be  horribly  complex  for  an  application </nobr></div>
<div style="position: absolute; top: 6556px; left: 472px;"><nobr>developer  to  send  a  message  while  working  on  a </nobr></div>
<div style="position: absolute; top: 6573px; left: 472px;"><nobr>transaction,  have  the  message  sent,  and  then  the </nobr></div>
<div style="position: absolute; top: 6590px; left: 472px;"><nobr>transaction  abort.  This  would  mean  that  you  have  no </nobr></div>
<div style="position: absolute; top: 6608px; left: 472px;"><nobr>memory of causing something to happen and yet it does</nobr></div>
<div style="position: absolute; top: 6625px; left: 472px;"><nobr>happen!  For  this  reason,  transactional  enqueuing  of </nobr></div>
<div style="position: absolute; top: 6642px; left: 472px;"><nobr>messages is de rigueur.</nobr></div>
<div style="position: absolute; top: 6834px; left: 494px;"><nobr>If the message cannot be seen at the destination until </nobr></div>
<div style="position: absolute; top: 6851px; left: 472px;"><nobr>after the sending transaction commits, we see the message </nobr></div>
<div style="position: absolute; top: 6868px; left: 473px;"><nobr>as asynchronous with respect to the sending transaction.</nobr></div>
<div style="position: absolute; top: 6885px; left: 473px;"><nobr>Each  entity  advances  to  a  new  state  with  a  transaction.  </nobr></div>
<div style="position: absolute; top: 6903px; left: 473px;"><nobr>Messages are the stimuli coming from one transaction and </nobr></div>
<div style="position: absolute; top: 6920px; left: 473px;"><nobr>arriving into a new entity causing transactions.</nobr></div>
<div style="position: absolute; top: 6944px; left: 473px;"><nobr><b>Naming the Destination of Messages</b></nobr></div>
<div style="position: absolute; top: 6965px; left: 494px;"><nobr>Consider  the programming of  the  scale-agnostic part </nobr></div>
<div style="position: absolute; top: 6982px; left: 472px;"><nobr>of an application as one entity wants to send a message to </nobr></div>
<div style="position: absolute; top: 6999px; left: 472px;"><nobr>another  entity.  The  location  of  the  destination  entity  is </nobr></div>
<div style="position: absolute; top: 7017px; left: 472px;"><nobr>not known to the scale-agnostic code.  The entity-key is.</nobr></div>
<div style="position: absolute; top: 7034px; left: 494px;"><nobr>It  falls  on  the  scale-aware  part  of  the  application  to </nobr></div>
<div style="position: absolute; top: 7051px; left: 472px;"><nobr>correlate the entity-key to the location of the entity.</nobr></div>
<div style="position: absolute; top: 7075px; left: 473px;"><nobr><b>Repartitioning and Message Delivery</b></nobr></div>
<div style="position: absolute; top: 7096px; left: 494px;"><nobr>When the scale-agnostic part of the application sends a </nobr></div>
<div style="position: absolute; top: 7114px; left: 472px;"><nobr>message, the lower-level scale-aware portion hunts down </nobr></div>
<div style="position: absolute; top: 7131px; left: 472px;"><nobr>the destination and delivers the message at-least-once.</nobr></div>
<div style="position: absolute; top: 7148px; left: 494px;"><nobr>As  the  system  scales,  entities  move. </nobr></div>
<div style="position: absolute; top: 7148px; left: 775px;"><nobr>This  is </nobr></div>
<div style="position: absolute; top: 7165px; left: 472px;"><nobr>commonly called repartitioning.  The location of the data </nobr></div>
</span></font>
<font face="Times" size="3"><span style="font-size: 13px; font-family: Times;">
<div style="position: absolute; top: 6685px; left: 705px;"><nobr>Entity-B</nobr></div>
<div style="position: absolute; top: 6777px; left: 682px;"><nobr>Transaction T2</nobr></div>
<div style="position: absolute; top: 6684px; left: 538px;"><nobr>Entity-A</nobr></div>
<div style="position: absolute; top: 6737px; left: 514px;"><nobr>Transaction T1</nobr></div>
<div style="position: absolute; top: 6685px; left: 705px;"><nobr>Entity-B</nobr></div>
<div style="position: absolute; top: 6777px; left: 682px;"><nobr>Transaction T2</nobr></div>
<div style="position: absolute; top: 6777px; left: 682px;"><nobr>Transaction T2</nobr></div>
<div style="position: absolute; top: 6684px; left: 538px;"><nobr>Entity-A</nobr></div>
<div style="position: absolute; top: 6737px; left: 514px;"><nobr>Transaction T1</nobr></div>
<div style="position: absolute; top: 6737px; left: 514px;"><nobr>Transaction T1</nobr></div>
<div style="position: absolute; top: 6602px; left: 293px;"><nobr>Entity keys indexed</nobr></div>
<div style="position: absolute; top: 6616px; left: 299px;"><nobr>by secondary key</nobr></div>
<div style="position: absolute; top: 6656px; left: 301px;"><nobr>K</nobr></div>
<div style="position: absolute; top: 6646px; left: 301px;"><nobr>e</nobr></div>
<div style="position: absolute; top: 6637px; left: 301px;"><nobr>y</nobr></div>
<div style="position: absolute; top: 6656px; left: 332px;"><nobr>K</nobr></div>
<div style="position: absolute; top: 6646px; left: 332px;"><nobr>e</nobr></div>
<div style="position: absolute; top: 6637px; left: 332px;"><nobr>y</nobr></div>
<div style="position: absolute; top: 6655px; left: 362px;"><nobr>K</nobr></div>
<div style="position: absolute; top: 6645px; left: 362px;"><nobr>e</nobr></div>
<div style="position: absolute; top: 6637px; left: 362px;"><nobr>y</nobr></div>
<div style="position: absolute; top: 6656px; left: 393px;"><nobr>K</nobr></div>
<div style="position: absolute; top: 6646px; left: 393px;"><nobr>e</nobr></div>
<div style="position: absolute; top: 6637px; left: 393px;"><nobr>y</nobr></div>
<div style="position: absolute; top: 6656px; left: 423px;"><nobr>K</nobr></div>
<div style="position: absolute; top: 6646px; left: 423px;"><nobr>e</nobr></div>
<div style="position: absolute; top: 6637px; left: 423px;"><nobr>y</nobr></div>
<div style="position: absolute; top: 6608px; left: 123px;"><nobr>Entities indexed</nobr></div>
<div style="position: absolute; top: 6622px; left: 130px;"><nobr>by unique key</nobr></div>
<div style="position: absolute; top: 6531px; left: 103px;"><nobr>Different keys (primary entity-key versus alternate</nobr></div>
<div style="position: absolute; top: 6547px; left: 103px;"><nobr>keys) cannot be collocated or updated atomically.</nobr></div>
<div style="position: absolute; top: 6602px; left: 293px;"><nobr>Entity keys indexed</nobr></div>
<div style="position: absolute; top: 6616px; left: 299px;"><nobr>by secondary key</nobr></div>
<div style="position: absolute; top: 6656px; left: 301px;"><nobr>K</nobr></div>
<div style="position: absolute; top: 6646px; left: 301px;"><nobr>e</nobr></div>
<div style="position: absolute; top: 6637px; left: 301px;"><nobr>y</nobr></div>
<div style="position: absolute; top: 6656px; left: 332px;"><nobr>K</nobr></div>
<div style="position: absolute; top: 6646px; left: 332px;"><nobr>e</nobr></div>
<div style="position: absolute; top: 6637px; left: 332px;"><nobr>y</nobr></div>
<div style="position: absolute; top: 6655px; left: 362px;"><nobr>K</nobr></div>
<div style="position: absolute; top: 6645px; left: 362px;"><nobr>e</nobr></div>
<div style="position: absolute; top: 6637px; left: 362px;"><nobr>y</nobr></div>
<div style="position: absolute; top: 6656px; left: 393px;"><nobr>K</nobr></div>
<div style="position: absolute; top: 6646px; left: 393px;"><nobr>e</nobr></div>
<div style="position: absolute; top: 6637px; left: 393px;"><nobr>y</nobr></div>
<div style="position: absolute; top: 6656px; left: 423px;"><nobr>K</nobr></div>
<div style="position: absolute; top: 6646px; left: 423px;"><nobr>e</nobr></div>
<div style="position: absolute; top: 6637px; left: 423px;"><nobr>y</nobr></div>
<div style="position: absolute; top: 6602px; left: 293px;"><nobr>Entity keys indexed</nobr></div>
<div style="position: absolute; top: 6616px; left: 299px;"><nobr>by secondary key</nobr></div>
<div style="position: absolute; top: 6656px; left: 301px;"><nobr>K</nobr></div>
<div style="position: absolute; top: 6646px; left: 301px;"><nobr>e</nobr></div>
<div style="position: absolute; top: 6637px; left: 301px;"><nobr>y</nobr></div>
<div style="position: absolute; top: 6656px; left: 332px;"><nobr>K</nobr></div>
<div style="position: absolute; top: 6646px; left: 332px;"><nobr>e</nobr></div>
<div style="position: absolute; top: 6637px; left: 332px;"><nobr>y</nobr></div>
<div style="position: absolute; top: 6655px; left: 362px;"><nobr>K</nobr></div>
<div style="position: absolute; top: 6645px; left: 362px;"><nobr>e</nobr></div>
<div style="position: absolute; top: 6637px; left: 362px;"><nobr>y</nobr></div>
<div style="position: absolute; top: 6656px; left: 393px;"><nobr>K</nobr></div>
<div style="position: absolute; top: 6646px; left: 393px;"><nobr>e</nobr></div>
<div style="position: absolute; top: 6637px; left: 393px;"><nobr>y</nobr></div>
<div style="position: absolute; top: 6656px; left: 423px;"><nobr>K</nobr></div>
<div style="position: absolute; top: 6646px; left: 423px;"><nobr>e</nobr></div>
<div style="position: absolute; top: 6637px; left: 423px;"><nobr>y</nobr></div>
<div style="position: absolute; top: 6608px; left: 123px;"><nobr>Entities indexed</nobr></div>
<div style="position: absolute; top: 6622px; left: 130px;"><nobr>by unique key</nobr></div>
<div style="position: absolute; top: 6608px; left: 123px;"><nobr>Entities indexed</nobr></div>
<div style="position: absolute; top: 6622px; left: 130px;"><nobr>by unique key</nobr></div>
<div style="position: absolute; top: 6531px; left: 103px;"><nobr>Different keys (primary entity-key versus alternate</nobr></div>
<div style="position: absolute; top: 6547px; left: 103px;"><nobr>keys) cannot be collocated or updated atomically.</nobr></div>
</span></font>
<font face="Times" size="3"><span style="font-size: 10px; font-family: Times;">
<div style="position: absolute; top: 7233px; left: 449px;"><nobr>137</nobr></div>
</span></font>

<div style="position: absolute; top: 7303px; left: 0pt;"><hr><table border="0" width="100%"><tbody><tr><td align="right" bgcolor="#eeeeee"><font face="arial,sans-serif"><a name="7"><b>Page 7</b></a></font></td></tr></tbody></table></div><font face="Times" size="3"><span style="font-size: 12px; font-family: Times;">
<div style="position: absolute; top: 7387px; left: 95px;"><nobr>for the entity and, hence, the destination for the message </nobr></div>
<div style="position: absolute; top: 7404px; left: 95px;"><nobr>may be in flux.  Sometimes, messages will chase to the </nobr></div>
<div style="position: absolute; top: 7422px; left: 95px;"><nobr>old location only to find out the pesky entity has been sent </nobr></div>
<div style="position: absolute; top: 7439px; left: 95px;"><nobr>elsewhere.  Now, the message will have to follow.</nobr></div>
<div style="position: absolute; top: 7456px; left: 116px;"><nobr>As  entities  move,  the  clarity  of  a  first-in-first-out</nobr></div>
<div style="position: absolute; top: 7473px; left: 95px;"><nobr>queue  between  the  sender  and  the  destination  is </nobr></div>
<div style="position: absolute; top: 7491px; left: 95px;"><nobr>occasionally  disrupted.  Messages  are  repeated.  Later </nobr></div>
<div style="position: absolute; top: 7508px; left: 95px;"><nobr>messages arrive before earlier ones.  Life gets messier.</nobr></div>
<div style="position: absolute; top: 7525px; left: 116px;"><nobr>For these reasons,  we  see scale-agnostic applications</nobr></div>
<div style="position: absolute; top: 7542px; left: 95px;"><nobr>are  evolving  to  support  idempotent  processing  of  all </nobr></div>
<div style="position: absolute; top: 7560px; left: 95px;"><nobr>application-visible  messaging</nobr></div>
</span></font>
<font face="Times" size="2"><span style="font-size: 7px; font-family: Times;">
<div style="position: absolute; top: 7556px; left: 275px;"><nobr>11</nobr></div>
</span></font>
<font face="Times" size="3"><span style="font-size: 12px; font-family: Times;">
<div style="position: absolute; top: 7560px; left: 285px;"><nobr>.  This implies reordering </nobr></div>
<div style="position: absolute; top: 7577px; left: 94px;"><nobr>in message delivery, too.</nobr></div>
</span></font>
<font face="Times" size="3"><span style="font-size: 16px; font-family: Times;">
<div style="position: absolute; top: 7610px; left: 95px;"><nobr><b>4. ENTITIES, SOA, AND OBJECTS</b></nobr></div>
</span></font>
<font face="Times" size="3"><span style="font-size: 12px; font-family: Times;">
<div style="position: absolute; top: 7634px; left: 116px;"><nobr>This section contrasts the ideas in this paper to those </nobr></div>
<div style="position: absolute; top: 7652px; left: 94px;"><nobr>of object orientation and service orientation. </nobr></div>
<div style="position: absolute; top: 7675px; left: 95px;"><nobr><b>Entities and Object Instances</b></nobr></div>
<div style="position: absolute; top: 7697px; left: 116px;"><nobr>One  may  ask:  “How  is  an  entity  different  than  an </nobr></div>
<div style="position: absolute; top: 7714px; left: 94px;"><nobr>object  instance?”  The  answer  is  not  black  and  white.  </nobr></div>
<div style="position: absolute; top: 7731px; left: 94px;"><nobr>Objects have many forms, some of which are entities and </nobr></div>
<div style="position: absolute; top: 7748px; left: 94px;"><nobr>others  which  are  not. </nobr></div>
<div style="position: absolute; top: 7748px; left: 273px;"><nobr>There  are  two  important </nobr></div>
<div style="position: absolute; top: 7766px; left: 94px;"><nobr>clarifications that must be made to consider an object to </nobr></div>
<div style="position: absolute; top: 7783px; left: 94px;"><nobr>be an entity.</nobr></div>
<div style="position: absolute; top: 7800px; left: 116px;"><nobr>First,  the  data  encapsulated  by  the  object  must  be </nobr></div>
<div style="position: absolute; top: 7817px; left: 94px;"><nobr>strictly disjoint from all other data.  Second, that disjoint</nobr></div>
<div style="position: absolute; top: 7834px; left: 94px;"><nobr>data may never be atomically updated with any other data.</nobr></div>
<div style="position: absolute; top: 7852px; left: 116px;"><nobr>Some  object  systems  have  ambiguous  encapsulation </nobr></div>
<div style="position: absolute; top: 7869px; left: 94px;"><nobr>of database  data.  To the extent  these  are  not crisp  and </nobr></div>
<div style="position: absolute; top: 7886px; left: 94px;"><nobr>diligently  enforced;  these  objects  are  not  entities  as </nobr></div>
<div style="position: absolute; top: 7904px; left: 94px;"><nobr>defined  herein.  Sometimes,  materialized  views  and </nobr></div>
<div style="position: absolute; top: 7921px; left: 94px;"><nobr>alternate  indices  are  used.  These  won’t  last  when  your </nobr></div>
<div style="position: absolute; top: 7938px; left: 94px;"><nobr>system attempts to scale and your objects aren’t entities.</nobr></div>
<div style="position: absolute; top: 7955px; left: 116px;"><nobr>Many object systems allow transaction scopes to span </nobr></div>
<div style="position: absolute; top: 7972px; left: 94px;"><nobr>objects.  This programmatic convenience obviates most of </nobr></div>
<div style="position: absolute; top: 7990px; left: 94px;"><nobr>the challenges described in this paper.  Unfortunately, that </nobr></div>
<div style="position: absolute; top: 8007px; left: 94px;"><nobr>doesn’t  work  under  almost-infinite  scaling  unless  your </nobr></div>
<div style="position: absolute; top: 8024px; left: 94px;"><nobr>transactionally-coupled  objects  are always  collocated</nobr></div>
</span></font>
<font face="Times" size="2"><span style="font-size: 7px; font-family: Times;">
<div style="position: absolute; top: 8021px; left: 432px;"><nobr>12</nobr></div>
</span></font>
<font face="Times" size="3"><span style="font-size: 12px; font-family: Times;">
<div style="position: absolute; top: 8024px; left: 442px;"><nobr>.  </nobr></div>
<div style="position: absolute; top: 8041px; left: 94px;"><nobr>To  do  this,  we  need  to  assign  them  a  common  key  to </nobr></div>
<div style="position: absolute; top: 8059px; left: 94px;"><nobr>ensure  co-location  and  then  realize  the  two </nobr></div>
<div style="position: absolute; top: 8076px; left: 94px;"><nobr>transactionally-coupled  objects  are  part  of  the  same </nobr></div>
<div style="position: absolute; top: 8093px; left: 94px;"><nobr>entity!</nobr></div>
<div style="position: absolute; top: 8111px; left: 116px;"><nobr>Objects are great but they are a different abstraction.</nobr></div>
</span></font>
<font face="Times" size="2"><span style="font-size: 7px; font-family: Times;">
<div style="position: absolute; top: 8215px; left: 95px;"><nobr>11</nobr></div>
</span></font>
<font face="Times" size="3"><span style="font-size: 12px; font-family: Times;">
<div style="position: absolute; top: 8218px; left: 113px;"><nobr>It  is  common  that  scale-aware  applications  are  not </nobr></div>
<div style="position: absolute; top: 8235px; left: 94px;"><nobr>initially  designed  for  idempotence  and  re-ordering  of </nobr></div>
<div style="position: absolute; top: 8253px; left: 94px;"><nobr>messages.  At  first,  small  scale  deployments  do  not </nobr></div>
<div style="position: absolute; top: 8270px; left: 94px;"><nobr>exhibit these subtle problems and work fine.  Only as time </nobr></div>
<div style="position: absolute; top: 8287px; left: 94px;"><nobr>passes  and  their  deployments  expand  do  the  problems </nobr></div>
<div style="position: absolute; top: 8304px; left: 94px;"><nobr>manifest and the applications respond to handle them.</nobr></div>
</span></font>
<font face="Times" size="2"><span style="font-size: 7px; font-family: Times;">
<div style="position: absolute; top: 8318px; left: 95px;"><nobr>12</nobr></div>
</span></font>
<font face="Times" size="3"><span style="font-size: 12px; font-family: Times;">
<div style="position: absolute; top: 8321px; left: 111px;"><nobr>Alternatively,  you  could  forget  about  collocation  and </nobr></div>
<div style="position: absolute; top: 8339px; left: 95px;"><nobr>use two  phase  commit.  Per  our  assumptions,  we assert </nobr></div>
<div style="position: absolute; top: 8356px; left: 95px;"><nobr>natural selection will kick in eliminating this problem…</nobr></div>
<div style="position: absolute; top: 7394px; left: 473px;"><nobr><b>Messages versus Methods</b></nobr></div>
<div style="position: absolute; top: 7415px; left: 494px;"><nobr>Method calls are typically synchronous with respect to </nobr></div>
<div style="position: absolute; top: 7432px; left: 472px;"><nobr>the  calling  thread.</nobr></div>
<div style="position: absolute; top: 7432px; left: 610px;"><nobr>They  are  also  synchronous  with </nobr></div>
<div style="position: absolute; top: 7449px; left: 472px;"><nobr>respect  to  the  calling  object’s  transaction.  While  the</nobr></div>
<div style="position: absolute; top: 7467px; left: 472px;"><nobr>called object may or may not be atomically coupled with </nobr></div>
<div style="position: absolute; top: 7484px; left: 472px;"><nobr>the  calling  object,  the  typical  method  call  does  not </nobr></div>
<div style="position: absolute; top: 7501px; left: 472px;"><nobr>atomically  record the  intent to  invoke  a  message  and </nobr></div>
<div style="position: absolute; top: 7518px; left: 472px;"><nobr>guarantee  the  at-least-once invocation  of  the  called</nobr></div>
<div style="position: absolute; top: 7536px; left: 472px;"><nobr>message. Some  systems  wrap  message-sending  into  a </nobr></div>
<div style="position: absolute; top: 7553px; left: 472px;"><nobr>method  call  and  I  consider those  to  be  messages,  not </nobr></div>
<div style="position: absolute; top: 7570px; left: 472px;"><nobr>methods.</nobr></div>
<div style="position: absolute; top: 7587px; left: 494px;"><nobr>We  don’t address the  differences  in  marshalling  and </nobr></div>
<div style="position: absolute; top: 7605px; left: 472px;"><nobr>binding  that  usually  separate messaging  from  methods.  </nobr></div>
<div style="position: absolute; top: 7622px; left: 472px;"><nobr>We  simply  point  out  that  transactional  boundaries </nobr></div>
<div style="position: absolute; top: 7639px; left: 472px;"><nobr>mandate asynchrony not usually found with method calls.</nobr></div>
<div style="position: absolute; top: 7663px; left: 473px;"><nobr><b>Entities and Service Oriented Architectures</b></nobr></div>
<div style="position: absolute; top: 7684px; left: 494px;"><nobr>Everything  discussed  in  this  paper  is  supportive  of </nobr></div>
<div style="position: absolute; top: 7701px; left: 472px;"><nobr>SOA.  Most SOA implementations embrace independent </nobr></div>
<div style="position: absolute; top: 7719px; left: 472px;"><nobr>transaction scopes across services.</nobr></div>
<div style="position: absolute; top: 7736px; left: 494px;"><nobr>The major enhancement to SOA presented here is the </nobr></div>
<div style="position: absolute; top: 7753px; left: 472px;"><nobr>notion  that  each  service  may  confront almost-infinite </nobr></div>
<div style="position: absolute; top: 7770px; left: 472px;"><nobr>scaling  within  itself  and  some  observations  about  what </nobr></div>
<div style="position: absolute; top: 7788px; left: 472px;"><nobr>that means.  These observations apply across services in a </nobr></div>
<div style="position: absolute; top: 7805px; left: 472px;"><nobr>SOA and within those individual services where they are </nobr></div>
<div style="position: absolute; top: 7822px; left: 472px;"><nobr>designed to independently scale.</nobr></div>
</span></font>
<font face="Times" size="3"><span style="font-size: 16px; font-family: Times;">
<div style="position: absolute; top: 7855px; left: 473px;"><nobr><b>5. ACTIVITIES:  COPING  WITH  MESSY</b></nobr></div>
<div style="position: absolute; top: 7876px; left: 500px;"><nobr><b>MESSAGES</b></nobr></div>
</span></font>
<font face="Times" size="3"><span style="font-size: 12px; font-family: Times;">
<div style="position: absolute; top: 7900px; left: 494px;"><nobr>This  section  discusses  means  to  cope  with  the </nobr></div>
<div style="position: absolute; top: 7917px; left: 472px;"><nobr>challenges  of  message  retries  and  reordering. </nobr></div>
<div style="position: absolute; top: 7917px; left: 803px;"><nobr>We </nobr></div>
<div style="position: absolute; top: 7935px; left: 472px;"><nobr>introduce the notion of an activity as the local information</nobr></div>
<div style="position: absolute; top: 7952px; left: 472px;"><nobr>needed to manage a relationship with a partner entity.</nobr></div>
<div style="position: absolute; top: 7976px; left: 473px;"><nobr><b>Retries and Idempotence</b></nobr></div>
<div style="position: absolute; top: 7997px; left: 494px;"><nobr>Since  any  message  ever  sent  may  be  delivered</nobr></div>
<div style="position: absolute; top: 8014px; left: 472px;"><nobr>multiple times, we need a discipline in the application to </nobr></div>
<div style="position: absolute; top: 8031px; left: 472px;"><nobr>cope with repeated messages.  While it is possible to build </nobr></div>
<div style="position: absolute; top: 8049px; left: 472px;"><nobr>low-level  support  for  the  elimination  of  duplicate </nobr></div>
<div style="position: absolute; top: 8066px; left: 472px;"><nobr>messages,  in an almost-infinite  scaling environment, the </nobr></div>
<div style="position: absolute; top: 8083px; left: 472px;"><nobr>low-level support would need to know about entities.  The</nobr></div>
<div style="position: absolute; top: 8100px; left: 472px;"><nobr>knowledge of which messages have been delivered to the </nobr></div>
<div style="position: absolute; top: 8118px; left: 472px;"><nobr>entity must travel  with the  entity  when  it  moves due  to </nobr></div>
<div style="position: absolute; top: 8135px; left: 472px;"><nobr>repartitioning.  In practice, the low-level management of </nobr></div>
<div style="position: absolute; top: 8152px; left: 472px;"><nobr>this knowledge rarely occurs; messages may be delivered </nobr></div>
<div style="position: absolute; top: 8169px; left: 472px;"><nobr>more than once.</nobr></div>
<div style="position: absolute; top: 8187px; left: 494px;"><nobr>Typically, the scale-agnostic (higher-level) portion of</nobr></div>
<div style="position: absolute; top: 8204px; left: 472px;"><nobr>the  application must  implement  mechanisms  to  ensure </nobr></div>
<div style="position: absolute; top: 8221px; left: 472px;"><nobr>that  the  incoming  message  is  idempotent.  This  is  not </nobr></div>
<div style="position: absolute; top: 8238px; left: 472px;"><nobr>essential  to  the  nature  of  the  problem. </nobr></div>
<div style="position: absolute; top: 8238px; left: 765px;"><nobr>Duplicate</nobr></div>
<div style="position: absolute; top: 8256px; left: 472px;"><nobr>elimination  could  certainly be  built into  the  scale-aware</nobr></div>
<div style="position: absolute; top: 8273px; left: 472px;"><nobr>parts of the application.  So far, this is not yet available. </nobr></div>
<div style="position: absolute; top: 8290px; left: 472px;"><nobr>Hence, we consider what the poor developer of the scale-</nobr></div>
<div style="position: absolute; top: 8307px; left: 472px;"><nobr>agnostic application must implement.</nobr></div>
</span></font>
<font face="Times" size="3"><span style="font-size: 10px; font-family: Times;">
<div style="position: absolute; top: 8421px; left: 449px;"><nobr>138</nobr></div>
</span></font>

<div style="position: absolute; top: 8491px; left: 0pt;"><hr><table border="0" width="100%"><tbody><tr><td align="right" bgcolor="#eeeeee"><font face="arial,sans-serif"><a name="8"><b>Page 8</b></a></font></td></tr></tbody></table></div><font face="Times" size="3"><span style="font-size: 12px; font-family: Times;">
<div style="position: absolute; top: 8575px; left: 95px;"><nobr><b>Defining Idempotence of Substantive Behavior</b></nobr></div>
<div style="position: absolute; top: 8597px; left: 116px;"><nobr>The  processing  of  a  message  is  idempotent  if  a </nobr></div>
<div style="position: absolute; top: 8614px; left: 94px;"><nobr>subsequent execution of the processing does not perform </nobr></div>
<div style="position: absolute; top: 8631px; left: 94px;"><nobr>a <i>substantive change </i>to the entity.  This is an amorphous </nobr></div>
<div style="position: absolute; top: 8649px; left: 95px;"><nobr>definition  which  leaves  open  to  the  application the </nobr></div>
<div style="position: absolute; top: 8666px; left: 95px;"><nobr>specification of what is and what is not substantive.  </nobr></div>
<div style="position: absolute; top: 8683px; left: 116px;"><nobr>If a  message does not change the invoked  entity but </nobr></div>
<div style="position: absolute; top: 8700px; left: 95px;"><nobr>only reads information, its processing is idempotent.  We </nobr></div>
<div style="position: absolute; top: 8718px; left: 95px;"><nobr>consider this to be true even if a log record describing the </nobr></div>
<div style="position: absolute; top: 8735px; left: 95px;"><nobr>read is written.  The log record is not substantive to the </nobr></div>
<div style="position: absolute; top: 8752px; left: 95px;"><nobr>behaviour  of  the  entity.  The  definition  of  what  is  and </nobr></div>
<div style="position: absolute; top: 8769px; left: 95px;"><nobr>what is not substantive is application specific.</nobr></div>
</span></font>
<font face="Times" size="2"><span style="font-size: 7px; font-family: Times;">
<div style="position: absolute; top: 8766px; left: 370px;"><nobr>13</nobr></div>
</span></font>
<font face="Times" size="3"><span style="font-size: 12px; font-family: Times;">
<div style="position: absolute; top: 8793px; left: 95px;"><nobr><b>Natural Idempotence</b></nobr></div>
<div style="position: absolute; top: 8814px; left: 116px;"><nobr>To  accomplish  idempotence, it  is  essential  that  the </nobr></div>
<div style="position: absolute; top: 8832px; left: 94px;"><nobr>message  does  not  cause  substantive  side-effects.  Some </nobr></div>
<div style="position: absolute; top: 8849px; left: 94px;"><nobr>messages provoke no substantive work any time they are </nobr></div>
<div style="position: absolute; top: 8866px; left: 94px;"><nobr>processed. These are <i>naturally idempotent</i>.  </nobr></div>
<div style="position: absolute; top: 8883px; left: 116px;"><nobr>A message that only reads some data from an entity is </nobr></div>
<div style="position: absolute; top: 8901px; left: 94px;"><nobr>naturally  idempotent.  What  if  the  processing  of  a </nobr></div>
<div style="position: absolute; top: 8918px; left: 94px;"><nobr>message does change the entity but not in a way that is </nobr></div>
<div style="position: absolute; top: 8935px; left: 94px;"><nobr>substantive?  Those, too, would be naturally idempotent.</nobr></div>
<div style="position: absolute; top: 8952px; left: 116px;"><nobr>Now,  it  gets  harder.  The  work  implied  by  some</nobr></div>
<div style="position: absolute; top: 8970px; left: 94px;"><nobr>messages  actually  cause  substantive  changes.  These</nobr></div>
<div style="position: absolute; top: 8987px; left: 94px;"><nobr>messages  are  not naturally  idempotent.  The  application </nobr></div>
<div style="position: absolute; top: 9004px; left: 94px;"><nobr>must  include  mechanisms to  ensure  that  these,  too,  are </nobr></div>
<div style="position: absolute; top: 9021px; left: 94px;"><nobr>idempotent.  This  means  remembering  in  some  fashion </nobr></div>
<div style="position: absolute; top: 9039px; left: 94px;"><nobr>that  the  message  has  been  processed  so  that  subsequent</nobr></div>
<div style="position: absolute; top: 9056px; left: 94px;"><nobr>attempts make no substantive change.</nobr></div>
</span></font>
<font face="Times" size="2"><span style="font-size: 7px; font-family: Times;">
<div style="position: absolute; top: 9052px; left: 322px;"><nobr>14</nobr></div>
</span></font>
<font face="Times" size="3"><span style="font-size: 12px; font-family: Times;">
<div style="position: absolute; top: 9073px; left: 116px;"><nobr>It is the processing of messages that are not naturally</nobr></div>
<div style="position: absolute; top: 9090px; left: 94px;"><nobr>idempotent that we consider next.</nobr></div>
<div style="position: absolute; top: 9114px; left: 95px;"><nobr><b>Remembering Messages as State</b></nobr></div>
<div style="position: absolute; top: 9135px; left: 116px;"><nobr>To ensure the idempotent processing of messages that </nobr></div>
<div style="position: absolute; top: 9153px; left: 94px;"><nobr>are  not  naturally  idempotent,  the  entity  must  remember </nobr></div>
<div style="position: absolute; top: 9170px; left: 94px;"><nobr>they have been processed. This knowledge is state.  The </nobr></div>
<div style="position: absolute; top: 9187px; left: 95px;"><nobr>state accumulates as messages are processed.</nobr></div>
<div style="position: absolute; top: 9204px; left: 116px;"><nobr>In addition  to  remembering that  a  message has been </nobr></div>
<div style="position: absolute; top: 9222px; left: 95px;"><nobr>processed, if a reply is required, the same reply must be </nobr></div>
</span></font>
<font face="Times" size="2"><span style="font-size: 7px; font-family: Times;">
<div style="position: absolute; top: 9265px; left: 95px;"><nobr>13</nobr></div>
</span></font>
<font face="Times" size="3"><span style="font-size: 12px; font-family: Times;">
<div style="position: absolute; top: 9268px; left: 113px;"><nobr>In  the  database  community,  we  frequently  use  this</nobr></div>
<div style="position: absolute; top: 9285px; left: 94px;"><nobr>technique.  For  example,  in  a  physiological  logging </nobr></div>
<div style="position: absolute; top: 9303px; left: 94px;"><nobr>(ARIES-style) system, a logical undo of a transaction will </nobr></div>
<div style="position: absolute; top: 9320px; left: 94px;"><nobr>leave  the  system  with  the  same  records  as  before  the </nobr></div>
<div style="position: absolute; top: 9337px; left: 94px;"><nobr>transaction.  In doing  so,  the  layout of  the pages  in  the </nobr></div>
<div style="position: absolute; top: 9354px; left: 94px;"><nobr>Btree  may  be  different.  This  is  not  substantive  to  the </nobr></div>
<div style="position: absolute; top: 9371px; left: 94px;"><nobr>record-level interpretation of the contents of the Btree.</nobr></div>
</span></font>
<font face="Times" size="2"><span style="font-size: 7px; font-family: Times;">
<div style="position: absolute; top: 9385px; left: 95px;"><nobr>14</nobr></div>
</span></font>
<font face="Times" size="3"><span style="font-size: 12px; font-family: Times;">
<div style="position: absolute; top: 9389px; left: 110px;"><nobr>Note that it is hard (but certainly doable) to build the </nobr></div>
<div style="position: absolute; top: 9406px; left: 95px;"><nobr>plumbing  to  eliminate  duplicates  as  messages  flow </nobr></div>
<div style="position: absolute; top: 9423px; left: 95px;"><nobr>between  fine-grained  entities.  Most  durable  duplicate </nobr></div>
<div style="position: absolute; top: 9441px; left: 95px;"><nobr>elimination is done on queues with a coarser granularity </nobr></div>
<div style="position: absolute; top: 9458px; left: 95px;"><nobr>than  the  entity. </nobr></div>
<div style="position: absolute; top: 9458px; left: 222px;"><nobr>Migrating  the  state  for  duplicate </nobr></div>
<div style="position: absolute; top: 9475px; left: 95px;"><nobr>elimination  with  the  entity  isn’t  commonly  available. </nobr></div>
<div style="position: absolute; top: 9492px; left: 95px;"><nobr>Hence,  a  common  pattern  is  that  the  scale-agnostic </nobr></div>
<div style="position: absolute; top: 9510px; left: 95px;"><nobr>application  contains  application  specific  logic  to  ensure </nobr></div>
<div style="position: absolute; top: 9527px; left: 95px;"><nobr>redundant  processing  of  messages  has  no  substantive </nobr></div>
<div style="position: absolute; top: 9544px; left: 95px;"><nobr>impact on the entity.</nobr></div>
<div style="position: absolute; top: 8575px; left: 473px;"><nobr>returned.  After all, we don’t know if the original sender </nobr></div>
<div style="position: absolute; top: 8592px; left: 473px;"><nobr>has received the reply or not.</nobr></div>
<div style="position: absolute; top: 8616px; left: 473px;"><nobr><b>Activities: Managing State for Each Partner</b></nobr></div>
<div style="position: absolute; top: 8637px; left: 494px;"><nobr>To track relationships and the messages received, each </nobr></div>
<div style="position: absolute; top: 8655px; left: 472px;"><nobr>entity  within  the  scale-agnostic  application must </nobr></div>
<div style="position: absolute; top: 8672px; left: 472px;"><nobr>somehow remember  state information about its partners.  </nobr></div>
<div style="position: absolute; top: 8689px; left: 473px;"><nobr>It  must  capture  this  state  on  a  partner  by  partner  basis.  </nobr></div>
<div style="position: absolute; top: 8706px; left: 473px;"><nobr>Let’s name this state an <i>activity</i>.  Each entity may have </nobr></div>
<div style="position: absolute; top: 8724px; left: 473px;"><nobr>many  activities  if  it  interacts  with  many  other  entities.  </nobr></div>
<div style="position: absolute; top: 8741px; left: 473px;"><nobr>Activities track the interactions with each partner.</nobr></div>
<div style="position: absolute; top: 8895px; left: 494px;"><nobr>Each entity comprises a set of activities and, perhaps, </nobr></div>
<div style="position: absolute; top: 8912px; left: 473px;"><nobr>some other data that spans the activities.</nobr></div>
<div style="position: absolute; top: 8934px; left: 473px;"><nobr>Consider  the  processing  of  an  order comprising  many </nobr></div>
<div style="position: absolute; top: 8951px; left: 473px;"><nobr>items for purchase.  Reserving inventory for shipment of </nobr></div>
<div style="position: absolute; top: 8968px; left: 473px;"><nobr>each separate item will be a separate activity.  There will </nobr></div>
<div style="position: absolute; top: 8985px; left: 473px;"><nobr>be  an  entity  for  the  order  and  separate  entities  for  each </nobr></div>
<div style="position: absolute; top: 9003px; left: 473px;"><nobr>item managed by the warehouse.  Transactions cannot be </nobr></div>
<div style="position: absolute; top: 9020px; left: 473px;"><nobr>assumed across these entities.</nobr></div>
<div style="position: absolute; top: 9042px; left: 473px;"><nobr>Within the order, each inventory item will be separately </nobr></div>
<div style="position: absolute; top: 9059px; left: 473px;"><nobr>managed.  The  messaging  protocol  must  be  separately</nobr></div>
<div style="position: absolute; top: 9077px; left: 473px;"><nobr>managed.  The per-inventory-item data contained  within </nobr></div>
<div style="position: absolute; top: 9094px; left: 473px;"><nobr>the order-entity is an activity.  While it is not named as </nobr></div>
<div style="position: absolute; top: 9111px; left: 473px;"><nobr>such, this pattern frequently exists in large-scale apps.</nobr></div>
<div style="position: absolute; top: 9136px; left: 494px;"><nobr>In an almost-infinitely scaled application, you need to </nobr></div>
<div style="position: absolute; top: 9153px; left: 472px;"><nobr>be very clear about relationships because you can’t just do </nobr></div>
<div style="position: absolute; top: 9170px; left: 472px;"><nobr>a query to figure out what is related.  Everything must be </nobr></div>
<div style="position: absolute; top: 9188px; left: 472px;"><nobr>formally  knit  together  using  a  web  of  two-party </nobr></div>
<div style="position: absolute; top: 9205px; left: 472px;"><nobr>relationships. </nobr></div>
<div style="position: absolute; top: 9205px; left: 574px;"><nobr>The  knitting  is  with  the  entity-keys.  </nobr></div>
<div style="position: absolute; top: 9222px; left: 472px;"><nobr>Because  the  partner  is  a  long  ways  away,  you  have  to </nobr></div>
<div style="position: absolute; top: 9239px; left: 472px;"><nobr>formally manage your understanding of the partners state </nobr></div>
<div style="position: absolute; top: 9257px; left: 472px;"><nobr>as  new  knowledge  of  the  partner  arrives.  The  local </nobr></div>
<div style="position: absolute; top: 9274px; left: 472px;"><nobr>information  that  you  know  about  a  distant  partner  is </nobr></div>
<div style="position: absolute; top: 9291px; left: 472px;"><nobr>referred to as an activity.</nobr></div>
</span></font>
<font face="Times" size="3"><span style="font-size: 13px; font-family: Times;">
<div style="position: absolute; top: 9508px; left: 477px;"><nobr>If an entity works with many partners, it will have</nobr></div>
<div style="position: absolute; top: 9526px; left: 477px;"><nobr>many activities. These are one per partner.</nobr></div>
<div style="position: absolute; top: 9329px; left: 723px;"><nobr>Entity-A</nobr></div>
<div style="position: absolute; top: 9393px; left: 499px;"><nobr>Entity-B</nobr></div>
<div style="position: absolute; top: 9419px; left: 498px;"><nobr>Entity-D</nobr></div>
<div style="position: absolute; top: 9379px; left: 584px;"><nobr>Entity-C</nobr></div>
<div style="position: absolute; top: 9508px; left: 477px;"><nobr>If an entity works with many partners, it will have</nobr></div>
<div style="position: absolute; top: 9526px; left: 477px;"><nobr>many activities. These are one per partner.</nobr></div>
<div style="position: absolute; top: 9329px; left: 723px;"><nobr>Entity-A</nobr></div>
<div style="position: absolute; top: 9393px; left: 499px;"><nobr>Entity-B</nobr></div>
<div style="position: absolute; top: 9419px; left: 498px;"><nobr>Entity-D</nobr></div>
<div style="position: absolute; top: 9379px; left: 584px;"><nobr>Entity-C</nobr></div>
<div style="position: absolute; top: 8783px; left: 564px;"><nobr>Entity-X</nobr></div>
<div style="position: absolute; top: 8814px; left: 492px;"><nobr>Msg-A</nobr></div>
<div style="position: absolute; top: 8847px; left: 492px;"><nobr>Msg-B</nobr></div>
<div style="position: absolute; top: 8781px; left: 665px;"><nobr>Activities are simple.</nobr></div>
<div style="position: absolute; top: 8798px; left: 656px;"><nobr>They are what an entity</nobr></div>
<div style="position: absolute; top: 8816px; left: 655px;"><nobr>remembers about other</nobr></div>
<div style="position: absolute; top: 8834px; left: 649px;"><nobr>entities it works with. This </nobr></div>
<div style="position: absolute; top: 8852px; left: 648px;"><nobr>includes knowledge about </nobr></div>
<div style="position: absolute; top: 8870px; left: 667px;"><nobr>received messages.</nobr></div>
<div style="position: absolute; top: 8783px; left: 564px;"><nobr>Entity-X</nobr></div>
<div style="position: absolute; top: 8814px; left: 492px;"><nobr>Msg-A</nobr></div>
<div style="position: absolute; top: 8847px; left: 492px;"><nobr>Msg-B</nobr></div>
<div style="position: absolute; top: 8783px; left: 564px;"><nobr>Entity-X</nobr></div>
<div style="position: absolute; top: 8814px; left: 492px;"><nobr>Msg-A</nobr></div>
<div style="position: absolute; top: 8814px; left: 492px;"><nobr>Msg-A</nobr></div>
<div style="position: absolute; top: 8814px; left: 492px;"><nobr>Msg-A</nobr></div>
<div style="position: absolute; top: 8847px; left: 492px;"><nobr>Msg-B</nobr></div>
<div style="position: absolute; top: 8847px; left: 492px;"><nobr>Msg-B</nobr></div>
<div style="position: absolute; top: 8781px; left: 665px;"><nobr>Activities are simple.</nobr></div>
<div style="position: absolute; top: 8798px; left: 656px;"><nobr>They are what an entity</nobr></div>
<div style="position: absolute; top: 8816px; left: 655px;"><nobr>remembers about other</nobr></div>
<div style="position: absolute; top: 8834px; left: 649px;"><nobr>entities it works with. This </nobr></div>
<div style="position: absolute; top: 8852px; left: 648px;"><nobr>includes knowledge about </nobr></div>
<div style="position: absolute; top: 8870px; left: 667px;"><nobr>received messages.</nobr></div>
</span></font>
<font face="Times" size="3"><span style="font-size: 10px; font-family: Times;">
<div style="position: absolute; top: 9609px; left: 449px;"><nobr>139</nobr></div>
</span></font>

<div style="position: absolute; top: 9679px; left: 0pt;"><hr><table border="0" width="100%"><tbody><tr><td align="right" bgcolor="#eeeeee"><font face="arial,sans-serif"><a name="9"><b>Page 9</b></a></font></td></tr></tbody></table></div><font face="Times" size="3"><span style="font-size: 12px; font-family: Times;">
<div style="position: absolute; top: 9770px; left: 95px;"><nobr><b>Ensuring At-Most-Once Acceptance via Activities</b></nobr></div>
<div style="position: absolute; top: 9791px; left: 116px;"><nobr>Processing messages that are not naturally idempotent</nobr></div>
<div style="position: absolute; top: 9808px; left: 94px;"><nobr>requires ensuring each message is processed at-most-once</nobr></div>
<div style="position: absolute; top: 9825px; left: 94px;"><nobr>(i.e. the substantive impact of the message must happen </nobr></div>
<div style="position: absolute; top: 9843px; left: 94px;"><nobr>at-most-once).  To do  this,  there  must  be  some  unique </nobr></div>
<div style="position: absolute; top: 9860px; left: 94px;"><nobr>characteristic of the message that is remembered to ensure </nobr></div>
<div style="position: absolute; top: 9877px; left: 94px;"><nobr>it will not be processed more than once.  </nobr></div>
<div style="position: absolute; top: 9894px; left: 116px;"><nobr>The entity must durably remember the transition from </nobr></div>
<div style="position: absolute; top: 9912px; left: 94px;"><nobr>a message being OK to process into the state where the </nobr></div>
<div style="position: absolute; top: 9929px; left: 94px;"><nobr>message will not have substantive impact.</nobr></div>
<div style="position: absolute; top: 9946px; left: 116px;"><nobr>Typically, an entity will use its activities to implement</nobr></div>
<div style="position: absolute; top: 9963px; left: 94px;"><nobr>this state management on a partner by partner basis.   This </nobr></div>
<div style="position: absolute; top: 9981px; left: 94px;"><nobr>is  essential  because  sometimes  an  entity  supports  many </nobr></div>
<div style="position: absolute; top: 9998px; left: 94px;"><nobr>different partners and each will pass through a pattern of </nobr></div>
<div style="position: absolute; top: 10015px; left: 94px;"><nobr>messages associated with that relationship.  By leveraging </nobr></div>
<div style="position: absolute; top: 10032px; left: 94px;"><nobr>a  per-partner  collection  of  state,  the  programmer  can </nobr></div>
<div style="position: absolute; top: 10049px; left: 94px;"><nobr>focus on the per-partner relationship.  </nobr></div>
<div style="position: absolute; top: 10067px; left: 116px;"><nobr>The assertion is that by focusing in on the per-partner</nobr></div>
<div style="position: absolute; top: 10084px; left: 95px;"><nobr>information,  it  is  easier  to  build  scalable  applications.</nobr></div>
<div style="position: absolute; top: 10101px; left: 95px;"><nobr>One  example  is  in  the  implementation  of  support  for </nobr></div>
<div style="position: absolute; top: 10119px; left: 95px;"><nobr>idempotent message processing.</nobr></div>
</span></font>
<font face="Times" size="3"><span style="font-size: 16px; font-family: Times;">
<div style="position: absolute; top: 10152px; left: 95px;"><nobr><b>6. ACTIVITIES:  COPING  WITHOUT </b></nobr></div>
<div style="position: absolute; top: 10173px; left: 122px;"><nobr><b>ATOMICITY</b></nobr></div>
</span></font>
<font face="Times" size="3"><span style="font-size: 12px; font-family: Times;">
<div style="position: absolute; top: 10197px; left: 116px;"><nobr>This  section  addresses  how  wildly  scalable  system </nobr></div>
<div style="position: absolute; top: 10214px; left: 94px;"><nobr>make decisions without distributed transactions.  </nobr></div>
<div style="position: absolute; top: 10231px; left: 116px;"><nobr>The emphasis of this section is that it is hard work to</nobr></div>
<div style="position: absolute; top: 10248px; left: 94px;"><nobr>manage distributed agreement.  In addition, though, in an </nobr></div>
<div style="position: absolute; top: 10266px; left: 94px;"><nobr>almost-infinitely scalable environment, the representation </nobr></div>
<div style="position: absolute; top: 10283px; left: 94px;"><nobr>of uncertainty must be done in a fine-grained fashion that </nobr></div>
<div style="position: absolute; top: 10300px; left: 94px;"><nobr>is oriented around per-partner relationships.  This data is </nobr></div>
<div style="position: absolute; top: 10318px; left: 95px;"><nobr>managed within entities using the notion of an activity.</nobr></div>
<div style="position: absolute; top: 10341px; left: 95px;"><nobr><b>Uncertainty at a Distance</b></nobr></div>
<div style="position: absolute; top: 10362px; left: 116px;"><nobr>The  absence  of  distributed  transactions  means  we </nobr></div>
<div style="position: absolute; top: 10380px; left: 94px;"><nobr>must  accept uncertainty  as  we  attempt  to  come  to </nobr></div>
<div style="position: absolute; top: 10397px; left: 94px;"><nobr>decisions across different entities.  It is unavoidable that </nobr></div>
<div style="position: absolute; top: 10414px; left: 94px;"><nobr>decisions  across  distributed systems  involve  accepting </nobr></div>
<div style="position: absolute; top: 10431px; left: 94px;"><nobr>uncertainty for a  while</nobr></div>
</span></font>
<font face="Times" size="2"><span style="font-size: 7px; font-family: Times;">
<div style="position: absolute; top: 10428px; left: 237px;"><nobr>15</nobr></div>
</span></font>
<font face="Times" size="3"><span style="font-size: 12px; font-family: Times;">
<div style="position: absolute; top: 10431px; left: 247px;"><nobr>.  When distributed transactions </nobr></div>
<div style="position: absolute; top: 10449px; left: 94px;"><nobr>can be used, that uncertainty is manifest in the locks held </nobr></div>
<div style="position: absolute; top: 10466px; left: 95px;"><nobr>on data and is managed by the transaction manager.</nobr></div>
<div style="position: absolute; top: 10483px; left: 116px;"><nobr>In  a  system  which  cannot  count  on  distributed </nobr></div>
<div style="position: absolute; top: 10500px; left: 95px;"><nobr>transactions,  the  management  of  uncertainty  must  be </nobr></div>
<div style="position: absolute; top: 10518px; left: 95px;"><nobr>implemented in the business logic.  The uncertainty of the</nobr></div>
<div style="position: absolute; top: 10535px; left: 95px;"><nobr>outcome is held in the business semantics rather than in </nobr></div>
<div style="position: absolute; top: 10552px; left: 95px;"><nobr>the  record lock.  This  is  simply  workflow. Nothing </nobr></div>
<div style="position: absolute; top: 10569px; left: 95px;"><nobr>magic, just that we can’t use distributed transaction so we </nobr></div>
<div style="position: absolute; top: 10587px; left: 95px;"><nobr>need to use workflow.</nobr></div>
<div style="position: absolute; top: 10604px; left: 116px;"><nobr>The assumptions that lead us to entities and messages, </nobr></div>
<div style="position: absolute; top: 10621px; left: 95px;"><nobr>lead  us  to  the  conclusion  that  the  scale-agnostic </nobr></div>
<div style="position: absolute; top: 10638px; left: 95px;"><nobr>application  must  manage  uncertainty  itself  using </nobr></div>
</span></font>
<font face="Times" size="2"><span style="font-size: 7px; font-family: Times;">
<div style="position: absolute; top: 10677px; left: 95px;"><nobr>15</nobr></div>
</span></font>
<font face="Times" size="3"><span style="font-size: 12px; font-family: Times;">
<div style="position: absolute; top: 10680px; left: 111px;"><nobr>At  the  time  of  writing  this paper, funny  action  at  a </nobr></div>
<div style="position: absolute; top: 10698px; left: 95px;"><nobr>distance has not been  proven  and  we are limited by the </nobr></div>
<div style="position: absolute; top: 10715px; left: 95px;"><nobr>speed of light.  There ain’t no such thing as simultaneity </nobr></div>
<div style="position: absolute; top: 10732px; left: 95px;"><nobr>at a distance… </nobr></div>
<div style="position: absolute; top: 9763px; left: 473px;"><nobr>workflow if it needs to reach  agreement across  multiple </nobr></div>
<div style="position: absolute; top: 9780px; left: 473px;"><nobr>entities. </nobr></div>
<div style="position: absolute; top: 9800px; left: 473px;"><nobr>Think  about  the  style  of  interactions  common  across </nobr></div>
<div style="position: absolute; top: 9817px; left: 473px;"><nobr>businesses.  Contracts  between  businesses  include  time </nobr></div>
<div style="position: absolute; top: 9834px; left: 473px;"><nobr>commitments,  cancellation  clauses,  reserved  resources, </nobr></div>
<div style="position: absolute; top: 9851px; left: 473px;"><nobr>and much more.  The semantics of uncertainty is wrapped </nobr></div>
<div style="position: absolute; top: 9869px; left: 473px;"><nobr>up in the behaviour of the business functionality.  While </nobr></div>
<div style="position: absolute; top: 9886px; left: 473px;"><nobr>more  complicated  to  implement  than  simply  using </nobr></div>
<div style="position: absolute; top: 9903px; left: 473px;"><nobr>distributed transactions, it is how the real world works…</nobr></div>
<div style="position: absolute; top: 9928px; left: 473px;"><nobr>Again, this is simply an argument for workflow.</nobr></div>
<div style="position: absolute; top: 9953px; left: 473px;"><nobr><b>Activities and the Management of Uncertainty</b></nobr></div>
<div style="position: absolute; top: 9975px; left: 494px;"><nobr>Entities sometimes accept uncertainty as they interact</nobr></div>
<div style="position: absolute; top: 9992px; left: 472px;"><nobr>with other entities.  This uncertainty must be managed on </nobr></div>
<div style="position: absolute; top: 10009px; left: 472px;"><nobr>a  partner-by-partner  basis  and one  can  visualize  that  as </nobr></div>
<div style="position: absolute; top: 10026px; left: 472px;"><nobr>being reified in the activity state for the partner.</nobr></div>
<div style="position: absolute; top: 10044px; left: 494px;"><nobr>Many times, uncertainty is represented by relationship.   </nobr></div>
<div style="position: absolute; top: 10061px; left: 472px;"><nobr>It  is  necessary  to  track  it  by partner.  As  each  partner </nobr></div>
<div style="position: absolute; top: 10078px; left: 472px;"><nobr>advances into a new state, the activity tracks this.</nobr></div>
<div style="position: absolute; top: 10098px; left: 473px;"><nobr>If  an  ordering  system  reserves  inventory  from  a </nobr></div>
<div style="position: absolute; top: 10115px; left: 473px;"><nobr>warehouse, the warehouse allocates the inventory without </nobr></div>
<div style="position: absolute; top: 10132px; left: 473px;"><nobr>knowing if it will be used.  That is accepting uncertainty.</nobr></div>
<div style="position: absolute; top: 10149px; left: 473px;"><nobr>Later  on,  the  warehouse  finds  out  if  the  reserved</nobr></div>
<div style="position: absolute; top: 10167px; left: 473px;"><nobr>inventory will be needed.  This resolves the uncertainty.</nobr></div>
<div style="position: absolute; top: 10189px; left: 473px;"><nobr>The warehouse inventory manager must keep relationship </nobr></div>
<div style="position: absolute; top: 10206px; left: 473px;"><nobr>data for each order encumbering its items.  As it connects </nobr></div>
<div style="position: absolute; top: 10224px; left: 473px;"><nobr>items and orders, these will be organized by item.  Within </nobr></div>
<div style="position: absolute; top: 10241px; left: 473px;"><nobr>each  item  will  be  information  about  outstanding  orders </nobr></div>
<div style="position: absolute; top: 10258px; left: 473px;"><nobr>against that item.  Each of these activities within the item </nobr></div>
<div style="position: absolute; top: 10276px; left: 473px;"><nobr>(one per order) manages the uncertainty of the order.</nobr></div>
<div style="position: absolute; top: 10301px; left: 473px;"><nobr><b>Performing Tentative Business Operations</b></nobr></div>
<div style="position: absolute; top: 10323px; left: 494px;"><nobr>To reach an agreement across entities, one entity has </nobr></div>
<div style="position: absolute; top: 10340px; left: 472px;"><nobr>to ask another to accept some uncertainty.  This is done </nobr></div>
<div style="position: absolute; top: 10357px; left: 472px;"><nobr>by sending a message which requests a commitment but </nobr></div>
<div style="position: absolute; top: 10374px; left: 472px;"><nobr>leaves open the possibility of cancellation.  This is called</nobr></div>
<div style="position: absolute; top: 10392px; left: 472px;"><nobr>a  <i>tentative  operation </i>and  it  represented  by  a  message </nobr></div>
<div style="position: absolute; top: 10409px; left: 472px;"><nobr>flowing between two entities.  At the end of this step, one </nobr></div>
<div style="position: absolute; top: 10426px; left: 472px;"><nobr>of the entities agrees to abide by the wishes of the other</nobr></div>
</span></font>
<font face="Times" size="2"><span style="font-size: 7px; font-family: Times;">
<div style="position: absolute; top: 10423px; left: 806px;"><nobr>16</nobr></div>
</span></font>
<font face="Times" size="3"><span style="font-size: 12px; font-family: Times;">
<div style="position: absolute; top: 10426px; left: 816px;"><nobr>.</nobr></div>
<div style="position: absolute; top: 10450px; left: 473px;"><nobr><b>Tentative Operations, Confirmation, and Cancellation</b></nobr></div>
<div style="position: absolute; top: 10471px; left: 494px;"><nobr>Essential to a tentative operation, is the right to cancel.  </nobr></div>
<div style="position: absolute; top: 10488px; left: 472px;"><nobr>Sometimes,  the  entity  that  requested  the  tentative </nobr></div>
<div style="position: absolute; top: 10506px; left: 472px;"><nobr>operation decides it is not going to proceed forward.  That </nobr></div>
<div style="position: absolute; top: 10523px; left: 472px;"><nobr>is  a  <i>cancelling  operation</i>.  When  the  right  to  cancel  is </nobr></div>
<div style="position: absolute; top: 10540px; left: 473px;"><nobr>released, that is a <i>confirming operation</i>.  Every tentative </nobr></div>
<div style="position: absolute; top: 10557px; left: 472px;"><nobr>operation eventually confirms or cancels.</nobr></div>
<div style="position: absolute; top: 10575px; left: 494px;"><nobr>When  an  entity  agrees  to  perform  a  tentative </nobr></div>
<div style="position: absolute; top: 10592px; left: 472px;"><nobr>operation,  it  agrees  to  let another  entity  decide  the </nobr></div>
<div style="position: absolute; top: 10609px; left: 472px;"><nobr>outcome.  This  is  accepting  uncertainty  and  adds  to  the </nobr></div>
<div style="position: absolute; top: 10626px; left: 472px;"><nobr>general  confusion  experience  by  that  entity. </nobr></div>
<div style="position: absolute; top: 10626px; left: 807px;"><nobr>As </nobr></div>
<div style="position: absolute; top: 10644px; left: 472px;"><nobr>confirmations  and  cancellations  arrive,  that  decreases</nobr></div>
</span></font>
<font face="Times" size="2"><span style="font-size: 7px; font-family: Times;">
<div style="position: absolute; top: 10677px; left: 473px;"><nobr>16</nobr></div>
</span></font>
<font face="Times" size="3"><span style="font-size: 12px; font-family: Times;">
<div style="position: absolute; top: 10680px; left: 487px;"><nobr>This is the simple case.  In some cases, the operations </nobr></div>
<div style="position: absolute; top: 10698px; left: 472px;"><nobr>can be partially handled.  In other cases, time-outs and/or </nobr></div>
<div style="position: absolute; top: 10715px; left: 472px;"><nobr>reneging can cause even more problems.  Unfortunately, </nobr></div>
<div style="position: absolute; top: 10732px; left: 472px;"><nobr>the real world is not pretty.</nobr></div>
</span></font>
<font face="Times" size="3"><span style="font-size: 10px; font-family: Times;">
<div style="position: absolute; top: 10797px; left: 449px;"><nobr>140</nobr></div>
</span></font>

<div style="position: absolute; top: 10867px; left: 0pt;"><hr><table border="0" width="100%"><tbody><tr><td align="right" bgcolor="#eeeeee"><font face="arial,sans-serif"><a name="10"><b>Page 10</b></a></font></td></tr></tbody></table></div><font face="Times" size="3"><span style="font-size: 12px; font-family: Times;">
<div style="position: absolute; top: 10951px; left: 95px;"><nobr>uncertainty.  It is normal to proceed through life with ever </nobr></div>
<div style="position: absolute; top: 10968px; left: 95px;"><nobr>increasing and decreasing uncertainty as old problems get </nobr></div>
<div style="position: absolute; top: 10986px; left: 95px;"><nobr>resolved and new ones arrive at your lap.</nobr></div>
<div style="position: absolute; top: 11003px; left: 116px;"><nobr>Again, this is simply  workflow but it is fine-grained </nobr></div>
<div style="position: absolute; top: 11020px; left: 95px;"><nobr>workflow with entities as the participants.</nobr></div>
<div style="position: absolute; top: 11044px; left: 95px;"><nobr><b>Uncertainty and Almost-Infinite Scaling</b></nobr></div>
<div style="position: absolute; top: 11065px; left: 116px;"><nobr>The  interesting  aspect  of  this  for  scaling  is  the </nobr></div>
<div style="position: absolute; top: 11082px; left: 94px;"><nobr>observation  that  the  management  of  uncertainty  usually </nobr></div>
<div style="position: absolute; top: 11100px; left: 94px;"><nobr>revolves  around  two-party  agreements.  It  is  frequently </nobr></div>
<div style="position: absolute; top: 11117px; left: 94px;"><nobr>the case that multiple two-party agreements happen. Still, </nobr></div>
<div style="position: absolute; top: 11134px; left: 94px;"><nobr>these are knit together as a web of fine-grained two-party </nobr></div>
<div style="position: absolute; top: 11151px; left: 94px;"><nobr>agreements using entity-keys as the links and activities to </nobr></div>
<div style="position: absolute; top: 11169px; left: 94px;"><nobr>track the known state of a distant partner.</nobr></div>
<div style="position: absolute; top: 11188px; left: 95px;"><nobr>Consider a house purchase and the relationships with the </nobr></div>
<div style="position: absolute; top: 11205px; left: 95px;"><nobr>escrow company.  The buyer enters into an agreement of </nobr></div>
<div style="position: absolute; top: 11223px; left: 95px;"><nobr>trust  with  the  escrow company.  So  does  the  seller,  the </nobr></div>
<div style="position: absolute; top: 11240px; left: 95px;"><nobr>mortgage company, and all the other parties involved in </nobr></div>
<div style="position: absolute; top: 11257px; left: 95px;"><nobr>the transaction.</nobr></div>
<div style="position: absolute; top: 11279px; left: 95px;"><nobr>When you go to sign papers to buy a house, you do not </nobr></div>
<div style="position: absolute; top: 11297px; left: 95px;"><nobr>know  the  outcome  of  the  deal.  You  accept  that,  until </nobr></div>
<div style="position: absolute; top: 11314px; left: 95px;"><nobr>escrow closes,  you  are  uncertain.  The  only  party  with </nobr></div>
<div style="position: absolute; top: 11331px; left: 95px;"><nobr>control over the decision-making is the escrow company.</nobr></div>
<div style="position: absolute; top: 11354px; left: 95px;"><nobr>This  is  a  hub-and-spoke  collection  of  two-party </nobr></div>
<div style="position: absolute; top: 11371px; left: 95px;"><nobr>relationships that are used to get a large set of parties to </nobr></div>
<div style="position: absolute; top: 11388px; left: 95px;"><nobr>agree without use of distributed transactions.</nobr></div>
<div style="position: absolute; top: 11408px; left: 116px;"><nobr>When  you  consider almost-infinite  scaling,  it  is </nobr></div>
<div style="position: absolute; top: 11425px; left: 94px;"><nobr>interesting  to  think  about  two-party  relationships.  By </nobr></div>
<div style="position: absolute; top: 11442px; left: 94px;"><nobr>building up from two-party tentative/cancel/confirm (just </nobr></div>
<div style="position: absolute; top: 11459px; left: 94px;"><nobr>like  traditional  workflow)  we  see  the  basis  for  how </nobr></div>
<div style="position: absolute; top: 11477px; left: 94px;"><nobr>distributed agreement is achieved.  Just as in the escrow </nobr></div>
<div style="position: absolute; top: 11494px; left: 94px;"><nobr>company, many entities may participate in an agreement </nobr></div>
<div style="position: absolute; top: 11511px; left: 94px;"><nobr>through composition.</nobr></div>
<div style="position: absolute; top: 11528px; left: 116px;"><nobr>Because  the  relationships  are  two-party,  the  simple </nobr></div>
<div style="position: absolute; top: 11546px; left: 94px;"><nobr>concept  of  an  activity  as  “stuff  I  remember  about  that</nobr></div>
<div style="position: absolute; top: 11563px; left: 95px;"><nobr>partner”  becomes  a  basis for  managing  enormous </nobr></div>
<div style="position: absolute; top: 11580px; left: 95px;"><nobr>systems.  Even when the data is stored in entities and you </nobr></div>
<div style="position: absolute; top: 11597px; left: 95px;"><nobr>don’t know where the data lives and must assume it is far </nobr></div>
<div style="position: absolute; top: 11615px; left: 95px;"><nobr>away, it can be programmed in a scale-agnostic way.</nobr></div>
<div style="position: absolute; top: 11632px; left: 116px;"><nobr>Real  world  almost-infinite  scale  applications  would </nobr></div>
<div style="position: absolute; top: 11649px; left: 95px;"><nobr>love the luxury of a  global  scope  of serializability as is </nobr></div>
<div style="position: absolute; top: 11666px; left: 95px;"><nobr>promised  by  two  phase  commit  and  other  related </nobr></div>
<div style="position: absolute; top: 11684px; left: 95px;"><nobr>algorithms.  Unfortunately, the fragility of these leads to </nobr></div>
<div style="position: absolute; top: 11701px; left: 95px;"><nobr>unacceptable  pressure  on  availability. </nobr></div>
<div style="position: absolute; top: 11701px; left: 368px;"><nobr>Instead,  the </nobr></div>
<div style="position: absolute; top: 11718px; left: 95px;"><nobr>management  of  the  uncertainty  of  the  tentative  work  is </nobr></div>
<div style="position: absolute; top: 11735px; left: 95px;"><nobr>foisted  clearly  into  the  hands  of  the  developer  of  the </nobr></div>
<div style="position: absolute; top: 11753px; left: 95px;"><nobr>scale-agnostic application.  It must be handled as reserved </nobr></div>
<div style="position: absolute; top: 11770px; left: 95px;"><nobr>inventory,  allocations  against  credit  lines,  and  other </nobr></div>
<div style="position: absolute; top: 11787px; left: 95px;"><nobr>application specific concepts.</nobr></div>
</span></font>
<font face="Times" size="3"><span style="font-size: 16px; font-family: Times;">
<div style="position: absolute; top: 11820px; left: 95px;"><nobr><b>7. CONCLUSIONS</b></nobr></div>
</span></font>
<font face="Times" size="3"><span style="font-size: 12px; font-family: Times;">
<div style="position: absolute; top: 11845px; left: 116px;"><nobr>As  usual,  the  computer  industry  is  in  flux.  One </nobr></div>
<div style="position: absolute; top: 11862px; left: 94px;"><nobr>emerging trend is for an application to scale to sizes that </nobr></div>
<div style="position: absolute; top: 11879px; left: 94px;"><nobr>do not fit onto a single machine or tightly-coupled set of </nobr></div>
<div style="position: absolute; top: 11896px; left: 94px;"><nobr>machines.  As we have always seen, specific solutions for </nobr></div>
<div style="position: absolute; top: 11914px; left: 94px;"><nobr>a single application emerge first and then general patterns </nobr></div>
<div style="position: absolute; top: 10951px; left: 472px;"><nobr>are  observed.  Based  upon  these  general  patterns,  new </nobr></div>
<div style="position: absolute; top: 10968px; left: 472px;"><nobr>facilities  are  built  empowering  easier  construction  of </nobr></div>
<div style="position: absolute; top: 10986px; left: 472px;"><nobr>business logic.</nobr></div>
<div style="position: absolute; top: 11003px; left: 494px;"><nobr>In the 1970s, many large-scale applications struggled</nobr></div>
<div style="position: absolute; top: 11020px; left: 472px;"><nobr>with  the  difficulties  of  handling  the  multiplexing  of </nobr></div>
<div style="position: absolute; top: 11037px; left: 472px;"><nobr>multiple  online  terminals  while  providing  business </nobr></div>
<div style="position: absolute; top: 11055px; left: 472px;"><nobr>solutions.  Emerging  patterns  of  terminal  control  were </nobr></div>
<div style="position: absolute; top: 11072px; left: 472px;"><nobr>captured and some high-end applications evolved into TP-</nobr></div>
<div style="position: absolute; top: 11089px; left: 472px;"><nobr>monitors.  Eventually, these patterns were repeated in the </nobr></div>
<div style="position: absolute; top: 11106px; left: 472px;"><nobr>creation  of  developed-from-scratch  TP-monitors.  These </nobr></div>
<div style="position: absolute; top: 11124px; left: 472px;"><nobr>platforms allowed the business-logic developers to focus </nobr></div>
<div style="position: absolute; top: 11141px; left: 472px;"><nobr>on what they do best: develop business logic.</nobr></div>
<div style="position: absolute; top: 11158px; left: 494px;"><nobr>Today,  we  see  new  design  pressures  foisted  onto </nobr></div>
<div style="position: absolute; top: 11175px; left: 472px;"><nobr>programmers  that  simply want  to  solve  business </nobr></div>
<div style="position: absolute; top: 11193px; left: 472px;"><nobr>problems.  Their realities are taking them into a world of </nobr></div>
<div style="position: absolute; top: 11210px; left: 472px;"><nobr>almost-infinite  scaling  and  forcing  them  into  design </nobr></div>
<div style="position: absolute; top: 11227px; left: 472px;"><nobr>problems largely unrelated to the real business at hand.</nobr></div>
<div style="position: absolute; top: 11244px; left: 494px;"><nobr>Unfortunately, programmers striving to solve business </nobr></div>
<div style="position: absolute; top: 11262px; left: 472px;"><nobr>goals  like  eCommerce,  supply-chain-management, </nobr></div>
<div style="position: absolute; top: 11279px; left: 472px;"><nobr>financial,  and  health-care  applications  increasingly  need </nobr></div>
<div style="position: absolute; top: 11296px; left: 472px;"><nobr>to  think  about  scaling  without  distributed transactions. </nobr></div>
<div style="position: absolute; top: 11313px; left: 472px;"><nobr>They  do  this  because  attempts  to  use  distributed </nobr></div>
<div style="position: absolute; top: 11331px; left: 472px;"><nobr>transactions are too fragile and perform poorly.</nobr></div>
<div style="position: absolute; top: 11348px; left: 494px;"><nobr>We are at a juncture where the patterns for building </nobr></div>
<div style="position: absolute; top: 11365px; left: 472px;"><nobr>these applications can be seen but no one is yet applying </nobr></div>
<div style="position: absolute; top: 11382px; left: 472px;"><nobr>these patterns consistently.  This paper argues that these </nobr></div>
<div style="position: absolute; top: 11400px; left: 472px;"><nobr>nascent patterns can be applied  more consistently in the </nobr></div>
<div style="position: absolute; top: 11417px; left: 472px;"><nobr>hand-crafted  development  of applications  designed  for </nobr></div>
<div style="position: absolute; top: 11434px; left: 472px;"><nobr>almost-infinite  scaling.  Furthermore, in a  few years  we </nobr></div>
<div style="position: absolute; top: 11452px; left: 472px;"><nobr>are likely to see the development of new middleware or </nobr></div>
<div style="position: absolute; top: 11469px; left: 472px;"><nobr>platforms which provide automated management of these </nobr></div>
<div style="position: absolute; top: 11486px; left: 472px;"><nobr>applications  and  eliminate  the  scaling  challenges  for </nobr></div>
<div style="position: absolute; top: 11503px; left: 472px;"><nobr>applications  developed within  a  stylized  programming </nobr></div>
<div style="position: absolute; top: 11521px; left: 472px;"><nobr>paradigm.  This is strongly parallel to the emergence of </nobr></div>
<div style="position: absolute; top: 11538px; left: 472px;"><nobr>TP-monitors in the 1970s.</nobr></div>
<div style="position: absolute; top: 11555px; left: 494px;"><nobr>In this paper, we have introduced and named a couple </nobr></div>
<div style="position: absolute; top: 11572px; left: 472px;"><nobr>of formalisms emerging in large-scale applications:</nobr></div>
<div style="position: absolute; top: 11585px; left: 473px;"><nobr></nobr></div>
<div style="position: absolute; top: 11590px; left: 500px;"><nobr>Entities are collections of named (keyed) data which </nobr></div>
<div style="position: absolute; top: 11608px; left: 499px;"><nobr>may be atomically updated within the entity but never </nobr></div>
<div style="position: absolute; top: 11625px; left: 499px;"><nobr>atomically updated across entities.</nobr></div>
<div style="position: absolute; top: 11638px; left: 473px;"><nobr></nobr></div>
<div style="position: absolute; top: 11643px; left: 500px;"><nobr>Activities comprise the collection of state within the </nobr></div>
<div style="position: absolute; top: 11661px; left: 500px;"><nobr>entities used to manage messaging relationships with </nobr></div>
<div style="position: absolute; top: 11678px; left: 500px;"><nobr>a single partner entity.</nobr></div>
<div style="position: absolute; top: 11702px; left: 473px;"><nobr>Workflow to reach decisions, as have been discussed for </nobr></div>
<div style="position: absolute; top: 11719px; left: 473px;"><nobr>many years, functions within activities within entities.  It </nobr></div>
<div style="position: absolute; top: 11736px; left: 473px;"><nobr>is the fine-grained nature of workflow that is surprising as </nobr></div>
<div style="position: absolute; top: 11754px; left: 473px;"><nobr>one looks at almost-infinite scaling.   </nobr></div>
<div style="position: absolute; top: 11772px; left: 494px;"><nobr>It  is  argued  that  many  applications  are  implicitly </nobr></div>
<div style="position: absolute; top: 11790px; left: 473px;"><nobr>designing with both entities and activities today.  They are </nobr></div>
<div style="position: absolute; top: 11807px; left: 473px;"><nobr>simply  not  formalized  nor  are  they  consistently  used.  </nobr></div>
<div style="position: absolute; top: 11824px; left: 473px;"><nobr>Where  the  use  is  inconsistent,  bugs  are  found  and </nobr></div>
<div style="position: absolute; top: 11842px; left: 473px;"><nobr>eventually patched.  By discussing and consistently using </nobr></div>
<div style="position: absolute; top: 11859px; left: 473px;"><nobr>these patterns, better large-scale applications can be built </nobr></div>
<div style="position: absolute; top: 11876px; left: 473px;"><nobr>and, as an industry, we can get closer to building solutions </nobr></div>
<div style="position: absolute; top: 11893px; left: 473px;"><nobr>that allow business-logic programmers to concentrate on </nobr></div>
<div style="position: absolute; top: 11911px; left: 473px;"><nobr>the business-problems rather than the problems of scale.</nobr></div>
</span></font>
<font face="Times" size="3"><span style="font-size: 10px; font-family: Times;">
<div style="position: absolute; top: 11985px; left: 449px;"><nobr>141</nobr></div>
</span></font>
</div></body></html>