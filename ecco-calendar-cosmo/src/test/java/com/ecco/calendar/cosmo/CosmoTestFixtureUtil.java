package com.ecco.calendar.cosmo;

import com.ecco.calendar.core.AvailableInterval;
import net.fortuna.ical4j.model.Component;
import net.fortuna.ical4j.model.Parameter;
import net.fortuna.ical4j.model.Property;
import net.fortuna.ical4j.model.component.VAvailability;
import org.hamcrest.Description;
import org.hamcrest.Matcher;
import org.hamcrest.TypeSafeMatcher;
import org.joda.time.DateTime;
import org.joda.time.Interval;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.osaf.cosmo.model.AvailabilityItem;
import org.osaf.cosmo.model.CollectionItem;
import org.osaf.cosmo.model.EventStamp;
import org.osaf.cosmo.model.StampUtils;
import org.osaf.cosmo.model.hibernate.HibEntityFactory;
import org.osaf.cosmo.model.hibernate.HibNoteItem;

import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.hasItem;
import static org.hamcrest.Matchers.nullValue;

public abstract class CosmoTestFixtureUtil {
    private static HibEntityFactory entityFactory = new HibEntityFactory();

    /** Simulate persistence - from org.osaf.cosmo.dao.hibernate.ItemDaoImpl.setBaseItemProps(). */
    public static Answer<HibNoteItem> persistedNoteItem() {
        return new Answer<HibNoteItem>() {
            @Override
            public HibNoteItem answer(InvocationOnMock invocation) throws Throwable {
                final HibNoteItem item;
                if (invocation.getArguments().length < 2) {
                    item = (HibNoteItem) invocation.getArguments()[0];
                } else {
                    item = (HibNoteItem) invocation.getArguments()[1];
                    item.addParent((CollectionItem) invocation.getArguments()[0]);
                }
                if (item.getUid() == null)
                    item.setUid(entityFactory.generateUid());
                if (item.getName() == null)
                    item.setName(item.getUid());
                if (item.getIcalUid() == null) {
                    item.setIcalUid(item.getUid());
                    EventStamp es = StampUtils.getEventStamp(item);
                    if (es != null)
                        es.setIcalUid(item.getIcalUid());
                }
                return item;
            }
        };
    }

    public static Answer<Void> itemWithUpdatedParents() {
        return new Answer<Void>() {
            @Override
            public Void answer(InvocationOnMock invocation) throws Throwable {
                final HibNoteItem item = (HibNoteItem) invocation.getArguments()[0];
                item.addParent((CollectionItem) invocation.getArguments()[1]);
                return null;
            }
        };
    }

    public static Matcher<CollectionItem> collectionWithUid(final String calId) {
        return collectionWithUid(equalTo(calId));
    }

    public static Matcher<CollectionItem> collectionWithUid(final Matcher<String> delegate) {
        return new TypeSafeMatcher<CollectionItem>() {
            @Override
            protected boolean matchesSafely(CollectionItem item) {
                return delegate.matches(item.getUid());
            }

            @Override
            public void describeTo(Description description) {
                description.appendText("a collection with ID ").appendDescriptionOf(delegate);
            }
        };
    }

    public static Matcher<Property> attendeeWithEmail(final String email) {
        return attendeeWithEmail(equalTo(email));
    }

    public static Matcher<Property> attendeeWithEmail(final Matcher<String> delegate) {
        return new TypeSafeMatcher<Property>() {
            @Override
            protected boolean matchesSafely(Property item) {
                return item.getName().equals(Property.ATTENDEE) && item.getValue().startsWith("mailto:") &&
                        delegate.matches(item.getValue().substring(7));
            }

            @Override
            public void describeTo(Description description) {
                description.appendText("an attendee with email ").appendDescriptionOf(delegate);
            }
        };
    }

    public static Matcher<Property> attendeeWithCommonName(final String cn) {
        return attendeeWithParameter(Parameter.CN, equalTo(cn));
    }

    public static Matcher<Property> attendeeWithDirectoryEntryReference(final String dir) {
        return attendeeWithParameter(Parameter.DIR, equalTo(dir));
    }

    public static Matcher<Property> attendeeWithParameter(final String parameter, final Matcher<String> delegate) {
        return propertyWithParameter(Property.ATTENDEE, parameter, delegate);
    }

    public static Matcher<Property> locationWithValue(final String loc) {
        return propertyWithValue(Property.LOCATION, equalTo(loc));
    }

    public static Matcher<Property> locationWithAlternateRepresentation(final String altRep) {
        return locationWithParameter(Parameter.ALTREP, equalTo(altRep));
    }

    private static Matcher<Property> locationWithParameter(final String parameter, final Matcher<String> delegate) {
        return propertyWithParameter(Property.LOCATION, parameter, delegate);
    }

    public static Matcher<Property> propertyWithValue(final String propertyName, final Matcher<String> delegate) {
        return new TypeSafeMatcher<Property>() {
            @Override
            protected boolean matchesSafely(Property item) {
                return item.getName().equals(propertyName) && delegate.matches(item.getValue());
            }

            @Override
            public void describeTo(Description description) {
                description.appendText("a ").appendText(propertyName).appendText(" with value ").appendDescriptionOf(delegate);
            }
        };
    }

    public static Matcher<Property> propertyWithParameter(final String propertyName, final String parameter, final Matcher<String> delegate) {
        return new TypeSafeMatcher<Property>() {
            @Override
            protected boolean matchesSafely(Property item) {
                return item.getName().equals(propertyName) && item.getParameter(parameter) != null
                        && delegate.matches(item.getParameter(parameter).getValue());
            }

            @Override
            public void describeTo(Description description) {
                description.appendText("a ").appendText(propertyName).appendText(" with ").appendText(parameter).appendText(" ").appendDescriptionOf(delegate);
            }
        };
    }

    public static Matcher<? super AvailabilityItem> hasNoICalUid() {
        return hasICalUid(nullValue(String.class));
    }

    public static Matcher<? super AvailabilityItem> hasICalUid(final Matcher<String> uidMatcher) {
        return new TypeSafeMatcher<AvailabilityItem>() {
            @Override
            protected boolean matchesSafely(AvailabilityItem item) {
                return uidMatcher.matches(item.getIcalUid());
            }

            @Override
            public void describeTo(Description description) {
                description.appendText("has iCal UID ").appendDescriptionOf(uidMatcher);
            }
        };
    }

    public static Matcher<? super AvailabilityItem> containsAvailability(Interval interval) {
        return containsAvailability(equalTo(interval));
    }

    public static Matcher<? super AvailabilityItem> containsAvailability(final Matcher<? super Interval> intervalMatcher) {
        final Matcher<Iterable<? super Component>> delegate = hasItem(componentWithInterval(intervalMatcher));

        return new TypeSafeMatcher<AvailabilityItem>() {
            @Override
            protected boolean matchesSafely(AvailabilityItem item) {
                VAvailability vAvailability = (VAvailability) item.getAvailabilityCalendar().getComponent(Component.VAVAILABILITY);
                return delegate.matches(vAvailability.getAvailable());
            }

            @Override
            public void describeTo(Description description) {
                description.appendText("contains AVAILABILITY matching ").appendDescriptionOf(intervalMatcher);
            }
        };
    }

    public static Matcher<? super AvailabilityItem> coversInterval(final Interval interval) {
        return coversInterval(equalTo(interval));
    }

    public static Matcher<? super AvailabilityItem> coversInterval(final Matcher<? super Interval> intervalMatcher) {
        final Matcher<? super VAvailability> delegate = componentWithInterval(intervalMatcher);

        return new TypeSafeMatcher<AvailabilityItem>() {
            @Override
            protected boolean matchesSafely(AvailabilityItem o) {
                VAvailability component = (VAvailability) o.getAvailabilityCalendar().getComponent(Component.VAVAILABILITY);
                return delegate.matches(component);
            }

            @Override
            public void describeTo(Description description) {
                description.appendText("VAVAILABILITY ").appendDescriptionOf(delegate);
            }
        };
    }

    public static Matcher<? super AvailableInterval> withInterval(Interval interval) {
        return withInterval(equalTo(interval));
    }

    public static Matcher<? super AvailableInterval> withInterval(final Matcher<? super Interval> intervalMatcher) {
        return new TypeSafeMatcher<AvailableInterval>() {
            @Override
            protected boolean matchesSafely(AvailableInterval item) {
                return intervalMatcher.matches(item.getInterval());
            }

            @Override
            public void describeTo(Description description) {
                description.appendText("interval ").appendDescriptionOf(intervalMatcher);
            }
        };
    }

    public static Matcher<Component> componentWithInterval(final Matcher<? super Interval> intervalMatcher) {
        return new TypeSafeMatcher<Component>() {
            @Override
            protected boolean matchesSafely(Component item) {
                ComponentWrapper<Component> component = new ComponentWrapper<Component>(item);
                return intervalMatcher.matches(new Interval(new DateTime(component.getStartDate().getDate()), new DateTime(component.getEndDate().getDate())));
            }

            @Override
            public void describeTo(Description description) {
                description.appendText("with interval ").appendDescriptionOf(intervalMatcher);
            }
        };
    }

}
