package com.ecco.calendar.cosmo;

import com.ecco.cosmo.dao.ItemTranslator;
import com.ecco.exceptions.BaseApplicationExceptionRuntime;
import com.ecco.calendar.dom.EventEntryDefinition;
import com.ecco.calendar.core.util.DateTimeUtils;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import net.fortuna.ical4j.model.Date;
import net.fortuna.ical4j.model.DateTime;
import net.fortuna.ical4j.model.Recur;
import net.fortuna.ical4j.model.parameter.Value;
import net.fortuna.ical4j.util.Dates;
import org.joda.time.DateTimeZone;
import org.joda.time.Days;
import org.osaf.cosmo.eim.*;
import org.osaf.cosmo.eim.schema.EimValidationException;
import org.osaf.cosmo.model.*;
import org.osaf.cosmo.model.hibernate.HibEventStamp;
import org.osaf.cosmo.model.hibernate.HibNoteItem;

import java.math.BigDecimal;
import java.text.ParseException;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;

import static java.time.temporal.ChronoUnit.DAYS;
import static org.osaf.cosmo.eim.schema.EimSchemaConstants.*;
import static org.osaf.cosmo.eim.schema.contentitem.ContentItemConstants.FIELD_CREATED_ON;
import static org.osaf.cosmo.eim.schema.contentitem.ContentItemConstants.FIELD_TITLE;
import static org.osaf.cosmo.eim.schema.modifiedby.ModifiedByConstants.*;
import static org.osaf.cosmo.eim.schema.note.NoteConstants.FIELD_BODY;
import static org.osaf.cosmo.eim.schema.note.NoteConstants.FIELD_ICALUID;

/** Converter for a local, non-calendaring Event to cosmo */
@Slf4j
public class CosmoConverterEntryToItem {

    public CosmoConverterEntryToItem() {
    }

    // separate out so we can change the order of advices more easily
    public String getUUID() {
        return UUID.randomUUID().toString();
    }

    // see org.osaf.cosmo.atom.processor.BaseEimProcessor.processCreation
    // this could be replaced more and more with the OutOfTheBox style code which we implement in convertItemEvent
    public Item convertEntryToItem(EventEntryDefinition event, CollectionItem collection, String modifiedByUsername, DateTimeZone timeZone) {

        EimRecordSet recordSet = createRecordSet(event, modifiedByUsername, true);

        // apply the new recordset to the old item in a translator
        // but we make this clear with a reference to the same object through a new name
        NoteItem newItem;
        try {
            newItem = createChild(collection, recordSet);
            ItemTranslator translator = new ItemTranslator(newItem);
            // if new record...
            translator.applyRecords(recordSet);
        } catch (EimValidationException e) {
            throw new BaseApplicationExceptionRuntime("Invalid recordset", e);
        } catch (BaseApplicationExceptionRuntime e) {
            throw e;
        } catch (Exception e) {
            throw new BaseApplicationExceptionRuntime("Unable to apply recordset", e);
        }

        convertEntryToItem(false, event, newItem, timeZone);

        return newItem;
    }

    /** Updates item from event */
    public void convertFromUpdatedEvent(EventEntryDefinition event, Item item, String username, DateTimeZone timeZone) {

        EimRecordSet recordSet = createRecordSet(event, username, false);

        // apply the new recordset to the old item in a translator
        try {
            ItemTranslator translator = new ItemTranslator(item);
            translator.applyRecords(recordSet);
        } catch (EimValidationException e) {
            throw new BaseApplicationExceptionRuntime("Invalid recordset", e);
        } catch (Exception e) {
            throw new BaseApplicationExceptionRuntime("Unable to apply recordset", e);
        }

        convertEntryToItem(true, event, item, timeZone);
    }

    // we struggled setting the EimRecord of recurring events, so tried from the code here
    // this could be partly because we were specifying against the home collection which cosmo doesn't show!
    // and because the OutOfTheBox code we used actually creates a date calculated from now
    // NB see some initial cosmo documentation in commit 222c2755

    /**
     * Convert a generic calendar definition to a specific calendar representation (non-recurring).
     * @see CosmoConverter#convertItemToEntry(NoteItem)
     */
    private void convertEntryToItem(boolean updating, EventEntryDefinition event, Item item, DateTimeZone timeZone) {

        BaseEventStamp es;
        if (!updating) {
            es = new HibEventStamp(item);
            item.addStamp(es);
            es.createCalendar();
        } else {
            es = StampUtils.getBaseEventStamp(item);
        }

        // timeEnabled is needed for converting the time - which isn't persisted!
        if (event.getEventDateToPersist().hasTime())
            event.getEventDateToPersist().setTimeEnabled(true);
        if (event.getEventEndDateToPersist().hasTime())
            event.getEventEndDateToPersist().setTimeEnabled(true);

        // we are strict - we force a year, unless its repeating
        // in which case, the year from 'now' with tz is taken (ie the next reminder date)
        // which is fine since its saved as recurring anyway in cosmo which is where we draw the feed from
        boolean requiresYear = !event.isRepeatYears();
        org.joda.time.DateTime jodaStart = DateTimeUtils.convertToDateTime(
                event.getEventDateToPersist(), requiresYear, event.getEventDateToPersist().hasTime(), timeZone);

        // med to joda is fine, since joda constructs the date from the elements of meddate
        // the joda time is constructed 'in' the timezone given - which is utc (specified in ItemIntegrationAdvice aop methods)
        // the ms of a correct ms time is correct in any tz - the textual representation may not be
        // we could but don't force utc since ical4j may treat this as non-floating - see working with timezones, http://ical4j.sourceforge.net/introduction.html
        // so with the default set we are all matching utc
        net.fortuna.ical4j.model.DateTime icalDate = new net.fortuna.ical4j.model.DateTime();
        // so the start dates all match and are all utc
        // and end date conversion is avoided by setting a duration
        icalDate.setTime(jodaStart.getMillis());
        org.joda.time.DateTime jodaEnd = DateTimeUtils.convertToDateTime(event.getEventEndDateToPersist(), true, event.getEventEndDateToPersist().hasTime(), timeZone);

        // allDay / anyTime
        // ================
        // allDay is an ECCO concept
        // anyTime is a COSMO concept
        //
        // This is a simple conversion from ECCO allDay to cosmo anyTime.
        // allDay/anyTime is when there is no time
        boolean anyTime = !event.getEventDateToPersist().hasTime();

        // Initial and earlier anyTime logic was mixed up in generated events with allDay and anyTime logic.
        // The one resulting inconsistency allows user events to be allDay but not as anyTime in cosmo.
        // This means the cosmo setAnyTime didn't apply, so the ical entry was not stamped with "DTSTART;VALUE=DATE;X-OSAF-ANYTIME=TRUE:20190204"
        // but it instead set "DTSTART;VALUE=DATE:20190129" - where there is no time element, or end date (see cosmo_event_stamp.icaldata).
        // However, according to the spec, this format allows it to be an allDay event - https://stackoverflow.com/questions/1716237/single-day-all-day-appointments-in-ics-files.
        // NB Cosmo's isFloating cosmo_event_stamp is used when no tz is set, not relating to allDay - see UTCTest.
        // (except the documentation of 222c2755 suggested: "an allday recurring item is not set as allday - it does get a 'isfloating' on cosmo_event_stamp")
        // Therefore the EntryConverter.convert.looks for an stamp.isAnyTime or 'date time' and sets allDay accordingly.

        // The history of the initial and eslier logic:
        //      Initial commit 760129b has this logic:
        //          generated events allDay = !start.hasTime
        //          generated events anyTime = !allDay
        //          user events allDay = !start.hasTime
        //          user events anyTime = false
        //      Early commit: 222c275: 'ECCO-943 Record time in the calendar for generated events if the time was specified on the event.'
        //      This is actually identical logic since generated events are now '!start.hasTime' which is what '!allDay' was previously.
        //          generated events allDay = false
        //          generated events anyTime = !start.hasTime
        //          user events allDay = !start.hasTime
        //          user events anyTime = false

        // start & duration
        // end date seems to be calculated for us - and probably could be specified for repeated events
        if (anyTime) {
            // ORDER of setters IS IMPORTANT
            // start date needs to be before anyTime!
            // the allDay/anyTime have a date set, not date_time
            Date startDate = Dates.getInstance(icalDate, Value.DATE);
            es.setStartDate(startDate);
            // if spans days - assume 1 day for now
            int days = 1;
            if (jodaEnd != null) {
                Days diff = Days.daysBetween(jodaStart, jodaEnd);
                if (diff.getDays() > 1)
                    days = diff.getDays();
            }
            // NB without an end date, this still sets the duration of an event
            // because the spec says something must exist for a one-off event
            // https://stackoverflow.com/questions/15295887/does-an-ical-file-have-to-have-an-enddate
            // we mimic the cosmo anytime logic by setting for a day
            es.setDuration(Duration.of(days, DAYS)); // TODO: This may no longer be needed with iCal 3 for the default days=1 - the default duration of a DATE being 1 day

            // if anyTime - eg, generated allDay events
            es.setAnyTime(true);
        } else {
            // not much validation since user events are actually generated through the calendar
            // which forces correct values through the gui
            DateTime startDate = (DateTime) Dates.getInstance(icalDate, Value.DATE_TIME);
            es.setStartDate(startDate);
            org.joda.time.Period diff = new org.joda.time.Period(jodaStart, jodaEnd);
            // Use daysBetween to handle durations of a week or more correctly - Period will use weeks, months and years as well as days.
            es.setDuration(Duration.of(Days.daysBetween(jodaStart, jodaEnd).getDays(), DAYS)
                    .plusHours(diff.getHours())
                    .plusMinutes(diff.getMinutes())
                    .plusSeconds(diff.getSeconds()));

            // if not anyTime - take off any previous attribute
            es.setAnyTime(false);
        }

        if (event.isRepeatYears()) {
            Recur recur = null;
            try {
                recur = new Recur(";FREQ=YEARLY");
            } catch (ParseException e) {
                log.error("parsing YEARLY ", e);
            }
            es.setRecurrenceRule(recur);
        }

        // don't specify a tz - causes cosmo ui to have problems loading olzon data...should fix
        // but also our data is consistent with cosmo, which doesn't specify a timezone
        // our dates come from meddate (timezone-less) are ensured in utc
    }

    // NB see org.osaf.cosmo.service.account.OutOfTheBoxHelper
    // - this would have been a very useful find!
    private EimRecordSet createRecordSet(EventEntryDefinition event, String modifiedByUsername, boolean creating) {

        // first we construct an eim
        EimRecordSet recordSet = new EimRecordSet();

        // ITEM
        // fill the record - see JsonStreamReader.readNextRecordSet
        recordSet.setUuid(event.getUid());
        EimRecord record = new EimRecord(PREFIX_ITEM, NS_ITEM);

        // key
        EimRecordKey recordKey = new EimRecordKey();
        EimRecordField field = new TextField(FIELD_UUID, event.getUid());
        recordKey.addField(field);
        record.setKey(recordKey);

        // fields
        List<EimRecordField> fields = new ArrayList<>();
        field = new TextField(FIELD_TITLE, event.getNameToPersist());
        fields.add(field);
        // add the current record if 'all' records need populating (eg a new item)
        long milis = org.joda.time.DateTimeUtils.currentTimeMillis();
        BigDecimal milisNow = new BigDecimal(milis);

        if (creating) {
            // createdOn - cosmo-js/cosmo/service/tests/translators/eim.js shows this as ms since epoch
            // which in js is new Date().getTime()
            field = new DecimalField(FIELD_CREATED_ON, milisNow, 13, 3);
            fields.add(field);
        }
        record.addFields(fields);
        // complete the record
        recordSet.addRecord(record);

        // NOTE, MODBY and EVENT
        // if 'all' records need populating (eg a new item)
        if (creating) {
            // NOTE
            record = new EimRecord(PREFIX_NOTE, NS_NOTE);
            record.setKey(recordKey);
            // fields
            fields = new ArrayList<>();
            field = new ClobField(FIELD_BODY, null);
            fields.add(field);
            field = new TextField(FIELD_ICALUID, event.getUid());
            fields.add(field);
            record.addFields(fields);
            recordSet.addRecord(record);
        }

        // MODBY
        record = new EimRecord(PREFIX_MODIFIEDBY, NS_MODIFIEDBY);
        EimRecordKey recordKeyModBy = new EimRecordKey();
        field = new TextField(FIELD_UUID, event.getUid());
        recordKeyModBy.addField(field);
        field = new TextField(FIELD_USERID, modifiedByUsername); // this is the med username
        recordKeyModBy.addField(field);
        if (creating)
            field = new IntegerField(FIELD_ACTION, 500);
        else
            field = new IntegerField(FIELD_ACTION, 100);
        recordKeyModBy.addField(field);
        field = new DecimalField(FIELD_TIMESTAMP, milisNow, 13, 3); // create
        recordKeyModBy.addField(field);
        record.setKey(recordKeyModBy);
        recordSet.addRecord(record);

        return recordSet;
    }

    private NoteItem createChild(CollectionItem collection, EimRecordSet recordset) {
        NoteItem child = new HibNoteItem();

        child.setUid(recordset.getUuid());
        child.setIcalUid(child.getUid());
        child.setOwner(collection.getOwner());

        // if the item is a modification, relate it to the item it
        // modifies
        if (CosmoCalendarService.isRecurrenceUid(child.getUid())) {
            ModificationUid modUid = toModificationUid(child.getUid());
            String masterUid = modUid.getParentUid();

            // modification should inherit icaluid
            child.setIcalUid(null);

            NoteItem master  = null;
            for (Item sibling : collection.getChildren()) {
                if (sibling.getUid().equals(masterUid)) {
                    if (! (sibling instanceof NoteItem))
                        throw new BaseApplicationExceptionRuntime("Modification master item " + sibling.getUid() + " is not a note item");
                    master = (NoteItem) sibling;
                    break;
                }
            }

            if (master != null)
                child.setModifies(master);
            else
                throw new BaseApplicationExceptionRuntime("Master item not found for " + child.getUid());
        }

        return child;
    }
    private ModificationUid toModificationUid(String uid) {
        try {
            return new ModificationUid(uid);
        } catch (ModelValidationException e) {
            throw new BaseApplicationExceptionRuntime("Invalid modification uid " + uid);
        }
    }
}
