package com.ecco.dto;

import org.jspecify.annotations.Nullable;
import javax.servlet.http.HttpServletResponse;


/**
 * A response that wraps results and or status information such as a status code that was received from a
 * delegated web service.
 *
 * @param <P> payload type
 */
public class DelegateResponse<P> {

    public static <T> DelegateResponse<T> ok(T payload) {
        return new DelegateResponse<>(payload, HttpServletResponse.SC_OK, "OK");
    }

    public static <T> DelegateResponse<T> fail(String message) {
        return new DelegateResponse<>(null, HttpServletResponse.SC_INTERNAL_SERVER_ERROR, message);
    }

    public static <I,O> DelegateResponse<O> emptyFrom(
            DelegateResponse<I> input) {
        return new DelegateResponse<>(null, input.getStatusCode(), input.getStatusText());
    }

    @Nullable
    private final P payload;

    private final int statusCode;

    private final String statusText;

    private DelegateResponse(@Nullable P payload, int statusCode, String statusText) {
        super();
        this.payload = payload;
        this.statusCode = statusCode;
        this.statusText = statusText;
    }

    /** Can be null if response is an error, but is non-null when response is okay */
    public P getPayload() {
        return payload;
    }

    public int getStatusCode() {
        return statusCode;
    }

    public String getStatusText() {
        return statusText;
    }
}
