package com.ecco.dom.clients;

import java.util.UUID;

import org.joda.time.Instant;

import org.jspecify.annotations.NonNull;

import javax.persistence.*;

import com.ecco.infrastructure.dom.BaseIntKeyedCommand;
import org.jspecify.annotations.Nullable;

@Entity
@Table(name = "clnt_commands")
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name = "commandname", discriminatorType = DiscriminatorType.STRING)
@DiscriminatorValue("update")
public class ClientCommand extends BaseIntKeyedCommand {

    @Column
    private long clientId;


    /** @deprecated Do not use. Required by JPA/Hibernate. */
    @Deprecated
    protected ClientCommand() {
        super();
    }

    public ClientCommand(@Nullable UUID uuid, @NonNull Instant remoteCreationTime,
                         long userId, @NonNull String body, long clientId) {
        super(uuid, remoteCreationTime, userId, body);
        this.clientId = clientId;
    }

    public long getClientId() {
        return clientId;
    }
}
