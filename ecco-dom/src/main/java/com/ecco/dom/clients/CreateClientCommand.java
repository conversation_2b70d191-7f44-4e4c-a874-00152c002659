package com.ecco.dom.clients;

import org.joda.time.Instant;

import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import java.util.UUID;

/**
 * As per ClientCommand, but with the correct discriminator
 */
@Entity
@DiscriminatorValue("create")
public class CreateClientCommand extends ClientCommand {

    public CreateClientCommand(@Nullable UUID uuid, @NonNull Instant remoteCreationTime,
                               long userId, @NonNull String body, long clientId) {
        super(uuid, remoteCreationTime, userId, body, clientId);
    }

    /**
     * @deprecated Do not use. Required by JPA/Hibernate.
     */
    @Deprecated
    protected CreateClientCommand() {
        super();
    }

}
