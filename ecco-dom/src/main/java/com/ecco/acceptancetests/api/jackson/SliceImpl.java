package com.ecco.acceptancetests.api.jackson;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.jspecify.annotations.NonNull;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.domain.Sort;

import java.util.Iterator;
import java.util.List;
import java.util.Spliterator;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 * Slice implementation that <PERSON> can deserialise to.
 * The Slices are generated by Spring Data internally and exposed
 * by web-api controllers such as SupportEvidenceController.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class SliceImpl<T> implements Slice<T> {

    private List<T> content;
    private int number;
    private boolean first;
    private boolean last;
    private int numberOfElements;
    private int size;

    @NonNull
    @Override
    public List<T> getContent() {
        return content;
    }

    public void setContent(List<T> content) {
        this.content = content;
    }

    @Override
    public boolean hasContent() {
        return content != null && content.size() > 0;
    }

    @Override
    public boolean isFirst() {
        return first;
    }

    public void setFirst(boolean first) {
        this.first = first;
    }

    @Override
    public boolean isLast() {
        return last;
    }

    public void setLast(boolean last) {
        this.last = last;
    }

    @Override
    public int getNumber() {
        return number;
    }

    public void setNumber(int number) {
        this.number = number;
    }

    @Override
    public int getNumberOfElements() {
        return numberOfElements;
    }

    public void setNumberOfElements(int numberOfElements) {
        this.numberOfElements = numberOfElements;
    }

    @Override
    public int getSize() {
        return size;
    }

    public void setSize(int size) {
        this.size = size;
    }

    @NonNull
    @Override
    public <U> Slice<U> map(@NonNull Function<? super T, ? extends U> converter) {
        throw new UnsupportedOperationException();
    }

    @NonNull
    @Override
    public Iterator<T> iterator() {
        return content.iterator();
    }

    @Override
    public void forEach(Consumer<? super T> action) {
        content.forEach(action);
    }

    @Override
    public Spliterator<T> spliterator() {
        return content.spliterator();
    }

    @Override
    public boolean hasPrevious() {
        throw new UnsupportedOperationException();
    }

    @Override
    public boolean hasNext() {
        throw new UnsupportedOperationException();
    }

    @NonNull
    @Override
    public Pageable nextPageable() {
        throw new UnsupportedOperationException();
    }

    @NonNull
    @Override
    public Pageable previousPageable() {
        throw new UnsupportedOperationException();
    }

    @NonNull
    @Override
    public Sort getSort() {
        throw new UnsupportedOperationException();
    }


}
