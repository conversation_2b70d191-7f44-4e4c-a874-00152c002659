package com.ecco.service;

import java.util.List;

import org.jspecify.annotations.NonNull;

import org.joda.time.DateTime;

import com.ecco.dom.EvidenceSupportWork;
import com.ecco.dom.Review;
import com.ecco.dto.EvidenceLegacyReviewDto;
import com.ecco.infrastructure.annotations.WriteableTransaction;
import org.joda.time.LocalDate;

@WriteableTransaction
public interface ReviewService {

    EvidenceLegacyReviewDto getReviewDto(int serviceRecipientId);

    Review getReview(long id);
    List<Review> getReviews(int serviceRecipientId);

    //List<DateTime> getFutureReviewDates(Long referralId);

    // not ideal, as really we only want the
    List<EvidenceSupportWork> getThreatsOutstanding(int serviceRecipientId, DateTime end);

    void setComplete(long reviewId);

    Review setReview(Review review);

    void setCustomReviewDate(int serviceRecipientId, DateTime reviewDate);

    /**
     * Set review dates according to the specified schedule using d,w,m,y for day, week, month, year
     */
    void setReviewDates(int serviceRecipientId, @NonNull String reviewSchedule);

    // this refers to review events with the client
    //List<DateTime> getReviewDates(Long referralId);

    List<LocalDate> getReviewDates(int srId);
    List<LocalDate> getNextReviewDates(int srId);

}
