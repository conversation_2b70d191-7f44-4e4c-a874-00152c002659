package com.ecco.security.ldap;

import com.ecco.security.ldap.service.LdapGroupAggregate;
import com.ecco.security.ldap.service.LdapGroupMappingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.composed.web.rest.json.GetJson;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.Set;

// ROLE_ADMIN required only as a consequence of access to editing a project
@PreAuthorize("hasAnyRole('ROLE_LOGIN', 'ROLE_ADMIN')")
@RestController
@RequestMapping(produces = MediaType.APPLICATION_JSON_VALUE)
public class LdapController {

    private final boolean enableLdap;
    private final LdapGroupMappingService ldapGroupMappingService;


    @Autowired
    public LdapController(boolean enableLdap, LdapGroupMappingService ldapService) {
        this.enableLdap = enableLdap;
        this.ldapGroupMappingService = ldapService;
    }

    /** Determine if ldap is configured
     */
    @GetJson("/ldap/enabled/")
    public Boolean isEnabled() {
        if (this.enableLdap) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    @RequestMapping(value = "/ldap/entries/", method = RequestMethod.GET)
    public Set<LdapGroupAggregate> findAll() {
        return ldapGroupMappingService.findAll();
    }

}
