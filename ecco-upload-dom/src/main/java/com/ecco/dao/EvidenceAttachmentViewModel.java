package com.ecco.dao;

import lombok.Getter;
import org.springframework.hateoas.RepresentationModel;

import java.beans.ConstructorProperties;
import java.util.UUID;

@Getter
public class EvidenceAttachmentViewModel extends RepresentationModel<EvidenceAttachmentViewModel>{

    // need to know the type, so we know how to render it */
    private final String taskEvidenceType;
    // used as an index client-side
    private final UUID workUuid;

    @ConstructorProperties({"taskEvidenceType", "workUuid"})
    public EvidenceAttachmentViewModel(String taskEvidenceType, UUID workUuid) {
        super();
        this.taskEvidenceType = taskEvidenceType;
        this.workUuid = workUuid;
    }

}
