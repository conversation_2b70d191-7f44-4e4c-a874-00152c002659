These are some examples of report definitions that can be used, and are based on what we originally had hard-coded.

The name is aligned fully left, and then the JSON object below should be pasted in to the definition in the report editor.


## example of thought process on designing reports

A request came in to add a chart to "support work by service, then by project, then time by referral breakdown" for comment types.

This requires adding, after 'visitCountsByProject' an 'visitCountsByCommentType' - however we realised that in the process of adding that
the analyser was grouping things back up into ReferralAggregate - and when unclicking (actually there was no ungroup - so we cloned
an 'visitSupportCountsByAuthorAnalyser' which did the same thing) it still counted all the elements - not the comment type we clicked on.

This was because the WorkWithRefToRA is a reference to the report item which still contains all the original work - no longer the filtered work.

HOWEVER, instead of trying to solve this, you should remember to bring the previous analyser back to basics - so this meant:
 - 'visitCountsByProject' -> 'workFromReferrals' -> 'workByCommentType' + chart -> then building up again -> 'workByRid' -> 'workGroupToVisitGroup'

Therefore each analysis was doing what was needed on exactly the required data and no more. Its better to build tools to breakdown, buildup and keep
analysis simple. These are thoughts we've had before, but worthy of a note so we can add to.


## selectionCriteria

see SelectionCriteria in charts/dto.ts
also see ReportCriteriaDtoFactory.ts for how the dates are gathered

```
// selectorType: TODO split this into selectionPropertyPath (if date-based) and calculatedReferralStatus (if 'byReferralStatus')`
```

## selectionMultiCriteria

eg
```
    "selectionMultiCriteria": [

        // specify the first set of data to load
        {
            "selectionRootEntity": "Questionnaire",
            "selectorType": "byStartOfQuarter",
            "questionnaireEvidenceGroup": "hactQuestionnaire",
            "relativeStartDate": "2016-04-01",
            "relativeStartIndex": 0,
            "relativeEndIndex": 1
        },

        // if there is another in the selectionMultiCriteria array, it must have a selectionCriteriaSource (or an error is thrown)
        {
            "selectionCriteriaSource": {
                "analyserType": "questionWorkToSrIds",
                "entityIdType": "serviceRecipientId"
            },
            "selectionRootEntity": "Client"
        },

        // the 'selectionCriteriaSource' is passed into the 'selectionRootEntity' (as 'previousDataSource') which is chosen
        // by the usual mechanism of getSingleDataSource, which finds 'Client' as ReferralQueryDataSource which then resolves
        // and handles the 'selectionCriteriaSource' and continues loading the 'fetchRelatedEntities' etc - which means
        // the criteria is not loaded again for the selectionRootEntity - effectively the 'selectionCriteriaSource' is the
        // data source and the 'selectionRootEntity' is the result handler, continuing to load fetchRelatedEntities etc.
        // NB the inheritDatesOfIndex is an index to choose the selectionCriteria dates, typically 0 for the first data source.
        // ?? HOWEVER it appears that this inheritDatesOfIndex is not applied when there is an alternative data source

        // another one:
        {
            "selectionCriteriaSource": {
                "analyserConfig": { "analyserType": "referralToClientId" } // ANALYSER to extract clientId which then passes to 'referralsFromPreviousDataSource'
                "entityIdType": "clientId" // this then loads all referrals ignoring criteria -> referralRepository.findAllReferralWithoutSecuritySummaryByClientAsReferral(id);
                "inheritDatesOfIndex": 0 // so that dates get passed through - otherwise NPE since hactAnalyser can't use the report dates
            },
            "selectionRootEntity": "Client"
        },

        // another one:
        {
            "selectionCriteriaSource": {
                "analyserType": "clientToClientId",
                "entityIdType": "clientId",
                "inheritDatesOfIndex": 0
            },
            "selectionRootEntity": "Referral",
            "hactSessionData": "true",
            "questionnaireEvidenceGroup": "hactQuestionnaire",
            "fetchRelatedEntities": [
               "client", "questionnaireWork"
            ]
        }],

        // another one:
        // this one uses 'self'...
        // ReferralsByMonth -> ReferralsByMonthReportCapability -> getData -> ReferralsTimeSeriesDataSource extends TimeSeriesDataSource
        //      -> ByMonthAnalysis which has 'self' and TimeSeriesDataSource is aware of previousDataSource - and just concatenates
            "selectionCriteriaSource": {
                "analyserType": "self",
                "inheritDatesOfIndex": 0
            },
            "groupBy": "exitedDate",
            "referralStatus": "exited",
            "selectionRootEntity": "ReferralsByMonth",
            "selectorType": "byStartOfMonth",
            "relativeStartIndex": -2,
            "relativeEndIndex": 1,
            "fetchRelatedEntities": []
```

##  stages

    0 = initialAnalysis
        initial data loaded ChartControl.fetchViewData
        which returns ReportDataSourceFactory.getDataSource(chartDef).getData();
        where getDataSource is ReportDataSourceFactory (eg ReferralQueryDataSource getData returns ReferralAggregateAnalysis)
    1 = our first stage output
    ...


###   stage.seriesDefs

```json5
    // example of percentages
{
    "seriesDefs": [
        {
            "label": "count",
            "valuePath": "count",
            "renderMode": "PIE",
            "renderOptions": {
                "percentage": "true"
            }
        }
    ]
}
```
```json5
{
    // example of donut with totals showing in the middle (jqplot needs upgrading for this)
    "seriesDefs": [
        {
            "label": "count",
            "valuePath": "count",
            "renderMode": "DONUT",
            "renderOptions": {
                "totalLabel": "true"
            }
        }
    ]
}
```
```json5
{
    // example of angled labels
    "seriesDefs": [
        {
            "label": "count",
            "valuePath": "count",
            "renderMode": "BAR",
            "renderOptions": {
                "angleLabels": "true"
            }
        }
    ]
}
```


###   stage.columns (for StageType.TABLE)

For a table column representation ("columns" field) we can either have just the representation key (e.g. "cid",
or we can have an object:

```json5
{
    "title": "client id",        // title to use instead of hte representation key
    "representation": "cid",     // the representation key
    "path": ["referral","textMap","Some key"]  // this uses a substitute value but the same representation function (search for '"path"' below - i.e with quotes)
}
```
The definition of "columns" is specified in controls/dto as ColumnSpecification. When rendering the table, types/SequenceAnalysis
finds the right "className" (which is the hard coded definition of what columns the analyser can show) and calls
getRecordRepresentation(with the list of "columns") which turns the "columns" into a list of the predefined ColumnRepresentation's.

This is where the type is judged to be a string or an object, and expanded accordingly.

This is where the object 'representation' is a little odd in that it inherits the ColumnRepresentation from the 'representation'
 - it would be better to specify the actual 'text/number' etc here, although needing to be of the same row type will cause problems.

NB "path" allows us to bypass hard coded definitions, so if we were to want to display the agency details, we can use:
```json5
{
    "title": "agency name",
    "representation": "some string value",
    "path": ["agencySource", "name"]
}
```
The code tables.getPathValue applies the path to the row, returning the answer or null if a property doesn't exit


// ****************************
//   stage.selectionAnalyser / click on a chart key/table row
// ****************************
On a CHART or TABLE report stage a click generated an event captured by
eg ReportStagesControl.onTableDataClick. This then chooses a selectionAnalyser
to determine how to process the data for the click.

A selectionAnalyser is only used with types.onClickWith(analyser, data) when clicking single keys/legends.
It is not used to specify what happens when clicking 'select all' or multiple keys. Currently,
when clicking select all, assumptions are made without reference to a converter/analyser.

"ungroup" - the default selection if no other (see charts/domain.ReportStage)
            such as when clicking on a chart, where the 'key' chosen will be a collection
            NB the ungroup analyser still needs to exist
            NB 'single' is defined in the analysers (and specifying the default) - addOnClickAnalyser("ungroup" - referenced by the definition
"single"  - for working with the analysis itself
            such as when doing chained KPI badges, passing the same piece of information on
            NB the single analyser still needs to exist
            NB 'single' is defined in the analysers - addOnClickAnalyser("single" - referenced by the definition
"singleCell" -
            when a matrix analyser stage is defined, cell data is gathered for the onClickWith(analyser, cellData)
            meaning that we can specify "selectionAnalyser": "singleCell" to be referenced in the analysis
            see questionnaireAnalysis.QuestionnaireMultiSnapshotMatrixAnalysis
            NB 'singleCell' is defined in the analysers - addOnClickAnalyser("singleCell" - referenced by the definition


// ****************************
//   stage.selectionKey / the clicked-on key/legend
// ****************************

ReportStagesControl (for ChartReportStage and AnalyserReportStage) also uses
the notion of a 'nextKeys' to move to the next stage if a next key exists
 - which could be from a previously rendered chart in the sequence in keySelections,
or from the "selectionKey" defined in the definition. This therefore means
that defining "selectionKey" on a CHART or ANALYSER automatically renders it.
```
{

    "stages": [
        ...
        {
            "description": "",
            "stageType": "CHART",
            "selectionAnalyser": "single",
            "selectionKey": "my service",
            "seriesDefs": [
```

CHARTS
    for a chart key click:
        it gets the key and count clicked into an ArrayKeySelector (this could be 'to start' in a bar chart showing 'status now')
        it then calls getAnalysisSubGroup which takes the analysis (GroupedReferralAggregateAnalysis) and calls:
            var selections = analysis.getData().filter( (item) => selector.matches(analysis.getKey(item)) );
                which takes the data in GroupedReferralAggregateAnalysis
                and is filtered by ArrayKeySelector.matches - which does: return this.keys.indexOf(key) >= 0;
                where (key) is the getKey(item) which was passed in the contructor as (item: Group<ReferralAggregate>) => item.key)
                and therefore it filters the match based on key value of 'status now', eg 'to start'
                ** key is the keyValue
            var data = analysis.onClickWith(stage.getSelectionAnalyser(), selection);
                the data (Analyser) is the analyser's this.addOnClickAnalyser which matches the stage (eg "selectionAnalyser": "referrals")
                or ungroup is chosen: return this.dto.selectionAnalyser || "ungroup";
         this is then passed to showDataForStage
TABLES
    for a row click:
        simpler showDataForStage based on the event data


// ****************************
//   canSkip / click on 'select all'
// ****************************
each stage can have canSkip, eg
    "description": "by region",
    "stageType": "CHART",
    "canSkip": "true",
    "seriesDefs": ...

this is also automatically determined - see charts/domain.ReportStage.showSelectAll
    when the 'description.match(/by\ project|by\ service|by\ worker$/)'
but can be overridden with canSkip: false

it allows the 'select all' to show - currently on ReportStagesControl.ChartReportStage and TableReportStage
which calls selectAll() [which does this in ChartReportStage this.chartData.getDataSeries()[0].clickAll()]
which in chart.ts fires an event with: this.clickEventBus.fire(new ClickEvent<TDatum>().selectAll(this.data));


the event is picked up by ReportStagesControl.onChartDataClick and onTableDataClick

CHARTS & TABLES
    for 'select all' click:
        it gets the previous analysis (GroupedReferralAggregateAnalysis) and passes to showDataForStage
        therefore "only works for group/ungroup giving same class type."
        because ungroup/group is skipped on select all, since the output of the previous stage is passed to the input of the next one
        eg:

```json5
    // incoming ReferralAggregateAnalysis
    [
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": { "analyserType": "referralCountsByStatus" }
        },
        // produces GroupedReferralAggregateAnalysis with data Group<ReferralAggregate>

        {
            "description": "referrals showing 'status now'",
            "stageType": "CHART",
            "canSkip": "true",
            "seriesDefs": [
                {
                    "label": "status now",
                    "valuePath": "count",
                    "renderMode": "BAR"
                }
            ]
        },

        // 'select all' means taking the previous stage data Group<ReferralAggregate>
        // expected ReferralAggregate (ReferralReportItem actually is ReferralAggregate)
        {
            "description": "breakdown of referrals",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "ReferralReportItem",
                "columns": ["rid", "cid", "client", "project", "from", "received", "status now", "worker"]
                }
        }
    ]
```


REPORTS
=======

referrals by service, then by project, then breakdown
order = 1
```json5
        {
            "description": "on referrals this quarter",
            "selectionCriteria": {
                "selectionRootEntity": "Referral",
                "selectorType": "byStartOfQuarter",
                "selectionPropertyPath": "referral.receivedDate",
                "relativeStartIndex": 0,
                "relativeEndIndex": 1,
                "fetchRelatedEntities": []
            },
            "stages": [
                {
                    "description": "-not visible-",
                    "stageType": "ANALYSER",
                    "analyserConfig": { "analyserType": "referralCountsByService" }
                },
                {
                    "description": "by service",
                    "stageType": "CHART",
                    "seriesDefs": [
                        { "label": "count",    "valuePath": "count",  "renderMode": "PIE" }
                    ]
                },
                {
                    "description": "-not visible-",
                    "stageType": "ANALYSER",
                    "analyserConfig": { "analyserType": "referralCountsByProject" }
                },
                {
                    "description": "by project",
                    "stageType": "CHART",
                    "seriesDefs": [
                        { "label": "count", "valuePath": "count", "renderMode": "PIE" }
                    ]
                },
                {
                    "description": "-not visible-",
                    "stageType": "ANALYSER",
                    "analyserConfig": { "analyserType": "referralCountsByWorker" }
                },
                {
                    "description": "by worker",
                    "stageType": "CHART",
                    "seriesDefs": [
                        { "label": "count", "valuePath": "count", "renderMode": "PIE" }
                    ]
                },
                {
                    "description": "breakdown of referrals",
                    "stageType": "TABLE",
                    "tableRepresentation": {
                        "className": "ReferralReportItem",
                        "columns": ["rid", "cid", "client", "project", "from", "received", "decided", "status now", "worker"]
                        }
                }
            ]
        }
```
referrals by 'status now', then breakdown with goals outstanding/achieved
order = 2
00200000-0000-babe-babe-dadafee1600d
```json5
        {
            "description": "on referrals this quarter",
            "selectionCriteria": {
                "selectionRootEntity": "Referral",
                "selectorType": "byStartOfQuarter",
                "selectionPropertyPath": "referral.receivedDate",
                "relativeStartIndex": 0,
                "relativeEndIndex": 1,
                "fetchRelatedEntities": ["supportWork"]
            },
            "stages": [
                {
                    "description": "-not visible-",
                    "stageType": "ANALYSER",
                    "analyserConfig": { "analyserType": "referralCountsByStatus" }
                },
                {
                    "description": "referrals by status",
                    "stageType": "CHART",
                    "canSkip": "true",
                    "seriesDefs": [
                        {
                            "label": "count",
                            "valuePath": "count",
                            "renderMode": "PIE"
                        }
                    ]
                },
                {
                    "description": "breakdown of referrals with goals outstanding/achieved",
                    "stageType": "TABLE",
                    "tableRepresentation": {
                        "className": "ReferralReportItem",
                        "columns": ["rid", "cid", "client", "project", "from", "received", "status now", "worker",
                            "achieved", "outstanding"]
                        }
                }
            ]
        }
```

referrals by demographic - age at referral, then breakdown
order = 23
        {
            "description": "on referrals this quarter",
            "selectionCriteria": {
                "selectionRootEntity": "Referral",
                "selectorType": "byStartOfQuarter",
                "selectionPropertyPath": "referral.receivedDate",
                "relativeStartIndex": 0,
                "relativeEndIndex": 1,
                "fetchRelatedEntities": ["client"]
            },
            "stages": [
                {
                    "description": "-not visible-",
                    "stageType": "ANALYSER",
                    "analyserConfig": { "analyserType": "referralCountsByAgeAtDateOfReferral" }
                },
                {
                    "description": "referrals by age",
                    "stageType": "CHART",
                    "seriesDefs": [
                        {
                            "label": "count",
                            "valuePath": "count",
                            "renderMode": "PIE"
                        }
                    ]
                },
                {
                    "description": "breakdown of referrals",
                    "stageType": "TABLE",
                    "tableRepresentation": {
                        "className": "ReferralReportItem",
                        "columns": ["rid", "cid", "client", "service", "project", "from", "received", "status now", "worker"]
                        }
                }
            ]
        }


[referrals with] goals outstanding/achieved by worker, then referral breakdown
order = 4
uuid 00400000-0000-babe-babe-dadafee1600d
        {
            "description": "on referrals this quarter",
            "selectionCriteria": {
                "selectionRootEntity": "Referral",
                "selectorType": "byStartOfQuarter",
                "selectionPropertyPath": "referral.receivedDate",
                "relativeStartIndex": 0,
                "relativeEndIndex": 1,
                "fetchRelatedEntities": ["supportWork"]
            },
            "stages": [
                {
                    "description": "-not visible-",
                    "stageType": "ANALYSER",
                    "analyserConfig": { "analyserType": "smartStepCountsByWorker" }
                },
                {
                    "description": "goals outstanding/achieved by worker",
                    "stageType": "CHART",
                    "seriesDefs": [
                        {
                            "label": "outstanding",
                            "valuePath": "totalOutstandingSmartSteps",
                            "renderMode": "BAR"
                        },
                        {
                            "label": "achieved",
                            "valuePath": "totalAchievedSmartSteps",
                            "renderMode": "BAR"
                        }
                    ]
                },
                {
                    "description": "breakdown of referrals with goals outstanding/achieved",
                    "stageType": "TABLE",
                    "tableRepresentation": {
                        "className": "ReferralReportItem",
                        "columns": ["rid", "cid", "client", "project", "from", "received", "status now", "worker",
                            "achieved", "outstanding"]
                        }
                }
            ]
        }

// TEST REPLACEMENT FOR above graph 4
// this is equivalent to above, but could be better since the key is the worker name, but there are 2 columns per key (outstanding / achieved)
[referrals with] goals outstanding/achieved by worker, then breakdown
        {
            "description": "on referrals this quarter",
            "selectionCriteria": {
                "selectionRootEntity": "Referral",
                "selectorType": "byStartOfQuarter",
                "selectionPropertyPath": "referral.receivedDate",
                "relativeStartIndex": 0,
                "relativeEndIndex": 1,
                "fetchRelatedEntities": ["supportWork"]
            },
            "stages": [
                {
                    "description": "-not visible-",
                    "stageType": "ANALYSER",
                    "analyserConfig": { "analyserType": "smartStepFromReferrals" }
                },
                {
                    "description": "-not visible-",
                    "stageType": "ANALYSER",
                    "analyserConfig": { "analyserType": "smartStepWithRefCountsByWorker" }
                },

                {
                    "description": "goals outstanding/achieved by worker",
                    "stageType": "CHART",
                    "seriesDefs": [
                        {
                            "label": "outstanding",
                            "valuePath": "totalOutstandingSmartSteps",
                            "renderMode": "BAR"
                        },
                        {
                            "label": "achieved",
                            "valuePath": "totalAchievedSmartSteps",
                            "renderMode": "BAR"
                        }
                    ]
                },
                {
                    "description": "breakdown of SMART STEPS",
                    "stageType": "TABLE",
                    "tableRepresentation": {
                        "className": "SmartStepWithRef",
                        "columns": ["a-id", "a-name", "a-status", "r-id", "client", "work date", "time (mins)", "type"]
                        }
                }
            ]
        }
// TEST REPLACEMENT FOR above graph 4

[referrals with] goals outstanding by worker, then by service, then by 'status now', then breakdown
00500000-0000-babe-babe-dadafee1600d
order = 5
        {
            "description": "on referrals this period",
            "selectionCriteria": {
                "selectionRootEntity": "Referral",
                "selectorType": "byStartOfQuarter",
                "selectionPropertyPath": "referral.receivedDate",
                "relativeStartIndex": 0,
                "relativeEndIndex": 1,
                "fetchRelatedEntities": ["supportWork"]
            },
            "stages": [
                {
                    "description": "-not visible-",
                    "stageType": "ANALYSER",
                    "analyserConfig": { "analyserType": "smartStepCountsByWorker" }
                },
                {
                    "description": "goals outstanding by worker",
                    "stageType": "CHART",
                    "seriesDefs": [
                        {
                            "label": "outstanding",
                            "valuePath": "totalOutstandingSmartSteps",
                            "renderMode": "PIE"
                        }
                    ]
                },
                {
                    "description": "-not visible-",
                    "stageType": "ANALYSER",
                    "analyserConfig": { "analyserType": "smartStepCountsByService" }
                },
                {
                    "description": "by service",
                    "stageType": "CHART",
                    "seriesDefs": [
                        {
                            "label": "outstanding",
                            "valuePath": "totalOutstandingSmartSteps",
                            "renderMode": "PIE"
                        }
                    ]
                },
                {
                    "description": "-not visible-",
                    "stageType": "ANALYSER",
                    "analyserConfig": { "analyserType": "smartStepCountsByStatus" }
                },
                {
                    "description": "by status",
                    "stageType": "CHART",
                    "seriesDefs": [
                        {
                            "label": "outstanding",
                            "valuePath": "totalOutstandingSmartSteps",
                            "renderMode": "PIE"
                        }
                    ]
                },
                {
                    "description": "breakdown of referrals with goals outstanding/achieved",
                    "stageType": "TABLE",
                    "tableRepresentation": {
                        "className": "ReferralReportItem",
                        "columns": ["rid", "cid", "client", "project", "from", "received", "status now", "worker",
                            "achieved", "outstanding"]
                        }
                }
            ]
        }

[referrals with] goals by service, then by project, then by worker, then breakdown
order = 5
005b0000-0000-babe-babe-dadafee1600d
        {
            "description": "on referrals this period",
            "selectionCriteria": {
                "selectionRootEntity": "Referral",
                "selectorType": "byStartOfQuarter",
                "selectionPropertyPath": "referral.receivedDate",
                "relativeStartIndex": 0,
                "relativeEndIndex": 1,
                "fetchRelatedEntities": ["supportWork"]
            },
            "stages": [
                {
                    "description": "-not visible-",
                    "stageType": "ANALYSER",
                    "analyserConfig": { "analyserType": "smartStepCountsByService" }
                },
                {
                    "description": "by service",
                    "stageType": "CHART",
                    "seriesDefs": [
                        {
                            "label": "count of goals",
                            "valuePath": "count",
                            "renderMode": "PIE"
                        }
                    ]
                },
                {
                    "description": "-not visible-",
                    "stageType": "ANALYSER",
                    "analyserConfig": { "analyserType": "smartStepCountsByProject" }
                },
                {
                    "description": "by project",
                    "stageType": "CHART",
                    "seriesDefs": [
                        {
                            "label": "count of goals",
                            "valuePath": "count",
                            "renderMode": "PIE"
                        }
                    ]
                },
                {
                    "description": "-not visible-",
                    "stageType": "ANALYSER",
                    "analyserConfig": { "analyserType": "smartStepCountsByWorker" }
                },
                {
                    "description": "by worker",
                    "stageType": "CHART",
                    "seriesDefs": [
                        {
                            "label": "count of goals",
                            "valuePath": "count",
                            "renderMode": "PIE"
                        }
                    ]
                },
                {
                    "description": "breakdown of referrals with goals outstanding/achieved",
                    "stageType": "TABLE",
                    "tableRepresentation": {
                        "className": "ReferralReportItem",
                        "columns": ["rid", "cid", "client", "project", "from", "received", "status now", "worker",
                            "achieved", "outstanding"]
                        }
                }
            ]
        }

goals outstanding/achieved by worker, then breakdown of goals
order = 6
        {
            "description": "on referrals this quarter",
            "selectionCriteria": {
                "selectionRootEntity": "Referral",
                "selectorType": "byStartOfQuarter",
                "selectionPropertyPath": "referral.receivedDate",
                "relativeStartIndex": 0,
                "relativeEndIndex": 1,
                "fetchRelatedEntities": ["supportWork"]
            },
            "stages": [
                {
                    "description": "-not visible-",
                    "stageType": "ANALYSER",
                    "analyserConfig": { "analyserType": "smartStepFromReferrals" }
                },
                {
                    "description": "-not visible-",
                    "stageType": "ANALYSER",
                    "analyserConfig": { "analyserType": "smartStepWithRefCountsByWorker" }
                },
                {
                    "description": "",
                    "stageType": "TABLE",
                    "tableRepresentation": {
                        "className": "SmartStepWithRefCounts",
                        "columns": ["key", "achieved", "outstanding"]
                        }
                },
                {
                    "description": "breakdown of smart steps",
                    "stageType": "TABLE",
                    "tableRepresentation": {
                        "className": "SmartStepWithRef",
                        "columns": ["a-id", "a-name", "a-status", "r-id", "client", "work date", "time (mins)", "type"]
                        }
                }
            ]
        }

support work by worker, then breakdown
            "description": "on support work this quarter showing time spent and number of entries",
            "selectionCriteria": {
                "selectionRootEntity": "Referral",
                "selectorType": "byStartOfQuarter",
                "selectionPropertyPath": "supportWork.workDate",
                "relativeStartIndex": 0,
                "relativeEndIndex": 1,
                "fetchRelatedEntities": ["supportWork"]
            },
legacy selectionCriteria now gets migrated into (this one is for staff notes)
            "selectionCriteria": {
                "selectionRootEntity": "SupportWork",
                "selectorType": "byStartOfQuarter",
                "supportEvidenceGroup": "supportStaffNotes",
                "relativeStartIndex": 0,
                "relativeEndIndex": 1,
                "fetchRelatedEntities": [
                    "referral"
                ]
            },
        {
            "stages": [
                {
                    "description": "-not visible-",
                    "stageType": "ANALYSER",
                    "analyserConfig": { "analyserType": "visitCountsByAuthor" }
                },
                {
                    "description": "inner ring: number of entries, outer ring: 'time (mins)'",
                    "stageType": "CHART",
                    "seriesDefs": [
                        {
                            "label": "mins spent",
                            "valuePath": "totalTimeSpentMins",
                            "renderMode": "PIE"
                        },
                        {
                            "label": "number of entries",
                            "valuePath": "totalVisits",
                            "renderMode": "PIE"
                        }
                    ]
                },
                {
                    "description": "breakdown of referrals with goals outstanding/achieved",
                    "stageType": "TABLE",
                    "tableRepresentation": {
                        "className": "ReferralReportItem",
                        "columns": ["rid", "cid", "client", "project", "from", "received", "status now", "worker",
                            "achieved", "outstanding"]
                        }
                }
            ]
        }

activity demand by project, then breakdown
        {
            "description": "on all referrals, showing the best days to schedule activities given the days clients are attending and wanting those activities",
            "selectionCriteria": {
                "selectionRootEntity": "Referral",
                "selectorType": "byReferralStatus",
                "selectionPropertyPath": "referral.serviceId",
                "serviceId": null,
                "referralStatus": "allNoDates",
                "fetchRelatedEntities": ["activityInterest"]
            },
            "stages": [
                {
                    "description": "-not visible-",
                    "stageType": "ANALYSER",
                    "analyserConfig": { "analyserType": "activitiesByProject" }
                },
                {
                    "description": "activity demand by project",
                    "stageType": "CHART",
                    "selectionAnalyser": "referrals",
                    "selectionKey": "project 4",
                    "seriesDefs": [
                        {
                            "label": "mon",
                            "valuePath": "monday",
                            "renderMode": "BAR"
                        },
                        {
                            "label": "tues",
                            "valuePath": "tuesday",
                            "renderMode": "BAR"
                        },
                        {
                            "label": "wed",
                            "valuePath": "wednesday",
                            "renderMode": "BAR"
                        },
                        {
                            "label": "thurs",
                            "valuePath": "thursday",
                            "renderMode": "BAR"
                        },
                        {
                            "label": "fri",
                            "valuePath": "friday",
                            "renderMode": "BAR"
                        },
                        {
                            "label": "sat",
                            "valuePath": "saturday",
                            "renderMode": "BAR"
                        },
                        {
                            "label": "sun",
                            "valuePath": "sunday",
                            "renderMode": "BAR"
                        }
                    ]
                },
                {
                    "description": "-not visible-",
                    "stageType": "ANALYSER",
                    "analyserConfig": { "analyserType": "activitiesByActivityInterest" }
                },
                {
                    "description": "breakdown of activity demand across days",
                    "stageType": "TABLE",
                    "tableRepresentation": {
                        "className": "DaysOfWeekAnalysis",
                        "columns": ["activity", "mon", "tue", "wed", "thur", "fri", "sat", "sun"]
                        }
                }
            ]
        }

** This needs to exist as: 403252ba-0001-babe-babe-dada7ee1600d (it's referenced from groupSupportActivity.jsp) **
referrals by project with activity demand on the LD service, then breakdown
        {
            "description": "on all referrals, showing the best days to schedule activities given the days and projects clients are attending and wanting those activities",
            "selectionCriteria": {
                "selectionRootEntity": "Referral",
                "selectorType": "byReferralStatus",
                "selectionPropertyPath": "referral.serviceId",
                "serviceId": 105,
                "referralStatus": "allNoDates",
                "fetchRelatedEntities": ["activityInterest"]
            },
            "stages": [
                {
                    "description": "-not visible-",
                    "stageType": "ANALYSER",
                    "analyserConfig": { "analyserType": "referralCountsByProject" }
                },
                {
                    "description": "referrals by project",
                    "stageType": "CHART",
                    "seriesDefs": [
                        {
                            "label": "count",
                            "valuePath": "count",
                            "renderMode": "PIE"
                        }
                    ]
                },
                {
                    "description": "-not visible-",
                    "stageType": "ANALYSER",
                    "analyserConfig": { "analyserType": "activitiesByActivityInterest" }
                },
                {
                    "description": "breakdown of activity demand across days",
                    "stageType": "TABLE",
                    "tableRepresentation": {
                        "className": "DaysOfWeekAnalysis",
                        "columns": ["activity", "mon", "tue", "wed", "thur", "fri", "sat", "sun"]
                        }
                }
            ]
        }

table of all referrals on the LD Day Centre service
        {
            "description": "",
            "selectionCriteria": {
                "selectionRootEntity": "Referral",
                "selectorType": "byReferralStatus",
                "selectionPropertyPath": "referral.serviceId",
                "serviceId": 105,
                "referralStatus": "allNoDates",
                "fetchRelatedEntities": ["activityInterest", "client"]
            },
            "stages": [
                {
                    "description": "all referrals on service",
                    "stageType": "TABLE",
                    "tableRepresentation": {
                        "className": "ReferralReportItem",
                        "columns": ["rid", "cid", "client", "project", "from", "received", "status now", "worker",
                            "address", "town", "postcode"]
                        }
                }
            ]
        }

support work entries by service, then by project, then by assigned worker, then breakdown with type 'one-to-one'
        {
            "description": "on support work this period",
            "selectionCriteria": {
                "selectionRootEntity": "Referral",
                "selectorType": "byStartOfQuarter",
                "selectionPropertyPath": "supportWork.workDate",
                "relativeStartIndex": 0,
                "relativeEndIndex": 1,
                "fetchRelatedEntities": ["supportWork"]
            },
            "stages": [
                {
                    "description": "-not visible-",
                    "stageType": "ANALYSER",
                    "analyserConfig": { "analyserType": "visitCountsByService" }
                },
                {
                    "description": "by service",
                    "stageType": "CHART",
                    "seriesDefs": [
                        { "label": "count", "valuePath": "totalVisits", "renderMode": "PIE" }
                    ]
                },
                {
                    "description": "-not visible-",
                    "stageType": "ANALYSER",
                    "analyserConfig": { "analyserType": "visitCountsByProject" }
                },
                {
                    "description": "by project",
                    "stageType": "CHART",
                    "seriesDefs": [
                        { "label": "count", "valuePath": "totalVisits", "renderMode": "PIE" }
                    ]
                },
                {
                    "description": "-not visible-",
                    "stageType": "ANALYSER",
                    "analyserConfig": { "analyserType": "visitCountsByWorker" }
                },
                {
                    "description": "by worker",
                    "stageType": "CHART",
                    "seriesDefs": [
                        { "label": "count", "valuePath": "totalVisits", "renderMode": "PIE" }
                    ]
                },
                {
                    "description": "-not visible-",
                    "stageType": "ANALYSER",
                    "analyserConfig": { "analyserType": "visitCountsByClient" }
                },
                {
                    "description": "breakdown of visits",
                    "stageType": "TABLE",
                    "tableRepresentation": {
                        "className": "VisitsAnalysis",
                        "columns": ["client", "time spent", "latest work",
                            {"title": "total visits",       "representation": "visits", "path":["totalVisits"]},
                            {"title":"visits : -",          "representation": "visits", "path":["countByCommentType","-"]},
                            {"title":"visits : one-to-one", "representation": "visits", "path":["countByCommentType","one-to-one"]},
                            "breakdown"
                        ]
                    }
                }
            ]
        }

// 04100000-0000-babe-babe-dadafee1600d
support work by service, then by project, then time by referral breakdown
        {
            "description": "on support work this period",
            "selectionCriteria": {
                "selectionRootEntity": "Referral",
                "selectorType": "byStartOfWeekMonday",
                "selectionPropertyPath": "supportWork.workDate",
                "relativeStartIndex": 0,
                "relativeEndIndex": 1,
                "fetchRelatedEntities": ["supportWork"]
            },
            "stages": [
                {
                    "description": "-not visible-",
                    "stageType": "ANALYSER",
                    "analyserConfig": { "analyserType": "visitCountsByService" }
                },
                {
                    "description": "by service",
                    "stageType": "CHART",
                    "seriesDefs": [
                        { "label": "count", "valuePath": "totalVisits", "renderMode": "PIE" }
                    ]
                },
                {
                    "description": "-not visible-",
                    "stageType": "ANALYSER",
                    "analyserConfig": { "analyserType": "visitCountsByProject" }
                },
                {
                    "description": "by project",
                    "stageType": "CHART",
                    "seriesDefs": [
                        { "label": "count", "valuePath": "totalVisits", "renderMode": "PIE" }
                    ]
                },
                {
                   "description": "-not visible-",
                   "stageType": "ANALYSER",
                   "analyserConfig": { "analyserType": "workFromReferrals" }
                },
                {
                    "description": "-not visible-",
                    "stageType": "ANALYSER",
                    "analyserConfig": { "analyserType": "workByRid" }
                },
                {
                    "description": "-not visible-",
                    "stageType": "ANALYSER",
                    "analyserConfig": { "analyserType": "workGroupToVisitGroup" }
                },
                {
                    "description": "breakdown of visits",
                    "stageType": "TABLE",
                    "tableRepresentation": {
                        "className": "VisitsAnalysis",
                        "columns": [
                            "r-id",
                            "c-id",
                            "client name",
                            "worker",
                            "time spent",
                            "visits",
                            "average time spent"
                        ]
                    }
                }

            ]
        }

support work by service, then by author, then referral breakdown [P.S. not entirely sure this splits the last data correctly]
        {
            "description": "on support work this period showing time spent and number of entries",
            "selectionCriteria": {
                "selectionRootEntity": "Referral",
                "selectorType": "byStartOfQuarter",
                "selectionPropertyPath": "supportWork.workDate",
                "relativeStartIndex": 0,
                "relativeEndIndex": 1,
                "fetchRelatedEntities": ["supportWork"]
            },
            "stages": [
                {
                    "description": "-not visible-",
                    "stageType": "ANALYSER",
                    "analyserConfig": { "analyserType": "visitCountsByService" }
                },
                {
                    "description": "by service",
                    "stageType": "CHART",
                    "seriesDefs": [
                        { "label": "count",    "valuePath": "totalVisits",  "renderMode": "PIE" }
                    ]
                },
                {
                    "description": "-not visible-",
                    "stageType": "ANALYSER",
                    "analyserConfig": { "analyserType": "visitCountsByAuthor" }
                },
                {
                    "description": "inner ring: number of entries, outer ring: 'time (mins)'",
                    "stageType": "CHART",
                    "seriesDefs": [
                        {
                            "label": "mins spent",
                            "valuePath": "totalTimeSpentMins",
                            "renderMode": "PIE"
                        },
                        {
                            "label": "number of entries",
                            "valuePath": "totalVisits",
                            "renderMode": "PIE"
                        }
                    ]
                },
                {
                    "description": "breakdown of referrals with goals outstanding/achieved",
                    "stageType": "TABLE",
                    "tableRepresentation": {
                        "className": "ReferralReportItem",
                        "columns": ["rid", "cid", "client", "project", "from", "received", "status now", "worker",
                            "achieved", "outstanding"]
                        }
                }
            ]
        }

support work by service, then by project, then by type, then time by referral breakdown
    {
        "description": "on support work this period",
        "selectionCriteria": {
            "selectionRootEntity": "Referral",
            "selectorType": "byStartOfWeekMonday",
            "selectionPropertyPath": "supportWork.workDate",
            "relativeStartIndex": 0,
            "relativeEndIndex": 1,
            "fetchRelatedEntities": [
                "supportWork"
            ]
        },
        "stages": [
            {
                "description": "-not visible-",
                "stageType": "ANALYSER",
                "analyserConfig": {
                    "analyserType": "visitCountsByService"
                }
            },
            {
                "description": "by service",
                "stageType": "CHART",
                "seriesDefs": [
                    {
                        "label": "count",
                        "valuePath": "totalVisits",
                        "renderMode": "PIE"
                    }
                ]
            },
            {
                "description": "-not visible-",
                "stageType": "ANALYSER",
                "analyserConfig": {
                    "analyserType": "visitCountsByProject"
                }
            },
            {
                "description": "by project",
                "stageType": "CHART",
                "seriesDefs": [
                    {
                        "label": "count",
                        "valuePath": "totalVisits",
                        "renderMode": "PIE"
                    }
                ]
            },
            {
                "description": "-not visible-",
                "stageType": "ANALYSER",
                "analyserConfig": {
                    "analyserType": "workFromReferrals"
                }
            },
            {
                "description": "-not visible-",
                "stageType": "ANALYSER",
                "analyserConfig": {
                    "analyserType": "workByCommentType"
                }
            },
            {
                "description": "by type",
                "stageType": "CHART",
                "seriesDefs": [
                    {
                        "label": "count",
                        "valuePath": "count",
                        "renderMode": "PIE"
                    }
                ]
            },
            {
                "description": "-not visible-",
                "stageType": "ANALYSER",
                "analyserConfig": {
                    "analyserType": "workByRid"
                }
            },
            {
                "description": "-not visible-",
                "stageType": "ANALYSER",
                "analyserConfig": {
                    "analyserType": "workGroupToVisitGroup"
                }
            },
            {
                "description": "breakdown of visits",
                "stageType": "TABLE",
                "tableRepresentation": {
                    "className": "VisitsAnalysis",
                    "columns": [
                        "r-id",
                        "c-id",
                        "client name",
                        "worker",
                        "time spent",
                        "visits",
                        "average time spent"
                    ]
                }
            }
        ]
    }

support work by service, then by project, then by support work, then referral breakdown
        {
            "description": "on support work this quarter showing time spent and number of entries",
            "selectionCriteria": {
                "selectionRootEntity": "Referral",
                "selectorType": "byStartOfQuarter",
                "selectionPropertyPath": "supportWork.workDate",
                "relativeStartIndex": 0,
                "relativeEndIndex": 1,
                "fetchRelatedEntities": ["supportWork"]
            },
            "stages": [
                {
                    "description": "-not visible-",
                    "stageType": "ANALYSER",
                    "analyserConfig": { "analyserType": "referralCountsByService" }
                },
                {
                    "description": "by service",
                    "stageType": "CHART",
                    "seriesDefs": [
                        { "label": "count",    "valuePath": "count",  "renderMode": "PIE" }
                    ]
                },
                {
                    "description": "-not visible-",
                    "stageType": "ANALYSER",
                    "analyserConfig": { "analyserType": "referralCountsByProject" }
                },
                {
                    "description": "by project",
                    "stageType": "CHART",
                    "seriesDefs": [
                        { "label": "count", "valuePath": "count", "renderMode": "PIE" }
                    ]
                },
                {
                    "description": "-not visible-",
                    "stageType": "ANALYSER",
                    "analyserConfig": { "analyserType": "visitCountsByAuthor" }
                },
                {
                    "description": "inner ring: number of entries, outer ring: 'time (mins)'",
                    "stageType": "CHART",
                    "seriesDefs": [
                        {
                            "label": "mins spent",
                            "valuePath": "totalTimeSpentMins",
                            "renderMode": "PIE"
                        },
                        {
                            "label": "number of entries",
                            "valuePath": "totalVisits",
                            "renderMode": "PIE"
                        }
                    ]
                },
                {
                    "description": "breakdown of referrals with goals outstanding/achieved",
                    "stageType": "TABLE",
                    "tableRepresentation": {
                        "className": "ReferralReportItem",
                        "columns": ["rid", "cid", "client", "project", "from", "received", "status now", "worker",
                            "achieved", "outstanding"]
                        }
                }
            ]
        }

support work entries by service, then breakdown of support work
        {
            "description": "on support work this quarter",
            "selectionCriteria": {
                "selectionRootEntity": "Referral",
                "selectorType": "byStartOfQuarter",
                "selectionPropertyPath": "supportWork.workDate",
                "relativeStartIndex": 0,
                "relativeEndIndex": 1,
                "fetchRelatedEntities": ["supportWork"]
            },
            "stages": [
                {
                    "description": "-not visible-",
                    "stageType": "ANALYSER",
                    "analyserConfig": { "analyserType": "visitCountsByService" }
                },
                {
                    "description": "by service",
                    "stageType": "CHART",
                    "seriesDefs": [
                        { "label": "count", "valuePath": "totalVisits", "renderMode": "PIE" }
                    ]
                },
                {
                    "description": "-not visible-",
                    "stageType": "ANALYSER",
                    "analyserConfig": { "analyserType": "workFromReferrals" }
                },
                {
                    "description": "breakdown of support work",
                    "stageType": "TABLE",
                    "tableRepresentation": {
                        "className": "Work",
                        "columns": ["r-id", "client", "created", "worker", "work date", "type", "time (mins)"]
                        }
                }
            ]
        }

support work entries by service, then by project, then by assigned worker, then breakdown of support work
        {
            "description": "on support work this quarter",
            "selectionCriteria": {
                "selectionRootEntity": "Referral",
                "selectorType": "byStartOfQuarter",
                "selectionPropertyPath": "supportWork.workDate",
                "relativeStartIndex": 0,
                "relativeEndIndex": 1,
                "fetchRelatedEntities": ["supportWork"]
            },
            "stages": [
                {
                    "description": "-not visible-",
                    "stageType": "ANALYSER",
                    "analyserConfig": { "analyserType": "visitCountsByService" }
                },
                {
                    "description": "by service",
                    "stageType": "CHART",
                    "seriesDefs": [
                        { "label": "count", "valuePath": "totalVisits", "renderMode": "PIE" }
                    ]
                },
                {
                    "description": "-not visible-",
                    "stageType": "ANALYSER",
                    "analyserConfig": { "analyserType": "visitCountsByProject" }
                },
                {
                    "description": "by project",
                    "stageType": "CHART",
                    "seriesDefs": [
                        { "label": "count", "valuePath": "totalVisits", "renderMode": "PIE" }
                    ]
                },
                {
                    "description": "-not visible-",
                    "stageType": "ANALYSER",
                    "analyserConfig": { "analyserType": "visitCountsByWorker" }
                },
                {
                    "description": "by worker",
                    "stageType": "CHART",
                    "seriesDefs": [
                        { "label": "count", "valuePath": "totalVisits", "renderMode": "PIE" }
                    ]
                },
                {
                    "description": "-not visible-",
                    "stageType": "ANALYSER",
                    "analyserConfig": { "analyserType": "workFromReferrals" }
                },
                {
                    "description": "breakdown of support work",
                    "stageType": "TABLE",
                    "tableRepresentation": {
                        "className": "Work",
                        "columns": ["r-id", "client", "created", "worker", "work date", "type", "time (mins)"]
                        }
                }
            ]
        }

support work minutes by service, then breakdown of support work
        {
            "description": "on support work this quarter",
            "selectionCriteria": {
                "selectionRootEntity": "Referral",
                "selectorType": "byStartOfQuarter",
                "selectionPropertyPath": "supportWork.workDate",
                "relativeStartIndex": 0,
                "relativeEndIndex": 1,
                "fetchRelatedEntities": ["supportWork"]
            },
            "stages": [
                {
                    "description": "-not visible-",
                    "stageType": "ANALYSER",
                    "analyserConfig": { "analyserType": "visitCountsByService" }
                },
                {
                    "description": "by service",
                    "stageType": "CHART",
                    "seriesDefs": [
                        { "label": "count", "valuePath": "totalTimeSpentMins", "renderMode": "PIE" }
                    ]
                },
                {
                    "description": "-not visible-",
                    "stageType": "ANALYSER",
                    "analyserConfig": { "analyserType": "workFromReferrals" }
                },
                {
                    "description": "breakdown of support work",
                    "stageType": "TABLE",
                    "tableRepresentation": {
                        "className": "Work",
                        "columns": ["r-id", "client", "created", "worker", "work date", "type", "time (mins)"]
                        }
                }
            ]
        }

support work minutes by service, then by project, then by worker, then breakdown of support work
        {
            "description": "on support work this quarter",
            "selectionCriteria": {
                "selectionRootEntity": "Referral",
                "selectorType": "byStartOfQuarter",
                "selectionPropertyPath": "supportWork.workDate",
                "relativeStartIndex": 0,
                "relativeEndIndex": 1,
                "fetchRelatedEntities": ["supportWork"]
            },
            "stages": [
                {
                    "description": "-not visible-",
                    "stageType": "ANALYSER",
                    "analyserConfig": { "analyserType": "visitCountsByService" }
                },
                {
                    "description": "by service",
                    "stageType": "CHART",
                    "seriesDefs": [
                        { "label": "count", "valuePath": "totalTimeSpentMins", "renderMode": "PIE" }
                    ]
                },
                {
                    "description": "-not visible-",
                    "stageType": "ANALYSER",
                    "analyserConfig": { "analyserType": "visitCountsByProject" }
                },
                {
                    "description": "by project",
                    "stageType": "CHART",
                    "seriesDefs": [
                        { "label": "count", "valuePath": "totalTimeSpentMins", "renderMode": "PIE" }
                    ]
                },
                {
                    "description": "by worker",
                    "stageType": "TABLE",
                    "tableRepresentation": {
                        "className": "ReferralReportItem",
                        "columns": ["rid", "cid", "client", "project", "from", "received", "status now", "worker",
                            "achieved", "outstanding"]
                        }
                },
                {
                    "description": "-not visible-",
                    "stageType": "ANALYSER",
                    "analyserConfig": { "analyserType": "workFromReferrals" }
                },
                {
                    "description": "breakdown of support work",
                    "stageType": "TABLE",
                    "tableRepresentation": {
                        "className": "Work",
                        "columns": ["r-id", "client", "created", "worker", "work date", "type", "time (mins)"]
                        }
                }
            ]
        }

support work type by service, then breakdown of support work
        {
            "description": "on support work this quarter",
            "selectionCriteria": {
                "selectionRootEntity": "Referral",
                "selectorType": "byStartOfQuarter",
                "selectionPropertyPath": "supportWork.workDate",
                "relativeStartIndex": 0,
                "relativeEndIndex": 1,
                "fetchRelatedEntities": ["supportWork"]
            },
            "stages": [
                {
                    "description": "-not visible-",
                    "stageType": "ANALYSER",
                    "analyserConfig": { "analyserType": "workFromReferrals_JOIN_workByCommentType" }
                },
                {
                    "description": "by type",
                    "stageType": "CHART",
                    "seriesDefs": [
                        { "label": "count", "valuePath": "count", "renderMode": "PIE" }
                    ]
                },
                {
                    "description": "breakdown of support work",
                    "stageType": "TABLE",
                    "tableRepresentation": {
                        "className": "Work",
                        "columns": ["r-id", "client", "created", "worker", "task", "work date", "type", "time (mins)"]
                        }
                }
            ]
        }

example badge report
        {
            "description": "work in progress",
            "selectionCriteria": {
                "selectionRootEntity": "Referral",
                "selectorType": "byStartOfQuarter",
                "selectionPropertyPath": "supportWork.workDate",
                "relativeStartIndex": 0,
                "relativeEndIndex": 1,
                "fetchRelatedEntities": ["supportWork"]
            },
            "stages": [
                {
                    "description": "-not visible-",
                    "stageType": "ANALYSER",
                    "analyserConfig": { "analyserType": "visitCountsByService" },
                    "selectionAnalyser": "single"
                },
                {
                    "description": "by service",
                    "stageType": "CHART",
                    "selectionAnalyser": "single",
                    "seriesDefs": [
                        { "label": "count", "valuePath": "totalVisits", "renderMode": "PIE" }
                    ]
                },
                {
                    "description": "Time spent with clients in period",
                    "stageType": "BADGE",
                    "badgeRepresentation": {
                        "badgeIconCssClasses": "fa fa-fire",
                        "recordRepresentationClassName": "VisitsAnalysis",
                        "mainIndicatorValue": "time spent"
                    }
                },
                {
                    "description": "average time per visit",
                    "stageType": "BADGE",
                    "badgeRepresentation": {
                        "badgeIconCssClasses": "fa fa-file",
                        "recordRepresentationClassName": "VisitsAnalysis",
                        "mainIndicatorValue": "average time spent"
                    }
                },
                {
                    "description": "latest work",
                    "stageType": "BADGE",
                    "badgeRepresentation": {
                        "badgeIconCssClasses": "fa fa-road",
                        "recordRepresentationClassName": "VisitsAnalysis",
                        "mainIndicatorValue": "latest work"
                    }
                }
            ]
        }

// Reports above are still in order 1-16 - we do need to add an order field in anyway
var all: dto.ChartDefinition[] = [
    chart11, chart2, chart3, chart1, chart4, chart5, chart12a, chart12b, chart6, chart13a, chart13b,
    chart14a, chart14b, chart15a, chart7, chart8, chart9, chart10, badge16
];

badge: number of referrals received/closed per month (with liveAtEnd)
{
    "description": "summary of referrals by service in period",
    "selectionMultiCriteria": [
        {
            "groupBy": "receivedDate",
            "referralStatus": "received",
            "selectionRootEntity": "ReferralsByMonth",
            "selectorType": "byStartOfMonth",
            "relativeStartIndex": -2,
            "relativeEndIndex": 1,
            "fetchRelatedEntities": []
        },
        {
            "selectionCriteriaSource": {
                "analyserType": "self",
                "inheritDatesOfIndex": 0
            },
            "groupBy": "exitedDate",
            "referralStatus": "exited",
            "selectionRootEntity": "ReferralsByMonth",
            "selectorType": "byStartOfMonth",
            "relativeStartIndex": -2,
            "relativeEndIndex": 1,
            "fetchRelatedEntities": []
        },
        {
            "selectionCriteriaSource": {
                "analyserType": "self",
                "inheritDatesOfIndex": 0
            },
            "groupBy": "liveAtEnd",
            "referralStatus": "liveAtEnd",
            "selectionRootEntity": "ReferralsByService",
            "selectorType": "byReferralStatus",
            "relativeStartIndex": -2,
            "relativeEndIndex": 1,
            "fetchRelatedEntities": []
        }
    ],
    "stages": [
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "referrals"
            }
        },
        {
            "description": "count",
            "stageType": "BADGE",
            "badgeRepresentation": {
                "renderType": "mui",
                "recordRepresentationClassName": "ByMonthBadgeAnalysis",
                "mainIndicatorValue": "value",
                "upIndicatorValue": "progressUp",
                "downIndicatorValue": "progressDown"
            }
        }
    ]
}

badge: number of tasks due per month by service
{
    "description": "summary of tasks by service in period",
    "selectionMultiCriteria": [
        {
            "groupBy": "dueByService",
            "selectionRootEntity": "TasksByMonth",
            "selectorType": "byStartOfMonth",
            "relativeStartIndex": -2,
            "relativeEndIndex": 1,
            "fetchRelatedEntities": []
        },
        {
            "selectionCriteriaSource": {
                "analyserType": "self",
                "inheritDatesOfIndex": 0
            },
            "groupBy": "completedWithDueByService",
            "selectionRootEntity": "TasksByMonth",
            "selectorType": "byStartOfMonth",
            "relativeStartIndex": -2,
            "relativeEndIndex": 1,
            "fetchRelatedEntities": []
        }
    ],
    "stages": [
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "tasks"
            }
        },
        {
            "description": "count",
            "stageType": "BADGE",
            "badgeRepresentation": {
                "renderType": "mui",
                "recordRepresentationClassName": "ByMonthBadgeAnalysis",
                "mainIndicatorValue": "value",
                "upIndicatorValue": "progressUp",
                "downIndicatorValue": "progressDown"
            }
        }
    ]
}

badge: number of tasks due per month by users
{
    "description": "summary of tasks by user in period",
    "selectionMultiCriteria": [
        {
            "groupBy": "dueByUsername",
            "selectionRootEntity": "TasksByMonth",
            "selectorType": "byStartOfMonth",
            "relativeStartIndex": -2,
            "relativeEndIndex": 1,
            "fetchRelatedEntities": []
        },
        {
            "selectionCriteriaSource": {
                "analyserType": "self",
                "inheritDatesOfIndex": 0
            },
            "groupBy": "completedWithDueByUsername",
            "selectionRootEntity": "TasksByMonth",
            "selectorType": "byStartOfMonth",
            "relativeStartIndex": -2,
            "relativeEndIndex": 1,
            "fetchRelatedEntities": []
        }
    ],
    "stages": [
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "tasks"
            }
        },
        {
            "description": "count",
            "stageType": "BADGE",
            "badgeRepresentation": {
                "recordRepresentationClassName": "ByMonthBadgeAnalysis",
                "renderType": "mui",
                "mainIndicatorValue": "value",
                "upIndicatorValue": "progressUp",
                "downIndicatorValue": "progressDown"
            }
        }
    ]
}

badge: number of referral workflow SLA tasks due per month by service
{
    "description": "due by month",
    "selectionMultiCriteria": [
        {
            "groupBy": "nextDueSlaDate",
            "referralStatus": "dueSlaTask",
            "selectionRootEntity": "ReferralsByMonth",
            "selectorType": "byReferralStatus",
            "relativeStartIndex": -2,
            "relativeEndIndex": 1,
            "fetchRelatedEntities": []
        }
    ],
    "stages": [
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "flattenToBadge"
            }
        },
        {
            "description": "count",
            "stageType": "BADGE",
            "badgeRepresentation": {
                "badgeIconCssClasses": "fa fa-road",
                "recordRepresentationClassName": "ByMonthBadgeAnalysis",
                "mainIndicatorValue": "value"
            }
        }
    ]
}

referrals by service, then by project, then by 'status now', then breakdown
order = 20
{
    "description": "on referrals this quarter",
    "selectionCriteria": {
        "selectionRootEntity": "Referral",
        "selectorType": "byStartOfQuarter",
        "selectionPropertyPath": "referral.receivedDate",
        "relativeStartIndex": 0,
        "relativeEndIndex": 1,
        "fetchRelatedEntities": []
    },
    "stages": [
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": { "analyserType": "referralCountsByService" }
        },
        {
            "description": "by service",
            "stageType": "CHART",
            "seriesDefs": [
                { "label": "count",    "valuePath": "count",  "renderMode": "PIE" }
            ]
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": { "analyserType": "referralCountsByProject" }
        },
        {
            "description": "by project",
            "stageType": "CHART",
            "seriesDefs": [
                { "label": "count", "valuePath": "count", "renderMode": "PIE" }
            ]
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": { "analyserType": "referralCountsByStatus" }
        },
        {
            "description": "referrals showing 'status now'",
            "stageType": "CHART",
            "seriesDefs": [
                {
                    "label": "status now",
                    "valuePath": "count",
                    "renderMode": "BAR"
                }
            ]
        },
        {
            "description": "breakdown of referrals",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "ReferralReportItem",
                "columns": ["rid", "cid", "client", "project", "from", "received", "status now", "worker"]
                }
        }
    ]
}
referrals by region, then by service, then by project, then by 'status now', then breakdown
order = 20
{
    "description": "on referrals this quarter",
    "selectionCriteria": {
        "selectionRootEntity": "Referral",
        "selectorType": "byStartOfQuarter",
        "selectionPropertyPath": "referral.receivedDate",
        "relativeStartIndex": 0,
        "relativeEndIndex": 1,
        "fetchRelatedEntities": []
    },
    "stages": [
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "referralCountsByRegion"
            }
        },
        {
            "description": "by region",
            "stageType": "CHART",
            "canSkip": "true",
            "seriesDefs": [
                {
                    "label": "count",
                    "valuePath": "count",
                    "renderMode": "PIE"
                }
            ]
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": { "analyserType": "referralCountsByService" }
        },
        {
            "description": "by service",
            "stageType": "CHART",
            "seriesDefs": [
                { "label": "count",    "valuePath": "count",  "renderMode": "PIE" }
            ]
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": { "analyserType": "referralCountsByProject" }
        },
        {
            "description": "by project",
            "stageType": "CHART",
            "seriesDefs": [
                { "label": "count", "valuePath": "count", "renderMode": "PIE" }
            ]
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": { "analyserType": "referralCountsByStatus" }
        },
        {
            "description": "referrals showing 'status now'",
            "stageType": "CHART",
            "seriesDefs": [
                {
                    "label": "status now",
                    "valuePath": "count",
                    "renderMode": "BAR"
                }
            ]
        },
        {
            "description": "breakdown of referrals",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "ReferralReportItem",
                "columns": ["rid", "cid", "client", "project", "from", "received", "status now", "worker"]
                }
        }
    ]
}

referrals (exited) by service, then by project, then by length on service, then breakdown
order = 21
{
    "description": "on referrals exited this quarter",
    "selectionCriteria": {
        "selectionRootEntity": "Referral",
        "selectorType": "byStartOfQuarter",
        "selectionPropertyPath": "referral.exitedDate",
        "referralStatus": "exited",
        "relativeStartIndex": 0,
        "relativeEndIndex": 1,
        "fetchRelatedEntities": []
    },
    "stages": [
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "referralCountsByService"
            }
        },
        {
            "description": "by service",
            "stageType": "CHART",
            "seriesDefs": [
                {
                    "label": "count",
                    "valuePath": "count",
                    "renderMode": "PIE"
                }
            ]
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "referralCountsByProject"
            }
        },
        {
            "description": "by project",
            "stageType": "CHART",
            "seriesDefs": [
                {
                    "label": "count",
                    "valuePath": "count",
                    "renderMode": "PIE"
                }
            ]
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "referralCountsByLengthOnService"
            }
        },
        {
            "description": "referrals by length on service",
            "stageType": "CHART",
            "canSkip": "true",
            "seriesDefs": [
                {
                    "label": "length on service",
                    "valuePath": "count",
                    "renderMode": "BAR"
                }
            ]
        },
        {
            "description": "breakdown of referrals",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "ReferralReportItem",
                "columns": [
                    "rid",
                    "cid",
                    "client",
                    "service",
                    "project",
                    "from",
                    "start",
                    "exited",
                    "length (days)",
                    "status now",
                    "worker"
                ]
            }
        }
    ]
}


referrals (live) breakdown for mail merge
NB when 'live' used with byReferralStatus - this ignores dates and is a snapshot of current/now live clients
   using 'liveAtEnd' is no different when no dates are used, but more understood when dates are used (avoids newReferralOnly complication)
   and is also consistent with other reporting newer statuses
order = 22
{
    "description": "on all referrals, export as CSV for mail merge",
    "selectionCriteria": {
        "selectionRootEntity": "Referral",
        "selectorType": "byReferralStatus",
        "selectionPropertyPath": "",
        "referralStatus": "liveAtEnd",
        "fetchRelatedEntities": ["client"]
    },
    "stages": [
        {
            "description": "breakdown of referrals",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "ReferralReportItem",
                "columns": ["service", "project", "rid", "cid", "title", "first name", "last name", "address", "town", "postcode"]
                }
        }
    ]
}

referrals by service, then by project, then by demographic - ethnicity, then breakdown
order = 24
{
    "description": "on referrals this quarter",
    "selectionCriteria": {
        "selectionRootEntity": "Referral",
        "selectorType": "byStartOfQuarter",
        "selectionPropertyPath": "referral.receivedDate",
        "relativeStartIndex": 0,
        "relativeEndIndex": 1,
        "fetchRelatedEntities": ["client"]
    },
    "stages": [
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": { "analyserType": "referralCountsByService" }
        },
        {
            "description": "by service",
            "stageType": "CHART",
            "seriesDefs": [
                { "label": "count", "valuePath": "count", "renderMode": "PIE" }
            ]
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": { "analyserType": "referralCountsByProject" }
        },
        {
            "description": "by project",
            "stageType": "CHART",
            "seriesDefs": [
                { "label": "count", "valuePath": "count", "renderMode": "PIE" }
            ]
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": { "analyserType": "referralCountsByEthnicity" }
        },
        {
            "description": "referrals by ethnicity",
            "stageType": "CHART",
            "seriesDefs": [
                {
                    "label": "count",
                    "valuePath": "count",
                    "renderMode": "PIE"
                }
            ]
        },
        {
            "description": "breakdown of referrals",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "ReferralReportItem",
                "columns": ["service", "project", "rid", "cid", "client", "phone", "mobile", "email", "from", "received", "status now", "worker"]
                }
        }
    ]
}

referrals by service, then by project, then by demographic - gender, then breakdown
order = 25
{
    "description": "on referrals this quarter",
    "selectionCriteria": {
        "selectionRootEntity": "Referral",
        "selectorType": "byStartOfQuarter",
        "selectionPropertyPath": "referral.receivedDate",
        "relativeStartIndex": 0,
        "relativeEndIndex": 1,
        "fetchRelatedEntities": ["client"]
    },
    "stages": [
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": { "analyserType": "referralCountsByService" }
        },
        {
            "description": "by service",
            "stageType": "CHART",
            "seriesDefs": [
                { "label": "count", "valuePath": "count", "renderMode": "PIE" }
            ]
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": { "analyserType": "referralCountsByProject" }
        },
        {
            "description": "by project",
            "stageType": "CHART",
            "seriesDefs": [
                { "label": "count", "valuePath": "count", "renderMode": "PIE" }
            ]
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": { "analyserType": "referralCountsByGender" }
        },
        {
            "description": "referrals by gender",
            "stageType": "CHART",
            "seriesDefs": [
                {
                    "label": "count",
                    "valuePath": "count",
                    "renderMode": "PIE"
                }
            ]
        },
        {
            "description": "breakdown of referrals",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "ReferralReportItem",
                "columns": ["service", "project", "rid", "cid", "client", "phone", "mobile", "email", "from", "received", "status now", "worker"]
                }
        }
    ]
}

referrals by service, then by project, then by demographic - religion, then breakdown
order = 26
{
    "description": "on referrals this quarter",
    "selectionCriteria": {
        "selectionRootEntity": "Referral",
        "selectorType": "byStartOfQuarter",
        "selectionPropertyPath": "referral.receivedDate",
        "relativeStartIndex": 0,
        "relativeEndIndex": 1,
        "fetchRelatedEntities": ["client"]
    },
    "stages": [
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": { "analyserType": "referralCountsByService" }
        },
        {
            "description": "by service",
            "stageType": "CHART",
            "seriesDefs": [
                { "label": "count", "valuePath": "count", "renderMode": "PIE" }
            ]
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": { "analyserType": "referralCountsByProject" }
        },
        {
            "description": "by project",
            "stageType": "CHART",
            "seriesDefs": [
                { "label": "count", "valuePath": "count", "renderMode": "PIE" }
            ]
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": { "analyserType": "referralCountsByReligion" }
        },
        {
            "description": "referrals by religions",
            "stageType": "CHART",
            "seriesDefs": [
                {
                    "label": "count",
                    "valuePath": "count",
                    "renderMode": "PIE"
                }
            ]
        },
        {
            "description": "breakdown of referrals",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "ReferralReportItem",
                "columns": ["service", "project", "rid", "cid", "client", "phone", "mobile", "email", "from", "received", "status now", "worker"]
                }
        }
    ]
}

referrals by service, then by project, then by demographic - sexual orientation, then breakdown
order = 27
{
    "description": "on referrals this quarter",
    "selectionCriteria": {
        "selectionRootEntity": "Referral",
        "selectorType": "byStartOfQuarter",
        "selectionPropertyPath": "referral.receivedDate",
        "relativeStartIndex": 0,
        "relativeEndIndex": 1,
        "fetchRelatedEntities": ["client"]
    },
    "stages": [
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": { "analyserType": "referralCountsByService" }
        },
        {
            "description": "by service",
            "stageType": "CHART",
            "seriesDefs": [
                { "label": "count", "valuePath": "count", "renderMode": "PIE" }
            ]
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": { "analyserType": "referralCountsByProject" }
        },
        {
            "description": "by project",
            "stageType": "CHART",
            "seriesDefs": [
                { "label": "count", "valuePath": "count", "renderMode": "PIE" }
            ]
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": { "analyserType": "referralCountsBySexualOrientation" }
        },
        {
            "description": "referrals by sexual orientation",
            "stageType": "CHART",
            "seriesDefs": [
                {
                    "label": "count",
                    "valuePath": "count",
                    "renderMode": "PIE"
                }
            ]
        },
        {
            "description": "breakdown of referrals",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "ReferralReportItem",
                "columns": ["service", "project", "rid", "cid", "client", "phone", "mobile", "email", "from", "received", "status now", "worker"]
                }
        }
    ]
}

referrals by service, then by project, then by demographic - first language, then breakdown
order = 28
{
    "description": "on referrals this quarter",
    "selectionCriteria": {
        "selectionRootEntity": "Referral",
        "selectorType": "byStartOfQuarter",
        "selectionPropertyPath": "referral.receivedDate",
        "relativeStartIndex": 0,
        "relativeEndIndex": 1,
        "fetchRelatedEntities": ["client"]
    },
    "stages": [
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": { "analyserType": "referralCountsByService" }
        },
        {
            "description": "by service",
            "stageType": "CHART",
            "seriesDefs": [
                { "label": "count", "valuePath": "count", "renderMode": "PIE" }
            ]
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": { "analyserType": "referralCountsByProject" }
        },
        {
            "description": "by project",
            "stageType": "CHART",
            "seriesDefs": [
                { "label": "count", "valuePath": "count", "renderMode": "PIE" }
            ]
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": { "analyserType": "referralCountsByFirstLanguage" }
        },
        {
            "description": "referrals by first language",
            "stageType": "CHART",
            "seriesDefs": [
                {
                    "label": "count",
                    "valuePath": "count",
                    "renderMode": "PIE"
                }
            ]
        },
        {
            "description": "breakdown of referrals",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "ReferralReportItem",
                "columns": ["service", "project", "rid", "cid", "client", "phone", "mobile", "email", "from", "received", "status now", "worker"]
                }
        }
    ]
}


referrals by service, then by project, then by demographic - disability, then breakdown
order = 28
{
    "description": "on referrals this quarter",
    "selectionCriteria": {
        "selectionRootEntity": "Referral",
        "selectorType": "byStartOfQuarter",
        "selectionPropertyPath": "referral.receivedDate",
        "relativeStartIndex": 0,
        "relativeEndIndex": 1,
        "fetchRelatedEntities": ["client"]
    },
    "stages": [
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": { "analyserType": "referralCountsByService" }
        },
        {
            "description": "by service",
            "stageType": "CHART",
            "seriesDefs": [
                { "label": "count", "valuePath": "count", "renderMode": "PIE" }
            ]
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": { "analyserType": "referralCountsByProject" }
        },
        {
            "description": "by project",
            "stageType": "CHART",
            "seriesDefs": [
                { "label": "count", "valuePath": "count", "renderMode": "PIE" }
            ]
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": { "analyserType": "referralCountsByDisability" }
        },
        {
            "description": "referrals by first language",
            "stageType": "CHART",
            "seriesDefs": [
                {
                    "label": "count",
                    "valuePath": "count",
                    "renderMode": "PIE"
                }
            ]
        },
        {
            "description": "breakdown of referrals",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "ReferralReportItem",
                "columns": ["service", "project", "rid", "cid", "client", "phone", "mobile", "email", "from", "received", "status now", "worker"]
                }
        }
    ]
}

referrals (live at end of period) by service, then by project, then all demographic breakdown
03500000-0000-babe-babe-dadafee1600d
order = 35
{
    "description": "on referrals this quarter",
    "selectionCriteria": {
        "selectionRootEntity": "Referral",
        "selectorType": "byStartOfQuarter",
        "selectionPropertyPath": "referral.receivedDate",
        "referralStatus": "liveAtEnd",
        "relativeStartIndex": 0,
        "relativeEndIndex": 1,
        "fetchRelatedEntities": ["client"]
    },
    "stages": [
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": { "analyserType": "referralCountsByService" }
        },
        {
            "description": "by service",
            "stageType": "CHART",
            "seriesDefs": [
                { "label": "count", "valuePath": "count", "renderMode": "PIE" }
            ]
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": { "analyserType": "referralCountsByProject" }
        },
        {
            "description": "by project",
            "stageType": "CHART",
            "seriesDefs": [
                { "label": "count", "valuePath": "count", "renderMode": "PIE" }
            ]
        },
        {
            "description": "breakdown of referrals",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "ReferralReportItem",
                "columns": ["service", "project", "rid", "cid", "client", "gender", "ethnicity", "birthdate", "age at ref.", "religion", "disability", "first lang.", "sex. orient.", "phone", "mobile", "email", "from", "received", "status now", "worker"]
                }
        }
    ]
}

referrals (exited) by service, then by project, then by exit reason, then breakdown
order = 29
{
    "description": "on referrals exited this quarter",
    "selectionCriteria": {
        "selectionRootEntity": "Referral",
        "selectorType": "byStartOfQuarter",
        "selectionPropertyPath": "referral.exitedDate",
        "referralStatus": "exited",
        "relativeStartIndex": 0,
        "relativeEndIndex": 1,
        "fetchRelatedEntities": []
    },
    "stages": [
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": { "analyserType": "referralCountsByService" }
        },
        {
            "description": "by service",
            "stageType": "CHART",
            "seriesDefs": [
                { "label": "count", "valuePath": "count", "renderMode": "PIE" }
            ]
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": { "analyserType": "referralCountsByProject" }
        },
        {
            "description": "by project",
            "stageType": "CHART",
            "seriesDefs": [
                { "label": "count", "valuePath": "count", "renderMode": "PIE" }
            ]
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": { "analyserType": "referralCountsByExitReason" }
        },
        {
            "description": "referrals showing closed reasons",
            "stageType": "CHART",
            "seriesDefs": [
                {
                    "label": "reason",
                    "valuePath": "count",
                    "renderMode": "BAR"
                }
            ]
        },
        {
            "description": "breakdown of referrals",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "ReferralReportItem",
                "columns": ["rid", "cid", "client", "project", "from", "received", "status now", "worker"]
                }
        }
    ]
}

referrals (signposted) by service, then by project, then by signposted reason, then breakdown
order = 30
{
    "description": "on referrals exited this quarter",
    "selectionCriteria": {
        "selectionRootEntity": "Referral",
        "selectorType": "byStartOfQuarter",
        "selectionPropertyPath": "referral.decision",
        "referralStatus": "signposted",
        "relativeStartIndex": 0,
        "relativeEndIndex": 1,
        "fetchRelatedEntities": []
    },
    "stages": [
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": { "analyserType": "referralCountsByService" }
        },
        {
            "description": "by service",
            "stageType": "CHART",
            "seriesDefs": [
                { "label": "count", "valuePath": "count", "renderMode": "PIE" }
            ]
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": { "analyserType": "referralCountsByProject" }
        },
        {
            "description": "by project",
            "stageType": "CHART",
            "seriesDefs": [
                { "label": "count", "valuePath": "count", "renderMode": "PIE" }
            ]
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": { "analyserType": "referralCountsBySignpostReason" }
        },
        {
            "description": "referrals showing signposted reasons",
            "stageType": "CHART",
            "seriesDefs": [
                {
                    "label": "reason",
                    "valuePath": "count",
                    "renderMode": "BAR"
                }
            ]
        },
        {
            "description": "breakdown of referrals",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "ReferralReportItem",
                "columns": ["rid", "cid", "client", "project", "from", "received", "status now", "worker"]
                }
        }
    ]
}


referrals (exited) by service, then by project, then by support work, then breakdown
order = 31
{
    "description": "on referrals exited this quarter",
    "selectionCriteria": {
        "selectionRootEntity": "Referral",
        "selectorType": "byStartOfQuarter",
        "selectionPropertyPath": "referral.exitedDate",
        "referralStatus": "exited",
        "relativeStartIndex": 0,
        "relativeEndIndex": 1,
        "fetchRelatedEntities": ["supportWork"]
    },
    "stages": [
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": { "analyserType": "referralCountsByService" }
        },
        {
            "description": "by service",
            "stageType": "CHART",
            "seriesDefs": [
                { "label": "count", "valuePath": "count", "renderMode": "PIE" }
            ]
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": { "analyserType": "actionDefFromReferrals" }
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": { "analyserType": "actionDefCountsAnalyser" }
        },
        {
            "description": "breakdown of work by smart steps",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "ActionDefCount",
                "columns": ["key", "outcome", "smart step", "achieved", "outstanding", "success %"]
            }
        },
        {
            "description": "breakdown of work by smart steps",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "ActionDef",
                "columns": ["a-id", "a-name", "a-status", "r-id", "client", "work date", "time (mins)", "type"]
                }
        }
    ]
}

as "support work by service, then by project, then by support work, then smart step breakdown"
   34
but with outcome pie chart - counting entries inside, but not necc achieved
{
    "description": "on support work this period showing number of entries",
    "selectionCriteria": {
        "selectionRootEntity": "Referral",
        "selectorType": "byStartOfQuarter",
        "selectionPropertyPath": "supportWork.workDate",
        "relativeStartIndex": 0,
        "relativeEndIndex": 1,
                "fetchRelatedEntities": [
            "supportWork"
        ]
    },
    "stages": [
                {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "actionDefFromReferrals"
            }
        },
                {
                    "description": "-not visible-",
                    "stageType": "ANALYSER",
                    "analyserConfig": {
                        "analyserType": "outcomeDefCountsAnalyser"
                    }
                },
                        {
                            "description": "by outcome achieved",
                            "stageType": "CHART",
                            "canSkip": "true",
                            "seriesDefs": [
                                {
                                    "label": "achieved",
                                    "valuePath": "totalAchievedSmartSteps",
                                    "renderMode": "PIE"                                }
                            ]
                        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "actionDefCountsAnalyser"
            }
        },
                {
            "description": "summary of work by smart steps",
            "stageType": "TABLE",
            "canSkip": "true",
            "tableRepresentation": {
                "className": "ActionDefCount",
                "columns": [
                    "a-id",
                    "outcome",
                    "smart step",
                    "achieved",
                    "outstanding",
                    "success %"
                ]
            }
        },
        {
            "description": "breakdown of work by smart steps",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "ActionDef",
                "columns": [
                    "a-id",
                    "a-name",
                    "a-status",
                    "r-id",
                    "c-id",
                    "client",
                    "status now",
                    "work date",
                    "time (mins)",
                    "type"
                ]
            }
        }
    ]
}

support work by service, then by project, then by support work, then smart step breakdown
34
{
    "description": "on support work this period showing number of entries",
    "selectionCriteria": {
        "selectionRootEntity": "Referral",
        "selectorType": "byStartOfQuarter",
        "selectionPropertyPath": "supportWork.workDate",
        "relativeStartIndex": 0,
        "relativeEndIndex": 1,
        "fetchRelatedEntities": ["supportWork"]
    },
    "stages": [
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": { "analyserType": "referralCountsByService" }
        },
        {
            "description": "by service",
            "stageType": "CHART",
            "seriesDefs": [
                { "label": "count",    "valuePath": "count",  "renderMode": "PIE" }
            ]
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": { "analyserType": "referralCountsByProject" }
        },
        {
            "description": "by project",
            "stageType": "CHART",
            "seriesDefs": [
                { "label": "count", "valuePath": "count", "renderMode": "PIE" }
            ]
        },
{
    "description": "-not visible-",
    "stageType": "ANALYSER",
    "analyserConfig": { "analyserType": "actionDefFromReferrals" }
},
{
    "description": "-not visible-",
    "stageType": "ANALYSER",
    "analyserConfig": { "analyserType": "actionDefCountsAnalyser" }
},
{
    "description": "summary of work by smart steps",
    "stageType": "TABLE",
    "tableRepresentation": {
        "className": "ActionDefCount",
        "columns": ["a-id", "smart step", "achieved", "outstanding", "success %"]
        }
},
{
    "description": "breakdown of work by smart steps",
    "stageType": "TABLE",
    "tableRepresentation": {
        "className": "ActionDef",
        "columns": ["a-id", "a-name", "a-status", "r-id", "c-id", "client", "status now", "work date", "time (mins)", "type"]
        }
}
    ]
}

referrals overdue (no service decision) by project, then by worker, then breakdown
order = 36

// for this report to use dates we need the report analysis to be passed the current end date
// or use server side to get the exact data

{
    "description": "on all referrals not decided in 2 weeks",
    "selectionCriteria": {
        "selectionRootEntity": "Referral",
        "selectorType": "byReferralStatus",
        "selectionPropertyPath": "",
        "referralStatus": "incompleteAtEnd",
        "fetchRelatedEntities": []
    },
    "stages": [
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": { "analyserType": "referralsNotAcceptedWithin2Weeks" }
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": { "analyserType": "referralCountsByProject" }
        },
        {
            "description": "by project",
            "stageType": "CHART",
            "seriesDefs": [
                { "label": "count", "valuePath": "count", "renderMode": "PIE" }
            ]
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": { "analyserType": "referralCountsByWorker" }
        },
        {
            "description": "by worker",
            "stageType": "CHART",
            "seriesDefs": [
                { "label": "count", "valuePath": "count", "renderMode": "PIE" }
            ]
        },
        {
            "description": "breakdown of referrals",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "ReferralReportItem",
                "columns": ["rid", "cid", "client", "service", "project", "from", "received", "decided", "status now", "worker"]
                }
        }
    ]
}


referrals with overdue needs (no work) by project, then by worker, then breakdown
order = 37

// this report loads a lot of data (referrals + support) for live clients
// when in reality we want those referrals without support entries

// for this report to use dates we need the report analysis to be passed the current end date
// or use server side to get the exact data

// could have done referralByVisitCounts and used selectionKey to find 0's and then filtered out the recent referrals
// but simpler to use the visitCountsByProject structure, and count 0 from there
//    "selectionAnalyser": "referrals",
//    "selectionKey": "project 4",

{
    "description": "on all referrals without support work within 2 weeks",
    "selectionCriteria": {
        "selectionRootEntity": "Referral",
        "selectorType": "byReferralStatus",
        "selectionPropertyPath": "",
        "referralStatus": "liveAtEnd",
        "fetchRelatedEntities": [
            "supportWork"
        ]
    },
    "stages": [
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "referralsWithoutWorkNotAcceptedWithin2Weeks"
            }
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": { "analyserType": "referralCountsByProject" }
        },
        {
            "description": "by project",
            "stageType": "CHART",
            "seriesDefs": [
                { "label": "count", "valuePath": "count", "renderMode": "PIE" }
            ]
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": { "analyserType": "referralCountsByWorker" }
        },
        {
            "description": "by worker",
            "stageType": "CHART",
            "seriesDefs": [
                { "label": "count", "valuePath": "count", "renderMode": "PIE" }
            ]
        },
        {
            "description": "breakdown of referrals",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "ReferralReportItem",
                "columns": [
                    "rid",
                    "cid",
                    "client",
                    "service",
                    "project",
                    "from",
                    "received",
                    "decided",
                    "status now",
                    "worker"
                ]
            }
        }
    ]
}


referrals with overdue risk management (no work) by project, then by worker, then breakdown
order = 38

// copy of report 37

{
    "description": "on all referrals without risk management work within 2 weeks",
    "selectionCriteria": {
        "selectionRootEntity": "Referral",
        "selectorType": "byReferralStatus",
        "selectionPropertyPath": "",
        "referralStatus": "liveAtEnd",
        "fetchRelatedEntities": [
            "riskWork"
        ]
    },
    "stages": [
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "referralsWithoutRiskWorkNotAcceptedWithin2Weeks"
            }
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": { "analyserType": "referralCountsByProject" }
        },
        {
            "description": "by project",
            "stageType": "CHART",
            "seriesDefs": [
                { "label": "count", "valuePath": "count", "renderMode": "PIE" }
            ]
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": { "analyserType": "referralCountsByWorker" }
        },
        {
            "description": "by worker",
            "stageType": "CHART",
            "seriesDefs": [
                { "label": "count", "valuePath": "count", "renderMode": "PIE" }
            ]
        },
        {
            "description": "breakdown of referrals",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "ReferralReportItem",
                "columns": [
                    "rid",
                    "cid",
                    "client",
                    "service",
                    "project",
                    "from",
                    "received",
                    "decided",
                    "status now",
                    "worker"
                ]
            }
        }
    ]
}


referrals with unsigned data protection (where configured) by project, then by worker, then breakdown
order = 39
{
    "description": "on all referrals ongoing (not signposted/exited)",
    "selectionCriteria": {
        "selectionRootEntity": "Referral",
        "selectorType": "byReferralStatus",
        "selectionPropertyPath": "",
        "referralStatus": "ongoing",
        "fetchRelatedEntities": []
    },
    "stages": [
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "referralsNotSignedDataProtection"
            }
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": { "analyserType": "referralCountsByProject" }
        },
        {
            "description": "by project",
            "stageType": "CHART",
            "seriesDefs": [
                { "label": "count", "valuePath": "count", "renderMode": "PIE" }
            ]
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": { "analyserType": "referralCountsByWorker" }
        },
        {
            "description": "by worker",
            "stageType": "CHART",
            "seriesDefs": [
                { "label": "count", "valuePath": "count", "renderMode": "PIE" }
            ]
        },
        {
            "description": "breakdown of referrals",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "ReferralReportItem",
                "columns": [
                    "rid",
                    "cid",
                    "client",
                    "service",
                    "project",
                    "from",
                    "received",
                    "decided",
                    "status now",
                    "worker"
                ]
            }
        }
    ]
}

referrals (live) with work count (support and risk) by service, then by project, then by worker, then by support/risk, then by breakdown
04000000-0000-babe-babe-dadafee1600d
order = 40
{
    "description": "on all referrals live",
    "selectionCriteria": {
        "selectionRootEntity": "Referral",
        "selectorType": "byReferralStatus",
        "selectionPropertyPath": "",
        "referralStatus": "liveAtEnd",
        "fetchRelatedEntities": ["supportWork", "riskWork"]
    },
    "stages": [
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": { "analyserType": "visitAllCountsByService" }
        },
        {
            "description": "by service",
            "stageType": "CHART",
            "seriesDefs": [
                { "label": "count", "valuePath": "totalVisits", "renderMode": "PIE" }
            ]
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": { "analyserType": "visitAllCountsByProject" }
        },
        {
            "description": "by project",
            "stageType": "CHART",
            "seriesDefs": [
                { "label": "count", "valuePath": "totalVisits", "renderMode": "PIE" }
            ]
        },

// visitAllCountsByAuthorAnalyser
// groupByWorker takes ReferralAggregate into Group<ReferralAggregate> (key=assigned worker, elements=all ReferralAggregate under the worker)
// visitAll returns a sequences of ReferralAggregateGroupWithVisits
//      where ReferralAggregateGroupWithVisits extends Group<ReferralAggregate>
//        latestWorkDate
//        ...
//        key (assigned worker)
//        elements (ReferralAggregate)
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": { "analyserType": "visitAllCountsByAuthor" }
        },

// visitAllCountsByAuthorAnalyser takes the result into an analysis GroupedReferralAggregateAnalysisWithVisits
        {
            "description": "inner ring: number of entries, outer ring: 'time (mins)'",
            "stageType": "CHART",
            "canSkip": "true",
            "seriesDefs": [
                {
                    "label": "mins spent",
                    "valuePath": "totalTimeSpentMins",
                    "renderMode": "PIE"
                },
                {
                    "label": "number of entries",
                    "valuePath": "totalVisits",
                    "renderMode": "PIE"
                }
            ]
        },
// which has an onClickAnalyser of ungroup calling WrapReferralSequenceAnalyser
// which extracts the elements (ReferralAggregate) for the key (author) into a new ReferralAggregateAnalysis

// workFromReferralsAnalyser creates each work item into WorkWithRefToReferralAggregate (extends baseWork, adds ref to ReferralAggregate)
// into WorkWithParentRefAnalysis
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": { "analyserType": "workAllFromReferrals" }
        },

// workBySourceTaskAnalyser is a derivativeAnalyser of WorkWithParentRefAnalysis
// groupBySourceTask takes WorkWithRefToReferralAggregate into WorkWithRefGroupWithCount (key=task, elements=WorkWithRefToReferralAggregate)
// workCountsBy returns a sequence of WorkWithRefGroupWithCount
//      where WorkWithRefGroupWithCount extends Group<WorkWithRefToReferralAggregate>
//        key (source task)
//        elements (WorkWithRefToReferralAggregate)
//        count
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": { "analyserType": "workBySourceTask" }
        },

// workBySourceTaskAnalyser takes the result into an analysis GroupedWorkWithParentRefAnalysis
        {
            "description": "by support/work",
            "stageType": "CHART",
            "seriesDefs": [
                { "label": "count", "valuePath": "count", "renderMode": "PIE" }
            ]
        },
// which has an onClickAnalyser of ungroup calling wrapWorkWithParentRefSequenceAnalyser
// which extracts the elements (WorkWithRefToReferralAggregate) for the key (task) into a new WorkWithParentRefAnalysis

// **
// we now want to do 'visit' calculations grouped by the rid (not the grouped task)
// which we could do through a one-off analyser, but thats not very component/reusable
// so after the above ungrouped WorkWithRefToReferralAggregate we want to regroup by rid
// so that we can transform the resulting WorkWithRefGroupWithCount into ReferralAggregateGroupWithVisits
// visits are calculations around groups, so we need to transfer at the group level
// **

// workByRidAnalyser is a derivativeAnalyser of WorkWithParentRefAnalysis
// groupByRid takes WorkWithRefToReferralAggregate into Group<WorkWithRefToReferralAggregate> (key=rid, elements=WorkWithRefToReferralAggregate)
// workCountsBy returns a sequence of WorkWithRefGroupWithCount
//      where WorkWithRefGroupWithCount extends Group<WorkWithRefToReferralAggregate>
//        key (rid)
//        elements (WorkWithRefToReferralAggregate)
//        count
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": { "analyserType": "workByRid" }
        },

// workGroupToVisitGroupAnalyser takes WorkWithRefGroupWithCount into ReferralAggregateGroupWithVisits
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": { "analyserType": "workGroupToVisitGroup" }
        },

// workGroupToVisitGroupAnalyser takes the result into an analysis GroupedReferralAggregateAnalysisWithVisits
        {
            "description": "breakdown of visits",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "VisitsAnalysis",
                "columns": [
                    "client",
                    "time spent",
                    "visits",
                    "average time spent",
                    "last signed work",
                    "last unsigned work"
                ]
            }
        }
    ]
}
-- summary table r-id with totals for support / risk
{
    "description": "on support and risk work this period",
    "selectionCriteria": {
        "selectionRootEntity": "SupportRiskWork",
        "selectorType": "byStartOfWeekMonday",
        "relativeStartIndex": 0,
        "relativeEndIndex": 1,
        "fetchRelatedEntities": [
            "referral"
        ]
    },
    "stages": [
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "visitCountsBySrIdAndWorkType"
            }
        },
        {
            "description": "breakdown of visits",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "VisitsByType",
                "columns": [
                    "r: name",
                    "total: time spent",
                    "support: time spent",
                    "risk: time spent"
                ]
            }
        },
        {
            "description": "breakdown of visits",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "WorkWithReferral",
                "columns": [
                    "w-id",
                    "sr-id",
                    "r: c-id",
                    "r: r-id",
                    "r: name",
                    "created",
                    "author",
                    "work date",
                    "type",
                    "time (mins)",
                    "task",
                    "signed"
                ]
            }
        }
    ]
}


CUSTOM

CDO days attending vs actual for council submission

{
    "description": "",
    "selectionCriteria": {
        "selectionRootEntity": "Referral",
        "selectionPropertyPath": "groupActivityDate",
        "selectorType": "byStartOfWeekMonday",
        "relativeStartIndex": 0,
        "relativeEndIndex": 1,
        "serviceId": 105,
        "referralStatus": "allNoDates",
        "fetchRelatedEntities": [
            "activityInvolvement",
            "client"
        ]
    },
    "stages": [
        {
            "description": "all referrals on service",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "ReferralReportItem",
                "columns": [
                    {
                        "title": "SwiftID",
                        "representation": "external ref",
                        "path": null
                    },
                    {
                        "title": "Provision ID",
                        "representation": "rid",
                        "path": [
                            "referral",
                            "textMap",
                            "provisionId"
                        ]
                    },
                    {
                        "title": "service",
                        "comment": "TODO: this will become service",
                        "representation": "project"
                    },
                    {
                        "title": "funding type",
                        "representation": "rid",
                        "path": [
                            "referral",
                            "choicesMap",
                            "funding-type",
                            "name"
                        ]
                    },
                    "last name",
                    "first name",
                    "full address",
                    {
                        "title": "mon",
                        "representation": "attended mon count"
                    },
                    {
                        "title": "tue",
                        "representation": "attended tue count"
                    },
                    {
                        "title": "wed",
                        "representation": "attended wed count"
                    },
                    {
                        "title": "thu",
                        "representation": "attended thu count"
                    },
                    {
                        "title": "fri",
                        "representation": "attended fri count"
                    },
                    {
                        "title": "invited",
                        "representation": "invited count"
                    },
                    {
                        "title": "planned attendance",
                        "representation": "attending count"
                    },
                    {
                        "title": "actual attendance",
                        "representation": "attended count"
                    }
                ]
            }
        }
    ]
}


All service recipients with appointments for building 1002

        {
            "description": "",
            "selectionCriteria": {
                "selectionRootEntity": "ServiceRecipient",
                "selectorType": "byStartOfWeekMonday",
                "serviceRecipientFilter": "buildings:1002",
                "relativeStartIndex": 0,
                "relativeEndIndex": 1,
                "fetchRelatedEntities": ["appointments"]
            },
            "stages": [
                {
                    "description": "-not visible-",
                    "stageType": "ANALYSER",
                    "analyserConfig": { "analyserType": "attachAppointmentsToAgreement" }
                },
                {
                    "description": "all service recipients",
                    "stageType": "TABLE",
                    "tableRepresentation": {
                        "className": "ServiceRecipientItem",
                        "columns": ["id", "name", "agreements count", "appts count",
                                            "appts (confirmed)", "appts (tentative)", "appts (dropped)"]
                        }
                },
                {
                    "description": "-not visible-",
                    "stageType": "ANALYSER",
                    "analyserConfig": { "analyserType": "extractAgreements" }
                },
                {
                    "description": "all service recipients",
                    "stageType": "TABLE",
                    "tableRepresentation": {
                        "className": "AgreementItem",
                        "columns": ["id", "start date", "end date", "appts count",
                                            "appts (confirmed)", "appts (tentative)", "appts (dropped)"]
                        }
                }
            ]
        }

Agreement agreed hours vs actual

{
    "description": "",
    "selectionCriteria": {
        "selectionRootEntity": "ServiceRecipient",
        "selectorType": "byStartOfWeekMonday",
        "relativeStartIndex": 0,
        "relativeEndIndex": 1,
        "serviceRecipientFilter": "buildings:1002",
        "fetchRelatedEntities": [
            "appointments"
        ]
    },
    "stages": [
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "attachAppointmentsToAgreement"
            }
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "extractAgreements"
            }
        },
        {
            "description": "all agreements",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "AgreementItem",
                "columns": [
                    "name",
                    "agreement id",
                    "start date",
                    "end date",
                    "agreed hours per week",
                    "appt hours in period (confirmed)",
                    "appts count",
                    "appts (confirmed)",
                    "appts (tentative)",
                    "appts (dropped)"
                ]
            }
        }
    ]
}

// configure a questionnaire
// with a question of 'support level' with choices (eg low/med/high)
// specify the choice 'values' in the params as the hours

// add 'supportHoursQn' to service type referralView

{
    "description": "on support work this quarter",
    "selectionCriteria": {
        "selectionRootEntity": "Referral",
        "selectorType": "byStartOfWeekMonday",
        "selectionPropertyPath": "supportWork.workDate",
        // "relativeStartDate": "2016-01-04",
        "relativeStartIndex": 0,
        "relativeEndIndex": 1,
        "fetchRelatedEntities": [
            "supportWork",
            "client",
            "singleValueQnrHistory"
        ]
    },
    "stages": [
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "workFromReferrals"
            }
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "workSummaryBySVHId"
            }
        },
        // NB KEY can be ignored, since it's always the same 'hours'
        // but when svh could be counting many things, the key made sense
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "svhCountsByKey"
            }
        },
        {
            "description": "by keys",
            "stageType": "CHART",
            "seriesDefs": [
                {
                    "label": "count",
                    "valuePath": "count",
                    "renderMode": "PIE"
                }
            ]
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "svhQuantisedWeekPivot"
            }
        },
        {
            "description": "breakdown",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "SVHQuantisedWithWorkSummary",
                "columns": [
                    "rid",
                    "cid",
                    "first name",
                    "last name",
                    "postcode",
                    "phone",
                    "mobile",
                    "team",
                    "worker",
                    "received",
                    "first contact",
                    "interview",
                    "start",
                    "week1 level",
                    "week1 mins",
                    "week2 level",
                    "week2 mins",
                    "week3 level",
                    "week3 mins",
                    "week4 level",
                    "week4 mins",
                    "exited",
                    "exit reason"
                ]
            }
        }
    ]
}

{
    "description": "on support work this period",
    "selectionCriteria": {
        "selectionRootEntity": "Referral",
        "selectorType": "byStartOfWeekMonday",
        "selectionPropertyPath": "supportWork.workDate",
        // "relativeStartDate": "2016-01-04",
        "relativeStartIndex": 0,
        "relativeEndIndex": 1,
        "fetchRelatedEntities": [
            "supportWork",
            "client",
            "singleValueQnrHistory"
        ]
    },
    "stages": [
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "visitCountsByService"
            }
        },
        {
            "description": "by service",
            "stageType": "CHART",
            "seriesDefs": [
                {
                    "label": "count",
                    "valuePath": "totalVisits",
                    "renderMode": "PIE"
                }
            ]
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "visitCountsByProject"
            }
        },
        {
            "description": "by project",
            "stageType": "CHART",
            "seriesDefs": [
                {
                    "label": "count",
                    "valuePath": "totalVisits",
                    "renderMode": "PIE"
                }
            ]
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "workFromReferrals"
            }
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "workSummaryBySVHId"
            }
        },
        // NB KEY can be ignored, since it's always the same 'hours'
        // but when svh could be counting many things, the key made sense
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "svhCountsByKey"
            }
        },
        {
            "description": "by keys",
            "stageType": "CHART",
            "seriesDefs": [
                {
                    "label": "count",
                    "valuePath": "count",
                    "renderMode": "PIE"
                }
            ]
        },
        {
            "description": "breakdown by keys",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "SVHWithWorkSummary",
                "columns": [
                    "rid",
                    "first name",
                    "last name",
                    "postcode",
                    "phone",
                    "mobile",
                    "team",
                    "worker",
                    "received",
                    "first contact",
                    "interview",
                    "start",
                    "exited",
                    "exit reason",
                    "level",
                    "valid from",
                    "time (mins)",
                    "valid to"
                ]
            }
        }
    ]
}


support work by service, then by support level, then work breakdown
{
    "description": "on support work this period",
    "selectionCriteria": {
        "selectionRootEntity": "Referral",
        "selectorType": "byStartOfQuarter",
        "selectionPropertyPath": "supportWork.workDate",
        "relativeStartIndex": 0,
        "relativeEndIndex": 4,
        "fetchRelatedEntities": [
            "supportWork", "singleValueHistory"
// fetch singleValueHistory also
        ]
    },
in => initialAnalysis defers to ReportDataSourceFactory getData() - where both referral and support return ReferralAggregate
out = ReferralAggregate

    "stages": [
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "visitCountsByService"

            }
        },
in => visitCountsByService = count the number of work items per service
    export var visitSupportCountsByServiceAnalyser: Transformer<ReferralAggregate, types.ReferralAggregateGroupWithVisits>
        = function(input: Sequence<ReferralAggregate>): SequenceAnalysis<types.ReferralAggregateGroupWithVisits> {
        return new workCommonAnalysis.GroupedReferralAggregateAnalysisWithVisits(visitSupportCounts(input, groupByReferredService));
    }
    function visitCountsFromWork(input: Sequence<ReferralAggregate>, groupFn: ReferralAggregateToGroupWorkWithRef): Sequence<types.ReferralAggregateGroupWithVisits> {
        return groupFn(input)
        .map((pair) => {
            var allWork: Sequence<WorkWithRefToReferralAggregate> = pair.elements;
            return new workCommonAnalysis.VisitsAnalysisFromWorkAccumulator(pair.key).reduce(allWork);
        });
    }
out => GroupedReferralAggregateAnalysisWithVisits

        {
            "description": "by service",
            "stageType": "CHART",
            "seriesDefs": [
                {
                    "label": "count",
                    "valuePath": "totalVisits",
                    "renderMode": "PIE"
                }
            ]
        },
out on click => ReferralAggregateAnalysis

in => visitCountsBySupportLevelAnalyser = count the number of work items per support level
    multiply the ReferralAggregate into SingleValueHistoryWithReferralAggregate
    group by the 'value' of the single value
    flatten the work in each SingleValueHistoryWithReferralAggregate
    construct a Group with key: 'value' and elements: work
    pass to the visits accumulator

        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "visitSupportCountsBySupportLevel"
            }
        },
out => GroupedReferralAggregateAnalysisWithVisits

        {
            "description": "inner ring: number of entries, outer ring: 'time (mins)'",
            "stageType": "CHART",
            "seriesDefs": [
                {
                    "label": "mins spent",
                    "valuePath": "totalTimeSpentMins",
                    "renderMode": "PIE"
                },
                {
                    "label": "number of entries",
                    "valuePath": "totalVisits",
                    "renderMode": "PIE"
                }
            ]
        },
        {
            "description": "breakdown of referrals with goals outstanding/achieved",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "ReferralReportItem",
                "columns": [
                    "rid",
                    "cid",
                    "client",
                    "project",
                    "from",
                    "received",
                    "status now",
                    "worker",
                    "achieved",
                    "outstanding"
                ]
            }
        }
    ]
}

referrals (live) by service, then by 'overview' levels, then by project, then breakdown
TODO we would like to use 'live' status but also specify the dates of a week
TODO analysis class to hold additional details for when the referral was on the support level
{
    "description": "on all live referrals",
    "selectionCriteria": {
        "selectionRootEntity": "Referral",
        "selectorType": "byReferralStatusWeekly",
        "newReferralsOnly": "false",
        "selectionPropertyPath": "",
        "referralStatus": "live",
        "relativeStartIndex": 0,
        "relativeEndIndex": 1,
        "fetchRelatedEntities": [
            "singleValueHistory"
        ]
    },
    "stages": [
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "visitCountsByService"
            }
        },
        {
            "description": "by service",
            "stageType": "CHART",
            "seriesDefs": [
                {
                    "label": "count",
                    "valuePath": "totalVisits",
                    "renderMode": "PIE"
                }
            ]
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "visitCountsByProject"
            }
        },
        {
            "description": "by project",
            "stageType": "CHART",
            "seriesDefs": [
                {
                    "label": "count",
                    "valuePath": "totalVisits",
                    "renderMode": "PIE"
                }
            ]
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "referralCountsByService"
            }
        },
        {
            "description": "by service",
            "stageType": "CHART",
            "seriesDefs": [
                {
                    "label": "count",
                    "valuePath": "count",
                    "renderMode": "PIE"
                }
            ]
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "referralCountsBySupportLevel"
            }
        },
        {
            "description": "by support level",
            "stageType": "CHART",
            "seriesDefs": [
                {
                    "label": "count",
                    "valuePath": "count",
                    "renderMode": "PIE"
                }
            ]
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "referralCountsByProject"
            }
        },
        {
            "description": "by project",
            "stageType": "CHART",
            "seriesDefs": [
                {
                    "label": "count",
                    "valuePath": "count",
                    "renderMode": "PIE"
                }
            ]
        },
        {
            "description": "breakdown of referrals",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "ReferralReportItem",
                "columns": [
                    "rid",
                    "cid",
                    "client",
                    "service",
                    "project",
                    "from",
                    "received",
                    "decided",
                    "start",
                    "exited",
                    "signposted",
                    "status now",
                    "worker"
                ]
            }
        }
    ]
}

(using hours) support work by service, then by project, then time by referral breakdown
{
    "description": "on support work this period",
    "selectionCriteria": {
    "selectionRootEntity": "Referral",
    "selectorType": "byStartOfWeekMonday",
    "selectionPropertyPath": "supportWork.workDate",
    "relativeStartIndex": 0,
    "relativeEndIndex": 1,
    "fetchRelatedEntities": [
        "supportWork"
    ]
    },
    "stages": [
    {
        "description": "-not visible-",
        "stageType": "ANALYSER",
        "analyserConfig": {
            "analyserType": "visitCountsByService"
        }
    },
    {
        "description": "by service",
        "stageType": "CHART",
        "seriesDefs": [
            {
                "label": "count",
                "valuePath": "totalVisits",
                "renderMode": "PIE"
            }
        ]
    },
    {
        "description": "-not visible-",
        "stageType": "ANALYSER",
        "analyserConfig": {
            "analyserType": "visitCountsByProject"
        }
    },
    {
        "description": "by project",
        "stageType": "CHART",
        "seriesDefs": [
            {
                "label": "count",
                "valuePath": "totalVisits",
                "renderMode": "PIE"
            }
        ]
    },
    {
        "description": "-not visible-",
        "stageType": "ANALYSER",
        "analyserConfig": {
            "analyserType": "workFromReferrals"
        }
    },
    {
        "description": "-not visible-",
        "stageType": "ANALYSER",
        "analyserConfig": {
            "analyserType": "workByRid"
        }
    },
    {
        "description": "-not visible-",
        "stageType": "ANALYSER",
        "analyserConfig": {
            "analyserType": "workGroupToVisitGroup"
        }
    },
    {
        "description": "breakdown of visits",
        "stageType": "TABLE",
        "tableRepresentation": {
            "className": "VisitsAnalysis",
            "columns": [
                "r-id",
                "c-id",
                "client name",
                "worker",
                "time spent",
                "visits",
                "average time spent",
                "time (hr)",
                "time (mins left)"
            ]
        }
    }
    ]
}



support work by service, then by project, then by 'overview' breakdown


{
    "description": "on support work this period",
    "selectionCriteria": {
        "selectionRootEntity": "Referral",
        "selectorType": "byStartOfWeekMonday",
        "selectionPropertyPath": "supportWork.workDate",
// we can now use a base date from which to use the relative
        "relativeStartDate": "2016-01-04",
        "relativeStartIndex": 0,
// 0-3, a rolling 4 week period
        "relativeEndIndex": 3,
        "fetchRelatedEntities": [
// we need all these
            "supportWork", "client", "singleValueHistory"
        ]
    },
    "stages": [
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "visitCountsByService"
            }
        },
        {
            "description": "by service",
            "stageType": "CHART",
            "seriesDefs": [
                {
                    "label": "count",
                    "valuePath": "totalVisits",
                    "renderMode": "PIE"
                }
            ]
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "visitCountsByProject"
            }
        },
        {
            "description": "by project",
            "stageType": "CHART",
            "seriesDefs": [
                {
                    "label": "count",
                    "valuePath": "totalVisits",
                    "renderMode": "PIE"
                }
            ]
        },
count is included in Group<ReferralAggregate>
    export interface ReferralAggregateGroupWithVisits extends Group<ReferralAggregate> {
        latestWorkDate: EccoDate;
        lastSignedWorkDate: EccoDate;
        lastUnSignedWorkDate: EccoDate;
        totalTimeSpentMins: number;
        totalVisits: number;
        averageVisitLength: number;
        countByCommentType: {[key: string]: number};
    }

        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "workFromReferrals"
            }
        },
// extract work from the referrals
export interface WorkWithRefToReferralAggregate extends evidenceDto.BaseOutcomeBasedWork {
    /** parent item */
    reportItem: ReferralAggregate;
}



        // don't need this
            {
                "description": "-not visible-",
                "stageType": "ANALYSER",
                "analyserConfig": {
                "analyserType": "workByRid"
                }
            },
        workCountsBy(input, groupByRid)
        interface WorkWithRefGroupWithCount extends Group<WorkWithRefToReferralAggregate> {
            count: number;
        }
        // don't need this



        // don't need this
// NB its possible to use an ANALYSER stage to ungroup
// therefore making the next analyser that little bit easier to manage
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": { "analyserType": "WorkWithParentRefAnalysis" },
            // NB WorkWithParentRefAnalysis will need adding to types.analysersByName at the end of referralAnalysis.ts
            "selectionAnalyser": "ungroup"
        },
        // don't need this



            {
                "description": "-not visible-",
                "stageType": "ANALYSER",
                "analyserConfig": {
                "analyserType": "workSummaryBySVHId"
                }
            },

        {
            "description": "breakdown of visits",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "SVHWithWorkSummary",
                "columns": [
                    "r-id",
                    "client"
                ]
            }
        }
    ]
}

Custom form snapshot
{
    "description": "on referrals with custom form work",
    "selectionCriteria": {
        "selectionRootEntity": "Referral",
        "referralStatus": "liveAtEnd",
        "customFormEvidenceGroup": "customForm3",
        "fetchRelatedEntities": [
            "customFormWork"
        ]
    },
    "stages": [
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "customFormWorkFromReferrals"
            }
        },
        {
            "description": "custom form work",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "CustomFormWork",
                "columns": [
                    "sr-id",
                    "created",
                    "work date",
                    "task",
                    "author",
                    {
                        "title": "prop",
                        "representation": "author",
                        "path": [
                            "form",
                            "overview"
                        ]
                    }
                ]
            }
        }
    ]
}

Questionnaire Answers Summary
-- key is 'questionAnswersCountByQuestionAnswerTotals'

{
    "description": "on all answers in the date range",
    "selectionCriteria": {
        "selectionRootEntity": "Referral",
        "selectorType": "byReferralStatus",
        "referralStatus": "liveAtEnd",
        "relativeStartIndex": 0,
        "relativeEndIndex": 1,
        "serviceId": 2,
        "questionnaireEvidenceGroup": "indep_checklist",
        "selectionKey": "",
        "fetchRelatedEntities": [
            "questionnaireWork"
        ]
    },
    "stages": [
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "qnAnswerWorkFromReferralsAnalyser"
            }
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "questionAnswersCountByQuestionAnswerTotals"
            }
        },
        {
            "description": "questions",
            "stageType": "CHART",
            "canSkip": "true",
            "seriesDefs": [
                {
                    "label": "total",
                    "valuePath": "numberTotal",
                    "renderMode": "BAR"
                }
            ]
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "questionAnswersTransposeByWork"
            }
        },
        {
            "description": "breakdown of question answers",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "TransposedQnAnswerWorkFromDef",
                "columnSourceDefIds": [
                    "100519"
                ],
                "columns": [
                    "r: r-id",
                    "r: c-id",
                    "r: client",
                    "r: start",
                    "r: worker",
                    "Contacts updated on ECCO",
                    "Mattress purchased"
                ]
            }
        }
    ]
}


// NB this report loads QuestionnaireReportCapability, but because it requests the referral,
// it then loads using RelatedWorkThenReferralQueryDataSource to load questionnaires (obeying the criteria)
// then the referral, producing a ReferralAggregateAnalysis
Questionnaire Answers
{
    "description": "on all answers in the date range",
    "selectionCriteria": {
        "selectionRootEntity": "Questionnaire",
        "selectorType": "byStartOfQuarter",
        "relativeStartIndex": 0,
        "relativeEndIndex": 1,
        "questionnaireEvidenceGroup": "feedbackQuestionnaire",
        "selectionKey": "",
        "fetchRelatedEntities": [
            "referral"
        ]
    },
    "stages": [
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "referralCountsByService"
            }
        },
        {
            "description": "by service",
            "stageType": "CHART",
            "seriesDefs": [
                {
                    "label": "count",
                    "valuePath": "count",
                    "renderMode": "PIE"
                }
            ]
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "referralCountsByProject"
            }
        },
        {
            "description": "by project",
            "stageType": "CHART",
            "seriesDefs": [
                {
                    "label": "count",
                    "valuePath": "count",
                    "renderMode": "PIE"
                }
            ]
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "referralCountsByWorker"
            }
        },
        {
            "description": "by worker",
            "stageType": "CHART",
            "seriesDefs": [
                {
                    "label": "count",
                    "valuePath": "count",
                    "renderMode": "PIE"
                }
            ]
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "qnAnswerWorkFromReferralsAnalyser"
            }
        },
        {
            "description": "breakdown of question answers",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "QnAnswerWork",
                "columns": [
                    "rid",
                    "cid",
                    "client",
                    "service",
                    "project",
                    "worker",
                    "work date",
                    "question",
                    "answer"
                ]
            }
        }
    ]
}

// an example of a questionnaire report but loading a service recipient (which overrides the default referral)
// we use the transposed report because this is a more desirable snapshot report
Questionnaire Snapshot Transposed
{
    "description": "on all checks snapshot smartsteps for the period",
    "selectionCriteria": {
        "selectionRootEntity": "QuestionnaireSingleSnapshot",
        "selectorType": "byStartOfQuarter",
        "questionnaireEvidenceGroup": "feedbackQuestionnaire",
        "relativeStartIndex": 0,
        "relativeEndIndex": 1,
        "fetchRelatedEntities": ["serviceRecipient"]
    },
    "stages": [
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "questionnaireAnswersSnapshotFlatten"
            }
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "questionAnswerSnapshotTransposeBy WorkUuid / SrId"
            }
        },
        {
            "description": "breakdown of cell",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "TransposedQnAnswerWorkFromDef",
                "columnSourceDefIds": [
                    "208"
                ],
                "columns": [
                    "sr: sr-id",
                    "sr: name",
                    "physical health"
                ]
            }
        }
    ]
}

Questionnaire Answers Transposed
// NB requires selection criteria of (errors without this):
//    "serviceId": ,
//    "questionnaireEvidenceGroup": "...",

// but criteria still obeyed
{
    "description": "on all answers in the date range",
    "selectionCriteria": {
        "selectionRootEntity": "Questionnaire",
        "selectorType": "byStartOfQuarter",
        "relativeStartIndex": 0,
        "relativeEndIndex": 1,
        "questionnaireEvidenceGroup": "feedbackQuestionnaire",
        "selectionKey": "",
        "fetchRelatedEntities": [
            "referral"
        ]
    },
    "stages": [
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "qnAnswerWorkFromReferralsAnalyser"
            }
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "questionAnswersTranspose"
            }
        },
        {
            "description": "breakdown of question answers",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "TransposedQnAnswerWorkFromDef",
                "columnSourceDefIds": ["<qnGroupId>"],
                "columns": [
                    "r: r-id",
                    "r: c-id",
                    "r: client",
                    "r: service",
                    "r: project",
                    "r: worker",
                    "... question name here ..."
                ]
            }

Questionnaire snapshot
{
    "description": "on all checks snapshot smartsteps for the period",
    "selectionCriteria": {
        "selectionRootEntity": "QuestionnaireSingleSnapshot",
        "selectorType": "byStartOfQuarter",
        "questionnaireEvidenceGroup": "generalQuestionnaire",
        "relativeStartIndex": 0,
        "relativeEndIndex": 1,
        "fetchRelatedEntities": []
    },
    "stages": [
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "questionnaireAnswersSnapshotFlatten"
            }
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "questionAnswerSnapshotFilterChoices"
            }
        },
        {
            "description": "breakdown of cell",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "questionAnswerSnapshotWithParent",
                "columns": [
                    "r: r-id",
                    "r: c-id",
                    "r: name",
                    "r: status now",
                    "r: start",
                    "snapshotPeriod",
                    "workDate",
                    "questionName",
                    "answer"
                ]
            }
        }
    ]
}

Questionnaire Snapshot Transposed
{
    "description": "on all checks snapshot smartsteps for the period",
    "selectionCriteria": {
        "selectionRootEntity": "QuestionnaireSingleSnapshot",
        "selectorType": "byStartOfQuarter",
        "questionnaireEvidenceGroup": "feedbackQuestionnaire",
        "relativeStartIndex": 0,
        "relativeEndIndex": 1,
        "fetchRelatedEntities": []
    },
    "stages": [
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "questionnaireAnswersSnapshotFlatten"
            }
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "questionAnswerSnapshotTransposeByCid"
            }
        },
        {
            "description": "breakdown of cell",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "TransposedQnAnswerWorkFromDef",
                "columnSourceDefIds": [
                    "208"
                ],
                "columns": [
                    "r: r-id",
                    "r: c-id",
                    "r: client",
                    "physical health"
                ]
            }
        }
    ]
}

Questionnaire Movement
// see ReportController.questionnaireSnapshotQuery
// fetchRelatedEntities is not required for ReferralSummary, as this is
// required as part of the snapshot (because the answer data doesn't come with srId)
// matrix shows as choice 'value' - so ensure your questions have that
// however, this should be set before the questionnaire is used as it's the command save value
// which gets saved into the database - see QuestionAnswerCommandHandler
// in which case we have just amended the code to make a best guess on using the displayValue
{
    "description": "on all pre/post snapshot answers around the period",
    "selectionCriteria": {
        "selectionRootEntity": "QuestionnaireSnapshot",
        "selectorType": "byStartOfQuarter",
        "questionnaireEvidenceGroup": "generalQuestionnaire",
        "relativeStartIndex": 0,
        "relativeEndIndex": 1,
        "fetchRelatedEntities": []
    },
    "stages": [
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "questionnaireAnswersSnapshotFlatten"
            }
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "questionAnswerMultiSnapshotFilterChoices"
            }
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "questionAnswerMultiSnapshotMatrixByQuestion"
            }
        },
        {
            "description": "by question",
            "stageType": "CHART",
            "seriesDefs": [
                {
                    "label": "count",
                    "valuePath": "count",
                    "renderMode": "PIE"
                }
            ]
        },
        {
            "description": "matrix",
            "stageType": "MATRIX",
            "selectionAnalyser": "singleCell",
            "canSkip": "true",
            "matrixRepresentation": {}
        },
        {
            "description": "breakdown of cell",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "questionAnswerSnapshotWithParent",
                "columns": [
                    "r: r-id",
                    "r: c-id",
                    "r: name",
                    "r: status now",
                    "r: start",
                    "snapshotPeriod",
                    "workDate",
                    "questionName",
                    "answer"
                ]
            }
        }
    ]
}


Questionnaire Difference
// typically for money - finding the different between two snapshots
// but now works on integer/number (see commit)
{
    "description": "on all pre/post snapshot answers around the period",
    "selectionCriteria": {
        "selectionRootEntity": "QuestionnaireSnapshot",
        "selectorType": "byStartOfQuarter",
        "questionnaireEvidenceGroup": "generalQuestionnaire",
        "relativeStartIndex": 0,
        "relativeEndIndex": 1,
        "fetchRelatedEntities": []
    },
    "stages": [
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "questionnaireAnswersSnapshotFlatten"
            }
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "questionAnswerMultiSnapshotFilterInteger"
            }
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "questionAnswerMultiSnapshotDifferenceByQuestion"
            }
        },
        {
            "description": "by question",
            "stageType": "CHART",
            "seriesDefs": [
                {
                    "label": "count",
                    "valuePath": "count",
                    "renderMode": "PIE"
                }
            ]
        },
        {
            "description": "breakdown of entries",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "differenceColumns",
                "columns": [
                    "r-id",
                    "difference",
                    "direction",
                    "answerPre",
                    "answerPost"
                ]
            }
        }
    ]
}


SmartStep Difference
-- NB alternative to the older % achieved report which loads all support (00400000-0000-babe-babe-dadafee1600d)
-- NB use 'absoluteFromDate' '1970-01-01' and 'byEndOfQuarter' for showing smart steps 'as at' their whole journey
{
    "description": "on all pre/post snapshot smartsteps for the period",
    "selectionCriteria": {
        "selectionRootEntity": "SmartStepMultiSnapshot",
        "supportEvidenceGroup": "needs",
        "selectorType": "byStartOfQuarter",
        "relativeStartIndex": 0,
        "relativeEndIndex": 1,
        "fetchRelatedEntities": ["referral"]
    },
    "stages": [
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "smartStepsSnapshotFlatten"
            }
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "smartStepsSnapshotDifferenceByActionInstance"
            }
        },
        {
            "description": "breakdown of entries",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "differenceColumnsWithReferral",
                "columns": [
                    "sr-id",
                    "a: o-id",
                    "a: o-name",
                    "a: a-group-id",
                    "a: a-id",
                    "a: a-name",
                    "a: instance-id",
                    "a: hierarchy",
                    "a: position",
                    "pre-a: workDate",
                    "pre-a: a-status",
                    "result",
                    "post-a: workDate",
                    "post-a: a-status"
                ]
            }
        }
    ]
}

SmartStep Snapshot
// 'outcome snapshot (smart steps)'
// this loads all smartsteps and isn't isReferralBased
// just add this to load worker: "r: worker",
{
    "description": "on all snapshot smartsteps for the period",
    "selectionCriteria": {
        "selectionRootEntity": "SmartStepSingleSnapshot",
        "supportEvidenceGroup": "needs",
        "fetchRelatedEntities": ["referral"]
    },
    "stages": [
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "smartStepsSnapshotFlatten"
            }
        },
        {
            "description": "breakdown of entries",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "SmartStepSingleSnapshotColumns",
                "columns": [
                    "r: r-id",
                    "r: name",
                    "r: start",
                    "r: status now",
                    "o-id",
                    "o-name",
                    "a-group-id",
                    "a-id",
                    "a-name",
                    "instance-id",
                    "hierarchy",
                    "position",
                    "workDate",
                    "a-status"
                ]
            }
        }
    ]
}

Support Work Snapshot
-- NB use 'absoluteFromDate' '1970-01-01' for showing all work 'as at'
{
    "description": "on all support work around the period",
    "selectionCriteria": {
        "selectionRootEntity": "SupportWorkSnapshot",
        "selectorType": "byEndOfQuarter",
        "absoluteFromDate": "1970-01-01",
        "relativeStartIndex": 0,
        "relativeEndIndex": 1,
        "supportEvidenceGroup": "needs"
    },
    "stages": [
        {
            "description": "breakdown of work",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "WorkOnly",
                "columns": [
                    "sr-id",
                    "w-id",
                    "worker",
                    "created (UTC)",
                    "work date (clock time)",
                    "type",
                    "time (mins)",
                    "task",
                    "signed",
                    "author"
                ]
            }
        }
    ]
}


HACT Management (per service/project - this report does NOT load all client details)
// NB for repeatable reports (and testing), 'now' is based on the end of the reporting period
// so that if a question hasn't been answered, the 'days expire' refers to the relevant time to the reporting 'to'
{
    "description": "on referrals this period",
    "selectionCriteria": {
            "selectionRootEntity": "Referral",
            "selectorType": "byStartOfQuarter",
            "questionnaireEvidenceGroup": "hactQuestionnaire",
            "hactSessionData": "true",
            "relativeStartDate": "2016-04-01",
            "relativeStartIndex": 0,
            "relativeEndIndex": 1,
            "fetchRelatedEntities": [
               "client", "questionnaireWork", "supportWork"
            ]
        },
    "stages": [
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "hactManagement"
            }
        },
        {
            "description": "breakdown of management",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "hactManagement",
                "columns": [
                    "c-id",
                    "questionName",
                    "pre survey support date",
                    "survey name",
                    "survey status",
                    "survey answer date",
                    "survey days expire"
                ]
            }
        }
    ]
}


HACT Management & HACT Social Value
// management liquibase id 'DEV-1741-repDef-hact-mgt-reports-liveAtEnd'
// socialvalue liquibase id: 'DEV-1741-repDef-hact-social-reports-body'
// see hact-impl.txt in Dropbox for thoughts on this


/*
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "referralsWithoutWorkNotAcceptedWithin2Weeks"
            }
        },
*/

time between needs and support entry
as below, but load supportWork and use column 'needs to support'


time between referral and first 'initial screening' questionnaire entry
{
    "description": "on all referrals received",
    "selectionCriteria": {
        "selectionRootEntity": "Referral",
        "selectorType": "byStartOfQuarter",
        "selectionPropertyPath": "",
        "referralStatus": "",
        "selectionKey": "",
        "fetchRelatedEntities": [
            "questionnaireWork"
        ]
    },
    "stages": [
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": { "analyserType": "referralCountsByProject" }
        },
        {
            "description": "by project",
            "stageType": "CHART",
            "seriesDefs": [
                { "label": "count", "valuePath": "count", "renderMode": "PIE" }
            ]
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": { "analyserType": "referralCountsByWorker" }
        },
        {
            "description": "by worker",
            "stageType": "CHART",
            "seriesDefs": [
                { "label": "count", "valuePath": "count", "renderMode": "PIE" }
            ]
        },
        {
            "description": "breakdown of referrals",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "ReferralReportItem",
                "columns": [
                    "rid",
                    "cid",
                    "client",
                    "service",
                    "project",
                    "from",
                    "received",
                    "decided",
                    "received to first quest. work",
                    "status now",
                    "worker"
                ]
            }
        }
    ]
}


referrals (live at end) by interviewer
{
    "description": "on all referrals received",
    "selectionCriteria": {
        "selectionRootEntity": "Referral",
        "selectorType": "byReferralStatus",
        "selectionPropertyPath": "",
        "referralStatus": "liveAtEnd",
        "selectionKey": "",
        "fetchRelatedEntities": [
        ]
    },
    "stages": [
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": { "analyserType": "referralCountsByProject" }
        },
        {
            "description": "by project",
            "stageType": "CHART",
            "seriesDefs": [
                { "label": "count", "valuePath": "count", "renderMode": "PIE" }
            ]
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": { "analyserType": "referralCountsByInterviewer1" }
        },
        {
            "description": "by worker",
            "stageType": "CHART",
            "seriesDefs": [
                { "label": "count", "valuePath": "count", "renderMode": "PIE" }
            ]
        },
        {
            "description": "breakdown of referrals",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "ReferralReportItem",
                "columns": [
                    "rid",
                    "cid",
                    "client",
                    "service",
                    "project",
                    "from",
                    "received",
                    "decided",
                    "status now",
                    "interviewer"
                ]
            }
        }
    ]
}


referrals (died) by service, then by project, then breakdown with date of death
{
    "description": "on referrals this quarter",
    "selectionCriteria": {
        "selectionRootEntity": "Referral",
        "selectorType": "byReferralStatus",
        "selectionPropertyPath": "date of death",
        "fetchRelatedEntities": [
            "client"
        ]
    },
    "stages": [
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "referralCountsByService"
            }
        },
        {
            "description": "by service",
            "stageType": "CHART",
            "seriesDefs": [
                {
                    "label": "count",
                    "valuePath": "count",
                    "renderMode": "PIE"
                }
            ]
        },
        {
            "description": "by project",
            "stageType": "CHART",
            "seriesDefs": [
                {
                    "label": "count",
                    "valuePath": "count",
                    "renderMode": "PIE"
                }
            ]
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "relationshipCountsByRelation"
            }
        },
        {
            "description": "by relationship",
            "stageType": "CHART",
            "seriesDefs": [
                {
                    "label": "count",
                    "valuePath": "count",
                    "renderMode": "PIE"
                }
            ]
        },
        {
            "description": "breakdown of referrals",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "ReferralReportItem",
                "columns": [
                    "rid",
                    "cid",
                    "client",
                    "service",
                    "project",
                    "from",
                    "received",
                    "decided",
                    "start",
                    "exited",
                    "date of death",
                    "date of death month",
                    "age at death",
                    "signposted",
                    "status now",
                    "worker"
                ]
            }
        }
    ]
}

relationships by primary service, by project, by relationship, then referral breakdown
{
    "description": "on all live referrals",
    "selectionCriteria": {
        "selectionRootEntity": "Referral",
        "selectorType": "byReferralStatus",
        "selectionPropertyPath": "",
        "referralStatus": "liveAtEnd",
        "fetchRelatedEntities": [
            "client",
            "relatedReferrals"
        ]
    },
    "stages": [
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "referralCountsByService"
            }
        },
        {
            "description": "by service",
            "stageType": "CHART",
            "seriesDefs": [
                {
                    "label": "count",
                    "valuePath": "count",
                    "renderMode": "PIE"
                }
            ]
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "referralCountsByProject"
            }
        },
        {
            "description": "by service",
            "stageType": "CHART",
            "seriesDefs": [
                {
                    "label": "count",
                    "valuePath": "count",
                    "renderMode": "PIE"
                }
            ]
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "relationshipsFromReferrals"
            }
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "relationshipCountsByRelation"
            }
        },
        {
            "description": "by relationship",
            "stageType": "CHART",
            "seriesDefs": [
                {
                    "label": "count",
                    "valuePath": "count",
                    "renderMode": "PIE"
                }
            ]
        },
        {
            "description": "breakdown of relationships",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "Relationship",
                "columns": [
                    "relationship",
                    "relation",
                    "birthDate",
                    "birthMonth",
                    "age",
                    "primary: service",
                    "primary: project",
                    "primary: rid",
                    "primary: cid",
                    "primary: title",
                    "primary: first name",
                    "primary: last name",
                    "primary: address",
                    "primary: town",
                    "primary: postcode"
                ]
            }
        }
    ]
}


-- address history
{
    "description": "all address history",
    "selectionCriteria": {
         "selectionRootEntity": "AddressHistory",
         "selectionPropertyPath": "validFrom",
         "selectorType": "byStartOfQuarter",
         "relativeStartIndex": 0,
         "relativeEndIndex": 1,
         "fetchRelatedEntities": ["referral"]
    },
    "stages": [
        {
            "description": "breakdown of address history",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "AddressHistoryWithReferralSummary",
                "columns": ["id", "sr-id", "r: name", "valid from", "valid to", "full address"]
            }
        }
    ]
}

-- finance charges
{
    "description": "finance charges",
    "selectionCriteria": {
         "selectionRootEntity": "FinanceCharge",
         "selectionPropertyPath": "validFrom",
         "selectorType": "byEndOfQuarter",
         "absoluteFromDate": "1970-01-01",
         "relativeStartIndex": 0,
         "relativeEndIndex": 1,
         // NB we look for 'r: ' or 'b: ' to determine what to load
         // but we look for 'receipts' to load the full analysis
         "fetchRelatedEntities": ["receipts"]
    },
    "stages": [
        {
            "description": "breakdown of service charges",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "FinanceChargeWithReferralAndBuilding",
                "columns": ["sr-id", "r: name", "b: name", "b: address", "b: postcode", "description", "netAmount", "receiptTotal", "dueAmount"]
            }
        }
    ]
}

-- finance receipts
{
    "description": "finance receipts",
    "selectionCriteria": {
         "selectionRootEntity": "FinanceReceipt",
         "selectionPropertyPath": "receivedDate",
         "selectorType": "byStartOfQuarter",
         "relativeStartIndex": 0,
         "relativeEndIndex": 1,
         // NB we look for 'r: ' to determine what to load
         // "fetchRelatedEntities": ["referral"]
    },
    "stages": [
        {
            "description": "breakdown of receipts",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "FinanceReceiptWithReferral",
                "columns": ["rec-id", "sr-id", "r: name", "amount", "received", "description"]
            }
        }
    ]
}

group support
{
    "description": "on all activities",
    "selectionCriteria": {
        "selectionRootEntity": "ActivityAttendance",
        "selectorType": "byStartOfQuarter",
        "relativeStartIndex": 0,
        "relativeEndIndex": 1,
        "selectionPropertyPath": "",
        "referralStatus": "",
        "selectionKey": "",
        "fetchRelatedEntities": []
    },
    "stages": [
        {
            "description": "breakdown of activities",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "ActivityAttendanceOnly",
                "columns": [
                    "g-id",
                    "g-service",
                    "g-project",
                    "venue",
                    "activity",
                    "date",
                    "r-id",
                    "c-id",
                    "client",
                    "invited",
                    "attending",
                    "attended"
                ]
            }
        }
    ]
}

commands/audit/activity stream BY CONTROL
{
    "description": "on all commands",
    "selectionCriteria": {
        "selectionRootEntity": "ServiceRecipientCommand",
        "selectorType": "byStartOfQuarter",
        "relativeStartIndex": 0,
        "relativeEndIndex": 1,
        "selectionPropertyPath": "",
        "referralStatus": "",
        "selectionKey": "",
        "fetchRelatedEntities": []
    },
    "stages": [
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "serviceRecipientCommandCountsByCommandName"
            }
        },
        {
            "description": "by name",
            "stageType": "CHART",
            "seriesDefs": [
                {
                    "label": "value",
                    "valuePath": "count",
                    "renderMode": "PIE"
                }
            ]
        },
        {
            "description": "breakdown of commands",
            "stageType": "AUDIT"
        }
    ]
}

commands/audit/activity latest in a TABLE
{
    "description": "on all commands",
    "selectionCriteria": {
        "selectionRootEntity": "Referral",
        "referralStatus": "liveAtEnd",
        "commandNameArr": ["!userAccessAudit"],
        "fetchRelatedEntities": [
            "serviceRecipientAuditsSnapshotAtEnd"
        ]
    },
    "stages": [
        {
            "description": "breakdown of referrals with latest commands",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "ReferralReportItem",
                "columns": [
                    "r-id",
                    "client",
                    "a: username",
                    "a: timestamp",
                    "a: command name",
                    {
                        "title": "name",
                        "representation": "_text",
                        "path": [
                            "displayName"
                        ]
                    },
                    "a: command"
                ]
            }
        }
    ]
}

commands/audit/activity stream in a TABLE
eg project change audits
{
    "description": "on all commands",
    "selectionCriteria": {
        "selectionRootEntity": "ServiceRecipientCommand",
        "commandNameArr": ["addressLoc", "!userAccessAudit"], // pick out the address audits only, or exclude userAudits
        "selectorType": "byStartOfDay",
        "relativeStartIndex": 0,
        "relativeEndIndex": 1,
        "selectionPropertyPath": "",
        "referralStatus": "",
        "selectionKey": "",
        "fetchRelatedEntities": []
    },
    "stages": [
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "serviceRecipientCommandFilterByCommand",
                "analyserFilters": [
                    "\"latestReferralId\":\"100044\""
                ]
            }
        },
        {
            "description": "by name",
            "stageType": "CHART",
            "seriesDefs": [
                {
                    "label": "value",
                    "valuePath": "count",
                    "renderMode": "PIE"
                }
            ]
        },
        {
            "description": "breakdown of commands",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "ServiceRecipientCommandOnly",
                "columns": [
                    "sr-id",
                    "username",
                    "timestamp",
                    "command name",
                    {
                        "title": "client",
                        "representation": "username",
                        "path": [
                            "displayName"
                        ]
                    },
                    {
                        "title": "project from",
                        "representation": "username",
                        "path": [
                            "projectChange",
                            "from"
                        ]
                    },
                    {
                        "title": "project to",
                        "representation": "username",
                        "path": [
                            "projectChange",
                            "to"
                        ]
                    },
                    "command"
                ]
            }
        }
    ]
}

audit srId
    "serviceRecipientFilter": ":<srId>"

audit user commands/audit/activity stream BY CONTROL
        "selectorType": "byStartOfWeekMonday",
        "userId": 1569837,

-- HIDE it from all reporting staff (although a search does show it up)
-- select uuid,hidden,name from reportdefinitions order by orderby;
-- update reportdefinitions set hidden=now() where uuid='';
commands/audit/activity stream BY TABLE (see by control)
        {
            "description": "breakdown of commands",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "ServiceRecipientCommandOnly",
                "columns": [
                    "sr-id",
                    "username",
                    "timestamp",
                    "command name",
                    "command"
                ]
            }
        }
    ]
}


RotaAgreement - calls findAgreementsByScheduleDate into RotaAgreementAnalysis
    only has "flattenToRelevantSchedules" which flattens to DemandScheduleDto, filtering only parents and in range
    opens in RotaScheduleAnalysis
    records cannot show

RotaSchedule - calls findAgreementsByScheduleDate into RotaScheduleAnalysis
    only has filters to extract demandScheduleDto.appointments which are loaded from fetchRelatedEntities
    with 'scheduleAptsConfirmedAtEnd' (loading findScheduleRecurrencesConfirmed)
    and opens in RotaAppointmentAnalysis
    records can show in rotaScheduleColumns

RotaAppointmentAnalysis - the result of RotaSchedule with 'scheduleAptsConfirmedAtEnd'
    records can show rotaAppointmentColumns

RotaDemand - calls findServiceRecipientsByDemandAndScheduleDate into RotaDemandSchedule, ServiceRecipientDemandDto
    has filters extractAgreements to get '.agreements'
    records can show in rotaDemandColumns


see DEV-1726 & 51d3caa5
rota schedules commands/audit/activity stream BY TABLE
-- schedules in date range, with end date and name of schedule (could be run availability, not demand)
-- load last month of confirmed apts and their UPDATED-BY to see if all same cmd
-- see command and re-use command to 'roll on' to new schedule end
-- (the work in the 'audit' popup wasn't completed and instead moved to the rota)
// "serviceRecipientFilter": "buildings:1002",
{
    "description": "on schedules",
    "selectionCriteria": {
        "selectionRootEntity": "RotaSchedule",
        "selectorType": "byStartOfWeekMonday",
        "serviceRecipientFilter": "",
        "relativeStartIndex": 0,
        "relativeEndIndex": 1,
        "fetchRelatedEntities": ["scheduleAptsConfirmedAtEnd"]
    },
    "stages": [
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": { "analyserType": "filterRelevantSchedules" }
        },
        {
            "description": "all schedules",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "DemandScheduleOnly",
                "renderType": "mui",
                "columns": [
                    "id",
                    "sr-id",
                    "name",
                    "start date",
                    "end date",
                    "edit"
                ]
            }
        },
        {
            "description": "breakdown of schedule",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "AppointmentOnly",
                "renderType": "mui",
                "columns": [
                    "id",
                    "sr-id",
                    "event",
                    "status",
                    "updated by",
                    "audit"
                ]
            }
        }
    ]
}


{
    "description": "on all commands",
    "selectionCriteria": {
        "selectionRootEntity": "ServiceRecipientCommand",
        "selectorType": "byStartOfWeekMonday",
        "relativeStartIndex": 0,
        "relativeEndIndex": 1,
        "selectionPropertyPath": "",
        "entityStatus": "",
        "selectionKey": "",
        "fetchRelatedEntities": []
    },
    "stages": [
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "serviceRecipientCommandFilterByRotaCommandName"
            }
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "serviceRecipientCommandCountsByCommandName"
            }
        },
        {
            "description": "by name",
            "stageType": "CHART",
            "seriesDefs": [
                {
                    "label": "value",
                    "valuePath": "count",
                    "renderMode": "PIE"
                }
            ]
        },
        {
            "description": "breakdown of commands",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "ServiceRecipientCommandOnly",
                "renderType": "mui",
                "columns": [
                    "sr-id",
                    "username",
                    "timestamp",
                    "command name",
                    "command"
                ]
            }
        }
    ]
}

agencies
{
    "description": "on all agencies",
    "selectionCriteria": {
        "selectionRootEntity": "Agency"
    },
    "stages": [
        {
            "description": "breakdown of agencies",
            "stageType": "TABLE",
            "tableRepresentation": {

                "className": "AgencyOnly",
                "columns": [
                    "contactId",
                    "company name",
                    "category",
                    "address",
                    "town",
                    "postcode",
                    "phone",
                    "email"
                ]
            }
        }
    ]
}

{
    "description": "on all professionals",
    "selectionCriteria": {
        "selectionRootEntity": "Professional"
    },
    "stages": [
        {
            "description": "breakdown of professionals",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "ProfessionalOnly",
                "columns": [
                    "a: company name",
                    "a: contactId",
                    "a: category",
                    "a: address",
                    "a: town",
                    "a: postcode",
                    "a: phone",
                    "a: email",
                    "a: archived",
                    "contactId",
                    "first name",
                    "last name",
                    "address",
                    "town",
                    "postcode",
                    "phone",
                    "email",
                    "archived"
                ]
            }
        }
    ]
}



contacts
{
    "description": "on contacts",
    "selectionCriteria": {
        "selectionRootEntity": "Referral",
        "referralStatus": "liveAtEnd",
        "fetchRelatedEntities": [
            "associatedContacts"
        ]
    },
    "stages": [
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "contactsFromReferrals"
            }
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "associatedContactsCountsByType"
            }
        },
        {
            "description": "by type",
            "stageType": "CHART",
            "seriesDefs": [
                {
                    "label": "count",
                    "valuePath": "count",
                    "renderMode": "PIE"
                }
            ]
        },
        {
            "description": "breakdown of contacts",
            "stageType": "TABLE",
            "tableRepresentation": {

                "className": "AssociatedContactsWithReferralSummary",
                "columns": [
                    "sr-id",
                    "r: r-id",
                    "r: name",
                    "created",
                    "archived",
                    "type(s)",
                    "i: contactId",
                    "i: first name",
                    "i: last name",
                    "i: full address",
                    "i: phone",
                    "i: mobile",
                    "i: email",
                    "a: company name"
                 ]
            }
        }
    ]
}


all incomplete tasks with due dates
{
    "description": "on all uncompleted tasks",
    "selectionCriteria": {
        "selectionRootEntity": "TaskStatus",
        "entityStatus": "incomplete_hasDueDate"
    },
    "stages": [
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "filterByNotCompleted"
            }
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "groupByDue"
            }
        },
        {
            "description": "by due date",
            "stageType": "CHART",
            "seriesDefs": [
                {
                    "label": "value",
                    "valuePath": "count",
                    "renderMode": "PIE"
                }
            ]
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "groupByService"
            }
        },
        {
            "description": "by service",
            "stageType": "CHART",
            "seriesDefs": [
                {
                    "label": "value",
                    "valuePath": "count",
                    "renderMode": "PIE"
                }
            ]
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "groupByAssignee"
            }
        },
        {
            "description": "by assignee",
            "stageType": "CHART",
            "seriesDefs": [
                {
                    "label": "value",
                    "valuePath": "count",
                    "renderMode": "PIE"
                }
            ]
        },
        {
            "description": "breakdown of tasks",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "TaskStatusOnlyColumns",
                "columns": [
                    "taskId",
                    "srId",
                    "r-id",
                    "c-id",
                    "client",
                    "service",
                    "project",
                    "task",
                    "description",
                    "due",
                    "assignee",
                    "created",
                    "completed"
                ]
            }
        }
    ]
}

tasks
{
    "description": "on all tasks",
    "selectionCriteria": {
        "selectionRootEntity": "TaskStatus"
    },
    "stages": [
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "groupByService"
            }
        },
        {
            "description": "by service",
            "stageType": "CHART",
            "seriesDefs": [
                {
                    "label": "value",
                    "valuePath": "count",
                    "renderMode": "PIE"
                }
            ]
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "groupByAssignee"
            }
        },
        {
            "description": "by assignee",
            "stageType": "CHART",
            "seriesDefs": [
                {
                    "label": "value",
                    "valuePath": "count",
                    "renderMode": "PIE"
                }
            ]
        },
        {
            "description": "breakdown of tasks",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "TaskStatusOnlyColumns",
                "columns": [
                    "taskId",
                    "srId",
                    "r-id",
                    "c-id",
                    "client",
                    "service",
                    "project",
                    "task",
                    "description",
                    "due",
                    "assignee",
                    "created",
                    "completed"
                ]
            }
        }
    ]
}

-- associated actions (needs / support / review)
{
    "description": "all associated actions",
    "selectionCriteria": {
        "selectionRootEntity": "SupportWork",
        "selectorType": "byStartOfQuarter",
        "relativeStartIndex": 0,
        "relativeEndIndex": 1,
        "fetchRelatedEntities": [
            "referral"
        ]
    },
    "stages": [
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "workFromReferrals"
            }
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "flattenToAssociatedActionsWithWork"
            }
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "associatedActionDefGroupByActionDef"
            }
        },
        {
            "description": "by link",
            "stageType": "CHART",
            "canSkip": "true",
            "seriesDefs": [
                {
                    "label": "count",
                    "valuePath": "count",
                    "renderMode": "PIE",
                    "renderOptions": {
                        "percentage": "true"
                    }
                }
            ]
        },
        {
            "description": "breakdown of associated actions with support work",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "AssociatedActionWithWorkWithReferral",
                "columns": [
                    "aa-id",
                    "o-name",
                    "aa-name",
                    "w-id",
                    "sr-id",
                    "created",
                    "author",
                    "work date",
                    "r: client",
                    "type",
                    "time (mins)",
                    "task",
                    "comment"
                ]
            }
        }
    ]
}


-- risk with referrals
{
    "description": "risk work",
    "selectionCriteria": {
        "selectionRootEntity": "RiskWork",
        "fetchRelatedEntities": [
            "referral"
        ]
    },
    "stages": [
        {
            "description": "breakdown of risk work",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "RiskWorkOnly",
                "columns": [
                    "w-id",
                    "sr-id",
                    "r: c-id",
                    "r: r-id",
                    "r: name",
                    "created",
                    "worker",
                    "author",
                    "work date",
                    "type",
                    "time (mins)",
                    "task",
                    "signed"
                ]
            }
        }
    ]
}


-- rota report
{
    "description": "on support work this quarter",
    "selectionCriteria": {
        "selectionRootEntity": "Referral",
        "selectorType": "byStartOfMonth",
        "selectionPropertyPath": "supportWork.workDate",
        "relativeStartIndex": 0,
        "relativeEndIndex": 1,
        "fetchRelatedEntities": [
            "supportWork",
            "supportWorkEvents"
        ]
    },
    "stages": [
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "visitCountsByService"
            }
        },
        {
            "description": "by service",
            "stageType": "CHART",
            "seriesDefs": [
                {
                    "label": "count",
                    "valuePath": "totalTimeSpentMins",
                    "renderMode": "PIE"
                }
            ]
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "workFromReferrals"
            }
        },
        {
            "description": "breakdown of support work",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "Work",
                "columns": [
                    "task",
                    "r-id",
                    "client",
                    "created",
                    "worker",
                    "author",
                    "type",
                    "location",
                    "event: title",
                    "signed",
                    "travel time (mins)",
                    "mileage to/from",
                    "mileage during visit",
                    "event: planned date",
                    "work date",
                    "event: planned time (mins)",
                     "time (mins)"
                 ]
            }
        }
    ]
}

-- review report live (nb doesn't include those not in the reviews table)
liveAtEnd selectionCriteria
    "selectionCriteria": {
         "selectionRootEntity": "Review",
         "entityStatus": "liveAtEnd",
         "selectionPropertyPath": "startDate",
         "fetchRelatedEntities": ["referral"]
    },

-- review report
{
    "description": "all reviews",
    "selectionCriteria": {
         "selectionRootEntity": "Review",
         "selectorType": "byStartOfMonth",
         "selectionPropertyPath": "startDate",
         "relativeStartIndex": 0,
         "relativeEndIndex": 1,
         "fetchRelatedEntities": ["referral"]
    },
    "stages": [
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "reviewCountsByComplete"
            }
        },
        {
            "description": "by completed",
            "stageType": "CHART",
            "seriesDefs": [
                {
                    "label": "count",
                    "valuePath": "count",
                    "renderMode": "PIE"
                }
            ]
        },
        {
            "description": "breakdown of reviews",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "ReviewWithReferralSummary",
                "columns": ["r: r-id", "r: c-id", "r: name", "r: service", "r: project", "r: received", "r: worker", "rev-id", "review date", "current page", "complete"]
            }
        }
    ]
}


-- risk flags report live (nb doesn't include those not in the reviews table)
liveAtEnd selectionCriteria
    "selectionCriteria": {
         "selectionRootEntity": "RiskFlags",
         "entityStatus": "liveAtEnd",
         "selectionPropertyPath": "startDate",
         "fetchRelatedEntities": ["referral"]
    },

-- risk flags report
{
    "description": "all risk flags",
    "selectionCriteria": {
         "selectionRootEntity": "RiskFlags",
         "selectorType": "byStartOfMonth",
         "selectionPropertyPath": "workDate",
         "relativeStartIndex": 0,
         "relativeEndIndex": 1,
         "fetchRelatedEntities": ["referral"]
    },
    "stages": [
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "riskFlagsCountsByValue"
            }
        },
        {
            "description": "by value",
            "stageType": "CHART",
            "seriesDefs": [
                {
                    "label": "count",
                    "valuePath": "count",
                    "renderMode": "PIE"
                }
            ]
        },
        {
            "description": "breakdown of risk flags",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "RiskFlagsWithReferralSummary",
                "columns": ["r: r-id", "r: c-id", "r: name", "r: service", "r: project", "r: received", "r: worker", "f-id", "work date", "name", "value"]
            }
        }
    ]
}


-- risk ratings report
{
    "description": "all risk ratings",
    "selectionCriteria": {
        "selectionRootEntity": "RiskRatings",
        "selectorType": "byStartOfMonth",
        "relativeStartIndex": 0,
        "relativeEndIndex": 1,
        "fetchRelatedEntities": [
            "referral"
        ]
    },
    "stages": [
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "riskRatingsCountsByLevel"
            }
        },
        {
            "description": "by level",
            "stageType": "CHART",
            "seriesDefs": [
                {
                    "label": "count",
                    "valuePath": "count",
                    "renderMode": "PIE"
                }
            ]
        },
        {
            "description": "breakdown of risk ratings",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "RiskRatingsWithReferralSummary",
                "columns": [
                    "r: r-id",
                    "r: c-id",
                    "r: name",
                    "r: service",
                    "r: project",
                    "r: received",
                    "r: worker",
                    "work date",
                    "ra-id",
                    "riskAreaName",
                    "level"
                ]
            }
        }
    ]
}

-- user report (NB restricted to role_admin, but should set hidden too)
-- server-side resticts to 'ROLE_STAFF' which are staff, manager, senior manager (select * from groups g inner join group_authorities ga on g.id = ga.group_id where ga.authority="ROLE_STAFF";)
{
    "description": "all users",
    "selectionCriteria": {
         "selectionRootEntity": "User",
         "selectorType": "",
         "fetchRelatedEntities": ["aclPermissions"]
    },
    "stages": [
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "userCountsByEnabled"
            }
        },
        {
            "description": "by enabled",
            "stageType": "CHART",
            "seriesDefs": [
                {
                    "label": "count",
                    "valuePath": "count",
                    "renderMode": "PIE"
                }
            ]
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "flattenToAclEntries"
            }
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "aclEntryCountsByClass"
            }
        },
        {
            "description": "by acl class",
            "stageType": "CHART",
            "seriesDefs": [
                {
                    "label": "count",
                    "valuePath": "count",
                    "renderMode": "PIE"
                }
            ]
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "aclEntryCountsById"
            }
        },
        {
            "description": "by acl entity",
            "stageType": "CHART",
            "seriesDefs": [
                {
                    "label": "count",
                    "valuePath": "count",
                    "renderMode": "PIE"
                }
            ]
        },
        {
            "description": "breakdown of users",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "AclEntryOnly",
                "columns": ["username", "type", "id", "name"]
            }
        }
    ]
}

-- project calendar
-- referralEvents gets events between start=to, end=to-1
-- calendarEventsFromReferrals just maps to the right data structure
{
    "description": "",
    "selectionCriteria": {
        "selectionRootEntity": "Referral",
        "referralStatus": "liveAtEnd",
        "fetchRelatedEntities": [
            "referralEvents"
        ],
        "selectorType": "byStartOfDay",
        "relativeStartIndex": 0,
        "relativeEndIndex": 1
    },
    "stages": [
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "calendarEventsFromReferrals"
            }
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "calendarCountsByService"
            }
        },
        {
            "description": "appointments by service",
            "stageType": "CHART",
            "seriesDefs": [
                {
                    "label": "count",
                    "valuePath": "count",
                    "renderMode": "PIE"
                }
            ]
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "calendarCountsByProject"
            }
        },
        {
            "description": "appointments by project",
            "stageType": "CHART",
            "seriesDefs": [
                {
                    "label": "count",
                    "valuePath": "count",
                    "renderMode": "PIE"
                }
            ]
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "calendarCountsByType"
            }
        },
        {
            "description": "appointments by event type",
            "stageType": "CHART",
            "seriesDefs": [
                {
                    "label": "count",
                    "valuePath": "count",
                    "renderMode": "PIE"
                }
            ]
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "calendarCountsByCategory"
            }
        },
        {
            "description": "appointments by event category",
            "stageType": "CHART",
            "seriesDefs": [
                {
                    "label": "count",
                    "valuePath": "count",
                    "renderMode": "PIE"
                }
            ]
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "calendarCountsByCategory"
            }
        },
        {
            "description": "appointments by event category",
            "stageType": "CHART",
            "seriesDefs": [
                {
                    "label": "count",
                    "valuePath": "count",
                    "renderMode": "PIE"
                }
            ]
        },
        {
            "description": "breakdown of events",
            "stageType": "CALENDAREVENT"
        }
    ]
}

-- calendar (load live referrals - hard coded, then load events in range)
{
    "description": "",
    "selectionCriteria": {
        "selectionRootEntity": "EventResource",
        "selectorType": "byStartOfMonth",
        "relativeStartIndex": 0,
        "relativeEndIndex": 1
    },
    "stages": [
        {
            "description": "breakdown of events",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "CalendarColumns",
                "columns": [
                    "uid",
                    "title",
                    "planned date",
                    "planned time (mins)",
                    "r: client"
                ]
            }
        }
    ]
}

-- buildings
-- IGNORES service level permission
{
    "description": "all buildings",
    "selectionCriteria": {
        "selectionRootEntity": "Building",
        "selectorType": "",
        "fetchRelatedEntities": []
    },
    "stages": [
        {
            "description": "breakdown of building",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "BuildingOnly",
                "columns": [
                    "b-id",
                    "sr-id",
                    "calendar-id",
                    "s-id",
                    "parent-id",
                    "parent-name",
                    "name",
                    "external ref",
                    "resourceTypeId",
                    "resourceTypeName",
                    "disabled",
                    "parent-adr-id",
                    "address",
                    "town",
                    "postcode"
                ]
            }
        }
    ]
}

-- repairs
-- RepairReportCapability
-- IGNORES service level permission
{
    "description": "all repairs",
    "selectionCriteria": {
        "selectionRootEntity": "Repair",
        "selectorType": "byEndOfQuarter",
        "relativeStartIndex": 0,
        "relativeEndIndex": 1
        "fetchRelatedEntities": []
    },
    "stages": [
        {
            "description": "breakdown of repairs",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "RepairOnly",
                "columns": [
                    "rp-id","b-id","sr-id","s-id","name","status","category","rate-id","rate","priority","received","signpost comment","decided","from","worker","start","exited","exit reason"
                ]
            }
        }
    ]
}

-- ADMIN: service types / service config
NB notice the TABLE stage has a selectionAnalyser to choose the next table
{
    "description": "all service types",
    "selectionCriteria": {
         "selectionRootEntity": "ServiceType",
         "selectorType": "",
         "fetchRelatedEntities": [""]
    },
    "stages": [
        {
            "description": "breakdown of service types",
            "stageType": "TABLE",
            "selectionAnalyser": "tasks",
            "tableRepresentation": {
                "className": "ServiceTypeOnly",
                "columns": ["id", "name", "hideOnNewReferrals", "hideOnLists", "services"]
            }
        },
        {
            "description": "breakdown of task definitions",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "TaskDefinitionEntry",
                "columns": ["st: id", "name", "settings"]
            }
        }
    ]
}

-- ADMIN: outcomes
{
    "description": "all service types",
    "selectionCriteria": {
         "selectionRootEntity": "ServiceType",
         "selectorType": "",
         "fetchRelatedEntities": [""]
    },
    "stages": [
        {
            "description": "breakdown of service types",
            "stageType": "TABLE",
            "selectionAnalyser": "outcomes",
            "tableRepresentation": {
                "className": "ServiceTypeOnly",
                "columns": ["id", "name", "hideOnNewReferrals", "hideOnLists", "services"]
            }
        },
        {
            "description": "breakdown of outcomes",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "SmartStepOnly",
                "columns": ["id", "name", "actionGroupId", "actionGroupName", "outcomeId", "outcomeName"]
            }
        }
    ]
}

// ************ EXPORT
- all exports are appendable (obey date ranges in the URL) except for referrals/clients need to use 'appendable' ones (see DEV-2002)
- personal information (client + emergency grabsheet)
- referral information + tasks
- relationship information (use referrals with selection criteria "includeRelated" : true, and include fields  "primaryReferralId" and "primaryRelationship")
- all support history
- all risk information
- group support (but not used)
- questionnaires (only for legacy/fixed configs)
- referral contacts

taskName on work_export

-- export clients (appendable) NB does require at least a from date
         "referralStatus": "created",

-- export clients (non-appendable)
// possible defined fields n/a
//      "date of death", (row) => Date.parseIso8601(row.client.dateOfDeath)),
{
    "description": "all clients",
    "selectionCriteria": {
         "selectionRootEntity": "Client",
         "referralStatus": "allNoDates",
         "includeRelated" : true,
         "fetchRelatedEntities": []
    },
    "stages": [
        {
            "description": "breakdown of clients",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "ClientOnly",
                "columns": [
                    "c-id", "c-code", "clientId", "title", "first name", "last name", "external ref", "address", "town", "postcode", "phone", "mobile", "email", "birthdate",
                    "disability", "first lang.", "sex. orient.", "religion", "gender", "ethnicity", "ni",
                    {"title": "tenancy start", "representation": "birthdate", "path":["client","dateMap","tenancy start"]},
                    "communication needs", "description", "emergency keyword", "emergency keycode", "emergency details", "medication details", "doctor details", "dentist details", "preferred contact"
                ]
            }
        }
    ]
}


-- export referrals (appendable) NB does require at least a from date
        "referralStatus": "created",
        "from": ...

-- export referrals (non-appendable)
// possible defined fields n/a
//      "client status", "area", "srcGeographicAreaName"
//      "age at ref.", "age at exit", - don't have client details
//      "received to first quest. work", "needs to support" - don't have data
// eg dateMap {"title": "diagnosis date", "representation": "service", "path":["referral","dateMap","DiagnosisDate"]}
// eg textMap {"title": "diagnosis details", "representation": "service", "path":["referral","textMap","referral.diagnosisText"]}
{
    "description": "all referrals",
    "selectionCriteria": {
         "selectionRootEntity": "Referral",
         "selectorType": "byReferralStatus",
         "referralStatus": "allNoDates",
         "includeRelated" : true,
         "fetchRelatedEntities": []
    },
    "stages": [
        {
            "description": "breakdown of referrals",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "ReferralReportItem",
                "columns": ["sr-id", "referralId", "r-id", "r-code", "primaryReferralId", "primaryRelationship",
                            "clientId", "c-id", "client", "sa-id", "service", "project", "region", "from", "received", "first contact", "interview (offered)",
                            "interview", "interviewer", "decided", "start", "data protection", "consent", "exited", "exit reason","signposted",
                            "signposted reason", "signposted to","length (days)", "status now", "worker"
                            ]
                }
        }
    ]
}


-- export referral contacts
-- NB we do have ProfessionalQueryDataSource for all the agencies and professionals
-- where we can add referrerAgencyId and referrerIndividualId to the export referrals report
-- that doesn't include other associated contacts to the referral, which this report does
-- NB we could consider a ServiceRecipientId entity which loads all global ids in one go, and has fetchRelatedEntities
-- this has the ADVANTAGE of doing security very easily on one domain, even though that is then bypassed on the second API
-- of "associatedContacts" to page load - but all the infrastructure to page is what we're doing here.
{
    "description": "all referral contacts",
    "selectionCriteria": {
         "selectionRootEntity": "AssociatedContact",
         "fetchRelatedEntities": []
    },
    "stages": [
        {
            "description": "breakdown of referral contacts",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "AssociatedContactsOnly",
                "columns": [
                    "sr-id",
                    "created",
                    "archived",
                    "type(s)",
                    "i: contactId",
                    "i: first name",
                    "i: last name",
                    "i: address",
                    "i: town",
                    "i: postcode",
                    "i: phone",
                    "i: mobile",
                    "i: email",
                    "i: preferred",
                    "i: job",
                    "a: company name",
                    "a: category",
                    "a: address",
                    "a: town",
                    "a: postcode",
                    "a: phone",
                    "a: email"
                ]
            }
        }
    ]
}


-- export work (needs / support / review)
// possible defined fields n/a
//      "travel time (mins)", "mileage to/from", "mileage during visit"
//      "clientStatusId", "meetingStatusId", "locationId", "eventId"
//      "riskManagementHandled", "actions",
//      "associatedActions", "attachments"
{
    "description": "all support work",
    "selectionCriteria": {
         "selectionRootEntity": "SupportWork",
         "fetchRelatedEntities": []
    },
    "stages": [
        {
            "description": "breakdown of support work",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "WorkOnly",
                "columns": ["w-id", "sr-id", "created", "worker", "work date", "type", "time (mins)", "task", "signed", "risk required", "author", "comment"]
            }
        }
    ]
}


-- export risk work
{
    "description": "all risk work",
    "selectionCriteria": {
         "selectionRootEntity": "RiskWork",
         "fetchRelatedEntities": []
    },
    "stages": [
        {
            "description": "breakdown of risk work",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "RiskWorkOnly",
                "columns": ["w-id", "sr-id", "created", "worker", "work date", "type", "time (mins)", "task", "signed", "author", "comment"]
            }
        }
    ]
}


-- export support smart steps
// possible defined fields n/a
//    "id", "instance-id", "a-group-id", "reason-id", "goal name", "goal plan", "score"
//    also work items might be wanted: meetingStatusId?: number, clientStatusId?: number, locationId?: number, eventId?: string
{
    "description": "all support work smart steps",
    "selectionCriteria": {
         "selectionRootEntity": "SupportWork",
         "fetchRelatedEntities": []
    },
    "stages": [
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": { "analyserType": "flattenToSmartSteps" }
        },
        {
            "description": "breakdown of smart steps",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "SmartStepOnly",
                "columns": ["w-id", "o-id", "o-name", "a-id", "a-name", "a-status", "status change", "target date"]
            }
        }
    ]
}


-- export risk smart steps
// possible defined fields n/a
//    "id", "instance-id", "a-group-id", "reason-id", "goal name", "goal plan", "score"
{
    "description": "all risk work smart steps",
    "selectionCriteria": {
         "selectionRootEntity": "RiskWork",
         "fetchRelatedEntities": []
    },
    "stages": [
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": { "analyserType": "flattenToRiskActions" }
        },
        {
            "description": "breakdown of smart steps",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "RiskActionWithWork",
                "columns": ["w: sr-id", "w-id", "o-id", "o-name", "a-id", "a-name", "a-status", "status change", "target date", "hazard", "intervention", "likelihood", "severity"]
            }
        }
    ]
}


-- export risk areas / risk ratings
{
    "description": "all risk work areas",
    "selectionCriteria": {
         "selectionRootEntity": "RiskWork",
         "fetchRelatedEntities": []
    },
    "stages": [
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": { "analyserType": "flattenToRiskAreas" }
        },
        {
            "description": "breakdown of risk areas",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "RiskAreaWithWork",
                "columns": ["w: sr-id", "w: w-id", "trigger", "control", "metric", "metric level", "ra-id", "ra-name"]
            }
        }
    ]
}


-- export risk flags
{
    "description": "all risk work flags",
    "selectionCriteria": {
         "selectionRootEntity": "RiskWork",
         "fetchRelatedEntities": []
    },
    "stages": [
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": { "analyserType": "flattenToRiskFlags" }
        },
        {
            "description": "breakdown of flags",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "RiskFlagWithWork",
                "columns": ["w-id", "id", "f-id", "f-name", "value"]
            }
        }
    ]
}


-- export group support
{
    "description": "on all activities",
    "selectionCriteria": {
        "selectionRootEntity": "ActivityAttendance",
        "selectorType": "byEndOfQuarter",
        "absoluteFromDate": "1970-01-01",
        "relativeStartIndex": 0,
        "relativeEndIndex": 1,
        "fetchRelatedEntities": []
    },
    "stages": [
        {
            "description": "breakdown of activities",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "ActivityAttendanceOnly",
                "columns": [
                    "g-id",
                    "g-service",
                    "g-project",
                    "venue",
                    "activity",
                    "date",
                    "sr-id",
                    "invited",
                    "attending",
                    "attended"
                ]
            }
        }
    ]
}


-- export questionnaires all
{
    "description": "all answers",
    "selectionCriteria": {
        "selectionRootEntity": "Questionnaire",
        "fetchRelatedEntities": []
    },
    "stages": [
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": { "analyserType": "flattenToAnswers" }
        },
        {
            "description": "question answers",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "QnAnswerWork",
                "columns": [
                    "sr-id",
                    "worker",
                    "work date",
                    "questiongroup",
                    "question",
                    "answer"
                ]
            }
        }
    ]
}


-- export custom form latest
{
    "description": "latest custom form work",
    "selectionCriteria": {
         "selectionRootEntity": "CustomFormSnapshot",
         "customFormEvidenceGroup": "my cf",
         "fetchRelatedEntities": []
    },
    "stages": [
        {
            "description": "breakdown of custom form",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "CustomFormWorkOnly",
                "columns": ["sr-id", "created", "work date", "task", "author",
                    {
                        "title": "carer",
                        "representation": "author",
                        "path": [
                            "form",
                            "parents/carers name (under 25s only)"
                        ]
                    }
                ]
            }
        }
    ]
}
