import URI = require("URI");
import * as applicationProperties from "application-properties";

class RequestPath {

    private applicationRootUri: URI.URI;

    constructor(private applicationRootPath: string) {
        var applicationRootUri = URI(applicationRootPath).normalize();
        if (applicationRootUri.scheme() || applicationRootUri.authority() ||
                applicationRootUri.query() || applicationRootUri.hash() ||
                applicationRootUri.path().charAt(0) !== "/") {
            throw new Error("Invalid application root path");
        }

        this.applicationRootUri = applicationRootUri;
    }

    public segment() : string[] {
        return URI().scheme("")
                .authority("")
                .relativeTo(this.applicationRootUri)
                .segmentCoded();
    }

    private static requestPath = new RequestPath(applicationProperties.applicationRootPath);

    public static segments() {
        return RequestPath.requestPath.segment();
    }
}

export = RequestPath;
