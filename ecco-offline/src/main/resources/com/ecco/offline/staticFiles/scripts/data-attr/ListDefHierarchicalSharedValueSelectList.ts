import $ = require("jquery");
import * as cfgDomain from "ecco-dto";
    import ListDefinitionEntry = cfgDomain.ListDefinitionEntry;
import ListDefSelectList = require("./ListDefSelectList");
import ListDefHierarchicalSelectList = require("./ListDefHierarchicalSelectList");
import {SessionDataRepository} from "ecco-dto";


/**
 * Hierarchical select lists which share a single hidden input (named using 'data-name-shared'). The shared input
 * is to be saved as part of the form and used to load the value. The value is the innermost selected value of the
 * hierarchical select lists, from which the values of the other select lists in the hierarchy can be determined.
 * eg With select lists of country (UK 11), langauges (english 1) and dialects (scouse 5) then selecting UK will save
 * the value 11, selecting english the value 1 and scouse the value 5. Equally the lists will select the right value on
 * loading.
 */
class ListDefHierarchicalSharedValueSelectList extends ListDefHierarchicalSelectList {

    /* A data structure to go from the hidden input shared-name to the lists referenced by it.
     * This just avoids being dependent on the listName which allows us can have many instances on the same page.
     * We could enhance the superclass to include this reference, but this is clean.
     */
    private sharedElementIdToLists: { [id: string]: ListDefSelectList[] } = {};

    constructor(selector: string, protected override featureConfigRepository: SessionDataRepository) {
        super(selector, featureConfigRepository);
        this.createSharedElements();
    }

    private createSharedElements() {
        this.lists.forEach( (list) => {
            this.createSharedElement(list);
        });
    }

    /**
     * Creates the hidden input for the list shared-name, if it doesn't exist.
     */
    protected createSharedElement(list: ListDefSelectList) {
        var element = list.element();
        var sharedName = element.data("name-shared");
        var sharedValue = element.data("initial-value-shared");

        if (!this.sharedElementIdToLists[sharedName]) {
            var $hidden = $("<input>").attr({ // IE is better with this syntax
                "type": "hidden",
                id: sharedName,
                name: sharedName
            });
            $hidden.val(sharedValue);
            $(element).parent().append($hidden);
            this.sharedElementIdToLists[sharedName] = [];
        }
        this.sharedElementIdToLists[sharedName].push(list);
    }

    protected override removeChildrenOf(list: ListDefSelectList) {
        super.removeChildrenOf(list);
        /*
        var element = list.element();
        var sharedName: string = element.data("name-shared");
        this.sharedElementIdToLists[sharedName].splice($.inArray(list,this.sharedElementIdToLists[sharedName]) ,1);
        */
    }

    /**
     * On the last item that was changed, update the hidden input
     */
    protected override postPopulate(newValue: string, changedList: ListDefSelectList, lastList: ListDefSelectList) {
        var sharedElementId = this.getSharedElementIdFromList(lastList);
        $('#'+sharedElementId).val(newValue);
    }

    /**
     * Calculates the initial value for the given list - because the value
     * could depend on what the list is showing in the hierarchy.
     */
    protected override calculateInitialValue(list: ListDefSelectList): string {
        //var sharedElementId = this.getSharedElementIdFromList(list);
        //var currentSharedValue = $('#'+sharedElementId).val(); // FAILS if not in dom

        // switch to get the shared value from the list object
        const currentSharedValue = list.element().data("initial-value-shared");
        if (!currentSharedValue) {
            return null;
        }
        var currentSharedEntry = this.getListDefEntryFromId(currentSharedValue);
        var initialValueForNodeList = this.lookUpEntryIdHierarchyForListNameMatch(list.getListName(), currentSharedEntry);
        return initialValueForNodeList == null ? null : initialValueForNodeList.toString();
    }

    protected override getSharedElementIdFromList(list: ListDefSelectList): string {
        for (var id in this.sharedElementIdToLists) {
            if (this.sharedElementIdToLists[id].indexOf(list) > -1) {
                return id;
            }
        }
        return null;
    }

    /**
     * Find the appropriate id of a list definition entry for the listName given - looping the entry's parents to find it
     */
    private lookUpEntryIdHierarchyForListNameMatch(listName: string, entry: ListDefinitionEntry): number {
        if (entry.getListName() == listName) {
            return entry.getId();
        } else {
            if (entry.getParentId() == null) {
                return null;
            }
            var parentEntry = this.getListDefEntryFromId(entry.getParentId());
            if (parentEntry != null) {
                return this.lookUpEntryIdHierarchyForListNameMatch(listName, parentEntry);
            }
        }
        return null;
    }

}
export = ListDefHierarchicalSharedValueSelectList;
