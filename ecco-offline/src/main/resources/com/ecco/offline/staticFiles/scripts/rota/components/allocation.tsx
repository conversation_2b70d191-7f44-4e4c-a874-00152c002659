import {EccoDate} from "@eccosolutions/ecco-common";
import {Button, Dialog, DialogActions, DialogContent, DialogTitle, Grid} from "@eccosolutions/ecco-mui";
import {Activity, AllocationOptions} from "ecco-rota";
import * as React from "react";
import {FC, useEffect, useState} from "react";
import * as ReactDOM from "react-dom";
import {useDemandSchedule, useServicesContext} from "ecco-components";
import {createButtonGroup, link} from "ecco-components-core";
import {calendarDaysAsMap, daysMapToJavaDayArray, ShortDayName} from 'ecco-dto';
import {mountWithServices} from "../../offline/ServicesContextProvider";
import {DatePickerEccoDate} from "@eccosolutions/ecco-mui-controls";

const mountPoint = document.createElement("div");

export const AllocationOptionsDialog: FC<{ onSubmit: (
        result: AllocationOptions) => void,
        activity: Activity
    }> = ({onSubmit, activity}) => {
    const {schedule} = useDemandSchedule(activity.getAgreementId(), activity.getScheduleId());
    const {sessionData} = useServicesContext();
    const [untilDate, setUntilDate] = useState<EccoDate | null>(null);
    const [maxUntilDate, setMaxUntilDate] = useState<EccoDate | null>(null);
    const [days, setDays] = useState({} as Record<ShortDayName, boolean>);
    useEffect(() => {
            if (schedule && sessionData) {
                setDays(calendarDaysAsMap(schedule.calendarDays)); // Only show the options that are available
                const end = EccoDate.parseIso8601(schedule.end);
                setUntilDate(end);
                setMaxUntilDate(end);
            }
        },
      [schedule, sessionData]);

    return <Dialog
        className="m-ui"
        open={true}
        aria-labelledby="dialog-title"
        // aria-describedby="dialog-description"
    >
        <DialogTitle id="dialog-title">allocate</DialogTitle>
        <DialogContent>
            <Grid container justify="center">
                <Grid item xs={12} sm={10}>
                    <DatePickerEccoDate
                        label="allocate until"
                        minDate={activity.getStart().toEccoDate()}
                        maxDate={maxUntilDate}
                        value={untilDate}
                        onChange={date => setUntilDate(date)}
                    />
                </Grid>
                <Grid item xs={12} sm={10}>
                    {createButtonGroup("days", "days to allocate", days,
                        (key, state) => setDays({...days, [key]: state}))}
                </Grid>
            </Grid>
        </DialogContent>
        <DialogActions>
            {link("cancel", () => onSubmit(null))}
            <Button
                onClick={() => onSubmit({
                    untilDate,
                    days: daysMapToJavaDayArray(days)
                })}
                color="primary"
            >
                allocate
            </Button>
        </DialogActions>
    </Dialog>;
};

export function getAllocationParams(activity: Activity): Promise<AllocationOptions | null> {
    return new Promise<AllocationOptions>(resolve => {
        const form = <AllocationOptionsDialog
            activity={activity}
            onSubmit={options => {
                resolve(options);
                ReactDOM.unmountComponentAtNode(mountPoint);
            }}
        />;
        // we don't want any buttons, but to achieve that we need to be DialogContent adapter
        mountWithServices(form, mountPoint);
    });
}
