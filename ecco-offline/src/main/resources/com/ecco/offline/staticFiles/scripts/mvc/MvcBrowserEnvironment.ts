import MvcUtils = require("./MvcUtils");
import URI = require("URI");

class MvcBrowserEnvironment {
    private window:Window;

    constructor(window:Window) {
        this.window = window;
    }

    public location():URI.URI {
        return URI(this.window.location.href);
    }

    private doNavigate(uri?:URI.URI, title?:string, body?:string, isReplace?:boolean) {
        if (!uri) {
            uri = this.location();
        } else {
            uri = URI(uri).absoluteTo(this.location().query("").fragment(""));
        }

        if (body) {
            if (title) {
                this.window.document.title = title;
            }
            this.window.document.body.innerHTML = String(body);

            if (isReplace) {
                this.window.history.replaceState(null, title, uri.toString());
            } else {
                this.window.history.pushState(null, title, uri.toString());
            }
        } else if (isReplace) {
            this.window.location.replace(uri.toString());
        } else {
            this.window.location.href = uri.toString();
        }
    }

    public replace(uri?:URI.URI, title?:string, body?:string) {
        this.doNavigate(uri, title, body, true);
    }

    public navigateStr(url:string, title?:string, body?:string) {
        this.doNavigate(URI(url), title, body, false);
    }

    public navigate(uri:URI.URI, title?:string, body?:string) {
        this.doNavigate(uri, title, body, false);
    }

    /** Redirect to a path relative to applicationRootPath */
    public redirectInternal(relPath: string) {
        var targetUriUri = URI(String(relPath));
        if (targetUriUri.scheme() || targetUriUri.authority() || targetUriUri.query() || targetUriUri.fragment()) {
            throw new Error("illegal relative path: " + relPath);
        }
        targetUriUri = URI(".").
                segment(targetUriUri.segment()).
                absoluteTo(MvcUtils.getApplicationRootUri());
        this.replace(targetUriUri);
    }
}

export = MvcBrowserEnvironment;