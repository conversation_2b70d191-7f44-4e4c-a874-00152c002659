/** A representation of an ldap as understood by ecco.
 *
 * This interface must match the Java class LdapGroupAggregate. */
export interface LdapGroupAggregateDto {

    /** The AD group */
    ldapGroup: string;

    /** Optional ecco roles (group security) that the AD group maps to */
    localGroups: string[];

    /** Optional ecco acl (id/class security) that the AD group maps to  */
    localAcls: LdapGroupLocalAcl[];
}

export class AclDomains {
    public static SERVICE: string = "com.ecco.dom.Service";
    public static PROJECT: string = "com.ecco.dom.Project";
}

export interface LdapGroupLocalAcl {

    /**
     * The id of the entity to secure
     */
    localId: number;
    /**
     * The entity class name to secure
     */
    localClass: string;
}
