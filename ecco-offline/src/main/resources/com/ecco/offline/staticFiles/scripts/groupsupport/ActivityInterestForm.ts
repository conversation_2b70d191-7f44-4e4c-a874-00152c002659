import $ = require("jquery");
import BaseCommandEmittingForm = require("../controls/BaseCommandEmittingForm");
import CheckboxList = require("../controls/CheckboxList");
import services = require("ecco-offline-data");
import {StringToObjectMap} from "@eccosolutions/ecco-common";
import {ActivityInterestChangeCommand} from "ecco-commands";
import {apiClient, getGlobalEccoAPI} from "ecco-components";
import {EvidenceGroup, ServiceRecipientWithEntities} from "ecco-dto";
import {SupportAction} from "ecco-dto/evidence-dto";
import {ActivityType} from "ecco-dto/service-config-dto";
import {SupportSmartStepsSnapshot} from "ecco-evidence";
import {GroupSupportActivityTypeAjaxRepository} from "ecco-dto";
import {showFormInModalDom} from "../components/MUIConverterUtils";

var activityTypeRepository = new GroupSupportActivityTypeAjaxRepository(apiClient);

interface ActivityTypeWithActions {activityType: ActivityType; actions: SupportAction[];}

class ActivityInterestForm extends BaseCommandEmittingForm {

    public static showInModalByIds(serviceRecipientId: number, title: string) {
        services.getReferralRepository().findOneServiceRecipientWithEntities(serviceRecipientId).then(serviceRecipient => {
            showFormInModalDom(new ActivityInterestForm(serviceRecipient, title));
        });

    }

    private $activitySubForm = $("<div>");

    constructor(private serviceRecipient: ServiceRecipientWithEntities, title: string) {

        super(title);

        this.disableSubmit();

        services.getSupportSmartStepsSnapshotRepository().findSupportSmartStepsSnapshotByServiceRecipientIdAndEvidenceGroup(
                serviceRecipient.serviceRecipientId, EvidenceGroup.needs.name)
            .then( dto => {
                this.onSnapshotLoaded( new SupportSmartStepsSnapshot(this.serviceRecipient.configResolver.getServiceType(), dto) );
            });

        this.form.append(this.$activitySubForm);
    }

    private onSnapshotLoaded(snapshot: SupportSmartStepsSnapshot) {
        const messages = getGlobalEccoAPI().sessionData.getMessages()

        // Create control and kick off request to populate it with associatedActivities
        var activitiesControl = new CheckboxList((id, checked) => this.activityChange(id, checked));
        activitiesControl.element().addClass("input-group");

        // Get a map of activities that are relevant and the actions that relate to them
        let activitiesWithActions = this.getRelevantActivities(snapshot);

        activityTypeRepository.findActivityInterestsByServiceRecipientId(this.serviceRecipient.serviceRecipientId)
            .then(activityInterests => {
                activitiesControl.populateFromList(activitiesWithActions.sort((a, b) => a.activityType.name.localeCompare(b.activityType.name)),
                    (atwa: ActivityTypeWithActions) => ({key: atwa.activityType.id.toString(), value: atwa.activityType.name}),
                    activityInterests.map(activity => activity.id.toString()));
            });

        this.$activitySubForm
            .empty()
            .addClass("alert alert-info")
            .append(
                $("<h4>")
                    .text("Activities for identified " + messages["terminology.smartstep"] + "(s)"))
            .append(
                $("<p>")
                    .text("The following activities are relevant to the " + messages["terminology.smartstep"] + "(s) "
                        + "identified. Please select any that you are interested in attending:"))
            .append(activitiesControl.element());
        this.enableSubmit();
    }

    // This is a candidate for moving to EvidenceSnapshot - if it had access to a list of all actions by id
    private getRelevantActivities(snapshot: SupportSmartStepsSnapshot) {
        let activitiesAndActionsByActivityType: StringToObjectMap<ActivityTypeWithActions> = {};
        snapshot.getAllActiveActions()
            .forEach(action => {
                let actionFromConfig = this.serviceRecipient.configResolver.getServiceType().getActionById(action.actionId);
                if (actionFromConfig) {
                    actionFromConfig.activityTypes.forEach(type => {
                        if (!activitiesAndActionsByActivityType[type.id]) {
                            activitiesAndActionsByActivityType[type.id] = {activityType: type, actions: []};
                        }
                        activitiesAndActionsByActivityType[type.id].actions.push(action);
                    });
                }
            });
        return Object.values<ActivityTypeWithActions>(activitiesAndActionsByActivityType);
    }

    /** Add an activity change against the appropriate/original referral - parent possibly */
    private activityChange(id: string, checked: boolean) {
        var cmd = new ActivityInterestChangeCommand(
            checked ? "add" : "remove",
            this.serviceRecipient.serviceRecipientId,
            parseInt(id));
        this.queueCommand(cmd);
        this.enableSubmit();
    }
}
export = ActivityInterestForm;