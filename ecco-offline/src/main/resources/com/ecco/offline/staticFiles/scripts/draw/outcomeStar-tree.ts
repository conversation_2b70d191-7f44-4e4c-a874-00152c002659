import raphael = require("raphael");

import {Sector, Vector2d} from "ecco-math";

import {Debounce} from "@eccosolutions/ecco-common";

import {
    DynamicTreeControl, ContextNodeChangedEvent, DynamicTreeNode, ChildAddedEvent,
    ChildRemovedEvent, NodeChangeEvent, DynamicTreeNodeStyle, DynamicTreeEdgeStyle, RedrawEvent
} from "./dynamic-tree";

export class OutcomeStarTreeControl extends DynamicTreeControl {

    private graphic: OutcomeStarTreeGraphic; // only needed so we can redraw exactly what we have (for debugging)
    private paperClone: raphael.RaphaelPaper; // for redraw, which we are using for debugging

    constructor(width: number, height: number, private useOfficialOutcomeStar: boolean) {
        super(width, height);
    }

    protected override createGraphicAndAttachPaper(paper : raphael.RaphaelPaper) {
        this.paperClone = paper;
        this.graphic = new OutcomeStarTreeGraphic(this, this.useOfficialOutcomeStar);
        this.graphic.attach(paper);

        // override dynamic-tree css moving up 20px which blocks the redraw icon
        this.element().css("margin-top", "0px");
    }

    public redraw() {
        this.paperClone.clear(); // clear so we can see things redraw
        this.graphic.attach(this.paperClone);
    }

    public isValid(): boolean {
        return true;
    }

}

/**
 * OutcomeStarTreeGraphic is largely a copy of DynamicTreeGraphic.
 * TODO refactoring opportunities may come later
 */
class OutcomeStarTreeGraphic {
    private paper: raphael.RaphaelPaper;

    private serviceRecipientNodeGraphic: ServiceRecipientNodeGraphic;

    private destroyed = false;

    constructor(private control: OutcomeStarTreeControl, private useOfficialOutcomeStar: boolean) {
        if (!control) {
            throw new TypeError("Argument 'control' must not be null.");
        }

        // If you add more event handlers here, be sure to remove them in
        // this.destroy().
        control.addContextNodeChangedEventHandler(this.onContextNodeChanged);

        this.attachContextNodeGraphic(control.getContextNode());
    }

    public attach(paper: raphael.RaphaelPaper) {
        this.paper = paper;

        if (this.serviceRecipientNodeGraphic) {
            var position = new Vector2d(0, 0);
            this.serviceRecipientNodeGraphic.attach(paper, position, null);
        }
    }

    private onContextNodeChanged = (contextNodeChangedEvent: ContextNodeChangedEvent): void => {
        this.attachContextNodeGraphic(contextNodeChangedEvent.getNewContextNode());
    };

    private attachContextNodeGraphic(newContextNode: DynamicTreeNode): void {
        if (this.serviceRecipientNodeGraphic) {
            this.serviceRecipientNodeGraphic.destroy();
        }

        if (newContextNode) {
            this.serviceRecipientNodeGraphic = new ServiceRecipientNodeGraphic(newContextNode, this.useOfficialOutcomeStar);
            var position = new Vector2d(0, 0);
            this.serviceRecipientNodeGraphic.attach(this.paper, position);

        } else {
            this.serviceRecipientNodeGraphic = null;
        }
    }

    public destroy(): void {
        if (this.destroyed) {
            return;
        }

        this.control.removeContextNodeChangedEventHandler(this.onContextNodeChanged);

        if (this.serviceRecipientNodeGraphic) {
            this.serviceRecipientNodeGraphic.destroy();
        }

        this.destroyed = true;
    }
}

var defaultBounds = new Sector(Math.PI, Math.PI * 3);

interface OutcomeStarTreeStyle {
    rootStyle: DynamicTreeNodeStyle;
    rootEdgeStyle: DynamicTreeEdgeStyle; // specify the line of the arms
    armStyle: DynamicTreeNodeStyle;
    scoreStyle: DynamicTreeNodeStyle;
    intersectionLineStyle: DynamicTreeEdgeStyle; // the lines between the scores
    outcomeStarStyle: DynamicTreeNodeStyle; // the bit that does the actual star
}

var defaultStarTreeStyle: OutcomeStarTreeStyle = {
    rootStyle: {
        radius: 32,
        strokeColor: "#3c83ca",
        strokeWidth: 1,
        fillColor: "#f4f4f4",
        highlightFillColor: "#f4f4f4", // unused
        fontSize: 12
    },
    rootEdgeStyle: {
        length: 280, // specify the length of the arms
        strokeColor: "#a2b7d8", // colour of the arm lines
        strokeWidth: 5
    },
    armStyle: {
        radius: 48,
        strokeColor: "#3c83ca",
        strokeWidth: 1,
        fillColor: "#f4f4f4",
        highlightFillColor: "#f4f4f4", // unused
        fontSize: 12
    },
    scoreStyle: {
        radius: 10,
        strokeColor: "#3c83ca",
        strokeWidth: 1,
        fillColor: "#f4f4f4",
        highlightFillColor: "#dddddd",
        fontSize: 12
    },
    intersectionLineStyle: {
        length: 0, // not used
        strokeColor: "#a2b7d8", // colour of the line
        strokeWidth: 5
    },
    outcomeStarStyle: {
        radius: 0, //unused
        strokeColor: "#aaaaaa",
        strokeWidth: 2,
        fillColor: "#dddddd",
        highlightFillColor: "#dddddd", // unused
        fontSize: 12 // unused
    }
};

// with the star we don't want strokes
var outcomeStarTreeStyle: OutcomeStarTreeStyle = JSON.parse(JSON.stringify(defaultStarTreeStyle));
outcomeStarTreeStyle.rootStyle.strokeWidth = 0;
outcomeStarTreeStyle.rootEdgeStyle.strokeWidth = 0;
outcomeStarTreeStyle.scoreStyle.strokeWidth = 0;


class ServiceRecipientNodeGraphic {
    private paper: raphael.RaphaelPaper;

    private position = new Vector2d(0, 0);
    private bounds: Sector = defaultBounds;

    private outcomeStarArmNodes = new Array<DynamicTreeNode>();
    private outcomeStarArmNodeGraphics = new Array<OutcomeStarArmNodeGraphic>();

    private style: OutcomeStarTreeStyle;

    private nodeSet: raphael.RaphaelSet;
    private edgeSet: raphael.RaphaelSet;
    private captionElement: raphael.RaphaelElement;
    private intersectionLine: raphael.RaphaelSet;
    private progressIntersectionLine: raphael.RaphaelSet;

    private destroyed = false;

    constructor(private node: DynamicTreeNode, private useOfficialOutcomeStar: boolean) {
        if (!node) {
            throw new TypeError("Argument 'node' must not be null.");
        }

        this.style = useOfficialOutcomeStar ? outcomeStarTreeStyle : defaultStarTreeStyle;

        // If you add more event handlers here, be sure to remove them in
        // this.destroy().
        node.addChangeEventHandler(this.onChange);
        node.addChildAddedEventHandler(this.onChildAdded);
        node.addChildRemovedEventHandler(this.onChildRemoved);
        node.addRedrawEventHandler(this.onRedraw);

        this.addChildGraphics(node.getChildren());
    }

    public attach(paper: raphael.RaphaelPaper, position: Vector2d, bounds?: Sector): void {
        if (!position) {
            throw new TypeError("Argument 'position' must not be null.");
        }

        this.paper = paper;
        this.position = position;
        this.bounds = bounds || defaultBounds;

        if (this.nodeSet) {
            this.nodeSet.remove();
        }

        if (paper) {
            paper.setStart();

            var x = this.position.getX();
            var y = this.position.getY();

            paper.circle(x, y, this.style.rootStyle.radius)
                .attr("stroke", this.style.rootStyle.strokeColor)
                .attr("stroke-width", this.style.rootStyle.strokeWidth)
                .attr("fill", this.style.rootStyle.fillColor);

            // this.captionElement = paper.text(x, y, this.node.getCaption())
            //     .attr("font-size", this.style.rootStyle.fontSize);

            this.nodeSet = paper.setFinish()
                .click(this.onClick);
        } else {
            this.nodeSet = null;
            this.captionElement = null;
            this.intersectionLine = null;
        }

        this.attachChildGraphics();
    }

    public detach(): void {
        this.attach(null, this.position, this.bounds);
    }

    private onChildAdded = (event: ChildAddedEvent): void => {
        this.addChildGraphic(event.getChild());
    };

    private addChildGraphic(child: DynamicTreeNode): void {
        this.addChildGraphics([child]);
    }

    private addChildGraphics(outcomeStarArmNodes: DynamicTreeNode[]): void {
        if (this.destroyed) {
            return;
        }

        for (var i = 0; i < outcomeStarArmNodes.length; ++i) {
            var outcomeStarNode = outcomeStarArmNodes[i];

            var outcomeStarNodeGraphic = new OutcomeStarArmNodeGraphic(outcomeStarNode, this.useOfficialOutcomeStar);

            this.outcomeStarArmNodes.push(outcomeStarNode);
            this.outcomeStarArmNodeGraphics.push(outcomeStarNodeGraphic);
        }

        this.attachChildGraphics();
    }

    private onChildRemoved = (event: ChildRemovedEvent): void => {
        this.removeChildGraphic(event.getChild());
    };

    private removeChildGraphic(outcomeStarArmNode: DynamicTreeNode): void {
        if (this.destroyed) {
            return;
        }

        var outcomeStarArmNodeIndex = this.outcomeStarArmNodes.indexOf(outcomeStarArmNode);

        if (outcomeStarArmNodeIndex >= 0) {
            this.outcomeStarArmNodeGraphics[outcomeStarArmNodeIndex].destroy();
            this.outcomeStarArmNodes.splice(outcomeStarArmNodeIndex, 1);
            this.outcomeStarArmNodeGraphics.splice(outcomeStarArmNodeIndex, 1);
        }

        this.attachChildGraphics();
    }

    private onChange = (event: NodeChangeEvent): void => {
        if (this.destroyed) {
            return;
        }

        this.captionElement.attr("text", event.getNode().getCaption());
    };

    /**
     * RedrawEvent only used to redraw a portion of the star - the intersectionLines
     * because each node has already redrawn iteself as appropriate. We're using this
     * event as a global trigger.
     */
    private onRedraw = (event: RedrawEvent): void => {
        this.updateIntersectionLine();
        this.updateProgressIntersectionLine();
    };

    /**
     * Redraw the line between score nodes.
     * This is invoked when adding a node (through attachChildGraphics).
     * Also invoked from a redraw event.
     * Also invoked from a NodeChangeEvent which is triggered from setHighlighted (in OutcomeStarScoreNodeWrapper)
     */
    private updateIntersectionLine() {
        if (this.intersectionLine) {
            this.intersectionLine.remove();
            this.intersectionLine = null;
        }

        let matchScoreNode = ((arm: OutcomeStarArmNodeGraphic) => {
            return arm.getHighlightedOutcomeStarScoreNodeGraphic();
        });
        this.intersectionLine = this.drawIntersectionLine(matchScoreNode,
            this.style.intersectionLineStyle.strokeColor,
            this.style.intersectionLineStyle.strokeWidth);
    }

    /**
     * Redraw the progress line between score nodes.
     * Invoked from a redraw event.
     * @see updateIntersectionLine
     */
    private updateProgressIntersectionLine() {

        if (this.progressIntersectionLine) {
            this.progressIntersectionLine.remove();
            this.progressIntersectionLine = null;
        }

        let matchScoreNode = ((arm: OutcomeStarArmNodeGraphic) => {
            return arm.getProgressOutcomeStarScoreNodeGraphic();
        });
        this.progressIntersectionLine = this.drawIntersectionLine(matchScoreNode,
            "#33aa33", // this.style.intersectionLineStyle.strokeColor,
            1); //this.style.intersectionLineStyle.strokeWidth);
    }

    private drawIntersectionLine(matchScoreNode: (arm: OutcomeStarArmNodeGraphic) => OutcomeStarScoreNodeGraphic,
                strokeColor: string,
                strokeWidth: number): raphael.RaphaelSet {

        let intersectionPathBuilder = "";
        this.outcomeStarArmNodeGraphics.forEach(arm => {

            let score: OutcomeStarScoreNodeGraphic = matchScoreNode(arm);
            if (score) {
                if (!intersectionPathBuilder) {
                    intersectionPathBuilder = "M " + score.getPosition().getX().toFixed(1) + " " + score.getPosition().getY().toFixed(1);
                } else {
                    intersectionPathBuilder += " L " + score.getPosition().getX().toFixed(1) + " " + score.getPosition().getY().toFixed(1);
                }
            }
        });

        if (intersectionPathBuilder) {

            // join the ends together
            intersectionPathBuilder += " z";

            this.paper.setStart();

            this.paper.path(intersectionPathBuilder)
                .attr("stroke", strokeColor)
                .attr("stroke-width", strokeWidth)
                .toFront(); // z-index

            return this.paper.setFinish();
        }

        return null;
    }

    private onClick = (): void => {
        if (Debounce.instance.shouldHandle()) {
            this.node.click();
        }
    };

    private attachChildGraphics(): void {
        if (this.destroyed) {
            throw new Error("ServiceRecipientNodeGraphic is destroyed.");
        }

        if (this.edgeSet) {
            this.edgeSet.remove();
            this.edgeSet = null;
        }

        // True if we are attaching graphics. False if we are detaching them.
        var attaching = !!this.paper;
            //&& (this.style.maxDepth == null || this.depth < this.style.maxDepth);

        if (attaching) {
            this.edgeSet = this.paper.set();

            var anglePerChild = (this.bounds.getEndAngle() - this.bounds.getStartAngle()) / this.outcomeStarArmNodeGraphics.length;

            let starOutlinePathBuilder = "";

            for (var i = 0; i < this.outcomeStarArmNodeGraphics.length; ++i) {
                var boundsStartAngle = this.bounds.getStartAngle() + i * anglePerChild;
                //var boundsEndAngle = boundsStartAngle + anglePerChild;
                //var bounds = new Sector(boundsStartAngle, boundsEndAngle);
                var childAngle = boundsStartAngle + anglePerChild / 2;
                var nodePosition = this.position.add(Vector2d.polar(childAngle, this.style.rootEdgeStyle.length + this.style.rootStyle.radius));

                var armStartPosition = this.position.add(Vector2d.polar(childAngle, this.style.rootStyle.radius));
                let lengthOfArmLineToFitNodesIn = this.style.rootEdgeStyle.length - this.style.rootStyle.radius - this.style.scoreStyle.radius * 2; // top and bottom

                // draw a line down the arm
                if (!this.useOfficialOutcomeStar) {
                    var armEndPosition = this.position.add(Vector2d.polar(childAngle, this.style.rootStyle.radius + lengthOfArmLineToFitNodesIn));
                    var armPathDefinition = "M " + armStartPosition.getX().toFixed(1) + " " + armStartPosition.getY().toFixed(1)
                        + " L " + armEndPosition.getX().toFixed(1) + " " + armEndPosition.getY().toFixed(1);
                    this.edgeSet.push(this.paper.path(armPathDefinition)
                        .attr("stroke", this.style.rootEdgeStyle.strokeColor)
                        .attr("stroke-width", this.style.rootEdgeStyle.strokeWidth));
                }

                // seems right, rather than being exactly accurate
                let edgeDippedLeftPosition = this.position.add(Vector2d.polar(childAngle - (anglePerChild / 2), this.style.rootStyle.radius + (lengthOfArmLineToFitNodesIn / 2) - 20));
                if (i == 0) {
                    starOutlinePathBuilder = "M " + edgeDippedLeftPosition.getX().toFixed(1) + " " + edgeDippedLeftPosition.getY().toFixed(1);
                }
                //let edgeNodePosition = this.position.add(Vector2d.polar(childAngle, this.style.rootStyle.radius + lengthOfArmLineToFitNodesIn));
                starOutlinePathBuilder += " L " + nodePosition.getX().toFixed(1) + " " + nodePosition.getY().toFixed(1);
                let edgeDippedRightPosition = this.position.add(Vector2d.polar(childAngle + (anglePerChild / 2), this.style.rootStyle.radius + (lengthOfArmLineToFitNodesIn / 2) - 20));
                starOutlinePathBuilder += " L " + edgeDippedRightPosition.getX().toFixed(1) + " " + edgeDippedRightPosition.getY().toFixed(1);

                // we had bounds when nodes were drawn at a distance
                //this.outcomeStarArmNodeGraphics[i].attach(this.paper, nodePosition, bounds);
                this.outcomeStarArmNodeGraphics[i].attach(this.paper, armStartPosition, childAngle, lengthOfArmLineToFitNodesIn);
            }

            if (this.useOfficialOutcomeStar) {
                this.edgeSet.push(this.paper.path(starOutlinePathBuilder)
                    .attr("stroke", this.style.outcomeStarStyle.strokeColor)
                    .attr("stroke-width", this.style.outcomeStarStyle.strokeWidth)
                    .attr("fill", this.style.outcomeStarStyle.fillColor)
                    .toBack());
            }

        } else {
            for (var i = 0; i < this.outcomeStarArmNodeGraphics.length; ++i) {
                this.outcomeStarArmNodeGraphics[i].detach();
            }
        }

        this.updateIntersectionLine();
    }

    public destroy(): void {
        if (this.destroyed) {
            return;
        }

        this.detach();

        this.node.removeChildAddedEventHandler(this.onChildAdded);
        this.node.removeChildRemovedEventHandler(this.onChildRemoved);
        this.node.removeChangeEventHandler(this.onChange);
        this.node.removeRedrawEventHandler(this.onRedraw);

        for (var i = 0; i < this.outcomeStarArmNodeGraphics.length; ++i) {
            this.outcomeStarArmNodeGraphics[i].destroy();
        }

        this.destroyed = true;
    }
}


class OutcomeStarArmNodeGraphic {
    private paper: raphael.RaphaelPaper;

    private startPosition: Vector2d;
    private armLength: number;
    private armAngle: number;

    private scoreNodes = new Array<DynamicTreeNode>();
    private scoreNodeGraphics = new Array<OutcomeStarScoreNodeGraphic>();

    private style: OutcomeStarTreeStyle;

    private nodeSet: raphael.RaphaelSet;
    private captionElement: raphael.RaphaelElement;

    private destroyed = false;

    constructor(private node: DynamicTreeNode, private useOfficialOutcomeStar: boolean) {
        if (!node) {
            throw new TypeError("Argument 'node' must not be null.");
        }

        this.style = useOfficialOutcomeStar ? outcomeStarTreeStyle : defaultStarTreeStyle;

        // If you add more event handlers here, be sure to remove them in
        // this.destroy().
        node.addChangeEventHandler(this.onChange);
        node.addChildAddedEventHandler(this.onChildAdded);
        node.addChildRemovedEventHandler(this.onChildRemoved);

        this.addChildGraphics(node.getChildren());
    }

    public attach(paper: raphael.RaphaelPaper, startPosition: Vector2d, armAngle: number, armLength: number): void {
        this.paper = paper;
        this.startPosition = startPosition;
        this.armAngle = armAngle;
        this.armLength = armLength;

        if (this.nodeSet) {
            this.nodeSet.remove();
        }

        if (paper) {
            paper.setStart();

            let distance = this.useOfficialOutcomeStar ? this.style.rootEdgeStyle.length + this.style.armStyle.radius + 10
                                                       : armLength + this.style.armStyle.radius + 10;
            var nodePosition = startPosition.add(Vector2d.polar(armAngle, distance));
            var x = nodePosition.getX();
            var y = nodePosition.getY();

            paper.rect(x - this.style.armStyle.radius, y - (this.style.armStyle.radius / 2), this.style.armStyle.radius * 2, this.style.armStyle.radius, 30)
                .attr("stroke", this.style.armStyle.strokeColor)
                .attr("stroke-width", this.style.armStyle.strokeWidth)
                .attr("fill", this.style.armStyle.fillColor);

            this.captionElement = paper.text(x, y, this.node.getCaption())
                .attr("font-size", this.style.armStyle.fontSize);

            this.nodeSet = paper.setFinish()
                .click(this.onClick);

        } else {
            this.nodeSet = null;
            this.captionElement = null;
        }

        this.attachChildGraphics();
    }

    public getHighlightedOutcomeStarScoreNodeGraphic(): OutcomeStarScoreNodeGraphic {
        let scoreOut: OutcomeStarScoreNodeGraphic = null;
        this.scoreNodeGraphics.forEach(score => {
            if (score.isHighlighted()) {
                scoreOut = score;
            }

        });
        return scoreOut;
    }

    public getProgressOutcomeStarScoreNodeGraphic(): OutcomeStarScoreNodeGraphic {
        let scoreOut: OutcomeStarScoreNodeGraphic = null;
        this.scoreNodeGraphics.forEach(score => {
            if (score.getProperty("progress") == "true") {
                scoreOut = score;
            }

        });
        return scoreOut;
    }

    public detach(): void {
        this.attach(null, this.startPosition, this.armAngle, this.armLength);
    }

    private onChildAdded = (event: ChildAddedEvent): void => {
        this.addChildGraphic(event.getChild());
    };

    private addChildGraphic(child: DynamicTreeNode): void {
        this.addChildGraphics([child]);
    }

    private addChildGraphics(childNodes: DynamicTreeNode[]): void {
        if (this.destroyed) {
            return;
        }

        for (var i = 0; i < childNodes.length; ++i) {
            var child = childNodes[i];

            var childGraphic = new OutcomeStarScoreNodeGraphic(child, this.useOfficialOutcomeStar);

            this.scoreNodes.push(child);
            this.scoreNodeGraphics.push(childGraphic);
        }

        this.attachChildGraphics();
    }

    private attachChildGraphics(): void {
        if (this.destroyed) {
            throw new Error("OutcomeStarArmNodeGraphic is destroyed.");
        }

        // True if we are attaching graphics. False if we are detaching them.
        var attaching = !!this.paper;
            //&& (this.style.maxDepth == null || this.depth < this.style.maxDepth);

        if (attaching) {
            let distancePerChild = this.armLength / this.scoreNodeGraphics.length;

            for (var i = 0; i < this.scoreNodeGraphics.length; ++i) {
                let distanceThisChild = (distancePerChild * i);
                let nodePosition = this.startPosition.add(Vector2d.polar(this.armAngle,
                                                // squash the scores in a bit by starting with a gap of a radius
                                                distanceThisChild + this.style.scoreStyle.radius*2));

                //this.scoreNodeGraphics[i].attach(this.paper, nodePosition, bounds);
                this.scoreNodeGraphics[i].attach(this.paper, nodePosition);
            }

        } else {
            for (var i = 0; i < this.scoreNodeGraphics.length; ++i) {
                this.scoreNodeGraphics[i].detach();
            }
        }
    }

    private onChildRemoved = (event: ChildRemovedEvent): void => {
        this.removeChildGraphic(event.getChild());
    };

    private removeChildGraphic(outcomeStarScoreNode: DynamicTreeNode): void {
        if (this.destroyed) {
            return;
        }

        var outcomeStarArmNodeIndex = this.scoreNodes.indexOf(outcomeStarScoreNode);

        if (outcomeStarArmNodeIndex >= 0) {
            this.scoreNodeGraphics[outcomeStarArmNodeIndex].destroy();
            this.scoreNodes.splice(outcomeStarArmNodeIndex, 1);
            this.scoreNodeGraphics.splice(outcomeStarArmNodeIndex, 1);
        }

        this.attachChildGraphics();
    }

    private onChange = (event: NodeChangeEvent): void => {
        if (this.destroyed) {
            return;
        }

        this.captionElement.attr("text", event.getNode().getCaption());
    };

    private onClick = (): void => {
        if (Debounce.instance.shouldHandle()) {
            this.node.click();
        }
    };

    public destroy(): void {
        if (this.destroyed) {
            return;
        }

        this.detach();

        this.node.removeChangeEventHandler(this.onChange);
        this.node.removeChildAddedEventHandler(this.onChildAdded);
        this.node.removeChildRemovedEventHandler(this.onChildRemoved);

        for (var i = 0; i < this.scoreNodeGraphics.length; ++i) {
            this.scoreNodeGraphics[i].destroy();
        }

        this.destroyed = true;
    }
}


class OutcomeStarScoreNodeGraphic {
    private paper: raphael.RaphaelPaper;

    private position: Vector2d;

    private style: OutcomeStarTreeStyle;

    private nodeSet: raphael.RaphaelSet;
    private circleElement: raphael.RaphaelElement;
    private captionElement: raphael.RaphaelElement;

    private destroyed = false;

    constructor(private node: DynamicTreeNode, private useOfficialOutcomeStar: boolean) {
        if (!node) {
            throw new TypeError("Argument 'node' must not be null.");
        }

        this.style = useOfficialOutcomeStar ? outcomeStarTreeStyle : defaultStarTreeStyle;

        // If you add more event handlers here, be sure to remove them in
        // this.destroy().
        node.addChangeEventHandler(this.onChange);
    }

    public attach(paper: raphael.RaphaelPaper, position: Vector2d): void {
        this.paper = paper;
        this.position = position;

        if (this.nodeSet) {
            this.nodeSet.remove();
        }

        if (paper) {
            paper.setStart();

            var x = this.position.getX();
            var y = this.position.getY();

            this.circleElement = paper.circle(x, y, this.style.scoreStyle.radius)
                .attr("stroke", this.style.scoreStyle.strokeColor)
                .attr("stroke-width", this.style.scoreStyle.strokeWidth)
                .attr("fill", this.fillColor());

            this.captionElement = paper.text(x, y, this.node.getCaption())
                .attr("font-size", this.style.scoreStyle.fontSize);

            this.nodeSet = paper.setFinish()
                .click(this.onClick);
        } else {
            this.nodeSet = null;
            this.circleElement = null;
            this.captionElement = null;
        }
    }

    public getPosition(): Vector2d {
        return this.position;
    }

    public isHighlighted() {
        return this.node.isHighlighted();
    }

    public getProperty(key: string) {
        return this.node.getProperty(key);
    }

    private fillColor() {
        return this.node.isHighlighted()
            ? this.style.scoreStyle.highlightFillColor
            : this.style.scoreStyle.fillColor;
    }

    public detach(): void {
        this.attach(null, this.position);
    }

    private onChange = (event: NodeChangeEvent): void => {
        if (this.destroyed) {
            return;
        }

        this.circleElement.attr("fill", this.fillColor());
        this.captionElement.attr("text", event.getNode().getCaption());
    };

    private onClick = (): void => {
        if (Debounce.instance.shouldHandle()) {
            this.node.click();
        }
    };

    public destroy(): void {
        if (this.destroyed) {
            return;
        }

        this.detach();

        this.node.removeChangeEventHandler(this.onChange);

        this.destroyed = true;
    }
}
