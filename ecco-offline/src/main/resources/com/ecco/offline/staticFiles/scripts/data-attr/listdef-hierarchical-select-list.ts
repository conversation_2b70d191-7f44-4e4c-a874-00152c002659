import $ = require("jquery");
import ListDefHierarchicalSelectList = require("./ListDefHierarchicalSelectList");
import {apiClient} from "ecco-components";
import {SessionDataAjaxRepository} from "ecco-dto";

var repositoryDefault = new SessionDataAjaxRepository(apiClient);

/**
 * Find select list elements with data-select-list-url attribute, and populate from that list.
 */
class Enhancer {

    constructor() {
    }

    /** Find the items we support and attach the appropriate component to them */
    public attach() {
        new ListDefHierarchicalSelectList($(".listdef-hierarchical-select-list"), repositoryDefault).load();
    }
}


var enhancer = new Enhancer();
enhancer.attach();

