import {EccoDate} from "@eccosolutions/ecco-common";
import * as React from "react";
import {FC} from "react";
import {
    Avatar,
    Button,
    Card,
    CardActions,
    CardContent,
    CardHeader,
    Grid,
    Table,
    TableCell,
    TableHead,
    TableRow,
    Typography
} from "@eccosolutions/ecco-mui";
import {useClient, useReferralBySrId, useServiceRecipient, useServicesContext} from "ecco-components";
import {link} from "ecco-components-core";
interface HMSDataSet {
    code: string,
    rentCheckDue: EccoDate,
    balance: number,
    balanceDifference: HMSHistory[],
    history: HMSHistory[]
}
interface HMSHistory {
    date: EccoDate,
    ref: string,
    unitRef: string,
    amount: number,
    amountType: string // slightly abused for now between charges and payment
}
const hmsDataSet1Day = EccoDate.todayLocalTime();
const hmsDataSet1: HMSDataSet = {
    code: "FINF005",
    rentCheckDue: hmsDataSet1Day.addMonths(6),
    balance: -25.16*2,
    balanceDifference: [
        {
            date: hmsDataSet1Day.subtractDays(7),
            ref: "HB-267661",
            unitRef: "Butterfly House: Room 12",
            amount: -25.16,
            amountType: 'top-up'
        },
        {
            date: hmsDataSet1Day.subtractDays(14),
            ref: "HB-264192",
            unitRef: "Butterfly House: Room 12",
            amount: -25.16,
            amountType: 'top-up'
        }
    ],
    history: [{
        date: EccoDate.todayLocalTime(), // matches oldest due, 21 days ago
        ref: "receipt-199",
        unitRef: "Butterfly House: Room 12",
        amount: 25.16,
        amountType: 'top-up'
    },{
        date: hmsDataSet1Day.subtractDays(7),
        ref: "CLN-BH09",
        unitRef: "Butterfly House: Room 12",
        amount: 1.55,
        amountType: 'clean [eligible]'
    },{
        date: hmsDataSet1Day.subtractDays(7),
        ref: "HB-267661",
        unitRef: "Butterfly House: Room 12",
        amount: 87.55,
        amountType: 'hb'
    },{
        date: hmsDataSet1Day.subtractDays(14),
        ref: "CLN-BH09",
        unitRef: "Butterfly House: Room 12",
        amount: 1.55,
        amountType: 'clean [eligible]'
    },{
        date: hmsDataSet1Day.subtractDays(14),
        ref: "HB-264192",
        unitRef: "Butterfly House: Room 12",
        amount: 87.55,
        amountType: 'hb'
    },{
        date: hmsDataSet1Day.subtractDays(21),
        ref: "CLN-BH09",
        unitRef: "Butterfly House: Room 12",
        amount: 1.55,
        amountType: 'clean [eligible]'
    },{
        date: hmsDataSet1Day.subtractDays(21),
        ref: "HB-261788",
        unitRef: "Butterfly House: Room 12",
        amount: 87.55,
        amountType: 'hb'
    },{
        date: hmsDataSet1Day.subtractDays(27),
        ref: "receipt-102",
        unitRef: "Butterfly House: Room 12",
        amount: 25.16,
        amountType: 'top-up'
    },{
        date: hmsDataSet1Day.subtractDays(28),
        ref: "CLN-BH09",
        unitRef: "Butterfly House: Room 12",
        amount: 1.55,
        amountType: 'clean [eligible]'
    },{
        date: hmsDataSet1Day.subtractDays(28),
        ref: "HB-258911",
        unitRef: "Butterfly House: Room 12",
        amount: 87.55,
        amountType: 'hb'
    },{
        date: hmsDataSet1Day.subtractDays(35),
        ref: "HB-255800",
        unitRef: "Butterfly House: Room 12",
        amount: 112.71,
        amountType: 'hb'
    },{
        date: hmsDataSet1Day.subtractDays(42),
        ref: "HB-249929",
        unitRef: "Butterfly House: Room 12",
        amount: 112.71,
        amountType: 'hb'
    },{
        date: hmsDataSet1Day.subtractDays(49),
        ref: "HB-218742",
        unitRef: "Butterfly House: Room 12",
        amount: 112.71,
        amountType: 'hb'
    }]
}
const hmsDataSet2Day = EccoDate.todayLocalTime().addDays(4);
const hmsDataSet2: HMSDataSet = {
    code: "FINF045",
    rentCheckDue: hmsDataSet2Day.addMonths(6),
    balance: 7.67,
    balanceDifference: [
        {
            date: hmsDataSet2Day.subtractDays(7),
            ref: "HB-367661",
            unitRef: "Haspalls Road: Room 4",
            amount: -6.33,
            amountType: 'utilities [ineligible]'
        },{
            date: hmsDataSet2Day.subtractDays(14),
            ref: "HB-364192",
            unitRef: "Haspalls Road: Room 4",
            amount: -6.33,
            amountType: 'utilities [ineligible]'
        },
        {
            date: hmsDataSet2Day.subtractDays(14),
            ref: "PERS",
            unitRef: "Haspalls Road: Room 4",
            amount: 20.33,
            amountType: 'sat tv [ineligible]'
        }
    ],
    history: [{
        date: hmsDataSet2Day.subtractDays(7),
        ref: "HB-367661",
        unitRef: "Haspalls Road: Room 4",
        amount: 87.55,
        amountType: 'hb'
    },{
        date: hmsDataSet2Day.subtractDays(14),
        ref: "HB-364192",
        unitRef: "Haspalls Road: Room 4",
        amount: 87.55,
        amountType: 'hb'
    },{
        date: hmsDataSet2Day.subtractDays(14),
        ref: "PERS",
        unitRef: "Haspalls Road: Room 4",
        amount: 20.33,
        amountType: 'sat tv [ineligible]'
    },{
        date: hmsDataSet2Day.subtractDays(14),
        ref: "PERS",
        unitRef: "Haspalls Road: Room 4",
        amount: 20.33,
        amountType: 'sat tv [ineligible]'
    },{
        date: hmsDataSet2Day.subtractDays(15),
        ref: "receipt-643",
        unitRef: "Haspalls Road: Room 4",
        amount: 6.33,
        amountType: 'utilities [ineligible]'
    },{
        date: hmsDataSet2Day.subtractDays(21),
        ref: "HB-361788",
        unitRef: "Haspalls Road: Room 4",
        amount: 87.55,
        amountType: 'hb'
    },{
        date: hmsDataSet2Day.subtractDays(22),
        ref: "receipt-589",
        unitRef: "Haspalls Road: Room 4",
        amount: 6.33,
        amountType: 'utilities [ineligible]'
    },{
        date: hmsDataSet2Day.subtractDays(28),
        ref: "HB-358911",
        unitRef: "Haspalls Road: Room 4",
        amount: 82.71,
        amountType: 'hb'
    },{
        date: hmsDataSet2Day.subtractDays(33),
        ref: "receipt-401",
        unitRef: "Haspalls Road: Room 4",
        amount: 6.33,
        amountType: 'utilities [ineligible]'
    },{
        date: hmsDataSet2Day.subtractDays(35),
        ref: "HB-255800",
        unitRef: "Haspalls Road: Room 4",
        amount: 82.71,
        amountType: 'hb'
    },{
        date: hmsDataSet2Day.subtractDays(38),
        ref: "receipt-322",
        unitRef: "Haspalls Road: Room 4",
        amount: 6.33,
        amountType: 'utilities [ineligible]'
    },{
        date: hmsDataSet2Day.subtractDays(42),
        ref: "HB-349929",
        unitRef: "Haspalls Road: Room 4",
        amount: 82.71,
        amountType: 'hb'
    },{
        date: hmsDataSet2Day.subtractDays(47),
        ref: "receipt-254",
        unitRef: "Haspalls Road: Room 4",
        amount: 6.33,
        amountType: 'utilities [ineligible]'
    },{
        date: hmsDataSet2Day.subtractDays(48),
        ref: "PERS",
        unitRef: "Haspalls Road: Room 4",
        amount: 20.33,
        amountType: 'sat tv [ineligible]'
    },{
        date: hmsDataSet2Day.subtractDays(49),
        ref: "HB-318742",
        unitRef: "Haspalls Road: Room 4",
        amount: 82.71,
        amountType: 'hb'
    }]
}
const hmsDataSets = [hmsDataSet1, hmsDataSet2];
const getHmsDataSet = (fileId: number) => hmsDataSets[fileId % hmsDataSets.length];
//const getHmsDataSet = (fileId: number) => hmsDataSets[1];

function financeColour(value: number) {
    return value < 0 ? 'red' : 'black'
}
function financeAvatar(value: number) {
    return {
        style: {
            color: financeColour(value),
            border: `0.5px solid ${value < 0 ? 'red' : 'lightgrey'}`,
            backgroundColor: 'transparent',
            width: '50px',
            height: '50px'
        },
        children: `£`
    };
}
// https://stackoverflow.com/questions/149055/how-to-format-numbers-as-currency-strings
const currencyFormat = new Intl.NumberFormat('en-UK', {
    style: 'currency',
    currency: 'GBP'
    // These options are needed to round to whole numbers if that's what you want.
    //minimumFractionDigits: 0, // (this suffices for whole numbers, but will print 2500.10 as $2,500.1)
    //maximumFractionDigits: 0, // (causes 2500.99 to be printed as $2,501)
});
const currencyItem = (value: number | undefined) => {
    if (value !== undefined) {
        return currencyFormat.format(value);
    }
    return null;
}
/*const currencyTotal = (h: HMSHistory) => {
    let total = 0;
    total += h.amountHB || 0;
    total += h.amountPersonal || 0;
    total += h.amountOther || 0;
    return currencyItem(total);
}*/

const InfoSectionHMS: FC<{srId: number}> = props => {
    const {sessionData} = useServicesContext();
    const {referral} = useReferralBySrId(props.srId);
    const {client} = useClient(referral?.clientId);

    if (!(referral && client)) {
        return null;
    }

    const dataSet = getHmsDataSet(referral.referralId);
    return <Card elevation={2}>
        <CardContent>
            <Grid container wrap={"nowrap"}>
                <Grid item>
                    <Avatar {...financeAvatar(dataSet.balance)}/>
                </Grid>
                <Grid item xs>
                    <Grid container>
                        <dl>
                            <dd><Typography style={{fontSize: 'small'}}>{`hms-id: ${dataSet.code}`}</Typography></dd>
                            <dd><Typography display={"inline"} style={{fontSize: 'small'}}>{`balance: `}</Typography>
                                <Typography display={"inline"} style={{fontSize: 'small', color: financeColour(dataSet.balance)}}>{currencyItem(dataSet.balance)}</Typography></dd>
                            <dd><Typography style={{fontSize: 'small'}}>{`rent check: ${dataSet.rentCheckDue.formatShort()}`}</Typography></dd>
                            <dd><Typography style={{fontSize: 'small'}}>{client.housingBenefit}</Typography></dd>
                        </dl>
                    </Grid>
                </Grid>
                <Grid item xs={1} container justify={"flex-end"}>
                    <Grid>
                        {link(<span className={"fa fa-external-link"}></span>,
                                undefined, `https://housingsystem?${dataSet.code}`)}
                    </Grid>
                </Grid>
            </Grid>
        </CardContent>
        <CardActions>
            <Grid container direction="row">
                <Grid item>
                    <Button onClick={() => {}} color="primary" variant="outlined" size="small">receive payment</Button>
                </Grid>
            </Grid>
        </CardActions>
    </Card>
};

const BalanceDifferenceSectionHMS: FC<{srId: number}> = props => {
    const {sessionData} = useServicesContext();
    const {referral} = useReferralBySrId(props.srId);
    const {client} = useClient(referral?.clientId);

    if (!(referral && client)) {
        return null;
    }

    const dataSet = getHmsDataSet(referral.referralId);
    return <>
        <Card elevation={2}>
            <CardHeader subheader={"balance differences"}/>
            <CardContent>
                {/*className={classes.table}*/}
                <Table >
                    <TableHead>
                        <TableRow>
                            <TableCell>date</TableCell>
                            <TableCell>reference</TableCell>
                            <TableCell>unit</TableCell>
                            <TableCell>£</TableCell>
                            <TableCell>type</TableCell>
                        </TableRow>
                    </TableHead>
                    {dataSet.balanceDifference.map((h, i) => <>
                                <TableRow key={i} style={{color: financeColour(h.amount)}}>
                                    <TableCell>{h.date.formatIso8601()}</TableCell>
                                    <TableCell>{h.ref}</TableCell>
                                    <TableCell>{h.unitRef}</TableCell>
                                    <TableCell>{currencyItem(h.amount)}</TableCell>
                                    <TableCell>{h.amountType}</TableCell>
                                    {/*<TableCell>{currencyTotal(h)}</TableCell>*/}
                                </TableRow>
                            </>
                    )}
                </Table>
            </CardContent>
        </Card>
    </>;
}

const HistorySectionHMS: FC<{srId: number}> = props => {
    const {sessionData} = useServicesContext();
    const {referral} = useReferralBySrId(props.srId);
    const {client} = useClient(referral?.clientId);

    if (!(referral && client)) {
        return null;
    }

    const dataSet = getHmsDataSet(referral.referralId);
    return <>
        <Card elevation={2}>
            <CardHeader subheader={`in the last 2 months`}/>
            <CardContent>
                {/*className={classes.table}*/}
                <Table >
                    <TableHead>
                        <TableRow>
                            <TableCell>date</TableCell>
                            <TableCell>reference</TableCell>
                            <TableCell>unit</TableCell>
                            <TableCell>£</TableCell>
                            <TableCell>type</TableCell>
                        </TableRow>
                    </TableHead>
                    {dataSet.history.map((h, i) => <>
                                <TableRow key={i}>
                                    <TableCell>{h.date.formatIso8601()}</TableCell>
                                    <TableCell>{h.ref}</TableCell>
                                    <TableCell>{h.unitRef}</TableCell>
                                    <TableCell>{currencyItem(h.amount)}</TableCell>
                                    <TableCell>{h.amountType}</TableCell>
                                    {/*<TableCell>{currencyTotal(h)}</TableCell>*/}
                                </TableRow>
                            </>
                    )}
                </Table>
            </CardContent>
        </Card>
    </>;
}

export const HousingManagementPage = (props: {srId: number}) => {
    const {serviceRecipient} = useServiceRecipient(props.srId);
    const {sessionData} = useServicesContext();
    if (!(serviceRecipient && sessionData)) {
        return <></>;
    }

    return <Grid container direction="row" justify="center" alignItems="center">
        <Grid item xs={12} md={8}>
            <InfoSectionHMS {...props}/>
        </Grid>
        <Grid item xs={12} md={8}>
            <BalanceDifferenceSectionHMS {...props}/>
        </Grid>
        <Grid item xs={12} md={8}>
            <HistorySectionHMS {...props}/>
        </Grid>
        <Grid item xs={12}>
            <div style={{marginTop: '5px'}}>&nbsp;</div>
        </Grid>
    </Grid>;
}
