import * as cmdDtos from "ecco-dto/command-dto";
import * as commands from "ecco-commands";
import {Operation} from "ecco-commands";
import {Mergeable} from "ecco-dto";


export class UpdateCommand {
    constructor(public type: string, public oldValue: string, public newValue: string) {
    }
}

export class TextUpdateCommand extends UpdateCommand {

    constructor(public override oldValue: string, public override newValue: string) {
        super("text", oldValue, newValue);
    }
}

export class ChoiceUpdateCommand extends UpdateCommand {

    constructor(public override oldValue: string, public override newValue: string) {
        super("choice", oldValue, newValue);
    }
}

export class DateUpdateCommand extends UpdateCommand {

    // TODO: Accept strings and convert them to dates, thus giving client-side validation that they
    // are valid dates
    constructor(public override oldValue: string, public override newValue: string) {
        super("date", oldValue, newValue);
        // TODO: parse the values and check they are valid
    }
}

// matches SingleValueHistoryCommandViewModel
export interface SingleValueHistoryCommandDto extends cmdDtos.UpdateCommandDto {
    id: number;

    operation: string;

    serviceRecipientId: number;

    key: string;

    value: cmdDtos.StringChangeOptional;

    validFrom: cmdDtos.StringChangeOptional;
}

/**
 * Command to add, remove or update
 * Matches SingleValueHistoryCommandController.java
 * service-recipients/{serviceRecipientId}/singlevaluehistory/{key}
 */
export class SingleValueHistoryCommand extends commands.BaseUpdateCommand {

    private value: cmdDtos.StringChangeOptional;

    private validFrom: cmdDtos.StringChangeOptional;

    /** operation should be either "update" or "delete" */
    constructor(private operation: Operation,
        private serviceRecipientId: number,
        private key: string,
        private id: number) {
        super(`service-recipients/${serviceRecipientId}/singlevaluehistory/command/`);
    }

    public override canMerge(candidate: Mergeable) {
        return false;
    }

    public override merge(previousCommand: this): this {
        return null;
    }

    /** Change data, but only if to != from */
    public changeValue(from: string, to: string) {
        this.value = this.asStringChange(from, to);
        return this;
    }
    /** Change data, but only if to != from */
    public changeValidFrom(from: string, to: string) {
        this.validFrom = this.asStringChange(from, to);
        return this;
    }

    /** We can build this command by throwing possibly 'non-change' data at it.  This will return 'true' only
     *  if the result was that there are changes to submit */

    public override hasChanges(): boolean {
        return this.value != null || this.validFrom != null
            || this.operation == "add" || this.operation == "remove";
    }

    public toDto(): SingleValueHistoryCommandDto {
        return ({
                    operation: this.operation,
                    id: this.id,
                    uuid: this.getUuid().toString(),
                    commandUri: this.commandUri,
                    timestamp: this.timestamp,
                    serviceRecipientId: this.serviceRecipientId,
                    key: this.key,
                    value: this.value,
                    validFrom: this.validFrom
        });
    }
}
