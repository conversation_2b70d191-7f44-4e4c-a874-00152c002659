@charset "utf-8";

/* common.css comes before this to reset styles and define newer rules that are common with _v2 */
/* Anything left here should eventually be migrated */
/* ------------------------------------------------ */

table {
    border-collapse: collapse;
    border-spacing: 0;
}

textarea {
    width: 97%;
    max-width: 1000px;
  /* from boostrap form-control */
  padding: 6px 12px;
  line-height: 1.42857143;
}

/* ----------------------------------- */
/* ----------------------------------- */
/* STRUCTURE */
/* ----------------------------------- */

/* went back to a list apart 2 column solution - http://www.alistapart.com/articles/negativemargins/ */
/* 3 column would be nice - since flexible and could put in logo (without scroll bar) */
/* however the 'clear' of rows needed within the container clears the content of the main page to the bottom of the left window */
/* and having it outside the container causes a scroll bar - so we can't win! */

/* changed width of left once logo introduced */
/* got stuck with ie6 sidebar with tkj solution - see archive of _tkj files */
/* if re-instating tkj, remove <div id=container> and </div> from main.jsp */

html {
    padding: 0 5px;

    font-size: 16px;
    line-height: 1.2;
}

#container {
    margin: 0 0;
}
#content {
}
#sidebar {
    width: 170px;
    float: right;
}
.clearing {
    height: 0;
    clear: both;
}
/* action buttons to float */
.tabs {
    float: right;
}


/* ----------------------------------- */
/* ----------------------------------- */
/* CLEAR FIX */
/* ----------------------------------- */

/* a non-intrusive way of clearing - http://www.positioniseverything.net/easyclearing.html */
/* this need only be applied for the parent of the floats - not every float element */
/* we hijack this fix for our existing parents */
/* don't forget to update classes in ie specifics */
.clearfix:after {
    content: ".";
    display: block;
    height: 0;
    clear: both;
    visibility: hidden;
}


/* ----------------------------------- */
/* ----------------------------------- */
/* TOOL TIPS */
/* ----------------------------------- */

.tooltip {
  position: relative;
  cursor: help;
}
/* also include the ie6hover functionality here - avoid repeating and relying on includes */
.tooltip .tip, .ietipsoff {
  display: none;
}
.tooltip:hover .tip, .ietipson {
  display: inline;
  z-index: 100;
  border: 1px solid;
  padding: 5px 5px;
  position: absolute;
  margin-top: 10px;
  margin-left: 10px;
  width: 300px;
  text-align: left;
  word-wrap: break-word;
}
/* tips can go off the screen, so move back left */
.tipleft {
    left: -300px;
}


/* ----------------------------------- */
/* ----------------------------------- */

/* ----------------------------------- */
/* ----------------------------------- */
/* HOUSE STYLE */
/* ----------------------------------- */

/*********************************************************/
/* structural */
/*********************************************************/

/* set the content to come back */

#block-top .rounded {
    padding-top: 6px;
    padding-bottom: 6px;
}

#block-content1 .rounded {
    padding-top: 4px;
    padding-bottom: 9px;
}
/* we can't fill the vertical perfectly, but this looks good on 'empty' */
#block-content {
    min-height: 350px;
}

#block-content2_wrapper {
    min-height: 275px;
}
#block-contentActionbar_wrapper {
    min-height: 50px;
}
#block-contentActionbar .rounded {
    padding-bottom: 9px;
}


/* give things a nice gap - within ui-tabs need 5px to get top border in right place */
.ui-tabs .top-divider,
.rounded {
    margin-top: 5px;
}

/* Adapt to support history etc. being in a JQUI tab panel which has lots of padding
   TODO clean up what we're using id=content-container for */
.ui-tabs-panel #content-container {
    margin: -18px -26px;
}
.ui-tabs-panel > .async-control {
    margin: -18px -24px -20px;
}

.ui-tabs .ui-tabs-nav { /* push in a bit to make work with larger radius below */
    padding-left: 7px;
}
.ui-tabs-anchor:focus { /* Fix bootstrap default */
    outline: none;
}

/*********************************************************/
/* tips pages */
/*********************************************************/

.tips dt {
    margin-left: 25px;
}
.tips dd {
    margin-left: 50px;
}


/*********************************************************/
/* general */
/*********************************************************/

#header .username {
    position: relative;
}

#header .username > div {
    position: absolute;
    right: 0;
    text-align: right;
    font-size: 13px;
    color: grey;
}


h1, h2 {
    font-size: 1.2em;
}

h1 {
    text-align: center;
}

h2 {
    margin: 0 auto 0.4em auto;
}

.legacy h2, h2.legacy {
    width: 90%;
}


#dialogContent h1 {
    text-align: left;
    line-height: 1.25;
    font-weight: bold;
}

#dialogContent p {
    margin: 1em;
}

#eventForm textarea { width: 90%; }

/* we change 'a' to a:link...' since 'a' can be an anchor too */
a:link, a:visited {
    padding: 2px 6px;
    text-decoration: none;
    display: inline-block; /* so that wrapped link doesn't get padding stagger */
}

i {
    min-width: 1em;
}


.lineSpace {
    margin-bottom: 7px;
}


/* class for lt ie7 browsers to apply hover on other elements, needs to be here in case don't include sheets */
.iehoveron {
    border: 0 solid;
    text-decoration: none;
}

/* useful (opposed to below) since we can place on the a link - not a wrapping div */
a.nohover:hover, a.nohover:link:hover, a.nohover:visited:hover {
    background-color: transparent;
}
/* useful for the safe logo - no block of red, even though its a link */
.nohover a:hover, .nohover a:link:hover, .nohover a:visited:hover {
    background-color: transparent;
}
.deleteMessage {
    text-align: center;
}

.e-radio label {
    padding-left: 0;
    margin-left: 0;
    padding-right: 10px;
    margin-right: 0;
}

.expander {
    cursor: pointer;
}
.popMsg {
    margin-top: 30px;
    margin-bottom: 30px;
    text-align: center;
}

.jsShow {
    display: none;
}

/* hide a close button on the dialog */
.jsDialogHideClose .ui-dialog-titlebar-close {
    display: none;
}


/*********************************************************/
/* menu layouts */
/*********************************************************/

/* for the menu header which is not a list so we define spaces */
#menu, #menu-loggedIn {
    text-align: center;
}
#menu .space, #menu-loggedIn .space {
    margin-left: 15px;
    margin-right: 15px;
}

/* move the contacts menu around */
/* this is actually a menu-context_contacts */

#menu-contacts .rowWrapper div.e-row span.e-label {
    width: 20%;
}
#menu-contacts .rowWrapper div.e-row span.input {
    width: 78%;
}
#menu-contacts .advancedfind {
    margin-left: 45%;
}
#menu-contacts .advancedfind_bus {
    margin-left: 20%;
}
#menu-contacts .group {
    margin-left: 20%;
}
/* override the left right padding of normal 'a' */
/* we did have alphabet as links */
#menu-contacts .alphabet .button {
    padding-left: 3px;
    padding-right: 3px;
}
#menu-contacts .alphabet {
    text-align: center;
    margin-bottom: 10px;
}

.add {
    float: left;
}
.lock, .export, .more {
    float: right;
}
.clearFind {
    text-align: center;
}
.actions {
    text-align: center;
}


/*********************************************************/
/* page layouts */
/*********************************************************/
.box {
    margin: 0 auto;
    max-width: 800px;
}

/* back is not floated unless requested - so can float right too */
.back-float {
    float: left;
}

.embeddedAction {
    text-align: right;
    margin-right: 5%;
}
#content-verifymobile .box {
    width: 80%;
}

.section {
    margin-bottom: 20px;
}


/*********************************************************/
/* form layouts */
/*********************************************************/
div.line {
    padding-top: 3px;
}

.rowWrapper15 div.e-row span.e-label {
    width: 15%;
}
.rowWrapper15 div.e-row span.input {
    width: 83%;
}

.entityForm .rightpoint {
    margin-left: 15%;
}

.entityAction {
    text-align: right;
    margin-right: 10%;
}
.entityActionLeft-float {
    float: left;
    padding-left: 10%;
}
.entityAction-float {
    float: right;
    padding-right: 10%;
}
.address div.e-row {
    padding-top: 0;
}

/* bring back the label a bit to the relevant input (mainly for inline groups eg radios)
   Only for legacy forms */
.legacy label {
    margin-right: 5px;
}

/*********************************************************/
/* view tables */
/*********************************************************/
table.view {
    width: 90%;
    text-align: center;
    margin-left:auto; margin-right:auto;
    margin-top: 5px;
}
table.view th,
table.view td {
    text-align: left;
    padding: 1px 4px;
    max-width: 180px;
}
table.view td a {
    display: block;
}
/* page 1, 2 etc */
.pages {
    text-align: center;
}

.view-header {
    border: 1px solid;
}


/* #### responsive #### */
body {
    margin: 0 70px;
}
@media(min-width:1000px){
    .new-layout body {
        margin-left: 270px;
    }
    .new-layout #context-badge {
        left: 74px;
    }
}
@media(max-width:1000px){
    body {
        margin: 0 0;
    }
    table.view {
        width: 98%;
    }
    .show-wide {
        display:none;
    }
    #header {
        margin: 0;
    }
    /* set the content to come back */
    #block-top_wrapper {
        margin-left:0;
    }

    #container {
        margin-left:0;
        margin-right:0;
    }
    #content {
        margin-left:0;
        margin-right:0;
    }
}
