import {ReferralDto, SupportWork} from "ecco-dto";

/**
 * DATA - supplementary
 * NB copied from output of CarePlanCommandAPITests via browser but amended serviceRecipientId to the referral1
 * where this currently gets filtered for target date and split into individual cards for each target
 */

const supportWorkViewModel = {
    "id":"d93d63fa-87de-4da9-8bbc-65ea3e2b1db8","serviceRecipientId":90070,"requestedDelete":false,
    "parentCode":null,"parentPrefix":null,"authorDisplayName":"sysadmin","comment":"setup oral NSAID medication schedule",
    "commentTypeId":null,"eventId":null,"minsSpent":144,"signatureId":null,"workDate":"2019-10-10T11:38:06.000",
    "createdDate":"2019-11-11T11:38:06.000","taskName":"needsAssessment","attachments":[],
    "locationId":null,"meetingStatusId":null,"mileageTo":null,"mileageDuring":null,"minsTravel":null,"associatedActions":[],
    "riskManagementRequired":false,"riskManagementHandled":null,"clientStatusId":null,
    "actions":[{
        "id":102993,"workId":"d93d63fa-87de-4da9-8bbc-65ea3e2b1db8","targetDate":null,"targetSchedule":null,
        "name":"Support to encourage discussion with Key Worker about use of temptation to use drugs/alcohol",
        "goalName":"Prednisolone","goalPlan":"take 6 5mg tablets twice a day for 4 weeks, then 3 5mg tablets for 4 weeks",
        "status":1,"statusChange":true,"actionId":100,"actionGroupId":80,"outcomeId":80,
        "actionInstanceUuid":"1d9637c3-2b29-448c-af09-16134166bd18","parentActionInstanceUuid":null,"hierarchy":null,"position":null,
        "score":null,"statusChangeReasonId":null
    },{
        "id":102994,"workId":"d93d63fa-87de-4da9-8bbc-65ea3e2b1db8","targetDate":"2019-11-14","targetSchedule":null,
        "name":"Support to encourage discussion with Key Worker about use of temptation to use drugs/alcohol",
        "goalName":"schedule 1","goalPlan":"take 6 5mg tablets twice a day for 4 weeks",
        "status":1,"statusChange":true,"actionId":100,"actionGroupId":80,"outcomeId":80,
        "actionInstanceUuid":"9ab4d25e-06a7-4934-b429-284938c433ee","parentActionInstanceUuid":"1d9637c3-2b29-448c-af09-16134166bd18","hierarchy":1,"position":"0-0",
        "score":null,"statusChangeReasonId":null
    },{
        "id":102995,"workId":"d93d63fa-87de-4da9-8bbc-65ea3e2b1db8","targetDate":"2019-12-11","targetSchedule":null,
        "name":"Support to encourage discussion with Key Worker about use of temptation to use drugs/alcohol",
        "goalName":"schedule 2","goalPlan":"take 3 5mg tablets twice a day for 4 weeks",
        "status":1,"statusChange":true,"actionId":100,"actionGroupId":80,"outcomeId":80,
        "actionInstanceUuid":"11a1c7cc-d456-46b2-b232-9a552ecfb414","parentActionInstanceUuid":"1d9637c3-2b29-448c-af09-16134166bd18","hierarchy":1,"position":"0-1",
        "score":null,"statusChangeReasonId":null
    }
    ]
} as any as SupportWork;

// taken from testData - but copied to avoid its import statements
const referral1 = {
    prefix: 'r',
    serviceRecipientId: 90070,
    referralId : 100070,
    referralCode: "R001",
    requestedDelete: null,
    clientId : 90010,
    contactId: null,
    clientCode: "C001",
    serviceId : 1,
    serviceAllocationId: -1,
    serviceTypeId : 2,
    clientDisplayName: "Mock Mike",
    displayName: "Mock Mike",
    firstName: "Mock",
    lastName: "Mike",
    source: "self-referral",
    sourceAgency: null,
    delivererAgencyName: null,
    deliveredById: null,
    deliveredByStartDate: null,
    receivedDate: "11/12/2013",
    srcGeographicAreaId: null,
    consentAgreementDate: null,
    consentSigned: false,
    dataProtectionAgreementDate: null,
    dataProtectionSigned: false,
    receivingServiceDate: null,
    firstResponseMadeOn: null,
    firstOfferedInterviewDate: null,
    decisionDate: null,
    decisionMadeOn: null,
    exitedDate: null,
    exitReason: null,
    exitReasonId: null,
    exitComment: null,
    signpostedReason: null,
    signpostedCommentId: 0,
    signpostedAgencyName: null,
    signpostedAgencyId: null,
    signpostedBack: false,
    signpostedComment: '',
    statusMessageKey: "status.started",
    supportWorkerDisplayName: "worker1",
    interviewer1WorkerDisplayName: "worker1",
    supportWorkerId: 80010,
    interviewer1ContactId: 80010,
    calendarEvents: [],
    daysAttending: 31,
    pendingStatusId: null,
    interviewer2ContactId: null,
    interviewLocation: null,
    interviewSetupComments: null,
    interviewDna: null,
    interviewDnaComments: null,
    fundingSourceId: 0,
    fundingSource: '',
    fundingPaymentRef: '',
    fundingAmount: 0,
    fundingReviewDate: '',
    fundingDecisionDate: '',
    fundingHoursOfSupport: 0,
    fundingAccepted: false,
    acceptOnServiceState: 'UNSET',
    decisionReferralMadeOn: null,
    appropriateReferralState: 'UNSET'
} as any as ReferralDto;


/**
 * DATA - EXPORT SCHEDULE
 */
export const careScheduleData = {
    referral: referral1,
    tasks: supportWorkViewModel.actions
};
