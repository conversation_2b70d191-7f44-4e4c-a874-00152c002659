import $ = require("jquery");
import SummarisedElement = require("../controls/SummarisedElement");
import {applicationRootPath} from "application-properties";
import {StaffDto} from "ecco-dto/hr-dto";


class WorkerSummaryControl implements SummarisedElement<StaffDto> {

    private $title: $.JQuery;
    private $icon: $.JQuery;

    constructor(public worker: StaffDto) {
        this.render();
    }

    private render() {

        var editUri = `${applicationRootPath}nav/r/welcome/staff/${this.worker.workerId}/`;

        this.$icon = $("<a>").addClass("fa fa-pencil")
            .attr("title", "edit worker")
            .attr("href", editUri.toString());

        this.$title = $("<div>").addClass("container-fluid")
            .append($("<div>").addClass("row")
                .append($("<div>").addClass("col-xs-10")
                    .text(this.worker.firstName + " " + this.worker.lastName)
                )
                .append($("<div>").addClass("col-xs-1")
                    .append(this.$icon)
                    .append("&nbsp;")
                )
            );
    }


    public searchId() {
        return String(this.worker.workerId);
    }

    public title(): $.JQuery {
        return this.$title;
    }

    public body(): $.JQuery {
        return null;
    }

    initiallyHidden():boolean {
        return false;
    }

    public target() {
        return this.worker;
    }
}

export = WorkerSummaryControl
