import $ = require("jquery");

import ActionButton = require("../controls/ActionButton");
import BaseAsyncDataControl = require("../controls/BaseAsyncDataControl");
import {ActionsChangedCallback, ActionState, bus, ModalStyle} from "@eccosolutions/ecco-common";
import {CommandQueue} from "ecco-commands";
import DialogContent, {Footer} from "../controls/DialogContent";
import {showNotification} from "ecco-components-core";
import {
    getCommandQueueRepository,
    OfflineSyncStatus,
    OfflineSyncStatusEvent,
    showErrorAsAlert
} from "ecco-offline-data";

/** For use with the following from the derived class
 *       var form = new ManageServiceActivitiesControl(serviceId, serviceTypeId);
 *      Modal.showInModal(form);
 *      form.load();
 */
abstract class BaseAsyncCommandForm<T> extends BaseAsyncDataControl<T> implements DialogContent {


    /** callback should be called when the Form has successfully saved it's content and the dialog can close.
     *  This is usually in response to a "done" button being clicked in the footer, for example */
    private onSavedBus = bus<void>();

    private submitAction: ActionState;

    protected commandQueue = new CommandQueue(getCommandQueueRepository()); /* TODO: Refactor to a something based on events, such that submitForm actions for here and CommandForm.tsx are
                                                 extracted to use the same code */

    protected actions: ActionState[];

    /** Callback whenever available actions change state */
    actionChangeCallback: (actions: ActionState[]) => void;

    /**
     * @param formTitle
     * @param readOnly - if set true, only show "close" button not save
     * @param modalClass - modal-full | modal-sm | modal-lg | "" (for default)
     */
    constructor(private formTitle: string, private readOnly = false, private modalClass: ModalStyle = "modal-full") {
        super();
        // this.submitButton = this.createSubmitButton();

        this.submitAction = !this.readOnly && {
            label: "save",
            clickedLabel: "saving...",
            onClick: () => {
                return this.submitForm() // TODO: THis should be events
                    .catch((e: Error) => {
                        this.submitFailed(e);
                        throw e;
                    })
            },
            disabled: true
        };

        this.actions = this.submitAction ? [this.submitAction] : [];

        this.onSavedBus.addHandler(() => {
            this.enableSubmit();
            OfflineSyncStatusEvent.bus.fire(new OfflineSyncStatusEvent(OfflineSyncStatus.COMMANDS_SAVED, 0, "Commands saved"));
        });
    }

    registerActionsChangeListener(updateActions: ActionsChangedCallback): void {
        this.actionChangeCallback = updateActions;
        this.actionChangeCallback(this.actions);
    }

    protected modifySubmit(modification: Partial<ActionState>) {
        if (this.submitAction) {
            for (const prop in modification) {
                this.submitAction[prop] = modification[prop];
            }
            this.actions = [this.submitAction]; // new array for changed state - immutability
            // NB actionChangeCallback only exists when a modal is built, through registerActionsChangeListener
            // it gets called still on first rendering of the modal, so this is fine
            if (this.actionChangeCallback) {
                this.actionChangeCallback(this.actions);
            }
        }
    }

    protected enableSubmit() {
        this.modifySubmit({disabled: false});
    }

    protected disableSubmit() {
        this.modifySubmit({disabled: true});
    }

    protected setSubmitLabel(text: string) {
        this.modifySubmit({label: text});
    }

    /**
     * This extracted method exists so that the button can be overriden
     * in order to re-label, re-locate and re style it.
     *
     * @returns {ActionButton}
     */
    protected createSubmitButton() {
        return new ActionButton("save", "saving...")
            .addClass("btn btn-primary pull-right")
    }

    protected submitForm(): Promise<void> {
        if (!this.onSavedBus.hasHandlers(2)) {
            throw new Error("no onFinished handers have been set - this doesn't make sense - you should notify the user/re-enable etc");
        }
        this.preSubmitCommandsHook();
        return this.commandQueue.flushCommands()
            .then(() => showNotification("info", "change saved"))
            .then(() => this.onSavedBus.fire())
            .catch(showErrorAsAlert);
    }

    /** Override this to populate the command queue only on save - prob good idea to empty it first in case of errors */
    protected preSubmitCommandsHook(): void {
    }

    private submitFailed(e: Error) {
        showNotification("warning", e.toString());
    }

    // DialogContent
    public getTitle() {
        return this.formTitle;
    }

    public getFooter(): $.JQuery {
        return new Footer(this, () => this.onSavedBus.fire()).element();
    }

    getModalClass(): ModalStyle {
        return this.modalClass;
    }

    public setOnFinished(onFinished: () => void) {
        this.onSavedBus.addHandler(onFinished);
    }
}

export = BaseAsyncCommandForm;
