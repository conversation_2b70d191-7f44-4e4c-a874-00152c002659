import $ = require("jquery");
import _ = require("lodash");
import StringInputControl = require("./StringInputControl");

class SearchInput extends StringInputControl {

    private $element: $.JQuery;

    public constructor(placeholderText: string, id: string = _.uniqueId("search_")) {
        var $input = $("<input>")
            .addClass("form-control")
            .attr("placeholder", placeholderText);

        var $element = $("<div>").addClass("input-group")
            .append("<span class='input-group-addon'><span class='glyphicon glyphicon-search'></span></span>")
            .append($input);

        super($element, $input, id);
    }

}

export = SearchInput;

