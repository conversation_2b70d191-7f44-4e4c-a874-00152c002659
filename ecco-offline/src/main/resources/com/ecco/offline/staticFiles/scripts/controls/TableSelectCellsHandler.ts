import ElementGrid = require("../controls/ElementGrid");


/** Active while a drag-to-select operation is active on a table.
 *
 * Maintains state for start, */
class TableSelectCellsHandler {
    /** @param AvailabilityGrid is 2-dimensional array of each table cell jqDOM node, accessed as AvailabilityGrid[row][col] */
    constructor(private elementGrid: ElementGrid) {
    }

    private selecting = false;
    private startRow = 0;
    private startCol = 0;
    private shiftPressed = false;

    public dragStart(row: number, col: number, shiftPressed: boolean) {
        if (this.selecting) {
            // If we were previously selecting, then deselect previous
            this.deactivateAllCells();
        }
//        console.debug("dragStart: [" + row + "][" + col + "], shiftPressed = " + shiftPressed);
        this.selecting = true;
        this.startRow = row;
        this.startCol = col;
        this.shiftPressed = shiftPressed;
        this.activateCell(row, col);
    }

    public dragExtend(row: number, col: number) {
//        console.debug("dragExtend: [" + row + "][" + col + "]");
        if (this.selecting) {
            this.deactivateAllCells();
            this.activateCells(this.startRow, this.startCol, row, col);
        } else {
            console.debug("dragExtend called but no drag started");
        }
    }

    public dragFinish(row: number, col: number) {
//        console.debug("dragFinish: [" + row + "][" + col + "]");
        if (this.selecting) {
            // TODO: update model etc .. by a callback
            // handler.dragNotify(startRow,startCol,row,col,shiftPressed);
            // TODO: and might want to sort out so start <= end in above
            this.deactivateAllCells();
            this.selecting = false;
        } else {
            console.debug("dragFinish called but no drag started");
        }
    }

    /**
     * To be called if we leave the table, or mouse up in between cells (will get cellIndex == null)
     */
    public cancel() {
//        console.debug("drag.cancel");
        this.deactivateAllCells();
        this.selecting = false;
    }

    private activateCell(row: number, col: number) {
        var cell = this.elementGrid.elementAt(row,col);

        if (this.shiftPressed && cell.hasClass("available")) {
            cell.addClass("to-unavailable");
        }
        else if (this.shiftPressed && cell.hasClass("to-available")) {
            cell.removeClass("to-available");
        }
        else if (!this.shiftPressed && cell.hasClass("unavailable")) {
            cell.addClass("to-available");
        }
        else if (!this.shiftPressed && cell.hasClass("to-unavailable")) {
            cell.removeClass("to-unavailable");
        }
        cell.addClass("selected"); // Always mark as selected when selecting
    }

    private activateCells(startRow: number, startCol: number, endRow: number, endCol: number) {
        if (startRow > endRow) this.activateCells(endRow, startCol, startRow, endCol);
        if (startCol > endCol) this.activateCells(startRow, endCol, endRow, startCol);

        for (var row = startRow; row <= endRow; row++) {
            for (var col = startCol; col <= endCol; col++) {
                this.activateCell(row, col);
            }
        }
    }

    private deactivateAllCells() {
        this.elementGrid.withEachElement( (element) => element.removeClass("selected") );
    }
}

export = TableSelectCellsHandler
