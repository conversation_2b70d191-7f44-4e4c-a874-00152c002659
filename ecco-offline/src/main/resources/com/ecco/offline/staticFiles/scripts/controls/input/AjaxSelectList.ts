import $ = require("jquery");
import URI = require("URI");

import BaseControl = require("../../controls/BaseControl");
import SelectList = require("../../controls/SelectList");
import {apiClient} from "ecco-components";

interface IdNameMaybeDisabled {
    id: number;
    name: string;
    disabled?: boolean;
}

/** Provide a select list, and uses select2 if over 15 entries */
class AjaxSelectList extends BaseControl {

    static enhanceElement(domElement: Element): AjaxSelectList {
        var name = domElement.getAttribute("data-name");
        var initialValue = domElement.getAttribute("data-initial-value");
        var dataUrl = domElement.getAttribute("data-list-url");
        var readOnlyStr = domElement.getAttribute("data-readonly");
        var readOnly = readOnlyStr === "true";
        var input = new AjaxSelectList(name, initialValue, dataUrl, readOnly);
        input.attach( $(domElement) );
        return input;
    }

    private selectList: SelectList;

    /** Note that initialValue is singular - don't use a <select> for multiple values - awful UX */
    constructor(private name: string, private initialValue: string, private dataUrl: string, private readonly: boolean) {
        super($("<span>").text("loading..."));
        this.selectList = new SelectList(name);
    }

    public load() {
        this.getData( this.dataUrl ).then( (entries) => {
            this.updateControl(entries);
        })
        .catch( (reason) => {
            this.handleError(reason);
        });
    }

    private getData(dataUrl: string): Promise<IdNameMaybeDisabled[]> {
        return (<Promise<IdNameMaybeDisabled[]>>apiClient.get(URI(dataUrl)));
    }

    private updateControl(entries: IdNameMaybeDisabled[]) {
        this.selectList.populateFromList<IdNameMaybeDisabled>(entries,
            (entry) => ({key: entry.id.toString(), value: entry.name, isHidden: entry.disabled}),
            (entry) => entry.id.toString() == this.initialValue);
        this.element().empty()
            .append(this.selectList.element());
        if (entries.length > 15) {
            (<any>this.selectList.element()).select2();
        }
        if (this.readonly) {
            this.selectList.setReadOnly();
        }
    }

    public val() {
        return this.selectList.val();
    }

    private handleError(reason: any) {
        this.element().text("failed to load: click to retry")
            .click( () => {
                this.load()
            });
    }
}
export = AjaxSelectList

