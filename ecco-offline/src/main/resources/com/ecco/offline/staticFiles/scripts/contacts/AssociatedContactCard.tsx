import $ = require("jquery");
import IndividualDetailModal = require("./components/IndividualDetailModal");
import AgencyDetailModal = require("./components/AgencyDetailModal");
import * as React from "react";
import {FC} from "react";

import {Card, CardActions, CardContent, CardHeader} from "@eccosolutions/ecco-mui";
import {ServiceRecipientAssociatedContact} from "ecco-dto/referral-dto";
import {Agency, Contact, Individual} from "ecco-dto/contact-dto";
import {update, withSessionData} from "ecco-components";
import {link} from "ecco-components-core";
import {CommandQueue, ServiceRecipientAssociatedContactCommand} from "ecco-commands";
import {EccoDate} from "@eccosolutions/ecco-common";
import {SessionData} from "ecco-dto";

interface CardProps {
    association: ServiceRecipientAssociatedContact;
    printView?: boolean;
}

interface CardState {
    displayName: string;
    mobile: string;
    phone: string;
    email: string;
    archived: string;
    org: string;
    associatedTypeIds: number[];
    editForm: string;
    organisation: Agency;
    jobTitle: string;
}

/**
 * Show a contact, and edit the contact.
 */
class _AssociatedContactCard extends React.Component<CardProps & {sessionData: SessionData}, CardState> {

    constructor(props) {
        super(props);

        this.state = {
            displayName: "",
            mobile: "",
            phone: "",
            email: "",
            archived: "",
            org: "",
            associatedTypeIds: [],
            editForm: null,
            organisation: props.association.organisation,
            jobTitle: ""
        };
    }

    override componentDidMount() {
        this.prepareContact(this.props.association);
    }

    override render() {

        const editIndividualPopup = <IndividualDetailModal
            sessionData={this.props.sessionData}
            serviceRecipientId={this.props.association.serviceRecipientId}
            organisationId={this.props.association.organisation?.contactId}
            individualId={this.props.association.contact?.contactId}
            show={this.state.editForm == 'individual'}
            onSave={(individual, associatedTypeIds) => this.handleIndividualAfterSave(individual, associatedTypeIds)}
            onCancel={() => this.handleModalCancel()}/>;
        const editAgencyPopup = <AgencyDetailModal
            contextId={this.props.association.serviceRecipientId}
            agencyId={this.props.association.organisation?.contactId}
            show={this.state.editForm == 'agency'}
            onSave={agency => this.handleAgencyAfterSave(agency)}
            onCancel={() => this.handleModalCancel()}/>;

        const editIndividualAction = link("edit", () => {
                    this.setState({editForm: 'individual'})
                });
        const editAgencyAction = link("edit org", () => {
            this.setState({editForm: 'agency'})
        });

        const archiveAction = link("archive", () => {
            const archCmd = new ServiceRecipientAssociatedContactCommand("update",
                this.props.association.serviceRecipientId,
                this.props.association.contactId);
            archCmd.changeArchived(null, EccoDate.todayLocalTime());

            const commandQueue = new CommandQueue();
            commandQueue.addCommand(archCmd);
            commandQueue.flushCommands().then(() =>
                this.setState({archived: EccoDate.todayLocalTime().formatIso8601()})
            );
        });

        const unArchiveAction = link("unarchive", () => {
            const archCmd = new ServiceRecipientAssociatedContactCommand("update",
                this.props.association.serviceRecipientId,
                this.props.association.contactId);
            archCmd.changeArchived(EccoDate.parseIso8601(this.state.archived), null);

            const commandQueue = new CommandQueue();
            commandQueue.addCommand(archCmd);
            commandQueue.flushCommands().then(() =>
                this.setState({archived: null})
            );
        });

        const opacity = this.state.archived ? 0.6 : 1;

        return (
            this.state.editForm == 'individual' ? editIndividualPopup
                : this.state.editForm == 'agency' ? editAgencyPopup
                :
                <>
                    <Card style={{marginTop: 2, marginBottom: 2, opacity: opacity}}>
                        <CardHeader
                            title={this.state.displayName}
                            subheader={this.state.associatedTypeIds.length > 0
                                ? this.state.associatedTypeIds.map(a => this.props.sessionData.getListDefinitionEntryById(a).getDisplayName()).join(",")
                                : this.state.jobTitle
                            }
                        />
                        <CardContent>
                            <div>{this.state.mobile}</div>
                            <div>{this.state.phone}</div>
                            <div>{this.state.email}</div>
                            <div><b>{this.state.org}</b></div>
                            <br/>
                            <div>{this.state.archived && "archived: ".concat(this.state.archived)}</div>
                        </CardContent>
                        {this.props.printView
                            ? null
                            : <CardActions disableSpacing={true}>
                                {this.state.archived ? null : <>
                                    {this.props.association.contact ? editIndividualAction : null}
                                    {this.props.association.organisation ? editAgencyAction : null}
                                </>}
                                {this.state.archived ? unArchiveAction : archiveAction}
                              </CardActions>
                        }
                    </Card>
                    {this.updateOtherContactDisplays()}
                </>
        );
    }

    private updateOtherContactDisplays() { // What the... Does this actually work
        this.state.associatedTypeIds.forEach(id => {
            if (!this.state.archived) {
                Array.from(document.getElementsByClassName('contacts-displayName-' + id)).forEach(el => {
                    $(el).text(this.state.displayName);
                });
                Array.from(document.getElementsByClassName('contacts-phone-' + id)).forEach(el => {
                    const txt = this.state.mobile || this.state.phone;
                    $(el).empty().append($("<a>").attr("href", "tel:" + txt).text(txt));
                });
            }
        });
    }

    private prepareContact(association: ServiceRecipientAssociatedContact) {

        if (this.props.association.archived) {
            this.setState(prevState => update(prevState, {archived: {$set: this.props.association.archived}}));
        }

        if (association.contact) {
            this.updateIndividualState(association.contact as Individual, association.associatedTypeIds);
            this.updateAgencyState(association.organisation);
        }
        if (association.organisation) {
            this.updateAgencyState(association.organisation);
        }
    }

    private updateIndividualState(individual: Individual, associatedTypeIds: number[]) {
        this.setState(prevState => update(prevState, {displayName: {$set: individual.firstName + " " + individual.lastName}}));
        this.setState(prevState => update(prevState, {associatedTypeIds: {$set: associatedTypeIds}}));
        if (individual.jobTitle) {
            this.setState(prevState => update(prevState, {jobTitle: {$set: individual.jobTitle}}));
        }
        this.updateIndividualStateFormatting(individual);
    }

    private updateAgencyState(agency: Agency) {
        this.setState(prevState => update(prevState, {organisation: {$set: agency}}));
        this.updateAgencyStateFormatting(agency);
    }

    private updateIndividualStateFormatting(contact: Contact) {
        if (contact.mobileNumber) {
            const info = "mobile " + contact.mobileNumber;
            this.setState(prevState => update(prevState, {mobile: {$set: info}}));
        }
        if (contact.phoneNumber) {
            const info = "phone " + contact.phoneNumber;
            this.setState(prevState => update(prevState, {phone: {$set: info}}));
        }
        if (contact.email) {
            const info = "email " + contact.email;
            this.setState(prevState => update(prevState, {email: {$set: info}}));
        }
    }

    private updateAgencyStateFormatting(agency: Agency) {
        if (agency) {
            const line = agency.companyName + (agency.phoneNumber ? ', ' + agency.phoneNumber : "");
            this.setState(prevState => update(prevState, {org: {$set: line}}));
        }
    }

    private handleIndividualAfterSave(individual: Individual, associatedTypeIds: number[]) {
        this.updateIndividualState(individual, associatedTypeIds);
        this.setState(prevState => update(prevState, {editForm: {$set: null}}));
    }

    private handleAgencyAfterSave(agency: Agency) {
        this.updateAgencyState(agency);
        this.setState({editForm: null});
    }

    private handleModalCancel() {
        this.setState({editForm: null});
    }
}

export const AssociatedContactCard: FC<CardProps> = props =>
    withSessionData(sessionData => <_AssociatedContactCard {...props} sessionData={sessionData}/>);

export default AssociatedContactCard;
