import $ = require("jquery")

import {ReportAjaxRepository} from "ecco-reports"
import ReportStatus = require("./ReportStatus");
import ReportControl = require("./ReportControl");
import ReportCriteriaControl = require("./ReportCriteriaControl");

// reports
import ReferralDataTable = require("./ReferralDataTable");
import ReferralChart = require("./ReferralChart");
import ReferralReportLoad = require("./ReferralReportLoad");
import SmartStepDataTable = require("./SmartStepDataTable");
import SmartStepChart = require("./SmartStepChart");
import SmartStepReportLoad = require("./SmartStepReportLoad");

var reportRepository = ReportAjaxRepository.instance;

/**
 * The class which sets up the reports and controls
 */
class ReportView {

    // registry of the reports - so we have a handle on them
    reports: ReportControl<any>[] = new Array();

    dataTablesId: string = "dataTables";
    chartsId: string = "charts";
    chartTemplateId: string = "chartTemplate";

    public init() {
        this.createReports();
    }

    private createReports(): void {
        var statusR = new ReportStatus("loadedSourceStatus-referral", "quickFilterStatus-referral", "chartFilterStatus-referral", "editDataCriteria-referral", "editQuickFilter-referral", "clearChartFilter-referral");
        var criteriaR = new ReportCriteriaControl();
        var chartR = new ReferralChart(this.cloneChartTemplate("chart-referrals"));
        var dataTableR = new ReferralDataTable("datatable-referral", this.dataTablesId, "quickfilter-referral", () => {});
        var repR = new ReferralReportLoad(reportRepository);
        var controlR = new ReportControl<any>(statusR, criteriaR, repR, chartR, dataTableR);
        this.registerReport(controlR);

        var statusSS = new ReportStatus("loadedSourceStatus-smartsteps", "quickFilterStatus-smartsteps", "chartFilterStatus-smartsteps", "editDataCriteria-smartsteps", "editQuickFilter-smartsteps", "clearChartFilter-smartsteps");
        var criteriaSS = new ReportCriteriaControl();
        var chartSS = new SmartStepChart(this.cloneChartTemplate("chart-smartsteps"));
        var dataTableSS = new SmartStepDataTable("datatable-smartsteps", this.dataTablesId, "quickfilter-smartsteps", () => {});
        var repSS = new SmartStepReportLoad(reportRepository);
        var controlSS = new ReportControl<any>(statusSS, criteriaSS, repSS, chartSS, dataTableSS);
        this.registerReport(controlSS);
    }

    private cloneChartTemplate(chartId: string): $.JQuery {
        var $chartTemplate: $.JQuery = $('#' + this.chartTemplateId);
        var $cloned = $chartTemplate.clone().removeAttr("id");
        $(".graphHolder", $cloned).attr("id", chartId);
        $('#' + this.chartsId).append($cloned);
        return $cloned;
    }

    // register the reports so we can control them after creation
    private registerReport(report: ReportControl<any>): void {
        this.reports[this.reports.length] = report;
    }

}

export = ReportView;
