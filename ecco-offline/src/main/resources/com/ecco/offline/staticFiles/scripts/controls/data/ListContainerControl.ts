import $ = require("jquery");

import Element = require("../Element");
import SearchableContainer = require("../SearchableContainer");
import {AdminMode} from "@eccosolutions/ecco-common";
import {adminModeEnabled} from "ecco-dto";

/**
 List control that gives us the ability to search
*/
class ListContainerControl implements SearchableContainer {
    private $container:$.JQuery;

    private items: { [itemId: string]: $.JQuery; } = {};

    constructor() {
        this.$container = $("<ul>").addClass("entry-list list-unstyled");
    }

    public addControlForNewEntry(addEntryText: string, iconClasses: string, adminOnly: boolean,
        addNewEntry: () => void) {
        var $newEntry = $("<li>").addClass("add-entry")
        .append( $("<i>").addClass(iconClasses) )
        .append( $("<span>").text(" " + addEntryText) )
        .click( () => addNewEntry() );

        if (adminOnly && !adminModeEnabled()) {
            $newEntry.hide();
        }
        this.$container.append($newEntry);
        AdminMode.bus.addHandler(event => {
            if (event.enabled) {
                $newEntry.show();
            }
            else {
                $newEntry.hide();
            }
        });
    }

    public append(itemId: string, heading:$.JQuery, body: $.JQuery, initiallyHidden: boolean): number;
    public append(itemId: string, heading: Element, body: Element, initiallyHidden: boolean): number;
    append(itemId: string, heading: any, body: any, initiallyHidden: boolean): number {

        this.items[itemId] = $("<li>").addClass(initiallyHidden && "hidden");
        this.items[itemId].append(this.toJQuery(heading));
        if (body) {
            this.items[itemId].append(this.toJQuery(body));
        }

        var index = this.$container.children().length;
        this.$container.append(this.items[itemId]);
        return index;
    }

    public hasItem(itemId: string): boolean {
        return !!this.items[itemId];
    }

    public hideAll(): void {
        this.$container.children().addClass('hidden');
    }

    public showAll(): void {
        this.$container.children().removeClass('hidden');
    }

    public show(itemId: string): void {
        this.items[itemId].removeClass('hidden');
    }

    public addClass(classes: string, itemId: string): void {
        this.items[itemId].addClass(classes);
    }

    public removeClass(classes: string, itemId?: string): void {
        if (itemId) {
            this.items[itemId].removeClass(classes);
        }
        else {
            this.$container.children().removeClass(classes);
        }
    }

    private toJQuery(element: any): $.JQuery {
        var $content: $.JQuery;
        if ((<Element>element).element) {
            $content = element.element();
        }
        else {
            $content = $(element);
        }
        return $content;
    }

    public element(): $.JQuery {
        return this.$container;
    }
}

export = ListContainerControl