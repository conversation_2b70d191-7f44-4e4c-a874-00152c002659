import * as domain from "ecco-dto";
import dynamicTree = require("../../draw/dynamic-tree");
import Node = dynamicTree.DynamicTreeNode;
import * as evidenceDto from "ecco-dto/evidence-dto";
import SupportAction = evidenceDto.SupportAction;
import ActionNode = require("./ActionNode");

/** An object that wraps a graphical Node */
interface NodeProxy {
    /** get the managed node */
    getNode(): Node;

    addAction(action: domain.Action, supportAction: SupportAction): void;

    withAllLeafNodes( callback: (leaf: ActionNode) => void ): void;
}
export = NodeProxy;