import $ = require("jquery");
import dataTableColumn = require("./dataTableColumn");
import dataTableEvent = require("./dataTableEvent")
import {ReportDataTable} from "./dataTableEvent";

// also see https://github.com/borisyankov/DefinitelyTyped/blob/master/jquery.dataTables/jquery.dataTables.d.ts
// but upgrading to datatables version 1.10 is completely different - check the d.ts has moved with it
// there is also some consideration in the datatables code itself which is worth investigating

// we share the legacy initial variables for now, to be consistent
declare var dataTableInit: any;

class DataTable {

    private dataTable: any;
    private filterChangeToggle = true; // datatables 'draw' also triggers a search, which we don't want
    private firstDraw = true;

    constructor(private dataTableId: string, private dataTableSearchId: string, private dataTableSearchTriggerId: string, private clickRow: (data: any) => void, private dataColumns: dataTableColumn.DataTableColumn[]) {
    }

    private setupOnFilterChange() {
        $('#' + this.dataTableId).on('search.dt', event => {
            let dataTable = event.currentTarget as any as ReportDataTable;
            // datatables fires a search event for a search and a draw
            // we need to filter the search, not draw, but only once
            if (this.filterChangeToggle) {
                dataTableEvent.DataTableUpdateEvent.bus.fire(new dataTableEvent.DataTableUpdateEvent(dataTable));
            }
            this.filterChangeToggle = !this.filterChangeToggle;
        } );
    }

    public draw(data: any) {

        var $dataTableHolder: $.JQuery = $("#" + this.dataTableId);

        var dtSettings: any = $.extend({}, dataTableInit);
        dtSettings['data'] = data;
        dtSettings['columns'] = this.getColumns();
        dtSettings["destroy"] = true; // destroy the previous table
        dtSettings["pageLength"] = 10;

        if (this.firstDraw) {
            // get footer in early so datatables remembers it
            this.applyFooterRow();
        }

        // seems every other redraw, that this removes "tfoot tr"
        // meaning the filters don't get applied every other draw
        this.dataTable = (<any> $dataTableHolder).dataTable(dtSettings);

        if (this.firstDraw) {
            // *** bootstrap specifics ***
            // see http://www.datatables.net/manual/styling/bootstrap
            // see tabletools bootstrap see https://datatables.net/release-datatables/extensions/TableTools/examples/bootstrap.html
            $dataTableHolder.addClass('table table-striped table-bordered');

            //this.dataTableColumnFilter();

            this.attachRowClickHandler();
        }

        this.firstDraw = false;
    }

    public getVisibleData(): any[] {
        var visibleData: any[] = this.dataTable._('tr', {"filter":"applied"});
        return visibleData;
    }

    public filterColumn(search: string, index: number) {
        this.dataTable.fnFilter(search, index);
    }

    public clearFilterColumn(index: number) {
        this.dataTable.fnFilter("", index);
    }

    // ideally we create a class with property -> column index -> column filter type
    private getColumns(): any[] {
        var columns: any[] = [];
        for (var i=0; i < this.dataColumns.length; i++ ) {

            if (this.dataColumns[i].display) {
                columns.push({
                        "sTitle": this.dataColumns[i].title,
                        "mData": this.dataColumns[i].property, // object property
                        "mRender": this.dataColumns[i].display // how to display the property without affecting the original data
                });
            } else {
                columns.push({
                        "sTitle": this.dataColumns[i].title,
                        "mData": this.dataColumns[i].property // object property
                });
            }

        };
        return columns;

        /* this gets all properties of the data structure
        var properties = [];
        for(var p in data[0]) {
            properties.push(p);
        }
        */
    }

    private applyFooterRow() {
        var $tds = "";
        for (var i=0; i < this.dataColumns.length; i++) {
            $tds += "<td/>";
        }
        var $dataTableHolder: $.JQuery = $("#" + this.dataTableId);
        $dataTableHolder.append($("<tfoot/>").append($("<tr/>").append($tds)));
    }

    private dataTableColumnFilter() {
        this.buildSearchForm();

        // assign searches
        var columns: any[] = [];
        for (var i=0; i < this.dataColumns.length; i++) {
            switch (this.dataColumns[i].type) {
                case dataTableColumn.FilterType.text:
                    columns.push({"sSelector": "#s_" + this.dataColumns[i].property});
                    break;
                case dataTableColumn.FilterType.num:
                    columns.push({"sSelector": "#s_" + this.dataColumns[i].property, type: "number-range"});
                    break;
                case dataTableColumn.FilterType.date:
                    columns.push({"sSelector": "#s_" + this.dataColumns[i].property, type: "date-range"});
                    break;
            };
        };

        this.dataTable.columnFilter({
            sRangeFormat: "from {from} to {to}",
            aoColumns: columns
        });

        this.setupShowSearchForm();

        this.setupOnFilterChange();
    }

    private buildSearchForm() {

        // the search form elements
        var $form = $("<div/>");
        for (var i=0; i < this.dataColumns.length; i++) {
            var $div = $("<div/>").attr("class", "e-row");
            var $label = $("<span/>").attr("class", "e-label").text(this.dataColumns[i].title);
            var $inp = $("<span/>").attr("class", "input").attr("id", "s_" + this.dataColumns[i].property);
            $form.append($div.append($label).append($inp));
        }
        var $searchHolder: $.JQuery = $("#" + this.dataTableSearchId);
        $searchHolder.append($form);
    }

    private setupShowSearchForm() {
        var $el: $.JQuery = $("#" + this.dataTableSearchTriggerId);
        if ($el) {
            $el.click((event: $.JQueryMouseEventObject) => {
                event.preventDefault();
                var $searchHolder: $.JQuery = $("#" + this.dataTableSearchId);
                (<any>$searchHolder).dialog();
            });
        }
    }

    private attachRowClickHandler() {
        var $dataTableHolder: $.JQuery = $("#" + this.dataTableId);
        var wrapperFn = ((data) => { this.clickRow(data) });
        var dataTableFinal = this.dataTable;
        $dataTableHolder.find('tr').click( event => {
            var data = dataTableFinal.fnGetData(event.currentTarget);
            wrapperFn(data);
        });
    }

}
export = DataTable;
