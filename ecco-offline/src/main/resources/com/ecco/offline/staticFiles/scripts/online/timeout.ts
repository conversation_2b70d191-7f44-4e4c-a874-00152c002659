import $ = require("jquery");
import {showModalWithActions} from "../components/MUIConverterUtils";
import {bus} from "@eccosolutions/ecco-common";

declare var urls: any;

let lastBrowserEventInstant = new Date().getTime();
// get ms to compare like with like
const logoutCheckInterval = 5000;
const countdownFrom = 30;
const logoutQuestionTime = 40 * 1000; // this must be more seconds than the countdownFrom
let timerCheckLogoutStatus;
let timerDontLogoutQuestionTimer;
let prevPageTitle: string;

const sessionLenMins = parseInt($("body").attr("data-session-len") || "0");
const sessionLen = sessionLenMins * 60 * 1000;

let lastServerRequestInstant = new Date().getTime();

/** Event indicating that the user did something so, for example, we can prevent session timeout */
export class UserActivityEvent {
    public static bus = bus<UserActivityEvent>();

    constructor(public source: $.JQuery) {
    }
}

// We register events to emit a form change
function emitFormChange( $el: $.JQuery) {
    UserActivityEvent.bus.fire( new UserActivityEvent($el) );
}

// Update last action whenever we get an event
UserActivityEvent.bus.addHandler( (event) => {lastBrowserEventInstant = new Date().getTime();} );

const $countdownSpan = $("<span>");
const $dialog = $("<div>")
    .append(
        $("<p>").attr("id", "dontLogoutQuestionText")
            .text("To keep your site secure, we log you out automatically after a period of inactivity. Do you want to remain logged in?")
    )
    .append($("<p>").append("logging out in ").append($countdownSpan));

// a timeout based on 'last client action' requires us to monitor the keypresses
// this duplicates
$( function() {

    // also on xhr requests - which need to reset the server timer
    $().ajaxStart(function() {lastServerRequestInstant = new Date().getTime();});
    // capture any form change
    // http://stackoverflow.com/questions/1948332/detect-all-changes-to-a-input-type-text-immediately-using-jquery
    $("input:not(input[type=hidden])")
        .bind("propertychange keyup input paste", event => emitFormChange($(event.currentTarget)));
    // and capture textarea on a keypress
    $("textarea").bind("propertychange keyup input paste",event => {
        // jquery site has a demo to trap only ascii
        // we just assume every keypress - and reset the client date
        emitFormChange($(event.currentTarget));
    });
});

function checkLogoutStatus() {
    // see if the serverLastRequest has almost reached the session length
    const now = new Date().getTime();
    // if so, if there has been lastBrowserEventInstant do a get else call dontLogoutQuestion
    if ((now - lastServerRequestInstant) > (sessionLen - logoutQuestionTime)) {
        if ((now - lastBrowserEventInstant) < (sessionLen - logoutQuestionTime)) {
            dontLogout(false);
            return;
        } else {
            dontLogoutQuestion();
            return;
        }
    }
    // if the server is not close to the session end, check that the client isn't
    if ((now - lastBrowserEventInstant) > (sessionLen - logoutQuestionTime)) {
        // if so, dontLogoutQuestion
        dontLogoutQuestion();
        return;
    }
    timerCheckLogoutStatus = setTimeout(checkLogoutStatus, logoutCheckInterval);
}
// keep the session alive
function dontLogout(resetClient = true) {
    // clear the stop timer
    clearTimeout(timerDontLogoutQuestionTimer);
    $.get(urls["noopHidden.url"]);    // this method ignores js actions, and so can trigger incorrectly
    resetTimeout(resetClient);
    document.title = prevPageTitle;
    // ensure we don't carry on in the counting down process and then logout!
    return false;
}
function resetTimeout(resetClient = true) {
    $countdownSpan.text(countdownFrom);
    lastServerRequestInstant = new Date().getTime();
    if (resetClient)
        lastBrowserEventInstant = new Date().getTime();
    timerCheckLogoutStatus = setTimeout(checkLogoutStatus, logoutCheckInterval);
}
function dontLogoutQuestion() {
    prevPageTitle = document.title;
    startQuestionCountdown();

    // we don't want any buttons, but to achieve that we need to be DialogContent adapter
    showModalWithActions("stay logged in", $dialog, [
        {
            label: 'stay logged in',
            onClick: () => { dontLogout(); return Promise.resolve(); },
            style: "primary"
        }
    ], "modal-lg");

    return false; // block the normal move
}
function startQuestionCountdown() {
    // if the timer gets to 0, close the box and leave the page to expire
    const num = parseInt($countdownSpan.text());
    // give us a second to redirect
    if (num < 1) {
        window.location.replace(urls["logoutTimerToUrl"]); // see main_head_jsbase and note that security-redirect doesn't seem to work on logout
        return;
    } else {
        $countdownSpan.text(num - 1);
        document.title = "WARNING: " + (num - 1) + " seconds to automatic logout";
        timerDontLogoutQuestionTimer = setTimeout(startQuestionCountdown, 1000);
    }
}

urls["logoutTimerToUrl"] = urls["logout.url"];

$(function() {
    if (sessionLen > 0) {
        resetTimeout();
    }
    else {
        console.log("timeout.ts: no session length set - user will not see timeout dialog");
    }
});
