import {EccoDate, EccoDateTime} from "@eccosolutions/ecco-common";
import {apiClient, CardSource} from "ecco-components";
import {ReferralAjaxRepository, ResourceList} from "ecco-dto";
import {ReferralsListRow} from "ecco-dto/referral-dto";
import {Observable} from "rxjs";
import {filter, flatMap, map} from "rxjs/operators";
import {TaskCommandAjaxRepository} from "ecco-dto"
import {ReferralRowCard} from "../referral/cards";
import {TaskCard, TaskCardGroup} from "./cards";
import services = require("ecco-offline-data");

const taskRepository = new TaskCommandAjaxRepository(apiClient);
const repository = new ReferralAjaxRepository(apiClient);

// ARCHIVE - 'dueSlaTask' is less used in the system in favour of all TaskStatus
class TaskListSource implements CardSource {

    getCards(adHocOnly = true) {
        const referralsQ: Promise<ResourceList<ReferralsListRow>> = repository.findAllReferrals(null, null, "dueSlaTask", 0, 20);

        const dueTodayReferrals = Observable.fromPromise(referralsQ).pipe(
            filter(resource => !!resource),
            flatMap(resource => resource.data),
            filter(a => a.nextDueSlaDate <= EccoDate.todayUtc().formatIso8601()),
            map(row => new ReferralRowCard(row))); // TODO: TaskCard for a referral row

        const futureReferrals = Observable.fromPromise(referralsQ)
            .filter(resource => !!resource)
            .flatMap(resource => resource.data)
            .filter(a => a.nextDueSlaDate > EccoDate.todayUtc().formatIso8601())
            .map(row => new ReferralRowCard(row));

        // NB activiti only currently
        const tasksQ = services.getFeatureConfigRepository().getSessionData()
            .then( sessionData => {
                const groups = taskRepository.getGroupNamesBelongingToMe(sessionData);
                return taskRepository.getTasksForUser(sessionData.getDto().username, groups);
            });

        let tasksDueToday = Observable.fromPromise(tasksQ).pipe(
            flatMap(a => a),
            filter( task => task.dueDate && EccoDate.parseIso8601FromDateTime(task.dueDate)
                .earlierThanOrEqual(EccoDate.todayLocalTime())),
            map(dto => new TaskCard(dto)));

        let futureTasks = Observable.fromPromise(tasksQ).pipe(
            flatMap(a => a),
            filter( task => task.dueDate && EccoDate.parseIso8601FromDateTime(task.dueDate)
                .laterThan(EccoDate.todayLocalTime())),
            map(dto => new TaskCard(dto)));

        const due = adHocOnly
                ? tasksDueToday
                : Observable.concat<TaskCard|ReferralRowCard>(tasksDueToday, dueTodayReferrals)
        const later = adHocOnly
                ? futureTasks
                : Observable.concat<TaskCard|ReferralRowCard>(futureTasks, futureReferrals)
        return Observable.from([
            new TaskCardGroup("tasks due or overdue", due,
                EccoDateTime.nowUtc().addHours(1)),
            new TaskCardGroup("tasks for later", later,
                EccoDateTime.nowUtc().addDays(2))
        ]);
    }
}
export = TaskListSource;
