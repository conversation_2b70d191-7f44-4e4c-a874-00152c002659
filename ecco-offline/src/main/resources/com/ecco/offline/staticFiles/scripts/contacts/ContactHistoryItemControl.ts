import $ = require("jquery");
import _ = require("lodash");

import BaseControl = require("../controls/BaseControl");
import commands = require("ecco-dto/contacts/commands");
import DL = require("../controls/DefinitionList");
import ContactChangeDto = commands.ContactChangeDto;
import StringChangeOptional = cmdDtos.StringChangeOptional;
import * as cmdDtos from "ecco-dto/command-dto";
import { iso8601UtcToFormatLocalShort } from 'ecco-dto';

var MAX_TITLE_LEN = 50;

class ContactHistoryItemControl extends BaseControl {

    private commandDto: ContactChangeDto;
    private latestTitle: string;
    private isNew: boolean;


    constructor(commandDto: ContactChangeDto, latestTitle: string = "Field changes", isNew = false) {
        var $container = $("<li>");
        super($container);
        this.commandDto = commandDto;
        this.latestTitle = commandDto.userName == "ecco_external" ? `SMS received: "${commandDto.messageBody}"`
            : commandDto.messageBody ? `SMS sent: "${commandDto.messageBody}"`
            : latestTitle;
        this.isNew = isNew;
        this.render();
    }


    private render() {
        var title = this.latestTitle;

        var $title = $("<span>").text(title || "(no title recorded)");
        var $header = $("<div>").addClass("comment");
        $header.append($title);

        this.element().empty()
            .append( _.escape(iso8601UtcToFormatLocalShort(this.commandDto.timestamp)) )

        this.element()
            .append($("<div>").addClass("clearfix").append($("<span>").addClass('user')
                .append(" recorded by ")
                .append( $("<em>").text(this.commandDto.userName) ) ) )
            .append($header);

        var $list = $("<ul>").addClass("list-unstyled changes container-fluid row");
        this.addChangeItem( $list, "title", this.commandDto.title );
        this.addChangeItem( $list, "company name", this.commandDto.companyName );
        this.addChangeItem( $list, "first name", this.commandDto.firstName );
        this.addChangeItem( $list, "last name", this.commandDto.lastName);
        this.addChangeItem( $list, "job title", this.commandDto.jobTitle );
        this.addChangeItem( $list, "mobile", this.commandDto.mobileNumber );
        this.addChangeItem( $list, "landline", this.commandDto.phoneNumber );
        this.element().append($list);
    }

    private addChangeItem($list: $.JQuery, field: string, change: StringChangeOptional) {
        if (change) {
            $list.append(
                $("<div>").addClass("col-sm-12")
//                    .append( $("<div>").addClass("updated").text( field ) )
                    .append(
                        new DL()
//                            .addEntry(field, change.to || "(deleted)")
//                            .addEntry("was:", change.from || "(empty)")
                            .addEntry(field,
                                "from: " + (change.from || "(empty)") +
                                ", to: " + (change.to || "(deleted)"))
                            .element()
                    )
            );
        }
        else {
//            $list.append(
//                $("<div>").addClass("col-sm-4")
//                    .append( $("<div>").addClass("unchanged").text( field ) )
//                    .append( $("<div>").addClass("small").text("(no change)" ) ) );
        }
    }
}
export = ContactHistoryItemControl;
