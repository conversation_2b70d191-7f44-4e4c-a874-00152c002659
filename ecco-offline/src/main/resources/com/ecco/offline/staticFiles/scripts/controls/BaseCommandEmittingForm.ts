import ActionButton = require("../controls/ActionButton");
import Form = require("../controls/Form");
import BaseAsyncCommandForm = require("../cmd-queue/BaseAsyncCommandForm");
import {CommandQueue, MergeableCommand, OfflineCommand} from "ecco-commands";


abstract class BaseCommandEmittingForm extends BaseAsyncCommandForm<void> {

    private submitButton: ActionButton;
    protected form = new Form();
    private submitCallback: (commandQueue: CommandQueue) => void | Promise<void>;


    constructor(private _title: string) {
        super(_title);
        this.submitButton = new ActionButton("save", "saving...")
                .addClass("btn btn-primary")
                .clickSynchronous( () => this.submitForm() )
                .enable();
    }

    protected override enableSubmit() {
        this.submitButton.enable();
    }

    protected override disableSubmit() {
        this.submitButton.disable();
    }

    protected fetchViewData(): Promise<void> {
        return Promise.resolve();
    }

    render() {
        this.element()
            .empty()
            .append(this.form.element());
    }

    protected queueCommand<T extends OfflineCommand>(possibleLegacyCmd: T | MergeableCommand) {
        this.commandQueue.addCommand(possibleLegacyCmd);
    }

    /** register a callback to receive the populated CommandQueue to be processed */
    public onSubmit(submitCallback: (commandQueue: CommandQueue) => void | Promise<void>) {
        this.submitCallback = submitCallback;
        return this;
    }

    /** Override this if you are producing only one command when submit is clicked otherwise
     * queue multiple commands via queueCommand */
    protected getCommandToSend(): OfflineCommand | MergeableCommand {
        return null;
    }

    protected override submitForm() {
        let cmd = this.getCommandToSend();
        if (cmd) {
            this.commandQueue.addCommand(cmd);
        }
        else if (this.commandQueue.size() == 0) {
            console.log("No commands to send (command queue is empty) - consider providing user feedback");
        }
        const promiseOrVoid = this.submitCallback && this.submitCallback( this.commandQueue );
        return Promise.resolve(promiseOrVoid).then(() => super.submitForm());
    }

    public override element() {
        return this.form.element();
    }
    public override getFooter() {
        return this.submitButton.element();
    }
}

export = BaseCommandEmittingForm;