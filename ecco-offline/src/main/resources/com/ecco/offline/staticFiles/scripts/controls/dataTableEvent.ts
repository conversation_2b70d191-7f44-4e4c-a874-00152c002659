import dataTableColumn = require("./dataTableColumn");
import {bus} from "@eccosolutions/ecco-common";

/**
 * Operations on the datatable
 */
export interface ReportDataTable {

    getDataTableSearchId(): string;

    render(entries: any[]): void;

    undoChartFilterApplied(): void;

    chartFilterApplied(searchApplied: string, propertyApplied: string): void;

    dataColumns(): dataTableColumn.DataTableColumn[];

    visibleIds(): number[];

}

/** Event emitted when DataTables column filtering search has been updated.
 */
export class DataTableUpdateEvent {
    public static bus = bus<DataTableUpdateEvent>();

    constructor(public source: ReportDataTable) {
    }
}
