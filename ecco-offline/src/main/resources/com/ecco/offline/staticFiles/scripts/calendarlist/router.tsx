import * as React from "react"

import {Route, Switch} from "react-router";
import CalendarList from "./CalendarList";
import {RouteManagedPage} from "../components/RouteManagedPage";
import * as ReactDom from "react-dom"

// NB this was probably a playground that could be deleted along with all calendarlist files
// /nav/r/calendarlist will show the 'project calendar' report since 'loadReportData' is called
ReactDom.render(
    <RouteManagedPage>
        <Switch>
            <Route path="/nav/r/calendarlist">
                {/* this is the 'project calendar' */}
                <CalendarList chartDefUuid={"04800000-0000-babe-babe-dadafee1600d"}/>
            </Route>
            <Route path="">
                <Route path="**" component={() => <h3>incorrect wiring - should never get here, or just put a '/' on the end?</h3>}/>
            </Route>
        </Switch>
    </RouteManagedPage>,
    document.getElementById("maincontrol"));
