import $ = require("jquery");

import * as cfgDomain from "ecco-dto";
    import ListDefinitionEntry = cfgDomain.ListDefinitionEntry;
import ListDefSelectList = require("./ListDefSelectList");
import {SessionDataRepository} from "ecco-dto";


/**
 * Select list based on select2 which combines hierarchies into one select element with entries of 'x - y - z'
 */
class ListDefSelect2List extends ListDefSelectList {

    /**
     * @param featureConfigRepository optionally provide, as can be provided through init
     */
    constructor($container: $.JQuery,
            featureConfigRepository?: SessionDataRepository,
            onChange?: (value: string) => void,
            private useSelect2 = true,
            private prefixListName = false
        ) {
        super($container, featureConfigRepository, onChange);
    }

    /**
     * Overide the getEntries method to cater for the children of the listName.
     */
    protected override getEntries(f: cfgDomain.SessionData, listName: string): Array<ListDefinitionEntry> {
        var initialEntries = listName != null
                ? f.getListDefinitionEntriesByListName(listName)
                : f.getListDefinitionEntriesAsFlatArray();
        var allEntries = initialEntries.slice(0);
        initialEntries.forEach((entry) => {
            allEntries = allEntries.concat(f.getListDefinitionEntriesUnderParentRecursively(entry.getId()));
        });
        return allEntries;
    }

    protected override getListEntryName(entry: ListDefinitionEntry) {
        if (this.prefixListName) {
            return entry.getListName() + " - " + entry.getFullName();
        } else {
            return entry.getFullName();
        }
    }

    public override populateControl(entries: ListDefinitionEntry[], overrideInitialValue?: string) {
        super.populateControl(entries);
        if (this.useSelect2) {
            this.applySelect2();
        }
    }

    public applySelect2() {
        if (this.getLength() > 10) {
            (<any>$("select", this.element())).select2(); // Declared in require-boot as a shim for this module
        }
    }

}
export = ListDefSelect2List;
