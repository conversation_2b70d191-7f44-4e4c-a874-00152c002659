import $ = require("jquery");

import ActivitiesGroupControl = require("./ActivitiesGroupControl");
import BaseControl = require("../../controls/BaseControl");
import {Activity, AllocationOptions, DemandResource, Rota, TimeSlot, TIME_SLOTS_PER_HOUR} from "ecco-rota";
import WorkerJobRowControl, {TOTAL_PADDING_LEFTRIGHT, timeSlotWidthPx} from "./WorkerJobRowControl";
import {getAllocationParams} from "./allocation";
import {EccoDate, EccoTime} from "@eccosolutions/ecco-common";
import {EventSnapshotDto} from "ecco-dto";

const CONTAINER_CLASS = "rota rota-day";
const WORKERJOB_NAME_HEAD_CLASS = "rota-worker-name-head";
const TIME_CLASS = "rota-time";
const TIME_MAJOR_CLASS = "rota-time-major";
const TIME_MINOR_CLASS = "rota-time-minor";
const TIME_TICK_CLASS = "rota-time-tick";
const TABLE_EXTRA_WIDTH = 50 + TOTAL_PADDING_LEFTRIGHT * 2;
const TABLE_EXTRA_HEIGHT = 50;
const RESOURCE_LABEL_WIDTH = 130;

export class RotaDayView extends BaseControl {
    private readonly $wrapper = $("<div>");
    private $table: $.JQuery;
    private readonly $nowLine = $("<div>").addClass("rota-now-line");
    private unassignedActivitiesGroupControl: ActivitiesGroupControl = null;
    private cancelledActivitiesGroupControl: ActivitiesGroupControl = null;
    private rota: Rota | null = null;
    private liveOverlay: boolean;
    private liveStatus: EventSnapshotDto[] = [];
    private recurringAllocate: boolean;

    constructor(
        private readonly onActivityClick: (activity: Activity) => void,
        private readonly onDragEnd: (activity: Activity) => void,
        private readonly onResourceClick: (resource: DemandResource, showAvailability?: boolean) => void
    ) {
        super();
    }

    getRota() {
        return this.rota;
    }

    /** Update and re-render the rota if it has changed - like React props */
    renderRota(rota: Rota, liveOverlay?: boolean, liveStatus?: EventSnapshotDto[]) {
        if (rota !== this.rota || liveStatus !== this.liveStatus) {
            this.rota = rota;
            this.liveOverlay = !!liveOverlay
            this.liveStatus = liveStatus || []
            this.render();
        }
    }

    updateNowLine(centerPage = false, minutesInDayOverride? : number) {
        // only for today - ignoring day/night boundary for now
        if (!this.rota.getDate().equals(EccoDate.todayLocalTime())) {
            this.$nowLine.hide()
            return;
        }
        if (!this.$table) {
            return
        }
        const distanceTopPage = this.$table.offset().top
        const height = this.$table.outerHeight()
        const extendBy = 25;
        const minutesInDaySoFar = minutesInDayOverride !== undefined ? minutesInDayOverride : (EccoTime.nowLocalTime().getHours() * 60) + EccoTime.nowLocalTime().getMinutes();
        const distanceLeftPage = this.$table.offset().left
        const distanceAtMidnight = distanceLeftPage + RESOURCE_LABEL_WIDTH
        const distancePerMinute = (TIME_SLOTS_PER_HOUR * timeSlotWidthPx) / 60
        // could check the time is within the rota
        //const maxTimeOnRota = TIME_SLOTS_PER_HOUR * this.rota.getTimeSlots().count()
        const left = distanceAtMidnight + (distancePerMinute * minutesInDaySoFar)
        this.$nowLine.css({"top": distanceTopPage - extendBy, "left": left, "height": height+(extendBy*2)})
        this.$nowLine.show()

        if (centerPage) {
            const $window = $(window);
            const lineInMiddleOffset = $window.width() / 2; // width is the visible screen width
            $window.scrollLeft(left - lineInMiddleOffset);
        }
    }

    /** Update and re-render the rota if it has changed - like React props */
    updateRotaDemand(rota: Rota) {
        this.renderRota(this.rota.withAddedDemandFrom(rota));
    }

    updateRecurringAllocation(recurringAllocation: boolean) {
        this.recurringAllocate = recurringAllocation;
    }

    render() {
        const rota = this.rota;

        const $headRow = $("<tr>");
        const $workerJobsGroup = $("<tbody>");
        const $unassignedGroup = $("<tbody>");
        const $cancelledGroup = $("<tbody>");

        // single draggable elements means one run can be very wide, so we fix widths of table and td's
        const tableWidth = (96 * timeSlotWidthPx) + RESOURCE_LABEL_WIDTH;
        this.$table = $("<table>").width(tableWidth);

        this.$wrapper.empty()
            .addClass(CONTAINER_CLASS)
            .append(this.$table
                .append($("<thead>")
                    .append($headRow))
                .append($unassignedGroup)
                .append($workerJobsGroup)
                .append($cancelledGroup));

        this.element()
            .empty()
            .append(this.$nowLine)
            .append(this.$wrapper);


        // Empty td for the workerJob name column.
        const $td = $("<td>").addClass(WORKERJOB_NAME_HEAD_CLASS)
        $td.width(RESOURCE_LABEL_WIDTH)
        $headRow.append($td)

        const timeSlots = rota.getTimeSlots();

        timeSlots.forEach((timeSlot: TimeSlot) => {
            const startTime = timeSlot.getStartTime();

            const $th = $("<th>")
                .addClass(TIME_CLASS)
                .append($("<p>")
                    .text(startTime.formatHoursMinutes()))
                    .width(RESOURCE_LABEL_WIDTH) // .rota th.rota-time p
                .append($("<div>")
                    .addClass(TIME_TICK_CLASS));

            $th.width(timeSlotWidthPx)

            if (startTime.isExactHour()) {
                $th.addClass(TIME_MAJOR_CLASS);
            } else {
                $th.addClass(TIME_MINOR_CLASS);
            }

            $headRow.append($th);
        });

        $headRow.append($("<th>").css({"padding": "0px " + TOTAL_PADDING_LEFTRIGHT + "px"}).text('total'));

        const workerJobsGroupControl = new WorkerJobsGroupControl({
            numTimeSlots: timeSlots.count(),
            workerJobs: rota.getResources(),
            liveOverlay: this.liveOverlay,
            liveStatus: this.liveStatus,
            onActivityClick: this.onActivityClick,
            onResourceClick: this.onResourceClick,
            $dragContainment: this.$wrapper,
            onActivityDragStart: (activity: Activity) => workerJobsGroupControl.dragStartCreateDropTargets(activity),
            onActivityDragEnd: (activity: Activity) => {
                this.onDragEnd(activity);
                workerJobsGroupControl.dragEndClearDropTargets();
            },
            isRecurringAllocate: () => this.recurringAllocate,
            requestAllocationParams: (activity) => {
                return getAllocationParams(activity)}
        });

        const unallocatedActivities: Activity[] = rota.getUnallocatedOrDroppedActivities()
            .filter((activity: Activity) => !activity.isDropped());

        this.unassignedActivitiesGroupControl = new ActivitiesGroupControl({
            numTimeSlots: timeSlots.count(),
            activities: unallocatedActivities,
            labelText: "unassigned",
            // labelActionText: "[+]",
            // labelAction: () => {alert('Would you like to allocate these items to a new run? (enter start/end)')},
            cssClass:  "rota-unassigned-activities-group",
            onActivityClick:  this.onActivityClick,
            $dragContainment: this.$wrapper,
            onActivityDragStart: activity =>
                    workerJobsGroupControl.dragStartCreateDropTargets(activity),
            onActivityDragEnd: (activity: Activity) => {
                this.onDragEnd(activity);
                workerJobsGroupControl.dragEndClearDropTargets();
            }
        });

        const cancelledActivities: Activity[] = rota.getAllActivities()
            .filter((activity: Activity) => {
                return activity.isDropped()
            });

        this.cancelledActivitiesGroupControl = new ActivitiesGroupControl({
            numTimeSlots: timeSlots.count(),
            activities: cancelledActivities,
            labelText: "dropped",
            cssClass:  "rota-cancelled-activities-group",
            onActivityClick:  this.onActivityClick,
            $dragContainment: this.$wrapper,
            onActivityDragStart: activity =>
                    workerJobsGroupControl.dragStartCreateDropTargets(activity),
            onActivityDragEnd: () => workerJobsGroupControl.dragEndClearDropTargets()
        });

        // add total to last row in the table
        const totalHoursStatic = Rota.calculateHours(rota.getAllActivities());
        this.cancelledActivitiesGroupControl.setFinalRotaTotal(totalHoursStatic);

        // Ensure that we get callback to add them back to UI when they're
        // deallocated (irrespective of whether started allocated or not)
        rota.getAllActivities().forEach( (activity: Activity) => {
            this.assignHandlerToActivity(activity);
        });
        /* FIXME: We also need to do correct thing for cancelled activities */

        workerJobsGroupControl.attach($workerJobsGroup);
        this.unassignedActivitiesGroupControl.attach($unassignedGroup);
        this.cancelledActivitiesGroupControl.attach($cancelledGroup);

        // see afc901cc
        // ensure any scroll events are triggered as required
        $(window).scroll();

        // see b81a12e5
        // Fix size of container before refreshing it - so we don't mess up scroll position
        if (this.$table.length > 0) {
            this.$wrapper.css("width", this.$table.width() + TABLE_EXTRA_WIDTH)
                .css("height", this.$wrapper.height()+ TABLE_EXTRA_HEIGHT);
        }
    }

    public assignHandlerToActivity(activity: Activity) {
        // TODO need to do this for out newly arriving 'show breakdown'
        activity.addActivityDeallocatedEventHandler( (event) => {
            if (!event.isHideOnly()) {
                this.unassignedActivitiesGroupControl.onActivityDeallocated(event.getActivity());
            }
        } );
    }
}

interface WorkerJobsGroupControlOptions {
    numTimeSlots: number;
    workerJobs: readonly DemandResource[];
    liveOverlay: boolean
    liveStatus: EventSnapshotDto[]
    onActivityClick: (activity: Activity) => void
    onResourceClick: (workerJob: DemandResource) => void
    $dragContainment: $.JQuery;
    onActivityDragStart?: (activity: Activity) => void;
    onActivityDragEnd?: (activity: Activity) => void;
    isRecurringAllocate: () => boolean;
    requestAllocationParams: (activity: Activity) => Promise<AllocationOptions | null>;
}

class WorkerJobsGroupControl {
    private static readonly WORKERJOBS_GROUP_CLASS = "rota-workers-group";

    private readonly $tbody = $("<tbody>");
    private readonly workerJobRowControls: WorkerJobRowControl[] = [];

    constructor(options: WorkerJobsGroupControlOptions) {
        for (let workerJobIndex = 0; workerJobIndex < options.workerJobs.length; ++workerJobIndex) {
            const workerJob = options.workerJobs[workerJobIndex];

            const $workerJobRow = $("<tr>").appendTo(this.$tbody);
            const workerJobRowControl = new WorkerJobRowControl({
                numTimeSlots: options.numTimeSlots,
                workerJob,
                liveOverlay: options.liveOverlay,
                liveStatus: options.liveStatus,
                onResourceClick: options.onResourceClick,
                onActivityClick: options.onActivityClick,
                $dragContainment: options.$dragContainment,
                onActivityDragStart: options.onActivityDragStart,
                onActivityDragEnd: options.onActivityDragEnd,
                isRecurringAllocate: options.isRecurringAllocate,
                requestAllocationParams: options.requestAllocationParams
            });
            workerJobRowControl.attach($workerJobRow);

            this.workerJobRowControls.push(workerJobRowControl);
        }
    }

    public attach($tbody: $.JQuery) {
        if (!$tbody.is("tbody")) {
            throw new Error("WorkersGroupControl must be attached to a tbody element.");
        }

        this.$tbody.attr("id", $tbody.attr("id"))
            .attr("class", $tbody.attr("class"))
            .addClass(WorkerJobsGroupControl.WORKERJOBS_GROUP_CLASS);

        $tbody.replaceWith(this.$tbody);

        return this.$tbody;
    }

    public dragStartCreateDropTargets(activity: Activity) {
        for (let i = 0; i < this.workerJobRowControls.length; ++i) {
            this.workerJobRowControls[i].dragStartCreateDropTargets(activity);
        }
    }

    public dragEndClearDropTargets() {
        for (let i = 0; i < this.workerJobRowControls.length; ++i) {
            this.workerJobRowControls[i].dragEndClearDropTargets();
        }
    }
}

