import $ = require("jquery");
import URI = require("URI");
import React from 'react';

import MvcUtils = require("../mvc/MvcUtils");
import {SignedAgreementsForm} from "./components/signedAgreements";
import {EvidenceDelegatingForm} from "../evidence/EvidenceDelegatingForm";
import {TaskNames} from "ecco-dto";
import {CustomFormUtils} from "./components/CustomForm";
import {getFeatureConfigRepository} from "ecco-offline-data";
import {mountWithServices} from "../offline/ServicesContextProvider";

// expect /service-recipient/<srid>/task/<taskName>/printable
// other print pages could be the old offline-able pages: http://localhost:8888/ecco-war/nav/referrals/100002/task/needsAssessment
let uri = URI(window.location.href);
const pathParts = MvcUtils.getAppPathComponents(uri);
const entityId = parseInt(pathParts[pathParts.length - 4]);
const taskName = pathParts[pathParts.length - 2];
const $elm = $("<div>");


class PrintTask extends React.Component {

    public override componentDidMount() {

        getFeatureConfigRepository().getSessionData().then(sessionData => {
            const taskDef = sessionData.getTaskDefinitionByName(taskName);
            if (sessionData.isTaskDefinitionAgreement(taskDef)) {
                SignedAgreementsForm.enhanceForPrinting($("#main-content"), () => {
                }, entityId, taskName);

                $("#main-content-wrapper").css({"padding-left": "0px", "padding-right": "0px"})
                $("body").css({'width': '210mm'});
                $(".container").css({'width': '210mm'});

            } else if (taskDef.type == "EVIDENCE_CUSTOMFORM" || taskDef.name == TaskNames.referralDetails) {
                const taskNameGroup = taskName;
                CustomFormUtils.enhanceForPrinting($("#main-content"), () => {
                }, entityId, taskName, taskNameGroup);

                $("#main-content-wrapper").css({"padding-left": "0px", "padding-right": "0px"})
                $("body").css({'width': '210mm'});
                $(".container").css({'width': '210mm'});

            } else {
                EvidenceDelegatingForm.enhance($("#main-content"), entityId, taskName, () => {
                });
            }

        })
    }

    override render() {
        return <></>;
    }
}

mountWithServices(<PrintTask/>, $elm[0]);

const $content = $("body");
$content.append($elm);
