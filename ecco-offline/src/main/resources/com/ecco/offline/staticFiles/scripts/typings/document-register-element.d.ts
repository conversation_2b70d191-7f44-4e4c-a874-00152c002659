
interface Document {
    // Relies on polyfill for non-Chrome browsers. see http://caniuse.com/#search=registerelement
    // Lightweight highly-compatible polyfill is: https://github.com/WebReflection/document-register-element
    // Works in Firefox, IE8+, Desktop Safari, iOS 5.1+, Android 2.2+, etc etc
    // TODO: Using HTMLElement doesn't work despite being able to extend an interface in tsc 1.6+ (bug report?)
    registerElement(tagName: string, implementation: any); //HTMLElement );
}
