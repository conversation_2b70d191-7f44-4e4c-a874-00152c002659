import $ = require("jquery");
import {Uuid} from "@eccosolutions/ecco-crypto";

import ActionButton = require("../../controls/ActionButton");
import BaseControl = require("../../controls/BaseControl");
import * as commandDtos from "ecco-dto/command-dto";
import {CommandQueue} from "ecco-commands";
import DL = require("../../controls/DefinitionList");
import {
    EvidenceDef,
    EvidenceContext,
    EvidenceDisplayOptions,
    ActionInstanceControlData,
    ActionInstanceFeatures,
    HierarchyPosition
} from "ecco-evidence";
import {GoalUpdateCommandDto} from "ecco-dto/evidence-dto";
import EditMyPlanGoalForm = require("./EditMyPlanGoalForm");
import {showFormInModalDom} from "../../components/MUIConverterUtils";
import Panel = require("../../controls/Panel");
import {StringUtils} from "@eccosolutions/ecco-common";
import {SessionData} from "ecco-dto";
import {ActionComponent} from "ecco-dto";
import {GoalUpdateCommand} from "ecco-commands";
import {ActionInstanceControl} from "../evidenceControls";

var MAX_TITLE_LEN = 50;

interface FormFields<V> {
    need?: V;
    objectives?: V;
    actions?: V;
    comments?: V;
}

class MyPlanInstanceControl extends BaseControl implements ActionInstanceControl {

    private $title = $("<span>");
    private $fields: FormFields<$.JQuery> = {};
    private latest: FormFields<string> = {};

    constructor(evidenceContext: EvidenceContext, private sessionData: SessionData, private serviceRecipientId: number,
                private evidenceDef: EvidenceDef, private actionDef: ActionComponent,
                private initialData: ActionInstanceControlData, private controlUuid: Uuid,
                controlFeatures: ActionInstanceFeatures) {
        super($("<div>").addClass("action-instance"));
        this.$fields.need = $("<div>");
        this.$fields.objectives = $("<div>");
        this.$fields.actions = $("<div>");
        this.$fields.comments = $("<div>");
        this.render();
    }

    public init() {
    }

    public applySnapshot(sa: ActionInstanceControlData) {}

    public changedDisplayEvidence(displayOptions: EvidenceDisplayOptions): boolean {
        return false;
    }

    public applyCommand(dto: GoalUpdateCommandDto): void {
        // these need to be individually updatable.. but for now just bash onto screen
        if (dto.goalNameChange) {
            // need is the comment change
            this.updateChange("need", dto.goalNameChange);
            this.$title.text(StringUtils.trimText(dto.goalNameChange.to, MAX_TITLE_LEN));
        }
        if (dto.annotationChange) {
            this.updateChange("objectives", dto.annotationChange["objectives"]);
            this.updateChange("actions", dto.annotationChange["actions"]);
            this.updateChange("comments", dto.annotationChange["comments"]);
        }
    }

    private updateChange(field: keyof FormFields<void>, change: commandDtos.StringChangeOptional) {
        if (change) {
            this.latest[field] = change.to;
            this.$fields[field].text(change.to || "");
        }
    }

    public render() {
        var dl = new DL().addClass("row");
        dl.addEntryJQueryWrapped("need", this.$fields.need, "col-sm-3");
        dl.addEntryJQueryWrapped("objectives", this.$fields.objectives, "col-sm-3");
        dl.addEntryJQueryWrapped("actions", this.$fields.actions, "col-sm-3");
        dl.addEntryJQueryWrapped("comments", this.$fields.comments, "col-sm-3");

        var panel = new Panel();
        panel.bodyElement()
            .append(dl.element())
            .append( new ActionButton("edit")
                .iconClasses("fa fa-pencil")
                .addClass("btn btn-xs btn-primary pull-right")
                .clickSynchronous( () => this.showEditGoalForm() )
                .autoDisable(false)
                .element() );


        this.element().empty().append( panel.element() );
    }

    private showEditGoalForm() {
        var form = new EditMyPlanGoalForm(this.serviceRecipientId, this.initialActionInstanceUuid(), this.latest);
        form.onSubmit( (commandQueue: CommandQueue) => {
            return this.applyCommands(commandQueue)
        });
        showFormInModalDom(form);
    }
    private applyCommands(commandQueue: CommandQueue): Promise<void> {
        return commandQueue.getCommands()
            .then(cmds => {

                if (cmds.length > 1) throw new Error("commandQueue should have only one entry");
                if (cmds.length == 0) return Promise.resolve();

                var loneCommand = cmds[0].toCommandDto(); // we just need to be able to apply after sent

                if (loneCommand.commandName == GoalUpdateCommand.discriminator) {
                    return commandQueue.flushCommands() // this also flushes the email command
                        .then( () => this.applyCommand(<GoalUpdateCommandDto>loneCommand) );
                }
                else {
                    throw new Error("something went wrong");
                }
            });
    }

    public isValid(): boolean {
        return true;
    }
    public isAchieved(): boolean {
        return true;
    }
    public isRelevant(): boolean {
        return true;
    }
    public showSummaryOnly() {
        this.render(); // render as before
    }
    public hasHistory(): boolean {
        return false;
    }
    public initialActionInstanceUuid() {
        // as per BaseActionInstanceControl
        return this.initialData && this.initialData.actionInstanceUuid ? this.initialData.actionInstanceUuid.toString() : null;
    }
    public actionInstanceUuidOrControlUuid() {
        // as per BaseActionInstanceControl
        return this.initialData && this.initialData.actionInstanceUuid ? this.initialData.actionInstanceUuid.toString() : this.controlUuid.toString();
    }
    public getActionDefId() {
        return null;
    }
    public getActionDef() {
        return null;
    }
    public emitChangesTo(queue: CommandQueue) {
        // changes aren't emitted by this control - the modal's push the commands instantly
    }

    getHierarchyPosition(): HierarchyPosition {
        return null;
    }
}
export = MyPlanInstanceControl;
