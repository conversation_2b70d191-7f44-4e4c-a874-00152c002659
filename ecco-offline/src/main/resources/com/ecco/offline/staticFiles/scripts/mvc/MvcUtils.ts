import URI = require("URI");
import * as applicationProperties from "application-properties";
import {NOT_FOUND_ERROR} from "ecco-offline-data";

class MvcUtils {

    public static getApplicationRootUri() {
        var applicationRootUri = URI(applicationProperties.applicationRootPath).normalize();
        if (applicationRootUri.scheme() || applicationRootUri.authority() ||
                applicationRootUri.query() || applicationRootUri.hash() ||
                applicationRootUri.path().charAt(0) !== "/") {
            throw new Error("Invalid application root path");
        }
        return applicationRootUri;
    }

    /** Return string[] of components of target URI, relative to applicationRootUri */
    public static getAppPathComponents(targetUri: URI.URI) {
        return MvcUtils.getTargetPathComponents(MvcUtils.getApplicationRootUri(), targetUri);
    }

    /** Return string[] of components of target URI, relative to rootUri */
    public static getTargetPathComponents(rootUri: URI.URI, targetUri: URI.URI) {
        var relativeTargetUri = URI(targetUri)
                .scheme("")
                .authority("")
                .relativeTo(rootUri);

        var targetPathString = relativeTargetUri.path();

        if (targetPathString.charAt(0) === "/") {
            throw NOT_FOUND_ERROR;
        }

        var targetPath = relativeTargetUri.segmentCoded();

        if (targetPath[0] === "..") {
            throw NOT_FOUND_ERROR;
        }
        return targetPath;
    }
}
export = MvcUtils;