import _ = require("lodash");
import Lazy = require("lazy");
import moment = require("moment");
import * as React from "react"
import {NumberToObjectMap} from "@eccosolutions/ecco-common";
import {apiClient, useServicesContext} from "ecco-components";
import {ContactsAjaxRepository, ReferralAjaxRepository} from "ecco-dto";
import {ReferralSummaryDto} from "ecco-dto/referral-dto";
import {Table} from "react-bootstrap";
import {mountWithServices} from "../../offline/ServicesContextProvider";
import {RowControl} from "../controls/ReferralsListControl";
import * as referralDto from "ecco-dto";
import {sequentialMapAll} from "ecco-commands";

interface Props {
    clientId: number;
    serviceRecipientId?: number;
    onChange?: (referral: ReferralSummaryDto) => void;
}

interface State {
    referralWithoutSecurityIndex?: NumberToObjectMap<ReferralSummaryDto>;
}

/**
 * This is the 'services' tab of a referral file
 */
function ReferralTable(props: {
    referrals: ReferralSummaryDto[]
    onClick: (referralId: number) => void,
    currentSrId?: number
}) {
    const sessionData = useServicesContext().sessionData;
    return <Table hover>
        <thead>
        <tr>
            <th>r-id</th>
            <th>service</th>
            <th>worker</th>
            <th>started</th>
            <th>status</th>
            <th>closed</th>
        </tr>
        </thead>
        <tbody>
            {props.referrals.map(referral => {
                const isAccessbile = !referral._readOnly;
                const linkable = isAccessbile && (referral.serviceRecipientId != props.currentSrId);
                const exitOrRejectedId = referral.exitReasonId || referral.signpostedReasonId;
                return (
                        <tr
                                onClick={linkable ? () => props.onClick(referral.referralId) : null}
                                key={referral.referralId}>
                            <td>
                                {/* TODO - allow open in new tab */}
                                {linkable ? <a>{referral.referralId}</a>
                                        : <span>{referral.referralId}</span>
                                }
                            </td>
                            <td>{sessionData.getServiceCategorisation(referral.serviceAllocationId).serviceName}</td>
                            <td>{referral.supportWorkerDisplayName}</td>
                            <td>{referral.receivingServiceDate && moment.utc(referral.receivingServiceDate).format("YYYY-MM-DD") || ""}</td>
                            <td>{referral.statusMessageKey && RowControl.summariseStatus(referral)}</td>
                            {/* NB Introduce a 'closedDate' if we want a single exited/signposted date. */}
                            <td>{exitOrRejectedId && sessionData.getListDefinitionEntryById(exitOrRejectedId).getDisplayName()}</td>
                        </tr>
                );
            })}
        </tbody>
    </Table>;
}

class ReferralOnSrList extends React.Component<Props, State> {
    private repository: ReferralAjaxRepository = new ReferralAjaxRepository(apiClient);
    private contactRepository = new ContactsAjaxRepository(apiClient);

    public static enhance(clientId: number, $elements: $.JQuery, onChange: (referral: ReferralSummaryDto) => void) {
        let component: ReferralOnSrList;
        $elements.each( (index, element) => {
            mountWithServices(
                <ReferralOnSrList ref = {c => component = c} clientId={clientId} onChange={onChange}/>,
                element);
        });
        return component;
    }

    constructor(props) {
        super(props);
        this.state = {
            referralWithoutSecurityIndex: {}
        };
    }

    public override componentDidMount() {
        this.loadReferralsWithoutSecurityForClientId(this.props.clientId, this.props.serviceRecipientId);
    }

    public override UNSAFE_componentWillReceiveProps(nextProps: Props) {
        this.loadReferralsWithoutSecurityForClientId(nextProps.clientId, nextProps.serviceRecipientId);
    }

    // TODO update referralIndex without loading
    private loadReferralsWithoutSecurityForClientId(clientId: number, serviceRecipientId?: number): void {
        // _readyOnly is verified below
        const loadClient = (clientId) => this.repository.findAllReferralWithoutSecuritySummaryByClient(clientId)
            .then(referrals => {
                 return this.populateSupportWorker(referrals);
            })
            .then((referrals: ReferralSummaryDto[]) => {
                this.setState({
                    referralWithoutSecurityIndex: _.keyBy(referrals, 'referralId')
                });
        });
        if (serviceRecipientId) {
            this.repository.findOneReferralByServiceRecipientId(serviceRecipientId).then(sr => {
                loadClient(sr.clientId).then(r => r);
            });
        } else {
            loadClient(clientId).then(r => r);
        }
    }

    private isNumber(str: string) {
        // Abuse isFinite to eliminate groupBy having created keys "undefined" or "null"
        // [1,null,"null",undefined,"undefined","1"].filter(isFinite)
        // (3) [1, null, "1"]
        return isFinite(str as any as number);
    }
    private populateSupportWorker(data: ReferralSummaryDto[]): Promise<ReferralSummaryDto[]> {
        const byContactId = _.groupBy(data, data => data.supportWorkerId);
        const chunkedSupportWorkerIds = Lazy(Object.keys(byContactId))
                .filter(this.isNumber)
                .map(s => parseInt(s))
                .chunk(10)
                .toArray();
        const singlePromise = (ids: number[]) => this.contactRepository.findAllContactsByContactId(ids)
                .then(contacts =>
                        contacts.forEach(contact => {
                            byContactId[contact.contactId].forEach(d => {
                                if (referralDto.isIndividual(contact)) {
                                    d.supportWorkerDisplayName = contact.firstName + " " + contact.lastName;
                                } else {
                                    d.supportWorkerDisplayName = "id: " + contact.contactId.toString();
                                }
                            })
                        })
                );
        return sequentialMapAll(chunkedSupportWorkerIds, singlePromise)
                .then(() => data);
    }

    private handleReferralClick = (id: number) => {
        const referral = this.state.referralWithoutSecurityIndex[id];
        this.props.onChange && this.props.onChange(referral);
    };
    private handleNewReferralClick() {
        this.props.onChange && this.props.onChange(null);
    }

    override render() {
        let ReferralTableElement;
        let NewReferralLinkElement;

        NewReferralLinkElement = (
            <div className="page-header">
                <a
                    onClick={() => {this.handleNewReferralClick()}}>
                    <span>new referral</span>
                </a>
            </div>
        );

        if (Object.keys(this.state.referralWithoutSecurityIndex).length > 0) {
            ReferralTableElement = <ReferralTable
                referrals={Object.values(this.state.referralWithoutSecurityIndex)}
                onClick={this.handleReferralClick}
                currentSrId={this.props.serviceRecipientId}
            />;
        }

        return (
            <div className='row'>
                <div className='col-xs-12'>
                    {NewReferralLinkElement}
                    {ReferralTableElement}
                </div>
            </div>
        );
    }
}

export = ReferralOnSrList;