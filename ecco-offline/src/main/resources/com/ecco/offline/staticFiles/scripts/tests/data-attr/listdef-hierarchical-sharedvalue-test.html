<!DOCTYPE html>
<html>
<head>
    <title>listdef-hierarchical-sharedvalue-test</title>
    <meta charset="UTF-8" />

    <link rel="stylesheet" href="../../../css/qunit/qunit.css"/>


    <script src="../../lib/require.min.js"></script>
    <script>
        var requirejs_baseUrl = "../../";
        var apiBaseUrl = "../../../../../api/";
        var requirejs_devMode = "true";
    </script>
    <script src="../../common/require-boot.js"></script>
    <script>require(["./tests/data-attr/listdef-hierarchical-sharedvalue-test"])</script>

</head>
<body>
    <div id="qunit"></div>
    <div id="qunit-fixture"></div>

    <b>innermost child value saved</b>
    <div id="test_country2" class="listdef-hierarchical-select-list4"
        data-name-shared="sharedName"
        data-initial-value-shared="11"
        list-name="my-countries"></div>

    <div id="test_language2" class="listdef-hierarchical-select-list4"
        data-name-shared="sharedName"
        data-initial-value-shared="11"
        list-name="my-languages"></div>

    <div id="test_dialect2" class="listdef-hierarchical-select-list4"
        data-name-shared="sharedName"
        data-initial-value-shared="11"
        list-name="my-dialects"></div>

    <b>innermost child value loaded</b>
    <div id="test_country3" class="listdef-hierarchical-select-list5"
        data-name-shared="sharedName2"
        data-initial-value-shared="5"
        list-name="my-countries"></div>

    <div id="test_language3" class="listdef-hierarchical-select-list5"
        data-name-shared="sharedName2"
        data-initial-value-shared="5"
        list-name="my-languages"></div>

    <div id="test_dialect3" class="listdef-hierarchical-select-list5"
        data-name-shared="sharedName2"
        data-initial-value-shared="5"
        list-name="my-dialects"></div>

</body>
</html>
