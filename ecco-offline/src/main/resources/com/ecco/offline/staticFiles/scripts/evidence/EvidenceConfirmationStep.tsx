import * as React from "react"
import {Command, CommandQueue} from "ecco-commands";
import {FC, MutableRefObject, useMemo} from "react";
import CommandHistoryListControl from "../service-recipients/CommandHistoryListControl";
import {ServiceRecipientWithEntities} from "ecco-dto";
import ControlWrapper from "../components/ControlWrapper";
import {SmartSignatureCanvas} from "ecco-components";
import {showReactInModal} from "ecco-components-core";
import {Box, Typography} from "@eccosolutions/ecco-mui";

function CaptureSignature(props: { sigXmlRef: MutableRefObject<string | null> }) {
    return <>
        <Typography variant="subtitle1">
            If the client agrees to the above, you can capture a signature below now,
            or just click 'continue' to skip without a signature at this stage
        </Typography>
        <SmartSignatureCanvas widthRatio={2} onPenUp={canvas => {props.sigXmlRef.current = canvas.toDataURL()}}/>
    </>;
}

export function showEvidenceConfirmation(
    serviceRecipient: ServiceRecipientWithEntities, evidenceAsCmdQueue: CommandQueue, captureSignature: boolean,
    onContinue: (svgXml: string | null) => void,
    onCancel: () => void
) {
    const sig: MutableRefObject<string | null> = {current: null}
    evidenceAsCmdQueue.getCommands().then(commands => // So we can later use getCommandsNoPending()
        showReactInModal(
            "confirmation",
            <>
                <EvidenceSummary serviceRecipient={serviceRecipient} commands={commands} />
                {captureSignature && <CaptureSignature sigXmlRef={sig} />}
            </>,
            {
                onAction: () => onContinue(sig.current),
                action: "continue",
                onCancel: () => onCancel()
            })
    )
}

interface Props {
    serviceRecipient: ServiceRecipientWithEntities
    commands: Command[]
}

const EvidenceSummary: FC<Props> = ({serviceRecipient, commands}) => {

    const control = useMemo(() =>
        // NB should probably be AuditHistoryPaged since that limits the amount of audits also
        CommandHistoryListControl.createWithEntities(serviceRecipient, commands, serviceRecipient.features.hasTopLevelGroup("manager", true)),
        [serviceRecipient, commands])
    return<>
        <Typography variant="subtitle1">
            Please confirm that the following summary is correct before clicking 'continue' or click 'cancel' to make further changes
        </Typography>
        <Box style={{margin: "auto -23px"}}>
            <ControlWrapper control={control}/>
        </Box>
        </>
}