import Lazy = require("lazy");
import * as featureConfigDomain from "ecco-dto";
import * as evidenceDto from "ecco-dto/evidence-dto";
import * as serviceConfigDto from "ecco-dto/service-config-dto";
import Analysers = require("ecco-reports");
import CaseLoadInputElement = Analysers.ReferralReportItem;
import {ReferralDto as Referral, SmartStepStatus} from "ecco-dto";
import {Client} from "ecco-dto/client-dto";
import {
    QuestionAnswerSnapshotDto,
    QuestionnaireAnswersSnapshotDto,
    QuestionnaireWorkDto,
    ReportCriteriaDto,
    SnapshotPeriod
} from "ecco-dto";
import {HactOutcomeEvidenceSurveyDto, HactOutcomeMappingDto, HactSocialValueBankDto} from "ecco-dto/hact-dto";
import {SessionDataDto} from "ecco-dto/session-data/feature-config-dto";

export var client_90010: Client = {
    clientId : 90010,
    code: "C001",
    birthDate: "1991-08-08",
    contactId: null,
    calendarId: null,
    externalSystemRef: null,
    externalSystemSource: null,
    nhs: null,
    ni: null,
    housingBenefit: null,
    residenceId: null,
    residenceName: null,
    title: null,
    firstName: "Joe",
    lastName: "Bloggs",
    discriminator: "client",
    archived: null

    /* client
    communicationNeeds?: string;
    description?: string;
    doctorDetails?: string;
    dentistDetails?: string;
    emergencyKeyCode?: string;
    emergencyKeyWord?: string;
    emergencyDetails?: string;
    medicationDetails?: string;
    preferredContactInfo?: string;

    // person
    birthDate?: string;
    age?: number;
    gender?: string;
    disability?: string;
    ethnicOrigin?: string;
    religion?: string;
    firstLanguage?: string;
    sexualOrientation?: string;

    // individual
    organisationId?: number;
    calendarId?: string;
    jobTitle?: string;
    title: string;
    firstName: string;
    lastName: string;
    preferredContactMethod?: string;

    // contact
    contactId?: number;
    code?: string;
    discriminator: "agency" | "client" | "individual";
    address?: Address;
    addressedLocationId?: number
    phoneNumber?: string;
    mobileNumber?: string;
    email?: string;
    isUser?: boolean;
    */
};
export var referral1: Referral = {
    prefix: 'r',
    serviceRecipientId: 90070,
    serviceAllocationId: -1,
    serviceTypeId: 0,
    referralId : 100070,
    referralCode: "R001",
    requestedDelete: null,
    clientId : 90010,
    contactId: null,
    calendarId: null,
    clientCode: "C001",
    clientDisplayName: "Mock Mike",
    displayName: "Mock Mike",
    firstName: "Mock",
    lastName: "Mike",
    source: "self-referral",
    sourceAgency: null,
    delivererAgencyName: null,
    deliveredById: null,
    deliveredByStartDate: null,
    receivedDate: "11/12/2013",
    srcGeographicAreaId: null,
    consentAgreementDate: null,
    consentSignedId: null,
    dataProtectionAgreementDate: null,
    dataProtectionSignedId: null,
    receivingServiceDate: null,
    firstResponseMadeOn: null,
    firstOfferedInterviewDate: null,
    decisionDate: null,
    decisionMadeOn: null,
    exitedDate: null,
    exitReason: null,
    exitReasonId: null,
    exitComment: null,
    signpostedReason: null,
    signpostedCommentId: 0,
    signpostedAgencyName: null,
    signpostedAgencyId: null,
    signpostedBack: false,
    signpostedComment: '',
    statusMessageKey: "status.started",
    supportWorkerDisplayName: "worker1",
    interviewer1WorkerDisplayName: "worker1",
    supportWorkerId: 80010,
    interviewer1ContactId: 80010,
    calendarEvents: [],
    daysAttending: 31,
    pendingStatusId: null,
    interviewer2ContactId: null,
    interviewLocation: null,
    interviewSetupComments: null,
    interviewDna: null,
    interviewDnaComments: null,
    fundingSourceId: 0,
    fundingSource: '',
    fundingPaymentRef: '',
    fundingAmount: 0,
    fundingReviewDate: '',
    fundingDecisionDate: '',
    fundingHoursOfSupport: 0,
    fundingAccepted: false,
    acceptOnServiceState: 'UNSET',
    decisionReferralMadeOn: null,
    appropriateReferralState: 'UNSET'
};

export var referral2: Referral = {
    prefix: 'r',
    serviceRecipientId: 90072,
    serviceAllocationId: -2,
    serviceTypeId: 0,
    referralId : 100072,
    referralCode: "R002",
    requestedDelete: null,
    clientId : 90012,
    contactId: null,
    calendarId: null,
    clientCode: "C002",
    clientDisplayName: "Mock Fred",
    displayName: "Mock Fred",
    firstName: "Mock",
    lastName: "Fred",
    source: "self-referral",
    sourceAgency: null,
    delivererAgencyName: null,
    deliveredById: null,
    deliveredByStartDate: null,
    receivedDate: "11/12/2012",
    srcGeographicAreaId: null,
    consentAgreementDate: null,
    consentSignedId: null,
    dataProtectionAgreementDate: null,
    dataProtectionSignedId: null,
    receivingServiceDate: null,
    firstResponseMadeOn: null,
    firstOfferedInterviewDate: null,
    decisionDate: null,
    decisionMadeOn: null,
    exitedDate: null,
    exitReason: null,
    exitReasonId: null,
    exitComment: null,
    signpostedCommentId: 0,
    signpostedComment: null,
    signpostedReason: null,
    signpostedAgencyName: null,
    signpostedAgencyId: null,
    signpostedBack: false,
    statusMessageKey: "status.started",
    supportWorkerDisplayName: "worker2",
    interviewer1WorkerDisplayName: "worker2",
    supportWorkerId: 80012,
    interviewer1ContactId: 80012,
    calendarEvents: [],
    daysAttending: 31,
    pendingStatusId: null,
    interviewer2ContactId: null,
    interviewLocation: null,
    interviewSetupComments: null,
    interviewDna: null,
    interviewDnaComments: null,
    fundingSourceId: 0,
    fundingSource: '',
    fundingPaymentRef: '',
    fundingAmount: 0,
    fundingReviewDate: '',
    fundingDecisionDate: '',
    fundingHoursOfSupport: 0,
    fundingAccepted: false,
    acceptOnServiceState: 'UNSET',
    decisionReferralMadeOn: null,
    appropriateReferralState: 'UNSET'
};

export var uuid1 = "ABCDEF12-ABCD-ABCD-ABCD-ABCDEF123451";
export var uuid2 = "ABCDEF12-ABCD-ABCD-ABCD-ABCDEF123452";
export var uuid3 = "ABCDEF12-ABCD-ABCD-ABCD-ABCDEF123453";
export var uuid4 = "ABCDEF12-ABCD-ABCD-ABCD-ABCDEF123454";
export var uuid5 = "ABCDEF12-ABCD-ABCD-ABCD-ABCDEF123455";
export var uuid6 = "ABCDEF12-ABCD-ABCD-ABCD-ABCDEF123456";

export var work1: evidenceDto.SupportWork = {
    "id": uuid1,
    requestedDelete: null,
    "parentCode": "a-2",
    "parentPrefix": "r",
    "serviceRecipientId": 200072,
    "authorDisplayName": "sysadmin",
    "comment": null,
    "commentTypeId": null,
    minsSpent: 0,
    signatureId: null,
    workDate: "2014-12-16T00:00",
    createdDate: "2014-12-16T15:23:12",
    actions:[],
    associatedActions: [],
    taskName: "needsReduction"
} as evidenceDto.SupportWork;

export var actions1: evidenceDto.SupportAction[] = [
    {"id":101812,actionInstanceUuid: "someUUID", parentActionInstanceUuid: null, hierarchy: null, position: null, "workId":null, workDate:null, "targetDateTime":null,"name":"Support to maximise income e.g. ...","goalName":"pg","goalPlan":"","status":3,"statusChange":true,"actionId":113,"actionGroupId":83,"outcomeId":83,"score":8,"statusChangeReasonId":null},
    {"id":101725,actionInstanceUuid: "otherUUID", parentActionInstanceUuid: null, hierarchy: null, position: null, "workId":null, workDate:null, "targetDateTime":null,"name":"Accept support and engage with keyworker...","goalName":"read jobs section in paper","goalPlan":"","status":1,"statusChange":true,"actionId":119,"actionGroupId":83,"outcomeId":83,"score":null,"statusChangeReasonId":null}
] as evidenceDto.SupportAction[];

export var work: evidenceDto.SupportWork[] =
[
    {"id":uuid1,"parentCode": "a-2", "parentPrefix": "r", "serviceRecipientId":200072,"authorDisplayName":"sysadmin","comment":null,"commentType":null,"minsSpent":0,
        requestedDelete: null,
        workDate: "2014-12-16T00:00",
        createdDate: "2014-12-16T15:24:32.000",
        signatureId: null,
        actions: actions1,
        associatedActions: [],
        taskName: "needsReduction"
        },
    {"id":uuid2,"parentCode": "a-2", "parentPrefix": "r", "serviceRecipientId":200072,"authorDisplayName":"sysadmin","comment":"jkjkjk","commentType":null,"minsSpent":0,
        requestedDelete: null,
        workDate: "2014-12-16T00:00",
        createdDate: "2014-12-16T15:24:12.123",
        signatureId: null,
        actions:[],
        associatedActions: [],
        taskName: "needsReduction"},
    {"id":uuid3,"parentCode": "a-2", "parentPrefix": "r", "serviceRecipientId":200072,"authorDisplayName":"sysadmin","comment":"dfdfd","commentType":null,"minsSpent":0,
        requestedDelete: null,
        workDate: "2014-12-16T00:00:00.000",
        createdDate: "2014-12-16T15:13:12.234",
        signatureId: null,
        actions:[],
        associatedActions: [],
        taskName: "needsReduction"},
    {"id":uuid4,"parentCode": "a-2", "parentPrefix": "r", "serviceRecipientId":200072,"authorDisplayName":"sysadmin","comment":"\n","commentType":null,"minsSpent":0,
        requestedDelete: null,
        workDate: "2014-12-16T00:00",
        createdDate: "2014-12-16T15:33:12",
        signatureId: null,
        actions:[],
        associatedActions: [],
        taskName: "needsReduction"},
//    {"id":101726,"parentCode": "a-2", "parentPrefix": "r", "serviceRecipientId":200072,"authorDisplayName":"sysadmin","comment":"say aaa","commentType":null,"minsSpent":0,"workDate":"December 9, 2014",
//        "actions":[
//            {"id":101725,"targetDate":null,"name":"Accept support and engage with keyworker...","comment":"read jobs section in paper","status":1,"actionId":119,"actionGroupId":83,"outcomeId":83,"score":null}
//        ]},
//    {"id":101716,"parentCode": "a-2", "parentPrefix": "r", "serviceRecipientId":200072,"authorDisplayName":"sysadmin","comment":"qqqqq","commentType":null,"minsSpent":0,"workDate":"December 9, 2014",
//        "actions":[{"id":101717,"targetDate":null,"name":"Support to maximise income e.g. ...","comment":null,"status":3,"actionId":113,"actionGroupId":82,"outcomeId":82,"score":null}
//        ]},
//    {"id":101709,"parentCode": "a-2", "parentPrefix": "r", "serviceRecipientId":200072,"authorDisplayName":"sysadmin","comment":null,"commentType":null,"minsSpent":0,"workDate":"December 9, 2014",
//        "actions":[
//            {"id":101708,"targetDate":null,"name":"Engage and accept support from keyworker the impact substance mis-use has on physical and mental health.","comment":null,"status":1,"actionId":106,"actionGroupId":81,"outcomeId":81,"score":3}
//        ]},
//    {"id":101706,"parentCode": "a-2", "parentPrefix": "r", "serviceRecipientId":200072,"authorDisplayName":"sysadmin","comment":null,"commentType":null,"minsSpent":0,"workDate":"December 9, 2014",
//        "actions":[
//            {"id":101705,"targetDate":null,"name":"Discuss with keyworker practical steps that can be taken to avoid substance mis-use.","comment":null,"status":1,"actionId":107,"actionGroupId":81,"outcomeId":81,"score":7}
//        ]},
//    {"id":101699,"parentCode": "a-2", "parentPrefix": "r", "serviceRecipientId":200072,"authorDisplayName":"sysadmin","comment":"qaqaq","commentType":null,"minsSpent":0,"workDate":"December 9, 2014",
//        "actions":[
//            {"id":101700,"targetDate":null,"name":"Accept support and engage with keyworker to investigate any opportunities with employment Agency opportunities","comment":null,"status":1,"actionId":119,"actionGroupId":83,"outcomeId":83,"score":null}
//        ]},
    {"id":uuid5,"parentCode": "a-2", "parentPrefix": "r", "serviceRecipientId":200072,"authorDisplayName":"sysadmin","comment":"qaqaqa","commentTypeId":null,"minsSpent":0,
    requestedDelete: null,
    workDate: "2014-12-09T00:00",
    createdDate: "2014-12-09T15:23:12",
    signatureId: null,
        "actions":[],
        associatedActions: [],
        taskName: "needsReduction"},
    {"id":uuid6,"parentCode": "a-2", "parentPrefix": "r", "serviceRecipientId":200072,"authorDisplayName":"sysadmin","comment":"3 Nov","commentTypeId":null,"minsSpent":0,
    requestedDelete: null,
    workDate: "2014-11-12T00:00",
    createdDate: "2014-11-12T12:23:12",
    signatureId: null,
    "actions":[],
    associatedActions: [],
    taskName: "needsReduction"}
] as evidenceDto.SupportWork[];


export var referrals: CaseLoadInputElement[] = [{
    referral: referral1,
    supportWork: Lazy(work),
    sessionData: null
    },
    {
    referral: referral2,
    supportWork: Lazy(work),
    sessionData: null
    }];


/**
 * Questionnaire Movement
 */

// 1st value
export var client1QnAnswer1: QuestionAnswerSnapshotDto[] = [
    {
        id: 2001,
        questionId: 100, // What is your employment status?
        answer: "10", // Unemployed
        workDate: "2017-09-18T00:00" // only used in breakdown data

    }
/* // can't currently test across questions
    ,
    {
        id: 2002,
        questionId: 200, // Have you recently been losing confidence in yourself
        answer: "1" // Not at all
    }
*/
];

// 2nd value
export var client1QnAnswer2: QuestionAnswerSnapshotDto[] = [
    {
        id: 2003,
        questionId: 100, // What is your employment status?
        answer: "4", // Self-employment
        workDate: "2017-09-18T00:00" // only used in breakdown data
    }
/* // can't currently test across questions
    ,
    {
        id: 2004,
        questionId: 300, // How well would you say you yourself are managing financially these days?
        answer: "1" // Living comfortably
    }
*/
];

export var client1QnSnapshot1: QuestionnaireAnswersSnapshotDto = {
    evidenceGroupKey: "generalQuestionnaire",
    serviceRecipientId: 200072,

    // populated client side
    referralSummary: null,
    snapshotPeriod: SnapshotPeriod.PRE,
    sessionData: null,
    // populated client side

    answers: client1QnAnswer1
};

export var client1QnSnapshot2: QuestionnaireAnswersSnapshotDto = {
    evidenceGroupKey: "generalQuestionnaire",
    serviceRecipientId: 200072,

    // populated client side
    referralSummary: null,
    snapshotPeriod: SnapshotPeriod.DURING1,
    sessionData: null,
    // populated client side

    answers: client1QnAnswer2
};

// as per client1QnSnapshot2 BUT with a different snapshot period
// NB we need a different javascript object - else the same object confuses the test!
export var client1QnAnswer2b: QuestionAnswerSnapshotDto[] = [
    {
        id: 2003,
        questionId: 100, // What is your employment status?
        answer: "4", // Self-employment
        workDate: "2017-09-18T00:00" // only used in breakdown data
    }];
export var client1QnSnapshot2b: QuestionnaireAnswersSnapshotDto = {
    evidenceGroupKey: "generalQuestionnaire",
    serviceRecipientId: 200072,

    // populated client side
    referralSummary: null,
    snapshotPeriod: SnapshotPeriod.DURING2,
    sessionData: null,
    // populated client side

    answers: client1QnAnswer2b
};


/**
 * HACT test data
 *
 * config summary
 * --------------
 * Smart step actionDefId 105 can trigger the survey 'Employment Status' which includes these outcomes:
 *      EMP1401 / EMP1402 / EMP1403
 *      NB actionDefIds are not configured in test data
 * EMP1401 triggers the employment survey, defined as 'What is your employment status?'
 *
 * Smart step actionDefId 200 can trigger the survey 'Health' which includes these outcomes:
 *      HEA1401
 * HEA1401 triggers the health survey, defined as 'Have you recently been losing confidence in yourself?'
 *
 * Smart step actionDefId 300,301 can trigger the survey 'Financial Comfort' which includes these outcomes:
 *      FIN1406 has 2 valuable answers: Doing alright / Living comfortably
 * FIN1406 triggers the financial survey, defined as 'How well would you say you yourself are managing financially these days?'
 *
 * Smart step actionDefId 400 can trigger the survey 'Financial Comfort' which includes these outcomes:
 *      FIN1407 has 1 valuable answer: Yes
 * FIN1407 triggers the financial survey, defined as 'Do you have regular access to the internet?'
 *
 * client work summary
 * -------------------
 * UUID1001: referral1 with support work with
 *      actionDefId 105
 *      & actionDefId 200
 *      & actionDefId 300
 *      5th April
 * UUID2001b: referral1 with questionnaire work with
 *      question 300 answer 'Doing alright'
 *      8th April
 * UUID2001: referral1 with questionnaire work with
 *      question 100 answer 'Unemployed'
 *      & question 200 answer 'Not at all'
 *      8th April
 * UUID2002: referral1 with questionnaire work with
 *      question 100 answer 'Self-Employment'
 *      & question 300 answer 'Living comfortably'
 *      8th Aug
 * UUID4001: referral1 with questionnaire work with
 *      question 400 answer 'No'
 *      8th Aug
 */


/**
 * HACT Config
 */

// SOCIAL VALUE BANK
export var hactValueBank: HactSocialValueBankDto[] = [
    {
        id: 1, hactOutcomeDefCode: "EMP1401", outcomeCategory: "Employment", outcomeName: "Full-time employment",
        valueAnywhereUnknown: 10767, valueAnywhere24: 9780, valueAnywhere49: 11688, valueAnywhereHigher: 10199,
        valueLondonUnknown: 10714, valueLondon24: 6460, valueLondon49: 11671, valueLondonHigher: 12502,
        valueOutsideLondonUnknown: 10767, valueOutsideLondon24: 10036, valueOutsideLondon49: 11705, valueOutsideLondonHigher: 10054
        ,percentDropYear1: 0,percentDropYear2: 0,percentDropYear3: 0,percentDropYear4: 0,percentDropYear5: 0,percentDropYear6: 0,percentDropYear7: 0
    },
    {
        id: 2, hactOutcomeDefCode: "EMP1402", outcomeCategory: "Employment", outcomeName: "Self-employment",
        valueAnywhereUnknown: 11588, valueAnywhere24: 13157, valueAnywhere49: 11952, valueAnywhereHigher: 10538,
        valueLondonUnknown: 12116, valueLondon24: 16471, valueLondon49: 12406, valueLondonHigher: 11027,
        valueOutsideLondonUnknown: 11537, valueOutsideLondon24: 12848, valueOutsideLondon49: 11887, valueOutsideLondonHigher: 10520
        ,percentDropYear1: 0,percentDropYear2: 0,percentDropYear3: 0,percentDropYear4: 0,percentDropYear5: 0,percentDropYear6: 0,percentDropYear7: 0
    },
    {
        id: 3, hactOutcomeDefCode: "EMP1403", outcomeCategory: "Employment", outcomeName: "Part-time employment",
        valueAnywhereUnknown: 1229, valueAnywhere24: 737, valueAnywhere49: 1824, valueAnywhereHigher: 1966,
        valueLondonUnknown: 1966, valueLondon24: 1966, valueLondon49: 1966, valueLondonHigher: 1966,
        valueOutsideLondonUnknown: 1176, valueOutsideLondon24: 737, valueOutsideLondon49: 1850, valueOutsideLondonHigher: 1966
        ,percentDropYear1: 0,percentDropYear2: 0,percentDropYear3: 0,percentDropYear4: 0,percentDropYear5: 0,percentDropYear6: 0,percentDropYear7: 0
    },
    {
        id: 4, hactOutcomeDefCode: "HEA1401", outcomeCategory: "Health", outcomeName: "High confidence (adult)",
        valueAnywhereUnknown: 13080, valueAnywhere24: 14224, valueAnywhere49: 13065, valueAnywhereHigher: 12565,
        valueLondonUnknown: 13188, valueLondon24: 15264, valueLondon49: 12801, valueLondonHigher: 12817,
        valueOutsideLondonUnknown: 13065, valueOutsideLondon24: 14152, valueOutsideLondon49: 13096, valueOutsideLondonHigher: 12549
        ,percentDropYear1: 0,percentDropYear2: 0,percentDropYear3: 0,percentDropYear4: 0,percentDropYear5: 0,percentDropYear6: 0,percentDropYear7: 0
    },
    {
        id: 5, hactOutcomeDefCode: "FIN1407", outcomeCategory: "Finance", outcomeName: "Access to internet",
        valueAnywhereUnknown: 1875, valueAnywhere24: 3000, valueAnywhere49: 3000, valueAnywhereHigher: 1125,
        valueLondonUnknown: 3000, valueLondon24: 3000, valueLondon49: 3000, valueLondonHigher: 2706,
        valueOutsideLondonUnknown: 1150, valueOutsideLondon24: 3000, valueOutsideLondon49: 3000, valueOutsideLondonHigher: 1125
        ,percentDropYear1: 0,percentDropYear2: 0,percentDropYear3: 0,percentDropYear4: 0,percentDropYear5: 0,percentDropYear6: 0,percentDropYear7: 0
    }
];

// 'hact' QUESTION GROUP
export var hactQnsAnswerChoices1: serviceConfigDto.QuestionAnswerChoice[] = [
    {
        id: 150,
        displayValue: "Prefer not to answer",
        value: "0",
        disabled: false
    },
    {
        id: 151,
        displayValue: "Unemployed",
        value: "1",
        disabled: false
    },
    {
        id: 152,
        displayValue: "Full-time employment",
        value: "2",
        disabled: false
    },
    {
        id: 153,
        displayValue: "Part-time employment",
        value: "3",
        disabled: false
    },
    {
        id: 154,
        displayValue: "Self-employment",
        value: "4",
        disabled: false
    }
];
export var hactQnsAnswerChoices2: serviceConfigDto.QuestionAnswerChoice[] = [
    {
        id: 200,
        displayValue: "Prefer not to answer",
        value: "0",
        disabled: false
    },
    {
        id: 201,
        displayValue: "Not at all",
        value: "1",
        disabled: false
    },
    {
        id: 202,
        displayValue: "No more than usual",
        value: "2",
        disabled: false
    },
    {
        id: 203,
        displayValue: "Rather more than usual",
        value: "3",
        disabled: false
    },
    {
        id: 204,
        displayValue: "Much more than usual",
        value: "4",
        disabled: false
    }
];
export var hactQnsAnswerChoices3: serviceConfigDto.QuestionAnswerChoice[] = [
    {
        id: 300,
        displayValue: "Prefer not to answer",
        value: "0",
        disabled: false
    },
    {
        id: 301,
        displayValue: "Living comfortably",
        value: "1",
        disabled: false
    },
    {
        id: 302,
        displayValue: "Doing alright",
        value: "2",
        disabled: false
    },
    {
        id: 303,
        displayValue: "Just about getting by",
        value: "3",
        disabled: false
    },
    {
        id: 304,
        displayValue: "Finding it quite difficult",
        value: "4",
        disabled: false
    },
    {
        id: 305,
        displayValue: "Finding it very difficult",
        value: "5",
        disabled: false
    }
];
export var hactQnsAnswerChoices4: serviceConfigDto.QuestionAnswerChoice[] = [
    {
        id: 400,
        displayValue: "Prefer not to answer",
        value: "0",
        disabled: false
    },
    {
        id: 401,
        displayValue: "Yes",
        value: "1",
        disabled: false
    },
    {
        id: 402,
        displayValue: "No",
        value: "2",
        disabled: false
    }
];
export var hactQns: serviceConfigDto.Question[] = [
    {
        id: 100,
        name: "What is your employment status?",
        disabled: false,
        freeTypes: [],
        orderby: 0,
        answerRequired: false,
        choices: hactQnsAnswerChoices1,
        parameters: {}
    },
    {
        id: 200,
        name: "Have you recently been losing confidence in yourself?",
        disabled: false,
        orderby: 0,
        freeTypes: [],
        answerRequired: false,
        choices: hactQnsAnswerChoices2,
        parameters: {}
    },
    {
        id: 300,
        name: "How well would you say you yourself are managing financially these days?",
        disabled: false,
        orderby: 0,
        freeTypes: [],
        answerRequired: false,
        choices: hactQnsAnswerChoices3,
        parameters: {}
    },
    {
        id: 400,
        name: "Do you have regular access to the internet?",
        disabled: false,
        orderby: 0,
        freeTypes: [],
        answerRequired: false,
        choices: hactQnsAnswerChoices4,
        parameters: {}
    }
];
export var hactQnGroups: serviceConfigDto.QuestionGroup[] = [
    {
        id: 100,
        name: "hact",
        disabled: false,
        questions: hactQns,
        parameters: null
    }
];

// HACT MAPPING actionDefId -> hact outcomes
// config per customer
export var hactOutcomeMapping: HactOutcomeMappingDto[] = [
    {
        id: 1,
        hactOutcomeDefCode: "EMP1401",
        actionDefId: 105
    },
    {
        id: 2,
        hactOutcomeDefCode: "EMP1402",
        actionDefId: 105
    },
    {
        id: 3,
        hactOutcomeDefCode: "EMP1403",
        actionDefId: 105
    },
    {
        id: 4,
        hactOutcomeDefCode: "HEA1401",
        actionDefId: 200
    },
    {
        id: 5,
        hactOutcomeDefCode: "FIN1406",
        actionDefId: 300
    },
    {
        id: 6,
        hactOutcomeDefCode: "FIN1406",
        actionDefId: 301
    },
    {
        id: 7,
        hactOutcomeDefCode: "FIN1407",
        actionDefId: 400 // this doesn't actually need defining anywhere
    }
];

// HACT outcome -> surveys (questions in the 'hact' questiongroup)
export var hactOutcomeEvidenceSurveys: HactOutcomeEvidenceSurveyDto[] = [
    {
        id: 1,
        hactOutcomeDefCode: "EMP1401",
        evidenceDescription: "Full-time employment",
        questionDefId: 100,
        valuableAnswerChoiceDefIds: [152]
    },
    {
        id: 2,
        hactOutcomeDefCode: "EMP1402",
        evidenceDescription: "Self employment",
        questionDefId: 100,
        valuableAnswerChoiceDefIds: [154]
    },
    {
        id: 3,
        hactOutcomeDefCode: "EMP1403",
        evidenceDescription: "Part-time employment",
        questionDefId: 100,
        valuableAnswerChoiceDefIds: [153]
    },
    {
        id: 4,
        hactOutcomeDefCode: "HEA1401",
        evidenceDescription: "High confidence (adult)",
        questionDefId: 200,
        valuableAnswerChoiceDefIds: [201]
    },
    {
        id: 5,
        hactOutcomeDefCode: "FIN1406",
        evidenceDescription: "Financial comfort",
        questionDefId: 300,
        valuableAnswerChoiceDefIds: [301,302]
    },
    {
        id: 6,
        hactOutcomeDefCode: "FIN1407",
        evidenceDescription: "Financial comfort",
        questionDefId: 400,
        valuableAnswerChoiceDefIds: [401]
    }
];


// SESSION DATA
const sessionDataDto: SessionDataDto = {
    userId: "1",
    calendarId: "calId",
    calendarIdUserReferenceUri: "calId",
    individualUserSummary: null,
    roles: [],
    softwareModulesEnabled: {},
    featureSets: {},
    listDefinitions: {},
    appointmentTypes: [],
    taskDefinitions: [],
    restrictedServicesProjects: null,
    services: [],
    projects: [],
    serviceCategorisations: [],
    serviceTypesById: {},
    signpostReasons: [],
    exitReasons: [],
    flags: [],
    settings: {},
    supportOutcomes: [],
    riskAreas: [],
    questionGroups: hactQnGroups,
    languages: [],
    religions: [],
    agencyCategories: [],
    restrictedServiceCategorisations: []
} as any as SessionDataDto;

export var testSessionData: featureConfigDomain.SessionData = new featureConfigDomain.SessionData(
    sessionDataDto as SessionDataDto
);

// HACT SESSION DATA
export var testHactSessionData: featureConfigDomain.HactSessionData = new featureConfigDomain.HactSessionData(
    hactOutcomeMapping,
    hactOutcomeEvidenceSurveys,
    [],
    hactValueBank,
    hactQnGroups[0].questions);


/**
 * HACT CLIENT DATA: support work for smart step triggers
 */
export var clientHactSupportAction1: evidenceDto.SupportAction[] = [
    {
        id: 1001,
        workId: null,
        workDate: null,
        targetDateTime: null,
        name: "trigger outcome EMP1401, EMP1402, EMP1403 with the same question for each: 'What is your employment status'",
        goalName: null,
        goalPlan:null,
        hierarchy: null,
        position: null,
        status: SmartStepStatus.WantToAchieve,
        statusChange: true,
        actionId: 105,
        actionInstanceUuid: "someUUID",
        parentActionInstanceUuid: null,
        score: null,
        statusChangeReasonId: null
    },
    {
        id: 1002,
        workId: null,
        workDate: null,
        targetDate: null,
        name: "trigger outcome HEA1401 with question: 'Have you recently been losing confidence in yourself'",
        goalName: null,
        goalPlan:null,
        hierarchy: null,
        position: null,
        status: SmartStepStatus.WantToAchieve,
        statusChange: true,
        actionId: 200,
        actionInstanceUuid: "otherUUID",
        parentActionInstanceUuid: null,
        score: null,
        statusChangeReasonId: null
    },
    {
        id: 1003,
        workId: null,
        workDate: null,
        targetDate: null,
        name: "trigger outcome FIN1406 with question: 'How well would you say you yourself are managing financially these days?'",
        goalName: null,
        goalPlan:null,
        hierarchy: null,
        position: null,
        status: SmartStepStatus.WantToAchieve,
        statusChange: true,
        actionId: 300,
        actionInstanceUuid: "anotherUUID",
        parentActionInstanceUuid: null,
        score: null,
        statusChangeReasonId: null
    }
] as evidenceDto.SupportAction[];

export var clientHactSupportWork1: evidenceDto.SupportWork[] = [
    {
        id: "UUID1001",
        requestedDelete: null,
        serviceRecipientId: 200072,
        parentCode: "100070",
        parentPrefix: "r",
        authorDisplayName: "Mini Me",
        comment: "1st Support Work",
        createdDate: "2016-04-05T00:00",
        workDate: "2016-04-05T00:00",
        commentTypeId: null,
        minsSpent: 25,
        signatureId: null,
        taskName: "needsAssessment",
        actions: clientHactSupportAction1
    }
] as evidenceDto.SupportWork[];

export var clientHactSupportAction2: evidenceDto.SupportAction[] = [
    {
        id: 4001,
        workId: null,
        workDate: null,
        targetDateTime: null,
        name: "trigger outcome FIN1407 with the question: 'Do you have regular access to the internet?'",
        goalName: null,
        goalPlan:null,
        hierarchy: null,
        position: null,
        status: SmartStepStatus.WantToAchieve,
        statusChange: true,
        actionId: 400,
        actionInstanceUuid: "someUUID",
        parentActionInstanceUuid: null,
        score: null,
        statusChangeReasonId: null
    }
] as evidenceDto.SupportAction[];

export var clientHactSupportWork2: evidenceDto.SupportWork[] = [
    {
        id: "UUID4001",
        requestedDelete: null,
        serviceRecipientId: 200072,
        parentCode: "100070",
        parentPrefix: "r",
        authorDisplayName: "Mini Me",
        comment: "1st Support Work",
        createdDate: "2016-04-08T00:00",
        workDate: "2016-04-08T00:00",
        commentTypeId: null,
        minsSpent: 25,
        signatureId: null,
        taskName: "needsAssessment",
        actions: clientHactSupportAction2
    }
] as evidenceDto.SupportWork[];

/**
 * HACT CLIENT DATA: questionnaire work for hact answers
 */

// 1st value
export var clientHactQnAnswer1: QuestionAnswerSnapshotDto[] = [
    {
        id: 2001,
        questionId: 100, // What is your employment status?
        answer: "1" // Unemployed
    },
    {
        id: 2002,
        questionId: 200, // Have you recently been losing confidence in yourself
        answer: "1" // Not at all
    }
] as QuestionAnswerSnapshotDto[];
export var clientHactQnWork1: QuestionnaireWorkDto = {
        id: "UUID2001",
        requestedDelete: null,
        serviceRecipientId: 200072,
        parentCode: "100070",
        parentPrefix: "r",
        authorDisplayName: "Mini Me",
        comment: "1st Survey Value",
        createdDate: "2016-04-08T00:00",
        workDate: "2016-04-08T00:00",
        commentTypeId: null,
        minsSpent: 45,
        signatureId: null,
        taskName: "hactQuestionnaire",
        answers: clientHactQnAnswer1
    } as QuestionnaireWorkDto;

// 2nd value
// see HactNotificationHandlerData.NotificationSummaryData
export var clientHactQnAnswer2: QuestionAnswerSnapshotDto[] = [
    {
        id: 2003,
        questionId: 100, // What is your employment status?
        answer: "4" // Self-employment
    },
    {
        id: 2004,
        questionId: 300, // How well would you say you yourself are managing financially these days?
        answer: "1" // Living comfortably
    }
] as QuestionAnswerSnapshotDto[];
export var clientHactQnWork2: QuestionnaireWorkDto = {
        id: "UUID2002",
        requestedDelete: null,
        serviceRecipientId: 200072,
        parentCode: "100070",
        parentPrefix: "r",
        authorDisplayName: "Mini Me",
        comment: "2nd Survey Value",
        createdDate: "2016-08-08T00:00", // > daysPreToPost1 (90 days) < daysPreToPost1Limit (8 months)
        workDate: "2016-08-08T00:00",
        commentTypeId: null,
        minsSpent: 55,
        signatureId: null,
        taskName: "hactQuestionnaire",
        answers: clientHactQnAnswer2
    } as QuestionnaireWorkDto;
export var clientHactQnWork2b: QuestionnaireWorkDto = {
        id: "UUID2002",
        requestedDelete: null,
        serviceRecipientId: 200072,
        parentCode: "100070",
        parentPrefix: "r",
        authorDisplayName: "Mini Me",
        comment: "2nd Survey Value",
        createdDate: "2016-08-08T00:00",
        workDate: "2016-08-08T00:00",
        commentTypeId: null,
        minsSpent: 55,
        signatureId: null,
        taskName: "hactQuestionnaire",
        answers: clientHactQnAnswer1
    } as QuestionnaireWorkDto;

// 1st value
export var clientHactQnAnswer1b: QuestionAnswerSnapshotDto[] = [
    {
        id: 2005,
        questionId: 300, // How well would you say you yourself are managing financially these days?
        answer: "2" // Doing alright
    }
] as QuestionAnswerSnapshotDto[];
export var clientHactQnWork1b: QuestionnaireWorkDto = {
    id: "UUID2001b",
    requestedDelete: null,
    serviceRecipientId: 200072,
    parentCode: "100070",
    parentPrefix: "r",
    authorDisplayName: "Mini Me",
    comment: "1st Survey Value",
    createdDate: "2016-04-08T00:00",
    workDate: "2016-04-08T00:00",
    commentTypeId: null,
    minsSpent: 55,
    signatureId: null,
    taskName: "hactQuestionnaire",
    answers: clientHactQnAnswer1b
} as QuestionnaireWorkDto;


// 1st value
export var clientHactQnAnswer4a: QuestionAnswerSnapshotDto[] = [
    {
        id: 2005,
        questionId: 400, // Do you have regular access to the internet?
        answer: "2"
    }
] as QuestionAnswerSnapshotDto[];
export var clientHactQnWork4a: QuestionnaireWorkDto = {
    id: "UUID4001a",
    requestedDelete: null,
    serviceRecipientId: 200072,
    parentCode: "100070",
    parentPrefix: "r",
    authorDisplayName: "Mini Me",
    comment: "1st Survey Value",
    createdDate: "2016-04-08T00:00",
    workDate: "2016-04-08T00:00",
    commentTypeId: null,
    minsSpent: 55,
    signatureId: null,
    taskName: "hactQuestionnaire",
    answers: clientHactQnAnswer4a
} as QuestionnaireWorkDto;

// 2nd value
export var clientHactQnAnswer4b: QuestionAnswerSnapshotDto[] = [
    {
        id: 2006,
        questionId: 400, // Do you have regular access to the internet?
        answer: "1"
    }
] as QuestionAnswerSnapshotDto[];
export var clientHactQnWork4b: QuestionnaireWorkDto = {
    id: "UUID4001b",
    requestedDelete: null,
    serviceRecipientId: 200072,
    parentCode: "100070",
    parentPrefix: "r",
    authorDisplayName: "Mini Me",
    comment: "2nd Survey Value",
    createdDate: "2016-04-10T00:00", // 2 days after the first answer of the 8th
    workDate: "2016-04-10T00:00",
    commentTypeId: null,
    minsSpent: 55,
    signatureId: null,
    taskName: "hactQuestionnaire",
    answers: clientHactQnAnswer4b
} as QuestionnaireWorkDto;

/**
 * HACT report vars
 */

// the translated criteria
export var clientHactReportCriteria: ReportCriteriaDto = {
    questionnaireEvidenceGroup: "hactQuestionnaire",
    questionnaireEvidenceGroupArr: null,
    commandNameArr: null,
    taskDefName: null,
    supportEvidenceGroup: null,
    customFormEvidenceGroup: null,
    from: "2016-04-01",
    to: "2017-05-19",
    selectionPropertyPath: null,
    companyId: null,
    clientGroupId: null,
    serviceGroupId: null,
    serviceId: null,
    projectId: null,
    serviceRecipientFilter: null,
    userId: null,
    username: null,
    isChild: false,
    geographicAreaIdSelected: null,
    geographicAreaIds: [],
    entityStatus: null,
    referralStatus: null,
    newReferralsOnly: false,
    includeRelated: false
};

export var clientHactReferralAgg1: CaseLoadInputElement[] = [
    {
        referral: referral1,
        client: client_90010,
        supportWork: Lazy(clientHactSupportWork1),
        //riskWork?: Sequence<RiskWork>;
        questionnaireWork: Lazy([clientHactQnWork2, clientHactQnWork1]), // ORDER MATTERS
        //activityInterest?: Sequence<ActivityType>;
        //groupActivities?: Sequence<ClientAttendanceDto>;
        //singleValueHistory?: Sequence<SingleValueHistoryDto>;
        sessionData: testSessionData,
        hactSessionData: testHactSessionData,
        reportCriteria: clientHactReportCriteria
    }
];

export var clientHactReferralAgg2: CaseLoadInputElement[] = [
    {
        referral: referral1,
        //client?: dto.Client;
        supportWork: Lazy(clientHactSupportWork1),
        //riskWork?: Sequence<RiskWork>;
        questionnaireWork: Lazy([clientHactQnWork1]), // ORDER MATTERS
        //activityInterest?: Sequence<ActivityType>;
        //groupActivities?: Sequence<ClientAttendanceDto>;
        //singleValueHistory?: Sequence<SingleValueHistoryDto>;
        sessionData: testSessionData,
        hactSessionData: testHactSessionData,
        reportCriteria: clientHactReportCriteria
    }
];

export var clientHactReferralAgg3: CaseLoadInputElement[] = [
    {
        referral: referral1,
        client: client_90010,
        supportWork: Lazy(clientHactSupportWork1),
        //riskWork?: Sequence<RiskWork>;
        questionnaireWork: Lazy([clientHactQnWork2, clientHactQnWork1b]), // ORDER MATTERS
        //activityInterest?: Sequence<ActivityType>;
        //groupActivities?: Sequence<ClientAttendanceDto>;
        //singleValueHistory?: Sequence<SingleValueHistoryDto>;
        sessionData: testSessionData,
        hactSessionData: testHactSessionData,
        reportCriteria: clientHactReportCriteria
    }
];


export var clientHactReferralAgg4: CaseLoadInputElement[] = [
    {
        referral: referral1,
        client: client_90010,
        supportWork: Lazy(clientHactSupportWork1),
        //riskWork?: Sequence<RiskWork>;
        questionnaireWork: Lazy([]), // ORDER MATTERS
        //activityInterest?: Sequence<ActivityType>;
        //groupActivities?: Sequence<ClientAttendanceDto>;
        //singleValueHistory?: Sequence<SingleValueHistoryDto>;
        sessionData: testSessionData,
        hactSessionData: testHactSessionData,
        reportCriteria: clientHactReportCriteria
    }
];


export var clientHactReferralAgg5: CaseLoadInputElement[] = [
    {
        referral: referral1,
        client: client_90010,
        supportWork: Lazy(clientHactSupportWork2),
        //riskWork?: Sequence<RiskWork>;
        questionnaireWork: Lazy([clientHactQnWork4b, clientHactQnWork4a]), // ORDER MATTERS
        //activityInterest?: Sequence<ActivityType>;
        //groupActivities?: Sequence<ClientAttendanceDto>;
        //singleValueHistory?: Sequence<SingleValueHistoryDto>;
        sessionData: testSessionData,
        hactSessionData: testHactSessionData,
        reportCriteria: clientHactReportCriteria
    }
];