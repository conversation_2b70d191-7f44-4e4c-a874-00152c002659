import $ = require("jquery");
import {Activity, Rota, TimeSlot, getSlotsOnDate} from "ecco-rota";
import DraggableActivityControl = require("./DraggableActivityControl");
import UnassignedActivitiesRowControlOptions = require("./UnassignedActivitiesRowControlOptions");
import {
    APPOINTMENT_CLASS,
    APPOINTMENT_COLOUR,
    timeSlotWidthPx,
    TOTAL_PADDING_LEFTRIGHT
} from "./WorkerJobRowControl";

class UnassignedActivitiesRowControl {

    private static TH_LABEL_CLASS = "rota-row-label nowrap";

    private $tr: $.JQuery;
    private $totalCell: $.JQuery;
    private finalRotaTotal: number;
    private $timeSlotCells: $.JQuery[] = [];
    private activitiesByTimeSlotIndex: Activity[] = [];
    private $activityCellsByTimeSlotIndex: $.JQuery[] = [];

    private nextRowControl: UnassignedActivitiesRowControl;

    private activityAllocatedHandler = (event) => { this.onActivityAssigned(event.getActivity()); }

    constructor(private options: UnassignedActivitiesRowControlOptions) {
    }

    public attach($tr: $.JQuery) {
        if (!$tr.is("tr")) {
            throw new Error("UnassignedActivitiesRowControl must be attached to a tr element.");
        }

        const $labelCell = $("<th>")
            .addClass(UnassignedActivitiesRowControl.TH_LABEL_CLASS)
            .append($("<span>").text(this.options.labelText));

        if (this.options.labelActionText) {
            const $clickText = $("<span>").css("margin-left", "5px").addClass("action").text(this.options.labelActionText);
            $clickText.on('click', (e) => { this.options.labelAction() });
            $labelCell.append($clickText);
        }

        $(window).scroll(() => {
            $labelCell.css("position", "")
                .css("left", "");

            const scrollLeft = $(window).scrollLeft();
            if (scrollLeft > 0) { // was > $labelCell.offset().left but we want zero left margin within table
                $labelCell.css("position", "relative")
                    .css("left", scrollLeft);
            }
        });

        this.$tr = $tr;
        $tr.empty()
            .append($labelCell);

        let timeSlotIndex = 0;
        while (timeSlotIndex < this.options.numTimeSlots) {
            const activity = this.activitiesByTimeSlotIndex[timeSlotIndex];
            const $td = this.$timeSlotCells[timeSlotIndex] = $("<td>");
            ++timeSlotIndex;
            $tr.append($td);
        }

        this.$totalCell = $("<td>").css({"padding": "0px " + TOTAL_PADDING_LEFTRIGHT.toString() + "px"});
        $tr.append(this.$totalCell);

        this.updateTotal();
    }

    public updateTotal() {
        const uniqueActivities = this.activitiesByTimeSlotIndex
            ? this.activitiesByTimeSlotIndex
                .filter(a => !!a)
                .filter((a, i, arr) => arr.indexOf(arr.find(t => t.getRef() === a.getRef())) === i)
            : [];
        const totalHrs = Rota.calculateHours(uniqueActivities);
        const totalDisplay = this.finalRotaTotal
            ? $("<span>").text(totalHrs.toFixed(2)).append("<br>").append($("<b>").text(this.finalRotaTotal.toFixed(2)))
            : $("<span>").text(totalHrs.toFixed(2));
        this.$totalCell.empty().append(totalDisplay);
    }

    public setFinalRotaTotal(total: number) {
        this.finalRotaTotal = total;
        this.updateTotal();
    }

    /**
     * When assigned to a worker, we remove the activity from one of the unassigned rows.
     * The row is determined from the activity.allocateWorkerJob.
     */
    public onActivityAssigned(activity: Activity) { // TODO: We need "initialPeriod" which is used when unallocate
        activity.removeActivityAllocatedEventHandler(this.activityAllocatedHandler);
//        console.debug("UnassignedActivitiesRowControl: removing activity: id = " + activity.getRef());

// TODO        const {startTimeSlot, endTimeSlot, durationInTimeSlots} = getSlotsOnDate(period, activity.getRota().getDate());

        const period = activity.getPreviousPeriod();

        const startTimeSlot = period.getStartTimeSlot();
        const endTimeSlot = period.getEndTimeSlot();

        let $cell = this.$timeSlotCells[startTimeSlot.getIndex()];
        // FIXME: Need to find the right one within the children
        console.assert($cell.children().length == 1, "Arg, you'll need to refresh - we removed clashes too")
        $cell.empty();

        startTimeSlot.forEachUntil(endTimeSlot, (timeSlot: TimeSlot) => {
            const timeSlotIndex = timeSlot.getIndex();
            this.activitiesByTimeSlotIndex[timeSlotIndex] = null;
            this.$activityCellsByTimeSlotIndex[timeSlotIndex] = null;
        });

        this.updateTotal();
    }

    /**
     * Update $tr's TDs to place a draggable activity in the correct place, and hook up event handler
     * for assigned
     */
    public onActivityUnassigned(activity: Activity) {

        activity.addActivityAllocatedEventHandler(this.activityAllocatedHandler);

        const period = activity.getPeriod();

        // if we have a split and period starts on rota day, then we need to use startTimeSlot2 to get the part that's up to midnight today
        const {startTimeSlot, endTimeSlot, durationInTimeSlots} = getSlotsOnDate(period, activity.getRota().getDate());

        const draggableActivityControl = new DraggableActivityControl({
            activity: activity,
            onClick: activity => this.options.onClick(activity),
            drag: {
                $containment: this.options.$dragContainment,
                onDragStart: this.options.onActivityDragStart, // TODO: Allow creation of clashes by Ctrl-drag
                onDragEnd: this.options.onActivityDragEnd
            }
        });

        const $appointmentDiv = $("<div>")
                .addClass(APPOINTMENT_CLASS);

        const offsetFraction = period.getStartPartialSlot() * 100
        if (offsetFraction > 0) {
            $appointmentDiv[0].style.left= `${offsetFraction.toFixed(0)}%`
        }

        const $draggableActivityElement = draggableActivityControl.attach()
            // FIXME .css({ background: ALLOCATED_COLOUR})
            .addClass(APPOINTMENT_COLOUR)
            .css("width", durationInTimeSlots * timeSlotWidthPx);

        $appointmentDiv.append($draggableActivityElement);

        const cell = this.$timeSlotCells[startTimeSlot.getIndex()];
        cell.append($appointmentDiv);

        startTimeSlot.forEachUntil(endTimeSlot, (timeSlot: TimeSlot) => {
            const timeSlotIndex = timeSlot.getIndex();
            this.activitiesByTimeSlotIndex[timeSlotIndex] = activity;
        });

        this.updateTotal();
    }

    public insertActivity(activity: Activity) {
        const period = activity.getPeriod();

        const {startTimeSlot, endTimeSlot} = getSlotsOnDate(period, activity.getRota().getDate());

        let hasAvailableTimeSlots = true;
        startTimeSlot.forEachUntil(endTimeSlot, (timeSlot: TimeSlot) => {
            if (this.activitiesByTimeSlotIndex[timeSlot.getIndex()]) {
                hasAvailableTimeSlots = false;
            }
        });

        if (hasAvailableTimeSlots) {
            startTimeSlot.forEachUntil(endTimeSlot, (timeSlot: TimeSlot) => {
                this.activitiesByTimeSlotIndex[timeSlot.getIndex()] = activity;
            });
            this.onActivityUnassigned(activity);
            activity.setDodgyReference(this);
        } else {
            this.getOrCreateNextRow().insertActivity(activity);
        }
    }

    private getOrCreateNextRow(): UnassignedActivitiesRowControl {
        if (!this.nextRowControl) {
            this.nextRowControl = new UnassignedActivitiesRowControl(this.options);
            const $row = $("<tr>");
            this.nextRowControl.attach($row);
            $row.insertAfter(this.$tr);
        }
        return this.nextRowControl;
    }
}

export = UnassignedActivitiesRowControl;
