import $ = require("jquery");
import BaseControl = require("./BaseControl");

/**
 * Control which presents a status panel UI with body and footer for 'more'
 *
 * Usage:
            var status = new StatusPanel()
                .faIcon("fa-medkit")
                .statusValue("23")
                .statusText("Medical visits")
                .footerText("View details");
            $content.append(status.element());

 * Inspired by:
 * Start Bootstrap - SB Admin 2 Bootstrap Admin Theme (http://startbootstrap.com)
 * Code licensed under the Apache License v2.0.
 * For details, see http://www.apache.org/licenses/LICENSE-2.0.

 */
class StatusPanel extends BaseControl {

    private $heading: $.JQuery;
    private $left: $.JQuery;
    private $statusValue: $.JQuery;
    private $statusText: $.JQuery;
    private $statusTextComment: $.JQuery;
    private $body: $.JQuery;
    private $footer: $.JQuery;
    private $footerAnchor: $.JQuery;
    private $footerContent: $.JQuery;

    constructor(panelStyle = "panel-primary") {
        var $panel = $("<div>").addClass("panel ecco-rounded-panel").addClass(panelStyle);
        super($panel);

        this.$left = $("<div>").addClass("col-xs-3");
        this.$statusValue = $("<div>").addClass("huge");
        this.$statusText = $("<div>");
        this.$statusTextComment = $("<div>").addClass("panel-comment").html("&nbsp;");

        this.$heading = $("<div>").addClass("panel-heading panel-no-footer")
            .append(
                $("<div>").addClass("row")
                    .append(this.$left)
                    .append(
                        $("<div>").addClass("col-xs-9 text-right")
                            .append(this.$statusValue)
                            .append(this.$statusText)
                            .append(this.$statusTextComment)
                    )
            );

        this.$body = $("<div>").addClass("panel-body hidden");

        this.$footerAnchor = $("<a>");
        this.$footerContent = $("<div>");

         this.$footer = $("<div>").addClass("panel-footer hidden")
                .append( this.$footerContent )
                .append( $("<div>").addClass("clearfix")
         );

        $panel
            .append(this.$heading)
            .append(this.$body)
            .append(this.$footer);
    }

    /** Use a font-awesome icon for the left part
     * iconClass = e.g. fa-comments
     */
    public faIcon( iconClass: string ) {
        this.$left
            .empty()
            .append( $("<i>").addClass("fa fa-3x").addClass(iconClass) );
        return this;
    }

    public statusValue(val: string) {
        this.$statusValue.text(val);
        return this;
    }

    public statusText(text: string) {
        this.$statusText.text(text);
        return this;
    }

    public statusTextComment(text: string) {
        this.$statusTextComment.text(text);
        return this;
    }

    /** Use for where we want navigation action related to footer href - could combine.. */
    public footerText(text: string) {
        var $footerText = $("<span>").addClass("pull-left").text(text);
        this.footerContent(
            this.$footerAnchor
                .append( $footerText )
                .append(
                    $("<span>").addClass("pull-right")
                        .append( $("<i>").addClass("fa fa-arrow-circle-right") )
                 )
            );
        return this;
    }

    public bodyContent( $body: $.JQuery ) {
        this.$heading.removeClass("panel-no-footer");
        this.$body.removeClass("hidden")
            .empty()
            .append($body);
        return this;
    }

    public footerContent( $footer: $.JQuery ) {
        this.$footer.removeClass("hidden");
        this.$heading.removeClass("panel-no-footer");
        this.$footerContent
            .empty()
            .append( $footer );
        return this;
    }

    public footerHref(href: string) {
        this.$footerAnchor.attr("href", href);
        return this;
    }

    /** Callback on click. Must do own event.preventDefault(); if want to suppress a navigation for example
     */
    public onFooterClick( callback: (event: $.JQueryEventObject) => void ) {
        this.$footer.click( (event: $.JQueryEventObject) => {
            callback(event);
        });
        return this;
    }

    public leftElement() {
        return this.$left;
    }

    public footerHide() {
        this.$footer.hide();
        return this;
    }
}
export = StatusPanel