package com.ecco.offline;

import java.util.ArrayList;
import java.util.Collection;

public abstract class OfflineResourceProviderBase implements OfflineResourceProvider {
    private final Collection<CachedResource> cachedResources = new ArrayList<>();
    private final Collection<FallbackResource> fallbackResources = new ArrayList<>();
    private final Collection<NetworkResource> networkResources = new ArrayList<>();

    @Override
    public final Collection<CachedResource> getCachedResources() {
        return cachedResources;
    }

    @Override
    public final Collection<FallbackResource> getFallbackResources() {
        return fallbackResources;
    }

    @Override
    public final Collection<NetworkResource> getNetworkResources() {
        return networkResources;
    }

    protected void addCachedResource(CachedResource cachedResource) {
        cachedResources.add(cachedResource);
    }

    protected void addFallbackResource(FallbackResource fallbackResource) {
        fallbackResources.add(fallbackResource);
    }

    protected void addNetworkResource(NetworkResource networkResource) {
        networkResources.add(networkResource);
    }
}
