package com.ecco.offline;

import com.ecco.infrastructure.cachebust.ResourceVersionSource;
import com.ecco.infrastructure.config.ApplicationProperties;
import com.ecco.resources.cachebust.OnlineResourceController;
import com.google.common.collect.ImmutableList;
import lombok.SneakyThrows;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.ResourceRegion;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpRange;
import org.springframework.http.MediaType;
import org.springframework.http.converter.ResourceHttpMessageConverter;
import org.springframework.http.converter.ResourceRegionHttpMessageConverter;
import org.springframework.http.server.ServletServerHttpRequest;
import org.springframework.http.server.ServletServerHttpResponse;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.handler.AbstractHandlerMapping;
import org.springframework.web.servlet.handler.SimpleUrlHandlerMapping;
import org.springframework.web.servlet.resource.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;

import org.jspecify.annotations.NonNull;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.util.Collection;
import java.util.List;

import static java.nio.charset.StandardCharsets.UTF_8;
import static java.util.Collections.singletonList;

/**
 * Configurer for our resources handlers
 */
@Configuration
public class ResourcesWebMvcConfig implements WebMvcConfigurer {

    private static final String LOCATION_ROOT = "/";
    private static final String CLASSPATH_COM_ECCO_OFFLINE_STATIC_FILES = "classpath:/com/ecco/offline/staticFiles/";
    private static final int CACHE_PERIOD = 365 * 24 * 3600;
    private final Logger log = LoggerFactory.getLogger(getClass());

    @Value("${ecco.mvc.resourcesPath:}")
    String resourcesPath;

    /**
     * Handlers for: /{cachebustvalue}/** (for infinite cache period), and /noCache/** (for no caching)
     *
     */
    @Bean
    public AbstractHandlerMapping offlineResourceHandlerMapping(
            ResourceVersionSource resourceVersionSource,
            ApplicationContext applicationContext,
            ResourceHttpRequestHandler appResourcesHandler,
            ResourceHttpRequestHandler testAppResourcesHandler) {

        List<Resource> locations = ImmutableList.of(
                applicationContext.getResource(LOCATION_ROOT),
                applicationContext.getResource(CLASSPATH_COM_ECCO_OFFLINE_STATIC_FILES));

        String cachebustPaths = "/" + resourceVersionSource.getVersion() + "/**";

        ResourceHttpRequestHandler cachebustHandler = cacheBustRequestHandler(locations);
        ResourceHttpRequestHandler noCacheHandler = noCacheRequestHandler(locations);

        SimpleUrlHandlerMapping handlerMapping = new SimpleUrlHandlerMapping();
        java.util.Map<String, ResourceHttpRequestHandler> urlMap = new java.util.HashMap<>();
        urlMap.put(resourcesPath + cachebustPaths, cachebustHandler);
        urlMap.put(resourcesPath + "/noCache/**", noCacheHandler);
        urlMap.put(resourcesPath + "/app/**", appResourcesHandler);
        urlMap.put(resourcesPath + "/app/", appResourcesHandler); // NOTE: Have to list this separately as otherwise path of "" is passed to the handler
        urlMap.put(resourcesPath + "/test/**", testAppResourcesHandler);
        urlMap.put(resourcesPath + "/test/", testAppResourcesHandler);
        handlerMapping.setUrlMap(urlMap);
        return handlerMapping;
    }

    @Bean
    public ResourceHttpRequestHandler noCacheRequestHandler(List<Resource> locations) {
        ResourceHttpRequestHandler noCacheRequestHandler = new ResourceHttpRequestHandler();
        noCacheRequestHandler.setLocations(locations);
        noCacheRequestHandler.setCacheSeconds(0);
        return noCacheRequestHandler;
    }

    @Bean
    public ResourceHttpRequestHandler appResourcesHandler(
            ApplicationContext applicationContext,
            ResourceResolver clientRoutedAppResourceResolver
    ) {
        ResourceHttpRequestHandler handler = new ResourceHttpRequestHandler();
        handler.setLocations(singletonList(applicationContext.getResource("classpath:/com/ecco/offline/staticFiles/build/dist/ecco-app/")));
        handler.setCacheSeconds(3600);
        handler.setResourceResolvers(singletonList(clientRoutedAppResourceResolver));
        return handler;
    }

    @Bean
    ResourceResolver clientRoutedAppResourceResolver() {
        return clientRoutedResourceResolver("/app/");
    }

    @Bean
    public ResourceHttpRequestHandler testAppResourcesHandler(
            ApplicationContext applicationContext,
            ResourceResolver clientRoutedTestAppResourceResolver
    ) {
        ResourceHttpRequestHandler handler = new ResourceHttpRequestHandler();
        handler.setLocations(singletonList(applicationContext.getResource("classpath:/com/ecco/offline/staticFiles/build/dist/ecco-test/")));
        handler.setCacheSeconds(3600);
        // handler.afterPropertiesSet turns list of resolvers into a chain, so we can just insert caching before  our client Routed one
        handler.setResourceResolvers(singletonList(clientRoutedTestAppResourceResolver));
        return handler;
    }

    @Bean
    ResourceResolver clientRoutedTestAppResourceResolver() {
        return clientRoutedResourceResolver("/test/");
    }

    /**
     * A resource resolver that resolves all text/html to index.html (so we can have / and /path/within/app etc).
     * All other types of resources are passed through so that sourcemaps etc work
     * @param path path to match
     */
    ResourceResolver clientRoutedResourceResolver(final String path) {
        return new PathResourceResolver() {
            private String cachedHost; // So that we prevent incorrect host (therefore CORS issues) if we can access via different host names
            private Resource cachedIndexHtml;
            private Resource cachedServiceWorkerJs;

            @NonNull
            @SneakyThrows
            @Override
            protected Resource resolveResourceInternal(
                    HttpServletRequest request,
                    @NonNull String requestPath, // /r/app/
                    @NonNull List<? extends Resource> locations,
                    @NonNull ResourceResolverChain chain
            ) {
                if (request.getPathInfo() != null && !request.getPathInfo().startsWith(path)) {
                    return null;
                }

                if (requestPath.equals("service-worker.js")) {
                    return getServiceWorkerJs(request, locations, chain);
                }
                var isAppRootPath = request.getPathInfo() != null && request.getPathInfo().equals(path)
                        || requestPath.equals(resourcesPath + path); // i.e. /r/app/

                if (isAppRootPath || requestPath.equals("index.html")) {
                    return getIndexHtml(request, locations, chain);
                }
                // If we can resolve it directly, then it'll be that it's a related resource
                var resource = super.resolveResourceInternal(request, requestPath, locations, chain);
                return resource != null ? resource : getIndexHtml(request, locations, chain);
            }

            private Resource getIndexHtml(HttpServletRequest request, List<? extends Resource> locations, ResourceResolverChain chain) throws IOException {
                if (cachedIndexHtml == null || !getHostRoot(request).equals(cachedHost)) {
                    // BEWARE: We will need to cache based on different remote URLs if multiple paths to the same host are supported
                    var resource = super.resolveResourceInternal(request, "/index.html", locations, chain);
                    assert resource != null;
                    var bytes = readAndTransform(resource.getInputStream(), request);
                    cachedIndexHtml = new TransformedResource(resource, bytes);
                    cachedHost = getHostRoot(request);
                }
                return cachedIndexHtml;
            }

            /** Note: This gets asset-manifest.json baked in */
            private Resource getServiceWorkerJs(HttpServletRequest request, List<? extends Resource> locations, ResourceResolverChain chain) throws IOException {
                if (cachedServiceWorkerJs == null || !getHostRoot(request).equals(cachedHost)) {
                    // BEWARE: We will need to cache based on different remote URLs if multiple paths to the same host are supported
                    var resource = super.resolveResourceInternal(request, "/service-worker.js", locations, chain);
                    assert resource != null;
                    var bytes = readAndTransform(resource.getInputStream(), request);
                    cachedServiceWorkerJs = new TransformedResource(resource, bytes);
                    cachedHost = getHostRoot(request);
                }
                return cachedServiceWorkerJs;
            }

            private byte[] readAndTransform(InputStream inputStream, HttpServletRequest request) throws IOException {
                if (inputStream == null) {
                    throw new FileNotFoundException("Missing templated resource: " + path);
                }

                try (inputStream) {
                    String responseBody = IOUtils.toString(inputStream, UTF_8);
                    return replaceTemplateVariables(request, responseBody).getBytes(UTF_8);
                }
            }

            private String replaceTemplateVariables(HttpServletRequest request, String responseBody) {
                String hostRoot = getHostRoot(request);
                return responseBody
                        .replace("_CONTEXT_PATH_", hostRoot + request.getContextPath());
            }

            @NotNull
            private String getHostRoot(HttpServletRequest request) {
                var uri = ServletUriComponentsBuilder.fromRequest(request).build();
                var hostRoot = uri.getScheme() + "://" + uri.getHost();
                if (uri.getPort() >= 0) {
                    hostRoot = hostRoot + ":" + uri.getPort();
                }
                return hostRoot;
            }
        };
    }

    @Bean
    public ResourceHttpRequestHandler cacheBustRequestHandler(List<Resource> locations) {

        // FIXME: This needs wiring up with resource resolvers etc
        ResourceHttpRequestHandler requestHandler = new ResourceHttpRequestHandler() {

            private final ResourceHttpMessageConverter resourceHttpMessageConverter = new ResourceHttpMessageConverter();
            private final ResourceRegionHttpMessageConverter resourceRegionHttpMessageConverter = new ResourceRegionHttpMessageConverter();

            @Override
            public void handleRequest(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response)
                    throws ServletException, IOException {

                checkRequest(request);
                prepareResponse(response);

                // check whether a matching resource exists
                Resource resource = getResource(request);
                if (resource == null) {
                    // missing .map /ecco-war/r/18852f289f5/scripts/lib/react-router-dom.js.map
                    // missing .map /ecco-war/r/18852f289f5/scripts/lib/react-jsonschema-form.js.map
			        // missing .map /ecco-war/r/18852f289f5/scripts/time/moment.min.js.map
                    // missing .map /ecco-war/r/18852f289f5/scripts/jquery/jquery-migrate.min.map
                    log.debug("No matching resource found - returning 404");
                    response.sendError(HttpServletResponse.SC_NOT_FOUND);
                    return;
                }

                // check the resource's media type
                MediaType mediaType = getMediaType(request, resource);
                if (mediaType != null) {
                    if (log.isDebugEnabled()) {
                        log.debug("Determined media type '" + mediaType + "' for " + resource);
                    }
                }
                else {
                    if (log.isDebugEnabled()) {
                        log.debug("No media type found for " + resource + " - not sending a content-type header");
                    }
                }

                // header phase
                // ECCO CHANGE:
                // Deleted If-Modified-Since handling as that set Last-Modified so defeated cache-bust
                response.addHeader(HEADER_CACHE_CONTROL, "max-age=" + CACHE_PERIOD);
                setHeaders(response, resource, mediaType);

                // content phase
                if (METHOD_HEAD.equals(request.getMethod())) {
                    log.trace("HEAD request - skipping content");
                    return;
                }
                writeContent(request, response, resource, mediaType);
            }

            void writeContent(HttpServletRequest request, HttpServletResponse response, Resource resource, MediaType mediaType) throws IOException {
                ServletServerHttpResponse outputMessage = new ServletServerHttpResponse(response);
                if (request.getHeader(HttpHeaders.RANGE) == null) {
                    setHeaders(response, resource, mediaType);
                    resourceHttpMessageConverter.write(resource, mediaType, outputMessage);
                }
                else {
                    response.setHeader(HttpHeaders.ACCEPT_RANGES, "bytes");
                    ServletServerHttpRequest inputMessage = new ServletServerHttpRequest(request);
                    try {
                        List<HttpRange> httpRanges = inputMessage.getHeaders().getRange();
                        response.setStatus(HttpServletResponse.SC_PARTIAL_CONTENT);
                        if (httpRanges.size() == 1) {
                            ResourceRegion resourceRegion = httpRanges.get(0).toResourceRegion(resource);
                            resourceRegionHttpMessageConverter.write(resourceRegion, mediaType, outputMessage);
                        }
                        else {
                            resourceRegionHttpMessageConverter.write(
                                    HttpRange.toResourceRegions(httpRanges, resource), mediaType, outputMessage);
                        }
                    }
                    catch (IllegalArgumentException ex) {
                        response.setHeader("Content-Range", "bytes */" + resource.contentLength());
                        response.sendError(HttpServletResponse.SC_REQUESTED_RANGE_NOT_SATISFIABLE);
                    }
                }
            }
        };

        requestHandler.setLocations(locations);

        // TODO: What we're doing is a VersionResourceResolver with a FixedVersionStrategy, and can go fully to do AppManfest stuff
        // see https://github.com/bclozel/spring-resource-handling
//        requestHandler.setResourceResolvers(singletonList(new PathResourceResolver())); // done by afterPropertiesSet
        requestHandler.setAlwaysMustRevalidate(false);
        return requestHandler;
    }

    @ConditionalOnMissingBean
    @Bean
    public OfflineResourceProvider applicationPropertiesOfflineResourceProvider(ApplicationProperties applicationProperties) {
        return new ApplicationPropertiesOfflineResourceProvider(applicationProperties);
    }

    @Bean
    public OnlineResourceController onlineResourceController(
            ApplicationContext applicationContext,
            Collection<OfflineResourceProvider> offlineResourceProviders) {
        return new OnlineResourceController(applicationContext, offlineResourceProviders);
    }
}
