package com.ecco.calendar;

import com.ecco.calendar.core.*;
import com.ecco.infrastructure.time.Clock;
import com.ecco.infrastructure.time.JodaToJDKAdapters;
import com.google.common.collect.Range;
import com.ecco.calendar.core.RecurringEntry.RecurringEntryHandle;

import org.junit.Before;
import org.junit.Test;

import java.net.URI;
import java.time.*;
import java.time.temporal.TemporalAdjusters;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

import static org.hamcrest.MatcherAssert.assertThat;
import org.hamcrest.Matchers;
import static org.mockito.Mockito.*;

public class CalendarRecurringSeriesUnitTest {

    private final com.ecco.infrastructure.time.Clock clock = Clock.DEFAULT;
    protected final org.joda.time.DateTime now = clock.nowWithoutMillies();

    private final CalendarRecurringService mockService = mock(CalendarRecurringService.class);
    private final CalendarRecurringSeriesService mockAttendeeService = mock(CalendarRecurringSeriesService.class);
    private final CalendarRecurringService service = new CalendarRecurringSeriesDecorator(mockService, mockAttendeeService);

    private final ZonedDateTime nextMon2pm = LocalDateTime.now().withHour(14).withMinute(0).withSecond(0).withNano(0)
            .atZone(ZoneId.of("UTC")).with(TemporalAdjusters.next(DayOfWeek.MONDAY));
    private final ZonedDateTime weekMon2pm = nextMon2pm.plusDays(7);
    private final ZonedDateTime nextMon11am = LocalDateTime.now().withHour(11).withMinute(0).withSecond(0).withNano(0)
            .atZone(ZoneId.of("UTC")).with(TemporalAdjusters.next(DayOfWeek.MONDAY));
    private final Range<Instant> nextMon2pmTo1Month = Range.closedOpen(nextMon2pm.toInstant(), nextMon2pm.plusMonths(1).toInstant());
    private final Range<Instant> nextMon2pmToInf = Range.atLeast(nextMon2pm.toInstant());
    private final Range<Instant> weekMon2pmToInf = Range.atLeast(weekMon2pm.toInstant());
    private final Range<Instant> weekMon2pmTo1Month = Range.closedOpen(weekMon2pm.toInstant(), weekMon2pm.plusMonths(1).toInstant());
    private final Range<Instant> nextMon11amToInf = Range.atLeast(nextMon11am.toInstant());
    private final DaysOfWeek mon = dayOfWeek -> Objects.equals(DayOfWeek.MONDAY, DayOfWeek.of(dayOfWeek));
    private final DaysOfWeek tuesWed = dayOfWeek -> List.of(DayOfWeek.TUESDAY, DayOfWeek.WEDNESDAY).contains(DayOfWeek.of(dayOfWeek));
    private final DaysOfWeek monTuesWed = dayOfWeek -> List.of(DayOfWeek.MONDAY, DayOfWeek.TUESDAY, DayOfWeek.WEDNESDAY).contains(DayOfWeek.of(dayOfWeek));
    private final URI updatedUri = URI.create("test://h1/");
    private final String attendeeCalendarId = "attendeeCalendarId";

    @Before
    public void setUp() throws Exception {
        createTestData();

        // whenever createRecurringEntry is called, return something to avoid NPE
        when(mockService.createRecurringEntry(any(String.class), any(RecurringEntryDefinition.class))).thenReturn(itemAny);
    }

    // ******** CLONE entry
    // we rely on cloning an entry

    @Test
    public void testCloneRecurringEntry() {

        // NB Cloning the RecurringEntry proxy fails, but cloning an impl works - so we create one in RecurringEntry (perhaps move to tests)
        //var testItemMock = Mockito.mock(RecurringEntry.class);

        var attendee = Attendee.AttendeeProxyImpl.builder()
                .calendarIdUserReferenceUri("owner1")
                .calendarId("calId1")
                .build();
        var testItem = RecurringEntry.RecurringEntryProxyImpl.builder()
                .handle(RecurringEntryHandle.fromString("h1"))
                .description("my desc")
                .title("h1")
                .start(JodaToJDKAdapters.dateTimeToJoda(nextMon2pm))
                .scheduleEndDate(JodaToJDKAdapters.localDateToJoda(nextMon2pm.toLocalDate().plusMonths(1)))
                .duration(org.joda.time.Duration.standardMinutes(10))
                .intervalType("WK")
                .intervalFrequency(1)
                .attendees(Collections.singleton(attendee))
                .locationUri(updatedUri)
                .managedByUri(updatedUri)
                .updatedByUri(updatedUri)
                .ownerCalendarId(attendeeCalendarId)
                .days(monTuesWed)
                .build();

        // clone and update as required
        RecurringEntry.Builder b = RecurringEntry.BuilderFactory.builderFrom(testItem);
        b.title("h1 updated");
        var clone = b.build();

        //assertThat(clone.getTitle(), Matchers.is("h1"));
        var propertyMatchers = Matchers.allOf(
                Matchers.hasProperty("handle", Matchers.equalTo(RecurringEntryHandle.fromString("h1"))),
                Matchers.hasProperty("description", Matchers.equalTo("my desc")),
                Matchers.hasProperty("title", Matchers.equalTo("h1 updated")),
                Matchers.hasProperty("start", Matchers.equalTo(JodaToJDKAdapters.dateTimeToJoda(nextMon2pm))),
                Matchers.hasProperty("scheduleEndDate", Matchers.equalTo(JodaToJDKAdapters.localDateToJoda(nextMon2pm.toLocalDate().plusMonths(1)))),
                Matchers.hasProperty("duration", Matchers.equalTo(org.joda.time.Duration.standardMinutes(10))),
                Matchers.hasProperty("intervalType", Matchers.equalTo("WK")),
                Matchers.hasProperty("intervalFrequency", Matchers.equalTo(1)),
                Matchers.hasProperty("attendees", Matchers.hasItem(attendee)),
                Matchers.hasProperty("locationUri", Matchers.equalTo(updatedUri)),
                Matchers.hasProperty("managedByUri", Matchers.equalTo(updatedUri)),
                Matchers.hasProperty("updatedByUri", Matchers.equalTo(updatedUri)),
                Matchers.hasProperty("ownerCalendarId", Matchers.equalTo(attendeeCalendarId)),
                Matchers.hasProperty("days", Matchers.equalTo(monTuesWed))
            );
        assertThat(clone, propertyMatchers);
    }

    // ******** WHOLE series
    // nothing particularly interesting in these tests - just matching and allocating to a whole series
    // which are represented by a normal, single schedule

    @Test
    public void allocateToWholeSeries_withEndDate_noMatchDays_specificRange() {
        // allocate straight onto the schedule - the range covers item1 and matchDays are null
        // item1 is MonTuesWed, relatedItems1 is only item1
        allocate(item1MonTuesWedWithEndDate, relatedItems1, nextMon2pmTo1Month, null);
        verifyMovedTruncatedDeletedAndCreated(0, 0, 0, 0);
        verifyAllocated(1);
    }

    @Test
    public void allocateToWholeSeries_withEndDate_sameMatchDays_specificRange() {
        // allocate straight onto the schedule - the range covers item1 and matchDays match exactly
        // item1 is MonTuesWed, relatedItems1 is only item1
        allocate(item1MonTuesWedWithEndDate, relatedItems1, nextMon2pmTo1Month, monTuesWed);
        verifyMovedTruncatedDeletedAndCreated(0, 0, 0, 0);
        verifyAllocated(1);
    }

    @Test
    public void allocateToWholeSeries_withEndDate_sameMatchDays_infiniteRange() {
        // allocate straight onto the schedule - the range (infinite) covers item1 and matchDays match exactly
        // item1 is MonTuesWed, relatedItems1 is only item1
        allocate(item1MonTuesWedWithEndDate, relatedItems1, nextMon2pmToInf, monTuesWed);
        verifyMovedTruncatedDeletedAndCreated(0, 0, 0, 0);
        verifyAllocated(1);
    }

    @Test
    public void allocateToWholeSeries_noEndDate_noMatchDays_infiniteRange() {
        // allocate straight onto the schedule - the range (infinite) covers item1 and matchDays are null
        // item2 is MonTuesWed, relatedItems2 is only item2
        allocate(item2MonTuesWed, relatedItems2, nextMon11amToInf, null);
        verifyMovedTruncatedDeletedAndCreated(0, 0, 0, 0);
        verifyAllocated(1);
    }

    @Test
    public void allocateToWholeSeries_noEndDate_sameMatchDays_infiniteRange() {
        // allocate straight onto the schedule - the range (infinite) covers item2 and matchDays match exactly
        // item2 is MonTuesWed, relatedItems2 is only item2
        allocate(item2MonTuesWed, relatedItems2, nextMon11amToInf, monTuesWed);
        verifyMovedTruncatedDeletedAndCreated(0, 0, 0, 0);
        verifyAllocated(1);
    }

    @Test
    public void allocateToWholeSeries_onRelatedEntry() {
        // this is more unusual because it may not happen - that we are allocating to any one of the entries in a series
        // however, its best to present as schedule-agnostic within a series so that the ui doesn't need to be concerned

        // allocate straight onto the schedule - the range (infinite) covers relatedItems34 and matchDays match item3 exactly
        allocate(item4Mon, relatedItems34, nextMon11amToInf, tuesWed);
        verifyMovedTruncatedDeletedAndCreated(0, 0, 0, 0);
        verifyAllocated(1);
    }

    // ******** PART series
    // truncate, move and create new recurring entry schedules accordingly to create one series

    @Test
    public void allocateToPartSeries_moveRange() {
        // allocate to part of the series - the range is short but starts at the schedule, so move up
        // and we create schedules for Mon and TuesWed in the range
        allocate(item5MonTuesWedWithLongEnd, relatedItems5, nextMon2pmTo1Month, mon);
        verifyMovedTruncatedDeletedAndCreated(1, 0, 0, 2);
        verifyAllocated(1);
    }

    @Test
    public void allocateToPartSeries_truncateRange() {
        // allocate to part of the series - the range is short and late so the schedule extends after
        // and we create schedules for Mon and TuesWed for the range
        allocate(item5MonTuesWedWithLongEnd, relatedItems5, weekMon2pmTo1Month, mon);
        verifyMovedTruncatedDeletedAndCreated(0, 1, 0, 3);
        verifyAllocated(1);
    }

    @Test
    public void allocateToPartSeries_splitDays1() {
        // allocate to part of the series - the range covers item1 but matchDays mean a split into mon/tuesWed
        // but since it splits at the start time, we delete the entry and create two and allocate to mon
        // and the tuesWed gets the allocation it already had
        allocate(item1MonTuesWedWithEndDate, relatedItems1, nextMon2pmTo1Month, mon);
        verifyMovedTruncatedDeletedAndCreated(0, 0, 1, 2);
        verifyAllocated(1);
    }

    @Test
    public void allocateToPartSeries_splitDays2() {
        // allocate to part of the series - the range covers item1 but matchDays mean a split into mon/tuesWed
        // but since it splits at the start time, we delete the entry and create two and allocate to tuesWed
        // and the mon gets the allocation it already had
        allocate(item1MonTuesWedWithEndDate, relatedItems1, nextMon2pmTo1Month, tuesWed);
        verifyMovedTruncatedDeletedAndCreated(0, 0, 1, 2);
        verifyAllocated(1);
    }

    @Test
    public void allocateToPartSeries_splitDays2WithoutAttendees() {
        // allocate to part of the series - the range covers item1 but matchDays mean a split into mon/tuesWed
        // but since it splits at the start time, we delete the entry and create two and allocate to tuesWed
        allocate(item6MonTuesWedWithEndDateNoAttendee, relatedItems6, nextMon2pmTo1Month, tuesWed);
        verifyMovedTruncatedDeletedAndCreated(0, 0, 1, 2);
        verifyAllocated(1);
    }

    @Test
    public void allocateToPartSeries_matchPart() {
        // allocate to all of a related schedule - the range covers relatedItems34 but matchDays match item3
        allocate(item4Mon, relatedItems34, nextMon11amToInf, tuesWed);
        verifyMovedTruncatedDeletedAndCreated(0, 0, 0, 0);
        verifyAllocated(1);
    }

    @Test
    public void allocateToPartSeries_mergeDays() {
        // allocate to all related schedules - the range covers item3 and item4 and matchDays means one part created for monTuesWed
        allocate(item4Mon, relatedItems34, nextMon11amToInf, monTuesWed);
        verifyMovedTruncatedDeletedAndCreated(0, 0, 2, 1);
        verifyAllocated(1);
    }

    @Test
    public void allocatePartSeries_truncateRelatedEntries() {
        // allocate to all related schedules - the range means item3 and item4 are truncated and one part created for monTuesWed
        // so item3 (TuesWed 11am) truncates on tues; item4 mon11am truncates on mon
        allocate(item4Mon, relatedItems34, weekMon2pmToInf, monTuesWed);
        verifyMovedTruncatedDeletedAndCreated(0, 2, 0, 1);
        verifyAllocated(1);
    }

    // ******** SUPPORT
    // test support methods

    private void allocate(RecurringEntry item, List<RecurringEntry> relatedItems, Range<Instant> range, DaysOfWeek days) {
        // capture a return of the related schedules that match the 'item' on the 'days' in the 'range'
        when(mockAttendeeService.getSeriesEntriesWithLegacyFallback(any(RecurringEntryHandle.class), any(LocalDate.class), any(LocalDate.class)))
                .thenReturn(relatedItems);
        // capture a return of the related schedules that match the 'item' on the 'days' when no end date
        when(mockAttendeeService.getSeriesEntriesWithLegacyFallback(any(RecurringEntryHandle.class), any(LocalDate.class), isNull()))
                .thenReturn(relatedItems);

        service.confirmRecurrencesInRange(item.getHandle(), range, days, updatedUri,
                null, null, attendeeCalendarId);
    }

    private void verifyMovedTruncatedDeletedAndCreated(int moved, int truncated, int deleted, int created) {
        // check we called moved
        verify(mockService, times(moved)).updateRecurringEntryBoundsStart(any(RecurringEntryHandle.class), any(org.joda.time.LocalDate.class));
        // check we called truncate
        verify(mockService, times(truncated)).updateRecurringEntryBoundsEnd(any(RecurringEntryHandle.class), any(org.joda.time.LocalDate.class));
        // check we called delete
        verify(mockService, times(deleted)).deleteRecurringEntry(any(RecurringEntryHandle.class), anyBoolean());
        // check we called clone
        verify(mockService, times(created)).createRecurringEntry(any(String.class), any(RecurringEntryDefinition.class));
    }

    private void verifyAllocated(int allocated) {
        // check we called allocate
        verify(mockService, times(allocated)).confirmRecurringEntry(any(RecurringEntryHandle.class), any(Recurrence.Status.class), any(Attendee.Status.class), any(), any(String.class));

        // check we DIDN'T call the original 'allocateActivitiesInRange' - since this uses recurrences not recurring items
        verify(mockService, times(0)).confirmRecurrencesInRange(any(RecurringEntryHandle.class), any(Range.class), nullable(DaysOfWeek.class),
                any(URI.class), nullable(Integer.class), nullable(Range.class), any(String.class));

        //assertThat(allocatedCount, is(1));
    }

    // moved here so click-throughs show createTestData
    RecurringEntry item1MonTuesWedWithEndDate, item2MonTuesWed, item3TuesWed, item4Mon, item5MonTuesWedWithLongEnd,
            item6MonTuesWedWithEndDateNoAttendee, itemAny;
    List<RecurringEntry> relatedItems1, relatedItems2, relatedItems34, relatedItems5, relatedItems6;
    private void createTestData() {
        this.item1MonTuesWedWithEndDate = RecurringEntry.RecurringEntryProxyImpl.builder()
                .handle(RecurringEntryHandle.fromString("item1MonTuesWedWithEndDate"))
                .ownerCalendarId("owner1-item1")
                .title("h1")
                .start(JodaToJDKAdapters.dateTimeToJoda(nextMon2pm))
                .scheduleEndDate(JodaToJDKAdapters.localDateToJoda(nextMon2pm.toLocalDate().plusMonths(1).minusDays(1)))
                .days(monTuesWed)
                .attendees(Collections.singleton(Attendee.AttendeeProxyImpl.builder()
                        .calendarIdUserReferenceUri("owner1")
                        .calendarId("calId1")
                        .build()))
                .build();

        this.item2MonTuesWed = RecurringEntry.RecurringEntryProxyImpl.builder()
                .handle(RecurringEntryHandle.fromString("item2MonTuesWed"))
                .ownerCalendarId("owner1-item2")
                .title("h2")
                .start(JodaToJDKAdapters.dateTimeToJoda(nextMon11am))
                .days(monTuesWed)
                .build();

        this.item3TuesWed = RecurringEntry.RecurringEntryProxyImpl.builder()
                .handle(RecurringEntryHandle.fromString("item3TuesWed"))
                .ownerCalendarId("owner1-item3")
                .title("h3")
                .start(JodaToJDKAdapters.dateTimeToJoda(nextMon11am.plusDays(1))) // tues
                .days(tuesWed)
                .build();

        this.item4Mon = RecurringEntry.RecurringEntryProxyImpl.builder()
                .handle(RecurringEntryHandle.fromString("item4Mon"))
                .ownerCalendarId("owner1-item4")
                .title("h4")
                .start(JodaToJDKAdapters.dateTimeToJoda(nextMon11am))
                .days(mon)
                .build();

        this.item5MonTuesWedWithLongEnd = RecurringEntry.RecurringEntryProxyImpl.builder()
                .handle(RecurringEntryHandle.fromString("item5MonTuesWedWithLongEndDate"))
                .ownerCalendarId("owner1-item5")
                .title("h5")
                .start(JodaToJDKAdapters.dateTimeToJoda(nextMon2pm))
                .scheduleEndDate(JodaToJDKAdapters.localDateToJoda(nextMon2pm.toLocalDate().plusMonths(2).minusDays(1)))
                .days(monTuesWed)
                .build();

        this.item6MonTuesWedWithEndDateNoAttendee = RecurringEntry.RecurringEntryProxyImpl.builder()
                .handle(RecurringEntryHandle.fromString("item6MonTuesWedWithEndDateNoAttendee"))
                .ownerCalendarId("owner1-item1")
                .title("h1")
                .start(JodaToJDKAdapters.dateTimeToJoda(nextMon2pm))
                .scheduleEndDate(JodaToJDKAdapters.localDateToJoda(nextMon2pm.toLocalDate().plusMonths(1).minusDays(1)))
                .days(monTuesWed)
                .build();

        this.itemAny = RecurringEntry.RecurringEntryProxyImpl.builder()
                .handle(RecurringEntryHandle.fromString("hAny"))
                .ownerCalendarId("owner1-itemAny")
                .title("hAny")
                .start(JodaToJDKAdapters.dateTimeToJoda(nextMon11am))
                .build();

        this.relatedItems1 = List.of(item1MonTuesWedWithEndDate);
        this.relatedItems2 = List.of(item2MonTuesWed);
        this.relatedItems34 = List.of(item3TuesWed, item4Mon);
        this.relatedItems5 = List.of(item5MonTuesWedWithLongEnd);
        this.relatedItems6 = List.of(item6MonTuesWedWithEndDateNoAttendee);
    }

}
