package com.ecco.calendar.core;

import java.util.Collections;
import java.util.Set;
import java.util.SortedSet;
import java.util.TreeSet;

import org.joda.time.DateTime;
import org.joda.time.Interval;

/**
 * Represents a calendar owner's availability over a time period.
 */
public class Availability implements HasAttributes<AvailabilityAttribute> {
    private final String id;
    private final String calendarId;
    private final String displayName;
    private final Interval interval;
    private final AttributeSet<AvailabilityAttribute> attributes = new AttributeSet<AvailabilityAttribute>(AvailabilityAttribute.class);
    private final SortedSet<AvailableInterval> availability = new TreeSet<AvailableInterval>();

    public Availability(String id, String calendarId, String displayName, Interval interval) {
        this.id = id;
        this.calendarId = calendarId;
        this.displayName = displayName;
        this.interval = interval;
    }

    public boolean isAvailable(DateTime instant) {
        throw new UnsupportedOperationException("Not yet implemented");
    }

    public boolean isAvailable(Interval interval) {
        throw new UnsupportedOperationException("Not yet implemented");
    }

    public String getId() {
        return id;
    }

    public String getCalendarId() {
        return calendarId;
    }

    public String getDisplayName() {
        return displayName;
    }

    public Interval getInterval() {
        return interval;
    }

    /**
     * @return set of intervals in which the calendar owner is available.
     */
    public Set<AvailableInterval> getAvailableIntervals() {
        return Collections.unmodifiableSet(availability);
    }

    /**
     * Adds an interval to the set of intervals in which the calendar owner is available.
     *
     * @param interval interval over which the calendar owner is unavailable
     * @return the unavailable interval, ready to have attributes added to it
     */
    public AvailableInterval addAvailableInterval(Interval interval) {
        // Ideally this would check it didn't overlap with any existing ones, but we can do that more coarsely at the service level.
        final AvailableInterval availableInterval = new AvailableInterval(interval);
        if (!availability.contains(availableInterval)) {
            availability.add(availableInterval);
        }
        return availableInterval;
    }

    @Override
    public String getAttributeValue(String key) {
        return attributes.getAttributeValue(key);
    }

    @Override
    public boolean hasAttribute(String key) {
        return attributes.hasAttribute(key);
    }

    @Override
    public void addAttribute(String key, String value) {
        attributes.addAttribute(key, value);
    }

    @Override
    public Set<AvailabilityAttribute> getAttributes() {
        return attributes.getAttributes();
    }

    @Override
    public void removeAttribute(String key) {
        attributes.removeAttribute(key);
    }
}
