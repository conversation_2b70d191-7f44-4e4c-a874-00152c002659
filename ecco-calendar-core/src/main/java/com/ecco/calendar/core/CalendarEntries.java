package com.ecco.calendar.core;

import org.joda.time.Interval;

import java.util.Set;

/**
 * Holds entries form a calendar that lie within a certain time interval
 * This is used as an intermediary class, not a dto to the client.
 * Its what is gathered from the calendaring system.
 */
public class CalendarEntries {

    private String calendarId;
    private Interval interval;
    private Set<Entry> entries;

    public CalendarEntries(String calendarId, Interval interval, Set<Entry> entries) {
        this.calendarId = calendarId;
        this.interval = interval;
        this.entries = entries;
    }

    public String getCalendarId() {
        return calendarId;
    }

    public Interval getInterval() {
        return interval;
    }

    public Set<Entry> getEntries() {
        return entries;
    }

}
