package com.ecco.calendar.dom;

import org.joda.time.LocalDate;
import org.jspecify.annotations.NonNull;

import javax.persistence.Embeddable;
import javax.persistence.Embedded;
import javax.persistence.Transient;
import java.io.Serializable;

@Embeddable
public class MedDate implements Serializable, Comparable<MedDate> {

    private static final long serialVersionUID = 1L;

    @Embedded
    private MedTime time = null;
    /** day of month 1-31 */
    private Short day = null;
    // TODO this is no longer of type month - since form now uses this class to bind
    // but it could be - but our query would have to be by example, since birthdate.month can't be resolved
    /** month of year 1-12 */
    private Short month = null;
    private Integer year = null;
    @Transient private boolean timeEnabled = false;
    @Transient private boolean dayEnabled = true;
    @Transient private boolean monthEnabled = true;
    @Transient private boolean yearEnabled = true;


    // java clonable interface is shallow copy
    // this could cause problems when wanting to change each date
    // or it could be to our advantage!
    @Transient
    public MedDate deepCopy() {
        MedDate deepCopy = new MedDate();
        if (day != null)
            deepCopy.setDay(Short.valueOf(day));
        if (month != null)
            deepCopy.setMonth(Short.valueOf(month));
        if (year != null)
            deepCopy.setYear(Integer.valueOf(year));
        if (time == null)
            deepCopy.setTime(null);
        else
            deepCopy.setTime(time.deepCopy());
        deepCopy.setTimeEnabled(timeEnabled);
        deepCopy.setDayEnabled(dayEnabled);
        deepCopy.setMonthEnabled(monthEnabled);
        deepCopy.setYearEnabled(yearEnabled);
        return deepCopy;
    }

    @Override
    @Transient
    public int compareTo(@NonNull MedDate that) {
        final int before = -1;
        final int after = 1;

        if (that == null) {
            return before;
        }

        Comparable thisCompare;
        Comparable thatCompare;
        int bestGuess = 0;

        thisCompare = this.getYear();
        thatCompare = that.getYear();
        if (this.yearEnabled && that.yearEnabled) {
            if ((thisCompare != null) && (thatCompare != null)) {
                if (thisCompare.compareTo(thatCompare) != 0)
                    return thisCompare.compareTo(thatCompare);
            } else {
                // one is not null - we make that one more important
                if (thisCompare != null)
                    bestGuess = before;
                if (thatCompare != null)
                    bestGuess = after;
            }
        }

        thisCompare = this.getMonth();
        thatCompare = that.getMonth();
        if (this.monthEnabled && that.monthEnabled) {
            if ((thisCompare != null) && (thatCompare != null)) {
                if (thisCompare.compareTo(thatCompare) != 0)
                    return thisCompare.compareTo(thatCompare);
            } else {
                // one is not null - we make that one more important
                if (thisCompare != null)
                    bestGuess = before;
                if (thatCompare != null)
                    bestGuess = after;
            }
        }

        thisCompare = this.getDay();
        thatCompare = that.getDay();
        if (this.dayEnabled && that.dayEnabled) {
            if ((thisCompare != null) && (thatCompare != null)) {
                if (thisCompare.compareTo(thatCompare) != 0)
                    return thisCompare.compareTo(thatCompare);
            } else {
                // one is not null - we make that one more important
                if (thisCompare != null)
                    bestGuess = before;
                if (thatCompare != null)
                    bestGuess = after;
            }
        }

        thisCompare = this.getTime();
        thatCompare = that.getTime();
        if (this.timeEnabled && that.timeEnabled) {
            if ((thisCompare != null) && (thatCompare != null)) {
                if (thisCompare.compareTo(thatCompare) != 0)
                    return thisCompare.compareTo(thatCompare);
            } else {
                // one is not null - we make that one more important
                if (thisCompare != null)
                    bestGuess = before;
                if (thatCompare != null)
                    bestGuess = after;
            }
        }

        // could be equal (even null equal) or could be a guess
        return bestGuess;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        MedDate other = (MedDate) obj;
        return compareTo(other) == 0;
    }

    @Transient
    public boolean isDayEnabled() {
        return dayEnabled;
    }

    public void setDayEnabled(boolean dayEnabled) {
        this.dayEnabled = dayEnabled;
    }

    @Transient
    public boolean isMonthEnabled() {
        return monthEnabled;
    }

    public void setMonthEnabled(boolean monthEnabled) {
        this.monthEnabled = monthEnabled;
    }

    @Transient
    public boolean isYearEnabled() {
        return yearEnabled;
    }

    public void setYearEnabled(boolean yearEnabled) {
        this.yearEnabled = yearEnabled;
    }


    public static MedDate from(LocalDate date) {
        return date == null ? null : new MedDate(date);
    }

    // this is required for creating new blank components (eg from Contact)
    public MedDate() {}

    public MedDate(boolean timeEnabled, boolean dayEnabled, boolean monthEnabled, boolean yearEnabled) {
        this.timeEnabled = timeEnabled;
        this.dayEnabled = dayEnabled;
        this.monthEnabled = monthEnabled;
        this.yearEnabled = yearEnabled;
        if (timeEnabled)
            time = new MedTime();
    }

    public MedDate(MedTime time, Short day, Short month, Integer year) {
        this.timeEnabled = true;
        this.time = time;
        this.day = day;
        this.month = month;
        this.year = year;
    }

    /**
     * @param day 1-31
     * @param month 1-12
     * @param year
     */
    public MedDate(Short day, Short month, Integer year) {
        this.time = null;
        this.day = day;
        this.month = month;
        this.year = year;
    }

    public MedDate(int day, int month, int year) {
        this.time = null;
        this.day = (short) day;
        this.month = (short) month;
        this.year = year;
    }


    /** @deprecated use {@link #from(LocalDate)} */
    @Deprecated
    public MedDate(LocalDate date) {
        this.time = null;
        this.day = (short) date.getDayOfMonth();
        this.month = (short) date.getMonthOfYear();
        this.year = date.getYear();
    }

    @Embedded
    public MedTime getTime() {
        if (time == null) this.time = new MedTime();
        return time;
//        return time;
    }
    public void setTime(MedTime time) {
        this.time = time;
//        if (time == null) this.time = new MedTime();
//        else this.time = time;
//        setTimeEnabled(time != null);
    }
    public Short getDay() {
        return day;
    }
    // this ensures we never save a value from a drop down which is -1 - which it must be in the ddl!
    public void setDay(Short day) {
        this.day = day;
        if (day != null)
            if (day < 0)
                this.day = null;
    }
    public Short getMonth() {
        return month;
    }
    // this ensures we never save a value from a drop down which is -1 - which it must be in the ddl!
    public void setMonth(Short month) {
        this.month = month;
        if (month != null)
            if (month < 0)
                this.month = null;
    }
    public Integer getYear() {
        return year;
    }

    public void setYear(Integer year) {
        this.year = year;
        if (year != null)
            if (year < 0)
                this.year = null;
    }

    @Transient
    public boolean isTimeEnabled() {
        return timeEnabled;
    }

    public void setTimeEnabled(boolean timeEnabled) {
        this.timeEnabled = timeEnabled;
    }

    @Transient
    @Override
    public String toString() {
        String tmp = "";
        if (year != null)
            tmp += year;
        tmp += "-";
        if (month != null)
            tmp += month;
        tmp += "-";
        if (day != null)
            tmp += day;
        return hasTime() ? tmp + " " + time.toString() : tmp;
    }

    // isTimeEnabled is a manual check used to force validation
    // this check is to see if a time has been set
    @Transient
    public boolean hasTime() {
        // we can't use isTimeEnabled since this is false by default
        // and a hibernate query for eventDate as eventDate uses the default
        if (time != null) {
            if ((time.getHour() != null) && (time.getMinute() != null))
                return true;
        }
        return false;
    }

    // hasDate checks if a date is valid according to what is allowed - eg birthdays only need a month!
    @Transient
    public boolean hasDate(boolean validateDay, boolean validateMonth, boolean validateYear) {
        if (validateDay && day == null)
            return false;
        if (validateMonth && month == null)
            return false;
        if (validateYear && year == null)
            return false;
        return true;
    }

    // validation type method where we use to check if something has been entered
    @Transient
    public boolean hasDetails() {
        if ((day != null) || (month != null) || (year != null))
            return true;
        if (timeEnabled)
            if ((time.getHour() != null) || (time.getMinute() != null))
                return true;
        return false;
    }

    /**
     * @return
     *      LocalDate representation of this instant, if at least year,month,day are set (and a valid combination)
     *      otherwise returns null
     */
    public LocalDate toLocalDate() {
        try {
            return year == null || month == null || day == null ? null : new LocalDate(year, month, day);
        } catch (Exception e) {
            return null;
        }
    }

}
