package com.ecco.dom.finance.commands;

import com.ecco.dom.servicerecipients.ServiceRecipientCommand;
import lombok.NoArgsConstructor;
import org.joda.time.Instant;

import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import java.util.UUID;

@Entity
@DiscriminatorValue("finReceipt")
@NoArgsConstructor
public class FinanceReceiptCommand extends ServiceRecipientCommand {

    public FinanceReceiptCommand(@Nullable UUID uuid, @NonNull Instant remoteCreationTime,
                                 long userId, @NonNull String body, @NonNull Integer serviceRecipientId) {
        super(uuid, remoteCreationTime, userId, body, serviceRecipientId);
    }

}
