package com.ecco.test.support;

import static org.junit.Assert.*;

import org.junit.Rule;
import org.junit.Test;

public class RetryRulePackageAnnotationTest {

    private int tryCount = 0;

    @Rule
    public RetryRule retryRule = new RetryRule(0);

    /** this test will fail on the first attempt but pass on the second. See package-info.java for
     *  where we specify <code>@Retries(2)</code> */
    @Test
    public void testPackageAnnotation() {

        if (++tryCount < 2) {
            fail("Failure number #" + tryCount);
        }
    }
}
