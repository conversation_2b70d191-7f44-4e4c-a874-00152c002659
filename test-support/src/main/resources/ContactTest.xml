<?xml version="1.0" encoding="UTF-8"?>

<!-- seems that unit<PERSON> generates the xsd before hibernate generates the schema!
 xsi:noNamespaceSchemaLocation="../../../../dataset.xsd"
-->
<dataset xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

    <test_partners id="33" version="0" name="helenPartner"/>
    <test_partners id="34" version="0" name="noPartner"/>

    <!-- it appears to be a dbunit quirk that the first occurrence of a table is taken, and subsequent defs have to have the properties null if they are not required -->
    <!-- see first part of http://www.adeptechllc.com/2009/07/07/some-unfortunate-behavior-in-dbunit/ -->
    <test_contacts id="15" version="1" partnersId="33" name="adam" addressline1="61a Belmont Hill" addresspostcode="SE13 5AX"/>
    <test_contacts id="17" version="0"  name="holly" addressline1="Winwick" addresspostcode="WA3"/>

    <test_children id="74" version="1" name="adamChild" contactId="15" testContactId="15"/>
    <test_children id="78" version="0" name="thatChild" contactId="15" testContactId="17"/>

</dataset>
