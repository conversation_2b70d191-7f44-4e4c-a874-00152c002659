package com.ecco.config.dom;

import com.ecco.infrastructure.entity.ConfigurableLongKeyedEntity;
import com.ecco.infrastructure.markdown.MarkdownProcessor;
import com.ecco.infrastructure.util.FlagMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.querydsl.QPageRequest;
import org.springframework.data.querydsl.QSort;
import org.springframework.util.Assert;

import javax.persistence.*;
import javax.validation.constraints.NotNull;

@SuppressWarnings("deprecation")
@Entity
@Table(name = "cfg_settings")
public class Setting extends ConfigurableLongKeyedEntity {

    private static final long serialVersionUID = 1L;

    public enum Type { STRING, NUMBER, ENUM, MULTI_ENUM, MULTI_STRING, BOOLEAN }

    @Autowired
    @Transient
    private MarkdownProcessor markdownProcessor;

    private String namespace;

    /* Note column name override since key is a reserved word in mysql
    See https://dev.mysql.com/doc/refman/5.5/en/reserved-words.html */
    @Column(name = "keyname")
    private String key;

    @Column(name = "keyvalue")
    private String value;

    private String description;

    /**
     * An indication that some settings are only used during the startup phase
     */
    private boolean consumedOnStartup;

    @Enumerated(EnumType.STRING)
    private Type type;

    /** Null if not an enum */
    @org.hibernate.annotations.Type(type="class")
    private Class<? extends Enum<?>> enumType;


    protected Setting() {
    }

    public Setting(String namespace, String key, String value, String description, Type type,
            Class<? extends Enum<?>> enumType) {
        super();
        this.namespace = namespace;
        this.key = key;
        this.value = value;
        this.description = description;
        this.type = type;
        this.enumType = enumType;
        this.consumedOnStartup = true;
    }


    public <T extends Enum<T>> T getAsEnum() {
        return Enum.valueOf(this.<T>getEnumType(), value);
    }

    /**
     * Determines if the setting value is 'true' (ignoring case).
     */
    public boolean isTrue() {
        Assert.state(type == Type.BOOLEAN);
        return Boolean.parseBoolean(value);
    }

    public FlagMap getAsFlagMap() {
        Assert.state(type == Type.MULTI_STRING);
        return new FlagMap(value);
    }

    @SuppressWarnings("WeakerAccess")
    public int getAsNumber() {
        Assert.state(type == Type.NUMBER);
        return Integer.parseInt(value);
    }

    public String getMarkdownAsHtml() {
        return markdownProcessor.getAsHtml(value);
    }

    public PageRequest asPageRequest(int page) {
        return PageRequest.of(page, getAsNumber());
    }

    public QPageRequest asQPageRequest(int page) {
        return new QPageRequest(page, getAsNumber());
    }

    public QPageRequest asQPageRequest(int page, QSort sort) {
        return new QPageRequest(page, getAsNumber(), sort);
    }



    // ACCESSORS - Put real code above here

    @SuppressWarnings({"unchecked", "WeakerAccess"})
    public <T extends Enum<T>> Class<T> getEnumType() {
        return (Class<T>) enumType;
    }

    @SuppressWarnings("unused")
    public void setEnumType(Class<? extends Enum<?>> enumClass) {
        this.enumType = enumClass;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @NotNull
    public String getNamespace() {
        return namespace;
    }

    public void setNamespace(String namespace) {
        this.namespace = namespace;
    }

    @NotNull
    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public Type getType() {
        return type;
    }

    public void setType(Type type) {
        this.type = type;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    @SuppressWarnings("unused")
    public boolean isConsumedOnStartup() {
        return consumedOnStartup;
    }

    @SuppressWarnings("unused")
    public void setConsumedOnStartup(boolean readOnStartup) {
        this.consumedOnStartup = readOnStartup;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }

        Setting setting = (Setting) o;

        return key == null ? setting.key == null : key.equals(setting.key);
    }

    @Override
    public int hashCode() {
        int result = super.hashCode();
        result = 31 * result + (key != null ? key.hashCode() : 0);
        return result;
    }
}
