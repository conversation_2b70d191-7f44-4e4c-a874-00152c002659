/*
 * This file was generated by the Gradle 'init' task.
 *
 * This project uses @Incubating APIs which are subject to change.
 */

plugins {
    id("org.eccosolutions.querydsl-java-library")
}

dependencies {
    implementation(project(":ecco-infrastructure"))
    implementation(project(":ecco-upload-dom"))

    implementation("joda-time:joda-time:2.10.8")
    implementation("com.google.guava:guava")

    testImplementation(project(":test-support"))
}

// Configure specific test to run with DROP_CREATE liquibase mode
tasks.withType<Test> {
    if (name == "test") {
        systemProperty("liquibase", "DROP_CREATE")
    }
}

description = "ecco-config"
