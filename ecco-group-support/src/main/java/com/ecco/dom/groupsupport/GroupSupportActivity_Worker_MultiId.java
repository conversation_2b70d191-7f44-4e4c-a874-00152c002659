package com.ecco.dom.groupsupport;

import java.io.Serializable;

import javax.persistence.Embeddable;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;

import com.ecco.dom.hr.Worker;

@Embeddable
public class GroupSupportActivity_Worker_MultiId implements Serializable {

    private static final long serialVersionUID = 1L;

    private GroupSupportActivity activity;
    private Worker worker;

    @ManyToOne(fetch=FetchType.LAZY, optional=false)
    public GroupSupportActivity getGroupSupportActivity() {
        return activity;
    }
    @ManyToOne(fetch=FetchType.LAZY, optional=false)
    public Worker getWorker() {
        return worker;
    }
    public void setGroupSupportActivity(GroupSupportActivity activity) {
        this.activity= activity;
    }
    public void setWorker(Worker worker) {
        this.worker = worker;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        GroupSupportActivity_Worker_MultiId that = (GroupSupportActivity_Worker_MultiId) o;

        if (activity != null ? !activity.equals(that.activity) : that.activity != null) return false;
        if (worker != null ? !worker.equals(that.worker) : that.worker != null)
            return false;

        return true;
    }

    @Override
    public int hashCode() {
        int result;
        result = (activity != null ? activity.hashCode() : 0);
        result = 31 * result + (worker != null ? worker.hashCode() : 0);
        return result;
    }

    @Override
    public String toString() {
        return "groupsupportactivity: " + (activity == null ? "null" : activity.getId()) + ", worker: " + (worker == null ? "null" : worker.getId());
    }
}
