package com.ecco.rota.service;

import com.ecco.buildings.dom.FixedContainer;
import com.ecco.buildings.repositories.FixedContainerRepository;
import com.ecco.dao.DemandScheduleRepository;
import com.ecco.dao.ServiceAgreementRepository;
import com.ecco.dom.agreements.AppointmentSchedule;
import com.ecco.dom.agreements.QDemandSchedule;
import com.ecco.dom.agreements.ServiceAgreement;
import com.ecco.dom.hr.WorkerJob;
import com.ecco.hr.dao.WorkerJobRepository;
import com.ecco.infrastructure.time.JodaToJDKAdapters;
import com.ecco.rota.webApi.dto.Rota;
import com.ecco.rota.webApi.dto.RotaParams;
import com.ecco.service.EventService;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.jpa.JPQLQuery;
import org.joda.time.DateTime;

import java.util.EnumSet;
import java.util.List;
import java.util.Objects;
import java.util.stream.StreamSupport;

import static com.ecco.dao.DemandSchedulePredicates.*;
import static com.ecco.rota.service.WorkerResourceableRotaHandler.WorkerOption.*;
import static com.ecco.calendar.core.Recurrence.Status.*;

/**
 * Handles worker resource to building demand.
 * This is the live rota for workers (with historical buildings demand, or the newer care runs demand).
 * See BuildingCareRunRotaHandler for the run builder.
 * Workers need a primary location on the building, referrals need to be residing at the building.
 */
public class BuildingWorkerRotaHandler extends WorkerResourceableRotaHandler {

    private static final String DEMAND_HANDLER_PREFIX = "buildings:"; // demand is the building which includes its demands and its associated referrals demands
    private static final String RESOURCE_HANDLER_PREFIX = "workers:"; // resource is the workers who belong to the building

    private final FixedContainerRepository fixedContainerRepository;


    public BuildingWorkerRotaHandler(DemandScheduleRepository demandScheduleRepository,
                                     WorkerJobRepository workerJobRepository, FixedContainerRepository fixedContainerRepository,
                                     ServiceAgreementRepository serviceAgreementRepository,
                                     RotaService rotaService,
                                     EventService eventService) {
        super(demandScheduleRepository, workerJobRepository, serviceAgreementRepository, rotaService, eventService);
        this.fixedContainerRepository = fixedContainerRepository;
    }

    @Override
    public boolean canHandle(String resourceFilter, String demandFilter) {
        return resourceFilter.startsWith(RESOURCE_HANDLER_PREFIX) && demandFilter.startsWith(DEMAND_HANDLER_PREFIX);
    }

    @Override
    public List<Integer> findAllResourceServiceRecipientIds(RotaParams params) {
        Rota rota = new Rota(params.getStartDate(), params.getEndDate(), params.getResourceFilter(), params.getDemandFilter(), params.getLoadResource(), params.getLoadDemand());

        // extract from getLoadResource
        var workerJobs = getResources(rota);
        return StreamSupport.stream(workerJobs.spliterator(), false).map(WorkerJob::getServiceRecipientId).toList();
    }

    @Override
    public void populateRota(Rota rota) {

        if (rota.getLoadResource()) {
            var workerJobs = getResources(rota);
            // add resources as per DefaultRotaHandler
            addWorkersEntries(rota, workerJobs, ALLOCATED_APPOINTMENTS, AVAILABILITY, EVENTS_WITH_CATEGORY);
        }

        populateRotaDemand(rota);
    }

    public void populateRotaDemand(Rota rota) {
        if (rota.getLoadDemand()) {
            // get unallocated demand that won't show on a resource
            // either get a specific demand srId (we cache per srId)
            // or get all referrals in the building and the demand on the building itself
            addDemandWithStatuses(rota, rota, EnumSet.of(TENTATIVE, DROPPED), this.getDemandScheduleWherePredicate(rota));

            // NB the below clause was added to populateRotaDemand in BuildingCareRunRotaHandler in ec027871 - but since its now refactored,
            // we add the comments here:
            //      NB this was refactored in BuildingWorkerRotaHandler, and copied here in ec027871 (part of ServiceCat rota handlers)
            //      seemingly it was missing this clause, which 'load the unallocated carerun demand (see a362461e)' - avoiding runs being loaded each time
            //      meaning that the above loads getWherePredicateForDemandSchedulesOfClientsInBuildingAndBuilding for the referral/bldg demand
            //      and this loads getWherePredicateForDemandSchedulesOfCareRunsInBuilding for the care runs demand in the building

            // if we ARE using a specific demand srId then we will already be calling the demand from findAllAgreementsByScheduleDate
            // if we AREN'T using a specific demand srId then we also need to load the unallocated carerun demand (see a362461e)
            if (getDemandServiceRecipientId(rota.getDemandFilter()) == null) {
                addDemandWithStatuses(rota, rota, EnumSet.of(TENTATIVE, DROPPED), this.getDemandScheduleCareRunsWherePredicate(rota));
            }
        }
    }

    private Iterable<WorkerJob> getResources(Rota rota) {
        Integer srId = getResourceServiceRecipientId(rota.getResourceFilter());
        int buildingId = getDemandBuildingId(rota);
        var startDate = JodaToJDKAdapters.dateTimeToJdk(rota.getStartDate().toDateTimeAtStartOfDay());
        return srId != null
                ? workerJobRepository.findAllByServiceRecipient_IdIn(List.of(srId))
                : workerJobRepository.findAllStaffWithPrimaryLocationBuildingIdEmployedAt(buildingId, startDate);
    }

    // TODO: convert this to being HATEOAS based with link to serviceRecipient/srid/ which has serviceagreements on it
    private int getDemandBuildingId(RotaParams rota) {
        return getDemandBuildingId(rota.getDemandFilter());
    }

    private int getDemandBuildingId(String demandFilter) {
        Objects.requireNonNull(demandFilter);
        String[] split = demandFilter.split(":");
        return Integer.parseInt(split[1]);
    }

    public FixedContainer getDemandBuilding(RotaParams rota) {
        return fixedContainerRepository.findById(getDemandBuildingId(rota))
                .orElseThrow(() -> new IllegalArgumentException("Requested building does not exist: id=" + getDemandBuildingId(rota)));
    }

    @Override
    public List<Integer> findAllAgreementSrIdsByScheduleDate(RotaParams params) {
        // find all the demand agreements for the client to loop through each srId
        // careruns also have a demand, and they may be unallocated on the building rota
        BooleanExpression wherePredicate = getDemandScheduleWherePredicate(params);
        BooleanExpression wherePredicateRuns = getDemandScheduleCareRunsWherePredicate(params);

        JPQLQuery<Integer> query = query(QDemandSchedule.demandSchedule)
                .where(wherePredicate.or(wherePredicateRuns))
                .select(QDemandSchedule.demandSchedule.agreement.serviceRecipientId)
                .distinct();

        return query.fetch();
    }

    @Override
    public List<ServiceAgreement> findAllAgreementsByScheduleDate(RotaParams params) {
        BooleanExpression wherePredicate = getDemandScheduleWherePredicate(params);
        // careruns also have a demand, and they may be unallocated on the building rota
        BooleanExpression wherePredicateRuns = getDemandScheduleCareRunsWherePredicate(params);

        JPQLQuery<ServiceAgreement> query = query(QDemandSchedule.demandSchedule)
                .where(wherePredicate.or(wherePredicateRuns))
                .select(QDemandSchedule.demandSchedule.agreement)
                .distinct();

        return query.fetch();
    }

    @Override
    protected BooleanExpression getDemandScheduleWherePredicate(RotaParams rota) {
        DateTime start = rota.getStartDate().toDateTimeAtStartOfDay();
        DateTime end = rota.getEndDate().toDateTimeAtStartOfDay();

        // get the buildingId from the demand filer - serviceRecipientFilter, eg 'buildings:1021"
        FixedContainer bldg = getDemandBuilding(rota);
        // get a specific srId from a demand filter - '<demand>:<id>:<srId>'
        // which allows a specific demand (not the entire building) to return rota appointments
        // used by the rota (see RotaView.tsx rotaRepository.findOneByDate) to get unallocated appointments
        // also used by RotaPreCacheAgent to pre-cache them
        var specificDemandSrId = getDemandServiceRecipientId(rota.getDemandFilter());
        return specificDemandSrId != null
                ? getWherePredicateForDemandSchedulesOfServiceRecipient(start, end, AppointmentSchedule.class, specificDemandSrId)
                : getWherePredicateForDemandSchedulesOfClientsInBuildingAndBuilding(start, end, AppointmentSchedule.class,
                    bldg.getId(), bldg.getServiceRecipientId(), fixedContainerRepository);
    }

    protected BooleanExpression getDemandScheduleCareRunsWherePredicate(RotaParams rota) {
        DateTime start = rota.getStartDate().toDateTimeAtStartOfDay();
        DateTime end = rota.getEndDate().toDateTimeAtStartOfDay();
        FixedContainer building = getDemandBuilding(rota);
        return getWherePredicateForDemandSchedulesOfCareRunsInBuilding(start, end, AppointmentSchedule.class,
                building.getId(), fixedContainerRepository);
    }
}
