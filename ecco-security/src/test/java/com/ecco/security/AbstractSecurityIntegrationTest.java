package com.ecco.security;

import com.ecco.config.service.SettingsService;
import com.ecco.config.service.SoftwareModuleService;
import com.ecco.config.service.StubSettingsService;
import com.ecco.dom.Individual;
import com.ecco.infrastructure.config.root.EccoApplicationContextInitializer;
import com.ecco.infrastructure.config.root.EnvironmentPropertiesConfig;
import com.ecco.infrastructure.config.root.InfrastructureConfig;
import com.ecco.infrastructure.config.root.Profiles;
import com.ecco.security.config.SecurityConfig;
import com.ecco.security.repositories.IndividualRepository;
import com.ecco.security.repositories.UserRepository;
import com.ecco.test.support.StackTraceSanitisingContextLoader;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.PostConstruct;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

@RunWith(SpringJUnit4ClassRunner.class)
@ActiveProfiles({Profiles.EMBEDDED, Profiles.TEST_FIXTURE})
@ContextConfiguration(
        initializers=EccoApplicationContextInitializer.class ,
        classes={InfrastructureConfig.class, SecurityConfig.class, AbstractSecurityIntegrationTest.ServiceConfig.class},
        loader = StackTraceSanitisingContextLoader.class)
public abstract class AbstractSecurityIntegrationTest {

    protected final Logger log = LoggerFactory.getLogger(getClass());

    @Configuration
    public static class ServiceConfig {

        @Bean public SettingsService settingsService() {
            return new StubSettingsService();
        }

        @Bean public SoftwareModuleService softwareModuleService() {
            return Mockito.mock(SoftwareModuleService.class);
        }

        @Bean public MessageSourceAccessor messageSourceAccessor() {
            return Mockito.mock(MessageSourceAccessor.class);
        }

    }

    @Autowired
    public IndividualRepository individualRepository;

    @Autowired
    public UserRepository userRepository;

    EnvironmentPropertiesConfig environmentPropertiesConfig;

    @Autowired
    public void setEnvironmentPropertiesConfig(EnvironmentPropertiesConfig environmentPropertiesConfig) {
        this.environmentPropertiesConfig = environmentPropertiesConfig;
    }

    @PersistenceContext
    protected EntityManager entityManager;

    @PostConstruct
    public void init() {
        System.out.println("got it");
    }

    protected void insert(Individual individual) {
        userRepository.save(individual.getUser());
        individualRepository.save(individual);
    }

}
