package com.ecco.security.authentication

import com.ecco.security.dom.User
import com.ecco.security.repositories.MagicTokenRepository
import com.ecco.security.service.AuthenticationCheck
import org.apache.commons.logging.Log
import org.apache.commons.logging.LogFactory
import org.springframework.data.repository.findByIdOrNull
import org.springframework.security.authentication.AuthenticationProvider
import org.springframework.security.authentication.BadCredentialsException
import org.springframework.security.authentication.CredentialsExpiredException
import org.springframework.security.core.Authentication
import org.springframework.security.core.AuthenticationException
import java.time.Instant

class MagicTokenAuthenticationProvider(
    private val repository: MagicTokenRepository,
    private val additionalAuthenticationCheck: AuthenticationCheck<User, MagicTokenAuthenticationToken> = AuthenticationCheck.none,
) : AuthenticationProvider {
    private val log: Log = LogFactory.getLog(javaClass)

    @Throws(AuthenticationException::class)
    override fun authenticate(authentication: Authentication?): Authentication? {
        if (supports(authentication?.javaClass)) {
            val token = authentication?.credentials?.toString()?.let { repository.findByIdOrNull(it) }
            if (token == null) {
                log.debug("Failed to authenticate because Magic Token is invalid")
                throw BadCredentialsException("Magic Token is invalid")
            } else if (token.expiry <= Instant.now()) {
                log.debug("Failed to authenticate because Magic Token has expired")
                throw CredentialsExpiredException("Magic Token has expired")
            } else if (token.revoked) {
                log.debug("Failed to authenticate because Magic Token has been revoked")
                throw CredentialsExpiredException("Magic Token has been revoked")
            } else {
                log.debug("additional authentication checks in progress")

                additionalAuthenticationCheck.check(token.targetUser, authentication as MagicTokenAuthenticationToken)

                return MagicTokenAuthenticationToken.authenticated(token, authentication.details).also {
                    log.debug("Authenticated user by Magic Token")
                }
            }
        } else {
            return null
        }
    }

    override fun supports(authentication: Class<*>?): Boolean = authentication?.let {
        MagicTokenAuthenticationToken::class.java.isAssignableFrom(it)
    } ?: false
}