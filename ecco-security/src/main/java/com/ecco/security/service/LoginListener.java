package com.ecco.security.service;

import org.jspecify.annotations.NonNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.security.authentication.event.AuthenticationFailureBadCredentialsEvent;
import org.springframework.security.authentication.event.AuthenticationSuccessEvent;
import org.springframework.security.core.Authentication;

import javax.annotation.Resource;

public class LoginListener implements ApplicationListener {

    protected final Logger log = LoggerFactory.getLogger(getClass());
    @Resource(name = "userManagementService")
    private UserManagementService userManagementService;

    @Override
    public void onApplicationEvent(@NonNull ApplicationEvent event) {

        // the failure events 'Makes no assertion as to whether or not the credentials were valid.'
        // and so only the bad credentials are supported here - expired etc is handled internally ourselves

        // this does not handle cases where we prevent access, or a user is disabled etc
        if (event instanceof AuthenticationFailureBadCredentialsEvent) {
            AuthenticationFailureBadCredentialsEvent authEvent = (AuthenticationFailureBadCredentialsEvent) event;
            Authentication auth = authEvent.getAuthentication();
            //log.debug("listener: a login failed: " + (authEvent.getAuthentication().getPrincipal()));
            try {
                userManagementService.recordFailedLogin(auth);
            }
            catch(Exception e) {
                log.warn("Skipping failure of userMgtService.recordFailedLogin() on successful login: ", e.getMessage());
            }
        }
        if (event instanceof AuthenticationSuccessEvent) {
            // rememberme also calls this
            AuthenticationSuccessEvent authEvent = (AuthenticationSuccessEvent) event;
            try {
                userManagementService.recordSuccessfulLoginTime(authEvent.getAuthentication().getName(), new DateTime(DateTimeZone.UTC));
            }
            catch(Exception e) {
                log.warn("Skipping failure of userMgtService.recordSuccessfulLoginTime() on successful login: ", e.getMessage());
            }
        }

    }

}
