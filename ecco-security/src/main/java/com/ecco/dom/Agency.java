package com.ecco.dom;

import com.ecco.config.dom.ListDefinitionEntry;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;

@Entity
@DiscriminatorValue("agency")
@Getter
@Setter
public class Agency extends Company {

    // "Subclasses cannot define attributes as not allowing null, as the other subclasses must insert null into those columns"
    // so for single table inheritance change from primitive type to object wrapper
    @Column
    private Boolean internal; // used to represent an internal transfer

    @Column
    private Boolean outOfArea;

    private Boolean deliveryPartner;

    @ManyToOne(fetch= FetchType.LAZY)
    @JoinColumn(name="agencycategoryId", updatable = false, insertable = false)
    private ListDefinitionEntry agencyCategory;

    @Column(name="agencycategoryId")
    private Integer agencyCategoryId;


    // *****************
    // GRAPH PARENT
    /*
    public void addGraphElement(String property, GraphElement ge) {
    }
    public GraphElement constructGraphElement(String property, String data) {
        return null;
    }
    public int nextTransientId(String property) {
        return 0;
    }
    public int lastTransientId(String property) {
        return 0;
    }
    public boolean removePersistentGraphElement(String property, long id) {
        return false;
    }
    public boolean removeTransientGraphElement(String property, int id) {
        return false;
    }
    public GraphElement findTransientGraphElement(String property, int id) {
        return null;
    }
    public GraphElement findPersistentGraphElement(String property, long id) {
        return null;
    }
    */

    /*
    @Override
    @Transient
    public String getDiscriminator() {
        return ContactBase.BUSINESS;
    }
    */

}
