package com.ecco.dom;

import javax.persistence.Column;
import javax.persistence.MappedSuperclass;
import javax.persistence.PrePersist;
import javax.persistence.Transient;

import com.ecco.calendar.dom.Calendarable;
import com.ecco.infrastructure.entity.ConfigurableLongKeyedEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEvent;

import com.ecco.infrastructure.bus.MessageBus;
import com.ecco.security.event.CalendarableCreated;
import com.ecco.calendar.core.CalendarOwnerDefinition;

/**
 * Prefer CalendarableIntKeyedEntity instead of this one which uses Long
 */
@MappedSuperclass
public abstract class CalendarableEntity extends ConfigurableLongKeyedEntity implements Calendarable {

    private static final long serialVersionUID = 1L;

    @Autowired
    @Transient
    protected transient MessageBus<ApplicationEvent> messageBus;

    @Column(name = "calendarId", length = 40)
    private String calendarId;


    public CalendarableEntity() {
        super();
    }

    protected abstract boolean isCalendarable();

    public CalendarableEntity(Long id) {
        super(id);
    }

    @Override
    public String getCalendarId() {
        return calendarId;
    }

    @Override
    public void setCalendarId(String calendarId) {
        this.calendarId = calendarId;
    }

    @PrePersist
    protected void prePersist() {
        fireCalendarableCreated();
    }

    /** Handle the case where the contact is created as a straightforward entity. */
    public void fireCalendarableCreated() {
        if (isCalendarable()) {
            fireCalendarableCreatedEvent();
        }
    }
    public void fireCalendarableCreatedEvent() {
        if (calendarId == null) {
            final CalendarableCreated event = new CalendarableCreated(this, this);
            messageBus.publishBeforeTxEnd(event);
        }
    }

    /**
     * Fill in calendar owner details from the contact subtype, except for email and password which can be found on here.
     * @param builder the builder to enrich
     * @return the builder passed in
     */
    @Override
    public abstract CalendarOwnerDefinition.Builder buildCalendarOwner(CalendarOwnerDefinition.Builder builder);

    /**
     * Fill in calendar owner name update details from the contact subtype
     * @param builder the builder to enrich
     * @return the builder passed in
     */
    @Override
    public abstract CalendarOwnerDefinition.Builder buildCalendarNamesUpdate(CalendarOwnerDefinition.Builder builder);
}