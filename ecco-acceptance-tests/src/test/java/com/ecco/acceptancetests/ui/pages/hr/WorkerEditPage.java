package com.ecco.acceptancetests.ui.pages.hr;

import com.ecco.acceptancetests.ui.pages.BasePageObject;

import org.openqa.selenium.WebDriver;

public class WorkerEditPage extends BasePageObject {

    private static final String URL = "/dynamic/secure/hr/worker/edit";

    private static final String FIELD_FIRST_NAME = "contact.firstName";
    private static final String FIELD_LAST_NAME = "contact.lastName";
    private static final String FIELD_WORKER_ID = "code";
    private static final String FIELD_GENDER = "gender";

    private static final String BUTTON_SAVE_ID = "save";
    private static final String CONTACT_TAB_HREF = "#fragment-2";

    public WorkerEditPage(WebDriver webDriver) {
        super(URL, webDriver);
    }

    public HrPage createNewWorker(String workerId, String firstName, String lastName) {
        setField(FIELD_FIRST_NAME, firstName);
        setField(FIELD_LAST_NAME, lastName);
        setField(FIELD_WORKER_ID, workerId);
        setField(FIELD_GENDER, "male");
        clickLinkHref(CONTACT_TAB_HREF);
        // Just in case we run against real outbound email!
        setField("contact.email", firstName + "." + lastName + "@fake.eccosolutions.co.uk");
        clickButton(BUTTON_SAVE_ID);
        return new HrPage(getWebDriver());
    }

}
