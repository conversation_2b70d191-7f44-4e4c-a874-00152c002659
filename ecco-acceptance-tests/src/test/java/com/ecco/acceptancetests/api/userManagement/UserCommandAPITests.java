package com.ecco.acceptancetests.api.userManagement;

import com.ecco.test.support.UniqueDataService;
import com.ecco.acceptancetests.api.BaseJsonTest;
import com.ecco.data.client.ServiceOptions;
import com.ecco.webApi.acls.AclEntryViewModel;
import com.ecco.webApi.evidence.BaseCommandViewModel;
import com.ecco.dto.ChangeViewModel;
import com.ecco.webApi.users.UserCoreCommandDto;
import com.ecco.webApi.users.UserMfaResetCommandDto;
import com.ecco.webApi.viewModels.UserViewModel;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;


@SuppressWarnings("ConstantConditions")
public class UserCommandAPITests extends BaseJsonTest {

    UniqueDataService uniqueDataService = UniqueDataService.instance;

    @Test
    public void canAddUser() {
        UserViewModel u = canAddUser("addme", "add", "me");
        assertThat(u.isEnabled()).isFalse();
    }

    @Test
    public void canAddUserThenUpdate() {
        UserViewModel u = canAddUser("updateme", "update", "me");
        UserCoreCommandDto vm = new UserCoreCommandDto(BaseCommandViewModel.OPERATION_UPDATE, u.userId);
//        vm.newPassword = ChangeViewModel.changeNullTo("ecco2");
        vm.enabled = ChangeViewModel.changeNullTo(true);
        commandActor.executeCommand(vm);

        u = userActor.getUser(u.username).getBody();
        assertThat(u.isEnabled()).isTrue();
    }

    @Test
    @Disabled
    public void cannotRemoveUser() {
    }

    @Test
    public void canAddUserAndGroup() {
        UserViewModel u = canAddUser("addgroups", "add", "groups");
        userActor.addUserGroups(u.userId, "staff", "offline");
        u = userActor.getUser(u.username).getBody();
        assertThat(u.getGroups()).contains("staff", "offline");
    }

    @Test
    public void canAddUserAndGroupAndAcls() {
        userActor.ensureAcls();

        UserViewModel u = canAddUser("addacls", "add", "acls");
        userActor.addUserGroups(u.userId, "staff");
        userActor.addUserAcls(u.userId, ServiceOptions.FLOATING_SUPPORT.getServiceViewModel().id, null);

        AclEntryViewModel[] acls = userActor.getUserAcls(u.username).getBody();
        assertThat(acls).hasSize(1);
        assertThat(acls[0].getSecureObjectId()).isEqualTo(ServiceOptions.FLOATING_SUPPORT.getServiceViewModel().id.longValue());
    }

    @Test
    public void canAddUserAndGroupAndAclsAndRemove() {
        userActor.ensureAcls();

        UserViewModel u = canAddUser("removeacls", "remove", "acls");
        userActor.addUserGroups(u.userId, "staff", "offline");
        userActor.addUserAcls(u.userId, ServiceOptions.FLOATING_SUPPORT.getServiceViewModel().id, null);

        userActor.removeUserGroups(u.userId, "staff");
        userActor.removeUserAcls(u.userId, ServiceOptions.FLOATING_SUPPORT.getServiceViewModel().id, null);

        u = userActor.getUser(u.username).getBody();
        assertThat(u.getGroups()).hasSize(1);
        AclEntryViewModel[] acls = userActor.getUserAcls(u.username).getBody();
        assertThat(acls).hasSize(0);
    }

    @Test
    public void mfaUserAdminFlow() {
        // GIVEN a user
        UserViewModel u;
        {
            u = canAddUser("mfa", "mfa", "mfa-test");
        }

        // WHEN set mfaRequired
        {
            UserCoreCommandDto vm = new UserCoreCommandDto(BaseCommandViewModel.OPERATION_UPDATE, u.userId);
            vm.enabled = ChangeViewModel.changeNullTo(true);
            vm.mfaRequired = ChangeViewModel.changeNullTo(true);
            commandActor.executeCommand(vm);

            u = userActor.getUser(u.username).getBody();
            assert u != null;
            assertThat(u.isEnabled()).isTrue();
            assertThat(u.isMfaRequired()).isEqualTo(true);
        }

        // WHEN reset mfa secret
        {
            var vm = new UserMfaResetCommandDto(u.userId);
            commandActor.executeCommand(vm);
        }

        // THEN mfa required is true, and mfaSecret is set
        {
            u = userActor.getUser(u.username).getBody();
            assert u != null;
            assertThat(u.isMfaValidated()).isFalse();
            assertThat(u.isMfaRequired()).isTrue();
        }
    }

    private UserViewModel canAddUser(String usernameIn, String firstName, String lastName) {
        String username = uniqueDataService.userNameFor(usernameIn);
        userActor.createUser(username, uniqueDataService.firstNameFor(firstName), uniqueDataService.lastNameFor(lastName));
        UserViewModel u = userActor.getUser(username).getBody();
        assertThat(u).describedAs("user not created").isNotNull();
        return u;
    }

}
