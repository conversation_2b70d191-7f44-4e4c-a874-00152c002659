package com.ecco.data.client.dataimport.csv.mappers

import org.mockito.kotlin.*
import org.springframework.beans.NotWritablePropertyException
import org.springframework.core.convert.converter.Converter
import org.springframework.format.support.DefaultFormattingConversionService
import kotlin.test.*

class CsvBeanMapperTest {
    private class TestData @JvmOverloads constructor(var a: Int = 0, var b: Int = 0, var c: C? = null, var d: D? = null)

    private class C(val a: Int)

    private class D(var a: String = "")

    private class CConverter : Converter<String, C> {
        override fun convert(source: String): C = C(source.toInt())
    }

    private val conversionService = DefaultFormattingConversionService().apply {
        addConverter(CConverter())
    }

    @Test
    fun emptyList() {
        val mapper = CsvBeanMapper(TestData::class.java, conversionService)
        val result = mapper(listOf())
        assertEquals(0, result.a)
        assertEquals(0, result.b)
        assertNull(result.c)
        assertNull(result.d)
    }

    @Test
    fun setA() {
        val mapper = CsvBeanMapper(TestData::class.java, conversionService)
        val result = mapper(listOf(Pair("a", "123")))
        assertEquals(123, result.a)
        assertEquals(0, result.b)
        assertNull(result.c)
        assertNull(result.d)
    }

    @Test
    fun setAbc() {
        val mapper = CsvBeanMapper(TestData::class.java, conversionService)
        val result = mapper(
            listOf(
                Pair("a", "837"),
                Pair("b", "888"),
                Pair("c", "133"),
            ),
        )
        assertEquals(837, result.a)
        assertEquals(888, result.b)
        assertEquals(133, result.c?.a)
        assertNull(result.d)
    }

    @Test
    fun setD() {
        val mapper = CsvBeanMapper(TestData::class.java, conversionService)
        val result = mapper(listOf(Pair("d.a", "hello")))
        assertEquals(0, result.a)
        assertEquals(0, result.b)
        assertNull(result.c)
        assertEquals("hello", result.d?.a)
    }

    @Test
    fun setInvalid() {
        val mapper = CsvBeanMapper(TestData::class.java, conversionService)
        assertFailsWith<NotWritablePropertyException> {
            mapper(listOf(Pair("invalid", "wobble")))
        }
    }

    @Test
    fun setInvalidWithHandler() {
        val onNotWritableProperty = mock<((exception: NotWritablePropertyException?, value: Any?) -> Unit)>()
        val mapper = CsvBeanMapper(TestData::class.java, conversionService, onNotWritableProperty)
        mapper(listOf(Pair("invalid", "wobble")))
        verify(onNotWritableProperty).invoke(isA<NotWritablePropertyException>(), eq("wobble"))
    }
}