package com.ecco.data.client.actors;

import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.client.RestTemplate;

import com.ecco.webApi.viewModels.Result;
import com.ecco.webApi.viewModels.TextUpdateCommand;
import com.google.common.collect.ImmutableMap;

public class UpdateTextCommandActor extends BaseActor {

    public UpdateTextCommandActor(RestTemplate restTemplate) {
        super(restTemplate);
    }

    public ResponseEntity<Result> updateProperty(String entity, Long id, String property, String newValue, String oldValue) {
        TextUpdateCommand c = new TextUpdateCommand(oldValue, newValue);

        ResponseEntity<Result> responseEntity = restTemplate.postForEntity(apiBaseUrl + "{entity}/{id}/textUpdate/{path}/",
                c, Result.class, ImmutableMap.of("entity", entity, "id", id, "path", property));

        Assert.state(responseEntity.getStatusCode().is2xxSuccessful(), "Update property failed");
        return responseEntity;
    }

}
