package com.ecco.data.client.dataimport.csv

import au.com.bytecode.opencsv.CSVParser.*
import au.com.bytecode.opencsv.CSVReader
import java.io.IOException
import java.io.Reader

class RowCountingCsvReader(
    reader: Reader,
    separator: Char = DEFAULT_SEPARATOR,
    quotechar: Char = DEFAULT_QUOTE_CHARACTER,
    escape: Char = DEFAULT_ESCAPE_CHARACTER,
    strictQuotes: Boolean = DEFAULT_STRICT_QUOTES,
    ignoreLeadingWhitespace: Boolean = DEFAULT_IGNORE_LEADING_WHITESPACE,
) : CSVReader(reader, separator, quotechar, escape, 0, strictQuotes, ignoreLeadingWhitespace) {
    var currentRow = 1
        private set

    override fun readNext(): Array<String>? {
        val row = super.readNext()
        ++currentRow
        return row
    }

    @Throws(IOException::class)
    fun skipToRow(row: Int) {
        if (row < currentRow) {
            throw IllegalStateException("Already passed row: $row")
        } else {
            while (currentRow < row) {
                if (readNext() == null) {
                    throw IllegalStateException("Ran out of lines skipping to row: $row")
                }
            }
        }
    }

    fun readAsSequenceWithRowCount(): Sequence<Pair<Int, List<String>>> = sequence {
        while (true) {
            val row = currentRow
            val data = readNext() ?: break
            yield(Pair(row, data.asList()))
        }
    }
}