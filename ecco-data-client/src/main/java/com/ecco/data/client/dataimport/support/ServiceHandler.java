package com.ecco.data.client.dataimport.support;

import com.ecco.data.client.actors.CacheActor;
import com.ecco.data.client.actors.ServiceActor;
import com.ecco.data.client.model.ServiceImportViewModel;
import org.springframework.web.client.RestTemplate;


public class ServiceHandler extends AbstractHandler<ServiceImportViewModel> {

    private final ServiceActor serviceActor;
    private final CacheActor cacheActor;

    public ServiceHandler(RestTemplate template) {
        super(template);
        this.serviceActor = new ServiceActor(template);
        this.cacheActor = new CacheActor(template);
    }

    @Override
    protected void processEntity(ImportOperation<ServiceImportViewModel> operation) {
        ServiceImportViewModel input = operation.record;
        serviceActor.createService(input.getName(), input.serviceTypeId);
    }

}
