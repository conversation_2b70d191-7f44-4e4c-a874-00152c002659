package com.ecco.data.client.model.evidence;

import lombok.Data;
import org.joda.time.LocalDate;

import java.util.HashMap;

/**
 * Created by adam on 27/10/16.
 */
@Data
public class BaseEvidenceImportViewModel {

    // we won't get the referralId since its generated in ecco
    public String referralCode;
    public Integer srId;

    /**
     * Map from multiple csv field names to our single comment box
     * Allows us to build a comment from multiple columns
     * eg commentConcat["Treatment"]
     */
    public HashMap<String, String> commentConcat = new HashMap<>();
    // evidence
    public LocalDate workDate;
    public String comment;
    public Integer commentTypeId;
    public Integer minsSpent;
    String evidenceTask;
    String evidenceGroupKey;
}
