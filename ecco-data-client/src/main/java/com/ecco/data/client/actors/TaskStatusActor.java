package com.ecco.data.client.actors;

import com.ecco.webApi.viewModels.TaskStatusViewModel;
import com.google.common.collect.ImmutableMap;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

public class TaskStatusActor extends ServiceRecipientActor {

    public TaskStatusActor(RestTemplate restTemplate) {
        super(restTemplate);
    }

    public ResponseEntity<TaskStatusViewModel[]> getTasksByServiceRecipient(long serviceRecipientId) {
        return restTemplate.getForEntity(apiBaseUrl + "taskStatus/byServiceRecipient/{serviceRecipientId}/",
                TaskStatusViewModel[].class, ImmutableMap.of("serviceRecipientId", serviceRecipientId));
    }

}
