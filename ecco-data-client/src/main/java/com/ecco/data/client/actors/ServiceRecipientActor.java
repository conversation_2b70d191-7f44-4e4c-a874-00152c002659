package com.ecco.data.client.actors;

import com.ecco.servicerecipient.ServiceRecipientSummary;
import com.ecco.webApi.calendar.ServiceRecipientCalendarEntryCommandViewModel;
import com.ecco.webApi.rota.AppointmentActionCommandDto;
import com.ecco.webApi.rota.AppointmentRecurringActionCommandDto;
import com.ecco.webApi.viewModels.DeleteEvidenceCommandViewModel;
import com.ecco.webApi.viewModels.DeleteRequestEvidenceCommandViewModel;
import com.ecco.webApi.viewModels.DeleteRequestServiceRecipientCommandViewModel;
import com.google.common.collect.ImmutableMap;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.util.LinkedHashMap;
import java.util.List;

public class ServiceRecipientActor extends BaseActor {

    public ServiceRecipientActor(RestTemplate restTemplate) {
        super(restTemplate);
    }

    public ResponseEntity<List<LinkedHashMap<String,Object>>> findCommands(int serviceRecipientId) {
        var typeRef = new ParameterizedTypeReference<List<LinkedHashMap<String,Object>>>() {};
        return this.findCommands(typeRef, serviceRecipientId, "service-recipients/{serviceRecipientId}/commands/");
    }

    public ResponseEntity<AppointmentRecurringActionCommandDto> findAllocateRecurringCommand(String uuid) {
        return this.findCommand(AppointmentRecurringActionCommandDto.class, uuid);
    }

    public ResponseEntity<AppointmentActionCommandDto> findAllocateCommand(String uuid) {
        return this.findCommand(AppointmentActionCommandDto.class, uuid);
    }

    public ResponseEntity<AppointmentRecurringActionCommandDto> findRecurringAllocateCommand(String uuid) {
        return this.findCommand(AppointmentRecurringActionCommandDto.class, uuid);
    }

    public ResponseEntity<ServiceRecipientCalendarEntryCommandViewModel> findCalendarCommand(String uuid) {
        return this.findCommand(ServiceRecipientCalendarEntryCommandViewModel.class, uuid);
    }

    public ResponseEntity<List<DeleteRequestServiceRecipientCommandViewModel>> findDeleteRequestCommands(int serviceRecipientId) {
        var typeRef = new ParameterizedTypeReference<List<DeleteRequestServiceRecipientCommandViewModel>>() {};
        return this.findCommands(typeRef, serviceRecipientId, "service-recipients/{serviceRecipientId}/commands/delete-request/");
    }

    public ResponseEntity<List<DeleteEvidenceCommandViewModel>> findDeleteEvidenceCommands(int serviceRecipientId) {
        var typeRef = new ParameterizedTypeReference<List<DeleteEvidenceCommandViewModel>>() {};
        return this.findCommands(typeRef, serviceRecipientId, "service-recipients/{serviceRecipientId}/evidence/commands/delete/");
    }

    public ResponseEntity<List<DeleteRequestEvidenceCommandViewModel>> findDeleteEvidenceRequestCommands(int serviceRecipientId) {
        var typeRef = new ParameterizedTypeReference<List<DeleteRequestEvidenceCommandViewModel>>() {};
        return this.findCommands(typeRef, serviceRecipientId, "service-recipients/{serviceRecipientId}/evidence/commands/delete-request/");
    }

    /**
     * NB This fails for referrals, since the controller sends back a super class which Jackson doesn't recognise
     * See ServiceRecipientController.findOne.
     * Instead use referralActor.getReferralSummaryByServiceRecipientId
     * */
    public ResponseEntity<ServiceRecipientSummary> findSummary(int serviceRecipientId) {
        return restTemplate.getForEntity(apiBaseUrl + "service-recipient/{srId}/", ServiceRecipientSummary.class, ImmutableMap.of("srId", serviceRecipientId));
    }

    public ResponseEntity<ServiceRecipientSummary> findSummaryCached(int serviceRecipientId) {
        return restTemplate.getForEntity(apiBaseUrl + "service-recipient/{srId}/cached/", ServiceRecipientSummary.class, ImmutableMap.of("srId", serviceRecipientId));
    }

    private <T>  ResponseEntity<T> findCommands(ParameterizedTypeReference<T> typeRef, int serviceRecipientId, String url) {
        ResponseEntity<T> response = restTemplate.exchange(apiBaseUrl + url,
                HttpMethod.GET, null, typeRef, ImmutableMap.of("serviceRecipientId", serviceRecipientId));
        return response;
    }

    public <T> ResponseEntity<T> findCommand(Class<T> clazz, String uuid) {
        return restTemplate.getForEntity(apiBaseUrl + "service-recipients/command/{uuid}/", clazz, ImmutableMap.of("uuid", uuid));
    }

}