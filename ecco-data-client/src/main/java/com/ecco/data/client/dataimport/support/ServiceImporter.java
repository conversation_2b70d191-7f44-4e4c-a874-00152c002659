package com.ecco.data.client.dataimport.support;

import com.ecco.data.client.dataimport.csv.CSVBeanReader;
import com.ecco.data.client.model.ServiceImportViewModel;
import com.google.common.base.Throwables;
import com.google.common.collect.ImmutableMap;
import com.google.common.io.CharSource;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.util.Map;

public class ServiceImporter {

    private final Map<String, String> aliases = ImmutableMap.<String, String>builder()
            .build();

    protected final RestTemplate restTemplate;

    public ServiceImporter(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    public void read(CharSource source) {
        final Map<String, String> allSynonyms = ImmutableMap.<String, String>builder().
                putAll(Synonyms.synonymsToNormalizedMap)
                .build();

        try {
            new CSVBeanReader<>(source, ServiceImportViewModel.class, aliases, allSynonyms)
                    .readUsing(new ServiceHandler(restTemplate));
        } catch (IOException e) {
            throw Throwables.propagate(e);
        }
    }
}
