package com.ecco.dom.repairs;

import java.util.UUID;

import org.joda.time.Instant;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.persistence.*;

import com.ecco.infrastructure.dom.BaseIntKeyedCommand;

@Entity
@Table(name = "repair_commands")
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name = "commandname", discriminatorType = DiscriminatorType.STRING)
@DiscriminatorValue("update")
public class RepairCommand extends BaseIntKeyedCommand {

    @Column(nullable=false)
    private Integer repairId;

    public RepairCommand(@Nullable UUID uuid, @Nonnull Instant remoteCreationTime,
                         long userId, @Nonnull String body, Integer repairId) {
        super(uuid, remoteCreationTime, userId, body);
        this.repairId = repairId;
    }

    /**
     * @deprecated Do not use. Required by JPA/Hibernate.
     */
    @Deprecated
    protected RepairCommand() {
        super();
    }

    public Integer getRepairId() {
        return repairId;
    }
}
