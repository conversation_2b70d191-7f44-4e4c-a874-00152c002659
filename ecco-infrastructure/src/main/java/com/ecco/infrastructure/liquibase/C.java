package com.ecco.infrastructure.liquibase;

import org.jasypt.encryption.pbe.PBEStringEncryptor;

import com.ecco.infrastructure.config.root.SecureConfig;

public class C {

    private static final SecureConfig config = new SecureConfig();
    private static final PBEStringEncryptor e = config.encryptor();
    private static final PBEStringEncryptor se = config.searchableEncryptor();

    static protected String e(String s) {
        return e.encrypt(s);
    }

    static protected String se(String s) {
        return se.encrypt(s);
    }
}
