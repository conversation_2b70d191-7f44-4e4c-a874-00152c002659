package com.ecco.infrastructure.spring;

import org.springframework.beans.factory.annotation.Configurable;
import org.springframework.beans.factory.aspectj.AnnotationBeanConfigurerAspect;

/**
 * Provide most of Spring's @{@link Configurable} support. Requires implementing class to have init method
 * <pre>
 *     {
 *         injectServices();
 *     }
 * </pre>
 */
@Configurable
public interface ConfigurableMixin {

    default Object readResolve() {
        injectServices();
        return this;
    }

    default void injectServices() {
        // NOTE: This expects to find @Configurable on the class
         AnnotationBeanConfigurerAspect.aspectOf().configureBean(this);
    }
}
