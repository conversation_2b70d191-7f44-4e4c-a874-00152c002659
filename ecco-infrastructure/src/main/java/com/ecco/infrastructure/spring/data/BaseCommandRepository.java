package com.ecco.infrastructure.spring.data;

import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.NoRepositoryBean;
import org.springframework.data.repository.Repository;

import java.io.Serializable;
import java.util.UUID;

@NoRepositoryBean
public interface BaseCommandRepository<COMMAND, KEY extends Serializable> extends Repository<COMMAND, KEY>,
        QuerydslPredicateExecutor<COMMAND> {
    <TCommand extends COMMAND> TCommand save(TCommand command);

    COMMAND findOneByUuid(UUID uuid);

    void deleteAllInBatch(Iterable<COMMAND> entities);
}