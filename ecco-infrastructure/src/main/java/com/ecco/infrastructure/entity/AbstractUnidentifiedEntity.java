package com.ecco.infrastructure.entity;

import com.ecco.dom.BaseEntity;
import java.io.Serializable;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.MappedSuperclass;
import javax.persistence.Transient;

/**
 * Field access based {@link BaseEntity<Long>} which leaves the implementation of ID to its
 * subclass, allowing for different generators to be used.
 */
@MappedSuperclass
@Access(AccessType.FIELD)
public abstract class AbstractUnidentifiedEntity<PK extends Serializable> implements BaseEntity<PK>, Serializable {
    private static final long serialVersionUID = 1L;

    @Transient
    private Integer collectionId;

    @Override
    public Integer getCollectionId() {
        return collectionId;
    }

    @Override
    public void setCollectionId(int collectionId) {
        this.collectionId = collectionId;
    }

    @Override
    public boolean isNewEntity() {
        return (getId() == null);
    }

    @Override
    public String toString() {
        return (isNewEntity() ? "new" : getId().toString());
    }

    /**
     * Our entity equals/hashCode logic is based on the id and the collectionId
     * if they are the same, we are equal for new entities (with no id), the
     * equals and hashCode are the same as {@link Object#equals} and
     * {@link Object#hashCode} unless overridden in the entity.
     *
     * @return a hash code value for this object.
     */
    @Override
    public int hashCode() {
        if (isNewEntity()) {
            return super.hashCode();
        }

        final int prime = 31;
        int result = 1;
        result = prime * result + ((collectionId == null) ? 0 : collectionId.hashCode());
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        return result;
    }

    /**
     * Our entity equals/hashCode logic is based on the id and the collectionId
     * if they are the same, we are equal for new entities (with no id), the
     * equals and hashCode are the same as {@link Object#equals} and
     * {@link Object#hashCode} unless overridden in the entity.
     *
     * @param obj
     *            the reference object with which to compare.
     * @return <code>true</code> if this object is the same as the obj argument;
     *         <code>false</code> otherwise.
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        if (isNewEntity()) {
            return false;
        }
        final BaseEntity<PK> other = (BaseEntity<PK>) obj;
        if (!getId().equals(other.getId())) {
            return false;
        }

        if (collectionId == null) {
            if (other.getCollectionId() != null) {
                return false;
            }
        } else if (!collectionId.equals(other.getCollectionId())) {
            return false;
        }
        return true;
    }
}
