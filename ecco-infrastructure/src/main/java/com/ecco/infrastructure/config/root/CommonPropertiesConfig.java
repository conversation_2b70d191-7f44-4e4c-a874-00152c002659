package com.ecco.infrastructure.config.root;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.context.support.PropertySourcesPlaceholderConfigurer;

@Configuration(proxyBeanMethods = false)
@PropertySource({"classpath:/properties/common-jdbc.properties", "classpath:/git.properties"})
public class CommonPropertiesConfig {

    @Bean
    static public PropertySourcesPlaceholderConfigurer propertiesSourcePlaceholderConfigurer() {
        PropertySourcesPlaceholderConfigurer configurer = new PropertySourcesPlaceholderConfigurer();
        return configurer;
    }
}
