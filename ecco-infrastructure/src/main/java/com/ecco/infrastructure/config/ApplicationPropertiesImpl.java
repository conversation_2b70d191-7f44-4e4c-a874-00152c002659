package com.ecco.infrastructure.config;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.jspecify.annotations.NonNull;
import javax.annotation.PostConstruct;
import javax.servlet.ServletContext;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;

import com.ecco.infrastructure.cachebust.ResourceVersionSource;
import org.springframework.web.context.ServletContextAware;

@Slf4j
@Getter
public class ApplicationPropertiesImpl implements ApplicationProperties, Serializable, ServletContextAware {

    private static final long serialVersionUID = 1L;

    private String resourceRootPath;

    private String applicationRootPath;

    private Map<String, Boolean> loginProviders;

    private boolean providerCalendarEnabled = false;

    @Getter(AccessLevel.NONE)
    @Value("${azure.activedirectory.client-id:#{null}}")
    private String azureClientId;

    @Getter(AccessLevel.NONE)
    @Value("${azure.calendar.enabled:false}")
    private boolean azureCalendar = false;

    @Value("${ecco.authn.loginProvidersOnly:false}")
    private boolean loginProvidersOnly = false;

    @Value("${ecco.websiteUrl:https://please.set.ecco.websiteUrl}")
    private String eccoWebsiteUrl;

    @Value("${java.version}")
    private String javaVersion;

    @Value("${git.branch}")
    private String gitBranch;

    @Value("${git.commit.id}")
    private String gitCommitId;

    @Value("${git.commit.id.describe}")
    private String gitCommitIdDescribe;

    @Value("${git.commit.time}")
    private String gitCommitTime;

    private String copyrightYear;

    private final ResourceVersionSource resourceVersionSource;

    public ApplicationPropertiesImpl(ResourceVersionSource resourceVersionSource) {
        this.resourceVersionSource = resourceVersionSource;
    }

    @PostConstruct
    public void postConstruct() {
        // We want the year from "01.01.2000 @ 00:00:00 GMT"
        Matcher matcher = Pattern.compile("\\d{2}\\.\\d{2}\\.(\\d{4})").matcher(gitCommitTime);
        matcher.find();
        copyrightYear = matcher.group(1);

        setLoginProviders();

        // also see ApplicationPropertiesImpl
        log.info("================== BUILD =====================");
        log.info("  commit: " + gitCommitId);
        log.info("  time:   " + gitCommitTime);
        log.info("  branch: " + gitBranch);
        log.info("==============================================");

        log.info("================= SERVLET ====================");
        log.info("resourceRootPath = {}", resourceRootPath);
        log.info("applicationRootPath = {}", applicationRootPath);
        log.info("eccoWebsiteUrl = {}", eccoWebsiteUrl);

        log.info("============= AUTH PROVIDERS =================");
        log.info("  Providers: " + loginProviders);
        log.info("  Provider calendar: " + providerCalendarEnabled);
    }

    @Override
    public void setServletContext(@NonNull ServletContext servletContext) {
        setRootPaths(servletContext, resourceVersionSource);
    }

    private void setRootPaths(ServletContext servletContext, ResourceVersionSource resourceVersionSource) {
        // These paths must end with a "/" so that we can perform relative URI
        // path resolution against them, as defined by RFC 3986 section 5.2.3.
        String servletContextPath = servletContext.getContextPath();
        if ("/".equals(servletContextPath)) {
            this.applicationRootPath = servletContextPath;
        } else {
            this.applicationRootPath = servletContextPath + "/";
        }
        this.resourceRootPath = applicationRootPath + "r/" + resourceVersionSource.getVersion() + "/";
    }

    public void setLoginProviders() {
        this.loginProviders = new HashMap<>();
        var azureEnabled = azureClientId != null && azureClientId.length() > 10; // Workaround for ecco-war is client-id=disabled
        this.loginProviders.put("azure", azureEnabled);
        this.providerCalendarEnabled = azureCalendar;
    }

}
