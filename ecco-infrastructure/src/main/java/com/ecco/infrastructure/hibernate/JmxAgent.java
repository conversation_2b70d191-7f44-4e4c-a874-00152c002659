package com.ecco.infrastructure.hibernate;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.management.ObjectName;
import javax.persistence.EntityManagerFactory;

import org.hibernate.SessionFactory;
import org.hibernate.stat.Statistics;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jmx.export.MBeanExporter;
import org.springframework.stereotype.Service;

/**
 * From: https://github.com/lookfirst/fallback/blob/master/src/java/com/github/fallback/util/JmxAgent.java
 *
 * which references http://apaker.blogspot.com/2010/06/enabling-jmx-monitoring-for-spring-jpa.html
 *
 * <AUTHOR>
 */
@Service
public class JmxAgent {

    private final EntityManagerFactory entityManagerFactory;

    private final MBeanExporter mBeanExporter;

    private ObjectName on;

    @Autowired
    public JmxAgent(EntityManagerFactory entityManagerFactory, MBeanExporter mBeanExporter) {
        super();
        this.entityManagerFactory = entityManagerFactory;
        this.mBeanExporter = mBeanExporter;
    }

    @PostConstruct
    public void start() throws Exception {

        SessionFactory sf = entityManagerFactory.unwrap(SessionFactory.class);
        on = new ObjectName("Hibernate:type=statistics,application=ecco");
        final Statistics statistics = sf.getStatistics();
        statistics.setStatisticsEnabled(true);
        mBeanExporter.registerManagedResource(statistics, on);
    }

    @PreDestroy
    public void stop() throws Exception {
        mBeanExporter.unregisterManagedResource(on);
    }
}
