package com.ecco.infrastructure.util;

import org.jspecify.annotations.Nullable;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Objects;


/**
 * Persisting date time's:
 *  - if it has happened use Instant
 *  - if it is going to happen in the future, and its a user's 'intended instant'
 *      then use ZonedDateTime (joda's LocalDateTime)
 *  - if it is going to happen in the future, and there is no local time (eg machine)
 *      then use Instant (represented in liquibase as DATETIME)
 */
public class EccoTimeUtils {

    public static final ZoneId LONDON = ZoneId.of("Europe/London");
    public static final ZoneId UTC = ZoneOffset.UTC;
    //public static final DateTimeZone LONDON_JODA = DateTimeZone.forID("Europe/London");

    // iso8601
    static DateTimeFormatter isoFormatter = DateTimeFormatter.ISO_DATE_TIME;


    public static LocalDateTime getUsersLocalDateTimeNow() {
        return Instant.now().atZone(LONDON).toLocalDateTime(); // TODO: Adam: do we need UTC chrono here?
    }

    // mimic DateTimeUtils of the same name, but with jdk
    // NB untested
    public static ZonedDateTime convertFromUsersLocalDateTime(@Nullable LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return null;
        }
        return localDateTime.atZone(LONDON);
    }

    public static LocalDate getUsersLocalDateNow() {
        return Instant.now().atZone(LONDON).toLocalDate();
    }

    public static ZonedDateTime getUtcNow() {
        return ZonedDateTime.now(Clock.systemUTC());
    }

    public static Instant min(Instant... dates) {
        return Arrays.stream(dates).filter(Objects::nonNull).min(Instant::compareTo).orElse(null);
    }
}
