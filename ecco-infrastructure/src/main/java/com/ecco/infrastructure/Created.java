package com.ecco.infrastructure;

import org.joda.time.DateTime;

import com.ecco.infrastructure.hibernate.CreatedInterceptor;

/**
 * Identify an entity that has a created date that is managed by {@link CreatedInterceptor}, i.e. the date-time when
 * the object was inserted into the database.
 *
 */
public interface Created {

    // TODO As 'created' is a system representation, we should be using Instant

    /** Do not use.  This is just to ensure that the method exists for Hibernate bean access to set the value.
     * Be suspicious of where it is used (and is possibly a workaround.. who knows) */
    void setCreated(DateTime now);

    /** Get the DateTime when this entity was first saved */
    DateTime getCreated();

}
