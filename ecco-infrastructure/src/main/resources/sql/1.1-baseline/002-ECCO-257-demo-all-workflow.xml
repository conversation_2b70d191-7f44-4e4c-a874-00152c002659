<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd">

    <!-- NOTE: Intention is to cover all the settings here -->


    <changeSet id="ECCO-257-servicetypes" author="neale" context="1.1-base-data">
        <insert tableName="servicetypes">
            <column name="id" valueNumeric="99"/>
            <column name="version" valueNumeric="0"/>
            <column name="accommodation" valueBoolean="false"/>
            <column name="anonymousSupport" valueBoolean="false"/>
            <column name="contractRequired" valueBoolean="false"/>
            <column name="logtime" valueBoolean="false"/>
            <column name="multipleReferrals" valueBoolean="false"/>
            <column name="name" value="scnhs-fasttrack"/>
        </insert>
        <insert tableName="services">
            <column name="id" valueNumeric="99"/>
            <column name="version" valueNumeric="0"/>
            <column name="name" value="demo-all-workflow"/>
            <column name="servicetypeid" valueNumeric="99"/>
        </insert>
    </changeSet>
    <changeSet id="ECCO-257-servicetypes-fix-name" author="neale" context="1.1-base-data">
        <update tableName="servicetypes">
            <column name="name" value="demo-all-workflow"/>
            <where>id=99</where>
        </update>
    </changeSet>
    <changeSet id="ECCO-257-enable-workflow" author="neale" context="1.1-base-data">
        <insert tableName="servicetypes_workflow">
            <column name="id" valueNumeric="99"/>
            <column name="version" valueNumeric="0"/>
            <column name="process_key" value="demo-all"/>
        </insert>
    </changeSet>

    <changeSet id="ECCO-257-st-referralaspects-wizard" author="neale" context="1.1-base-data">
        <insert tableName="servicetypes_referralaspects">
            <column name="allowNext" valueBoolean="false"/>
            <column name="orderby" valueNumeric="0"/>
            <column name="version" valueNumeric="0"/>
            <column name="referralaspectId" valueNumeric="45"/> <!-- clientWithContact -->
            <column name="servicetypeId" valueNumeric="99"/>
        </insert>
        <insert tableName="servicetypes_referralaspects">
            <column name="allowNext" valueBoolean="false"/>
            <column name="orderby" valueNumeric="1"/>
            <column name="version" valueNumeric="0"/>
            <column name="referralaspectId" valueNumeric="15"/> <!-- from / sourceWithIndividual -->
            <column name="servicetypeId" valueNumeric="99"/>
        </insert>
        <insert tableName="servicetypes_referralaspects">
            <column name="allowNext" valueBoolean="false"/>
            <column name="orderby" valueNumeric="2"/>
            <column name="version" valueNumeric="0"/>
            <column name="referralaspectId" valueNumeric="18"/>  <!-- referralView -->
            <column name="servicetypeId" valueNumeric="99"/>
        </insert>
    </changeSet>

    <changeSet context="1.1-base-data" author="baseline" id="demo-all-servicetypes_outcomesupports">
        <insert tableName="servicetypes_outcomesupports">
            <column name="servicetypeId" valueNumeric="99"/>
            <column name="outcomeId" valueNumeric="80"/>
        </insert>
        <insert tableName="servicetypes_outcomesupports">
            <column name="servicetypeId" valueNumeric="99"/>
            <column name="outcomeId" valueNumeric="81"/>
        </insert>
        <insert tableName="servicetypes_outcomesupports">
            <column name="servicetypeId" valueNumeric="99"/>
            <column name="outcomeId" valueNumeric="82"/>
        </insert>
        <insert tableName="servicetypes_outcomesupports">
            <column name="servicetypeId" valueNumeric="99"/>
            <column name="outcomeId" valueNumeric="83"/>
        </insert>
        <insert tableName="servicetypes_outcomesupports">
            <column name="servicetypeId" valueNumeric="99"/>
            <column name="outcomeId" valueNumeric="84"/>
        </insert>
    </changeSet>

</databaseChangeLog>
