<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd"
    logicalFilePath="classpath:sql/1.1-changes/013-updateMenuTable.xml">

    <!-- update security, but only where we matched the exact values before (ie ignore modifications in production) -->
    <!-- for already tweaked environments we can cherry pick the fixes from the sql here
        // welcome menu
        // referrals
        update menuitem set roles='ROLE_STAFF,ROLE_COMMISSIONER' where id=1;
        // support plans
        update menuitem set roles='ROLE_STAFF,ROLE_CLIENT,ROLE_COMMISSIONER' where id=2;
        // calendar
        update menuitem set roles='ROLE_STAFF,ROLE_CLIENT' where id=3;
        // clients (contacts icon)
        update menuitem set roles='ROLE_STAFF' where id=4;
        // group support
        update menuitem set roles='ROLE_STAFF' where id=5;
        // projects
        update menuitem set roles='ROLE_STAFF' where id=6;
        // hr
        update menuitem set roles='ROLE_HR,ROLE_HR-VOLUNTEER' where id=7;
        // rota
        update menuitem set roles='ROLE_STAFF' where id=8;
        // reports
        update menuitem set roles='ROLE_ADMIN,ROLE_COMMISSIONER,ROLE_HR' where id=9;
        // settings
        update menuitem set roles='ROLE_ADMIN,ROLE_LOGIN' where id=10;

        // settings menu
        // logins
        update menuitem set roles='ROLE_LOGIN' where id=11;
        // ldap
        update menuitem set roles='ROLE_LOGINADMIN' where id=12;
        // lists
        update menuitem set roles='ROLE_ADMIN' where id=13;
        // referral process
        update menuitem set roles='ROLE_SYSADMIN' where id=15;
        // upload logo
        update menuitem set roles='ROLE_ADMIN' where id=16;
        // dashboard
        update menuitem set roles='ROLE_STAFF' where id=17;
        // referrals
        update menuitem set roles='ROLE_STAFF' where id=18;
        // support plans
        update menuitem set roles='ROLE_STAFF' where id=19;
        // hr
        update menuitem set roles='ROLE_HR' where id=20;
        // group support
        update menuitem set roles='ROLE_STAFF' where id=21;
        // submissions
        update menuitem set roles='ROLE_ADMIN' where id=23;
        // user audits
        update menuitem set roles='ROLE_ADMIN' where id=26;
    -->
    <changeSet author="adamjhamer" id="ECCO-267">

        <!-- welcome menu items -->
        <update tableName="menuitem">
            <column name="roles" value="ROLE_STAFF,ROLE_COMMISSIONER"></column>
            <where>id=1 and roles='ROLE_USER,ROLE_COMMISSIONER'</where>
        </update>
        <update tableName="menuitem">
            <column name="roles" value="ROLE_STAFF,ROLE_CLIENT,ROLE_COMMISSIONER"></column>
            <where>id=2 and roles='ROLE_USER,ROLE_COMMISSIONER,ROLE_CLIENT'</where>
        </update>
        <update tableName="menuitem">
            <column name="roles" value="ROLE_STAFF,ROLE_CLIENT"></column>
            <where>id=3 and roles='ROLE_USER,ROLE_CLIENT'</where>
        </update>
        <update tableName="menuitem">
            <column name="roles" value="ROLE_STAFF"></column>
            <where>id=4 and roles='ROLE_USER'</where>
        </update>
        <update tableName="menuitem">
            <column name="roles" value="ROLE_STAFF"></column>
            <where>id=5 and roles='ROLE_USER'</where>
        </update>
        <update tableName="menuitem">
            <column name="roles" value="ROLE_STAFF"></column>
            <where>id=6 and roles='ROLE_USER'</where>
        </update>
        <update tableName="menuitem">
            <column name="roles" value="ROLE_HR,ROLE_HR-VOLUNTEER"></column>
            <where>id=7 and roles='ROLE_USER,ROLE_HR-VOLUNTEER,ROLE_HR'</where>
        </update>
        <update tableName="menuitem">
            <column name="roles" value="ROLE_STAFF"></column>
            <where>id=8 and roles='ROLE_USER'</where>
        </update>
        <update tableName="menuitem">
            <column name="roles" value="ROLE_ADMIN,ROLE_COMMISSIONER,ROLE_HR"></column>
            <where>id=9 and roles='ROLE_ADMIN,ROLE_COMMISSIONER'</where>
        </update>
        <update tableName="menuitem">
            <column name="roles" value="ROLE_ADMIN,ROLE_LOGIN"></column>
            <where>id=10 and roles='ROLE_SYSADMIN,ROLE_ADMIN'</where>
        </update>

        <!-- settings menu items -->
        <update tableName="menuitem">
            <column name="roles" value="ROLE_LOGIN"></column>
            <where>id=11 and roles='ROLE_USER'</where>
        </update>
        <update tableName="menuitem">
            <column name="roles" value="ROLE_LOGINADMIN"></column>
            <where>id=12 and roles='ROLE_USER'</where>
        </update>
        <update tableName="menuitem">
            <column name="roles" value="ROLE_ADMIN"></column>
            <where>id=13 and roles='ROLE_USER'</where>
        </update>
        <!-- id 14 keep outcomes and change id 15 referral processes closed to sysadmin for now -->
        <update tableName="menuitem">
            <column name="roles" value="ROLE_SYSADMIN"></column>
            <where>id=15 and roles='ROLE_USER'</where>
        </update>
        <update tableName="menuitem">
            <column name="roles" value="ROLE_ADMIN"></column>
            <where>id=16 and roles='ROLE_USER'</where>
        </update>

        <!-- reports menu items -->
        <!-- we set most to staff even though the reports link is admin -->
        <!-- this is because reports could be opened to staff when the data is restricted through acls -->
        <update tableName="menuitem">
            <column name="roles" value="ROLE_STAFF"></column>
            <where>id=17 and roles='ROLE_USER'</where>
        </update>
        <update tableName="menuitem">
            <column name="roles" value="ROLE_STAFF"></column>
            <where>id=18 and roles='ROLE_USER'</where>
        </update>
        <update tableName="menuitem">
            <column name="roles" value="ROLE_STAFF"></column>
            <where>id=19 and roles='ROLE_USER'</where>
        </update>
        <!-- HR access -->
        <update tableName="menuitem">
            <column name="roles" value="ROLE_HR"></column>
            <where>id=20 and roles='ROLE_USER'</where>
        </update>
        <update tableName="menuitem">
            <column name="roles" value="ROLE_STAFF"></column>
            <where>id=21 and roles='ROLE_USER'</where>
        </update>
        <!-- id 22 doesn't work yet, so leave changes -->
        <!-- ADMIN access to submissions -->
        <update tableName="menuitem">
            <column name="roles" value="ROLE_ADMIN"></column>
            <where>id=23 and roles='ROLE_USER'</where>
        </update>
        <!-- id 24 doesn't work yet, so leave changes -->
        <!-- id 25 doesn't work yet, so leave changes -->
        <!-- ADMIN access to audits -->
        <update tableName="menuitem">
            <column name="roles" value="ROLE_ADMIN"></column>
            <where>id=26 and roles='ROLE_USER'</where>
        </update>
    </changeSet>

    <changeSet author="adamjhamer" id="ECCO-267-1">
        <update tableName="menuitem">
            <column name="roles" value="ROLE_STAFF"></column>
            <where>(id=22 or id=24 or id=25 or id=27 or id=28 or id=29 or id=34) and roles='ROLE_USER'</where>
        </update>
    </changeSet>
</databaseChangeLog>
