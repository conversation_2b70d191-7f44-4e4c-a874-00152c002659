<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd"
        logicalFilePath="2022/general-domain">

    <!-- SQL HELP TO RE-CREATE THIS WORK -->
        <!--
        drop table tmp_migrate_ct;
        delete from DATABASECHANGELOG where id like 'DEV-2352-%' and id <> 'DEV-2352-drop-referralcomments-typeid';

        create table tmp_migrate_ct (
            id               bigint,
            version          int,
            name             varchar(255),
            value            varchar(3000),
            referralaspectId bigint,
            servicetypeId    bigint,
            outcomeId        bigint
        );
        -->

    <!-- MIGRATE ENTITIES -->
    <changeSet id="DEV-2352-ensure-entities-sequence" author="adamjhamer">
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="0">SELECT count(*) from hibernate_sequences where sequence_name='entities'</sqlCheck>
        </preConditions>
        <comment>Add entities sequence if it doesn't exist</comment>
        <insert tableName="hibernate_sequences">
            <column name="sequence_name" value="entities"/>
            <!-- set to AbstractIntKeyedEntity initialValue -->
            <column name="next_val" valueNumeric="1000"/>
        </insert>
    </changeSet>

    <!-- REMOVE TYPEID UNUSED -->
    <!-- check typeid not used in referralcomments (used by exit and signpost comments), and projectcomments -->
    <changeSet id="DEV-2352-drop-referralcomments-typeid" author="adamjhamer">
        <preConditions>
            <and>
                <sqlCheck expectedResult="0">select count(1) from referralcomments where typeid is not null</sqlCheck>
                <sqlCheck expectedResult="0">select count(1) from projectcomments where typeid is not null</sqlCheck>
            </and>
        </preConditions>
        <dropForeignKeyConstraint baseTableName="referralcomments" constraintName="FKB2536C9142BDAB2B"/>
        <dropForeignKeyConstraint baseTableName="projectcomments" constraintName="FKC0DC000D42BDAB2B"/>
        <dropColumn tableName="referralcomments" columnName="typeid"/>
        <dropColumn tableName="projectcomments" columnName="typeid"/>
    </changeSet>

    <!-- PREPARE TMP TABLE TO GO INTO st_referralaspectsettings -->
    <!-- create table mimicking st_referralaspectsettings -->
    <changeSet id="DEV-2352-prepare-commenttypes-create" author="adamjhamer">
        <createTable tableName="tmp_migrate_ct">
            <column name="migrationId" type="INT"/>
            <column name="id" type="BIGINT"/> <!-- for our own id -->
            <column name="id2" type="BIGINT"/> <!-- for going into st_referralaspectsettings -->
            <column name="version" type="INT"/>
            <column name="name" type="VARCHAR(255)"/>
            <column name="value" type="VARCHAR(3000)"/>
            <column name="referralaspectId" type="INT"/>
            <column name="servicetypeId" type="BIGINT"/>
            <column name="outcomeId" type="BIGINT"/>
        </createTable>
    </changeSet>
    <!-- insert fields, except for id and value -->
    <!-- insert one list per servicetype (as is the case currently with commenttypes) by using the referralView -->
    <changeSet id="DEV-2352-populate-commenttypes-referralView" author="adamjhamer">
        <preConditions onFail="MARK_RAN">
            <not>
                <changeSetExecuted id="DEV-2352-populate-commenttypes-insert" author="adamjhamer" changeLogFile="2022/general-domain"/>
            </not>
        </preConditions>
        <sql>
            INSERT INTO tmp_migrate_ct (version, name, referralaspectId, servicetypeId, outcomeId)
            SELECT 0, 'commentTypesById', 18, ct.servicetypeId, null
            FROM (select distinct(servicetypeid) from commenttypes) ct
        </sql>
        <!-- order by stra.servicetypeId, ra.id -->
    </changeSet>
    <!-- update value -->
    <changeSet id="DEV-2352-populate-commenttypes-update-value" author="adamjhamer">
        <validCheckSum>8:4a3d3e4cb79d6a90812147257eb63daa</validCheckSum>
        <!-- IGNORE h2 as syntax not same, but also can assume non-persisted -->
        <!-- this does mean the commenttypes in liquibase (4 of them) are largely useless -->
        <sql dbms="mysql">
            update tmp_migrate_ct tmp,
                (select ct.servicetypeId, GROUP_CONCAT(ct.id) as value_calc from commenttypes ct group by ct.servicetypeId) as valueLookup
            set tmp.value = valueLookup.value_calc
            where valueLookup.servicetypeId=tmp.servicetypeId;
        </sql>
        <sql dbms="oracle">
            update tmp_migrate_ct tmp,
                (select ct.servicetypeId, LISTAGG(ct.id, ',') WITHIN GROUP(ORDER BY ct.id) as value_calc from commenttypes ct group by ct.servicetypeId) as valueLookup
            set tmp.value = valueLookup.value_calc
            where valueLookup.servicetypeId=tmp.servicetypeId;
        </sql>
        <!--
        - substitute the STRING_AGG
        select ct.servicetypeId, (select concat(',', ct2.id) from commenttypes ct2 where ct2.servicetypeId=ct.servicetypeId for xml path('')) from commenttypes ct group by ct.servicetypeId;
        - stuff allows us to remove first ','
        select ct.servicetypeId, stuff((select concat(',', ct2.id) from commenttypes ct2 where ct2.servicetypeId=ct.servicetypeId for xml path('')), 1, 1, '') from commenttypes ct group by ct.servicetypeId;
        -->
        <sql dbms="mssql">
            update tmp
            set tmp.value = valueLookup.value_calc
            from tmp_migrate_ct tmp
            inner join (select ct.servicetypeId, stuff((select concat(',', ct2.id) from commenttypes ct2 where ct2.servicetypeId=ct.servicetypeId for xml path('')), 1, 1, '') as value_calc from commenttypes ct group by ct.servicetypeId) as valueLookup
            on tmp.servicetypeId=valueLookup.serviceTypeId
        </sql>
        <!-- 2017 syntax -->
        <!--<sql dbms="mssql">
            update tmp_migrate_ct tmp,
                (select ct.servicetypeId, STRING_AGG(ct.id, ',') as value_calc from commenttypes ct group by ct.servicetypeId) as valueLookup
            set tmp.value = valueLookup.value_calc
            where valueLookup.servicetypeId=tmp.servicetypeId;
        </sql>-->
    </changeSet>

    <!-- insert the listName into the referralView - because we're going to create a listname per servicetype -->
    <!-- this allows us to transition to how commenttypes currently behave, and avoid unique clashes -->
    <changeSet id="DEV-2352-populate-commenttypes-listName" author="adamjhamer">
        <preConditions onFail="MARK_RAN">
            <not>
                <changeSetExecuted id="DEV-2352-populate-commenttypes-insert" author="adamjhamer" changeLogFile="2022/general-domain"/>
            </not>
        </preConditions>
        <sql>
            INSERT INTO tmp_migrate_ct (version, name, value, referralaspectId, servicetypeId, outcomeId)
            SELECT 0, 'commentTypeListName', concat('migrate:commenttypes-', ct.servicetypeId), 18, ct.servicetypeId, null
            FROM (select distinct(servicetypeid) from commenttypes) ct
            ;
        </sql>
        <!-- order by stra.servicetypeId, ra.id -->
    </changeSet>

    <!-- GET 'id' TO GO INTO st_referralaspectsettings (from sequence 'entities') -->
    <!-- add primary key - we need one for PopulateHibernateSequenceChange -->
    <!-- Must declare the scalar variable "@a". [Failed SQL: (137) SET @a  = 0] -->
    <changeSet id="DEV-2352-populate-commenttypes-id" author="adamjhamer">
        <validCheckSum>8:0160d69fecb8f05d98adb4f0b2dee8a4</validCheckSum>
        <!-- mssql: Caused by: liquibase.exception.DatabaseException: Must declare the scalar variable "@a". [Failed SQL: (137) SET @a  = 0]-->
        <sql dbms="!oracle and !mssql">
            SET @a  = 0;
            UPDATE tmp_migrate_ct SET id = @a:=@a+1;
        </sql>
        <sql dbms="mssql">
            begin transaction;
            declare @a bigint = 0;
            UPDATE tmp_migrate_ct SET @a = id = @a + 1
            commit;
        </sql>
        <sql dbms="oracle">
            update tmp_migrate_ct set id=RowNum;
        </sql>
    </changeSet>
    <!-- add primary key for populate below -->
    <changeSet id="DEV-2352-populate-commenttypes-pk" author="adamjhamer">
        <validCheckSum>8:7d3c7431e26a9883ce81a93e948f0e31</validCheckSum>
        <addNotNullConstraint tableName="tmp_migrate_ct" columnName="id" columnDataType="BIGINT"/> <!-- h2 requires not null, mysql requires columnDataType -->
        <addPrimaryKey tableName="tmp_migrate_ct" columnNames="id"/>
    </changeSet>
    <!-- update migrationId -->
    <changeSet id="DEV-2352-populate-commenttypes-update-id" author="adamjhamer">
        <customChange class="com.ecco.infrastructure.liquibase.PopulateHibernateSequenceChange"
                      tableName="tmp_migrate_ct" uniqueKeyColumns="id"
                      newIdColumn="id2"
                      sequenceName="entities"
        />
    </changeSet>
    <!-- move to st_referralaspectsettings -->
    <changeSet id="DEV-2352-populate-settings-insert" author="adamjhamer">
        <validCheckSum>8:76797dec4105e676ecc4170f76bb16f2</validCheckSum>
        <sql dbms="!mssql">
            INSERT INTO st_referralaspectsettings (id, version, name, value, referralaspectId, servicetypeId, outcomeId)
            SELECT id2, version, name, `value`, referralaspectId, servicetypeId, outcomeId
            FROM tmp_migrate_ct;
        </sql>
        <sql dbms="mssql">
            INSERT INTO st_referralaspectsettings (id, version, name, value, referralaspectId, servicetypeId, outcomeId)
            SELECT id2, version, name, value, referralaspectId, servicetypeId, outcomeId
            FROM tmp_migrate_ct;
        </sql>
    </changeSet>
    <!-- when we did one global list def (DEV-2352) we ended up with a commentTypesById on every referralaspectId, including referralView (18) -->
    <!-- since we're now migrating to the referralView only (18), then we can remove the rest -->
    <!-- which also means its still there for the uat/demo systems which have already migrated -->
    <changeSet id="DEV-2352-populate-commenttypes-remove-previous" author="adamjhamer">
        <delete tableName="st_referralaspectsettings">
            <where>name='commentTypesById' and referralaspectId != 18</where>
        </delete>
    </changeSet>


</databaseChangeLog>
