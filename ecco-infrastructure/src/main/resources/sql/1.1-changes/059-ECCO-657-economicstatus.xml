<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd">

    <changeSet id="ECCO-657-economicstatus" author="adamjhamer">
        <insert tableName="referralaspects">
            <column name="id" valueNumeric="99"/>
            <column name="version" valueNumeric="0"/>
            <column name="display" valueBoolean="true"/>
            <column name="displayOverview" valueBoolean="true"/>
            <column name="external" valueBoolean="false"/>
            <column name="friendlyName" value="economicStatus"/>
            <column name="internal" valueBoolean="true"/>
            <column name="name" value="economicStatus"/>
        </insert>
    </changeSet>
    <changeSet id="ECCO-657-economicstatus-friendlyname" author="adamjhamer">
        <update tableName="referralaspects">
            <column name="friendlyName" value="economic status"/>
            <where>id=99</where>
        </update>
    </changeSet>

    <changeSet id="ECCO-657-currentDebt" author="adamjhamer">
        <addColumn tableName="contacts">
            <column name="currentDebt" type="DECIMAL(9,2)"/>
        </addColumn>
    </changeSet>

</databaseChangeLog>
