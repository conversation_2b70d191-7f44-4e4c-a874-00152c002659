<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd">

    <!-- remove the passwordhistory_AUD because passwords are referenced on the user table which is already audited -->
    <!-- so what we are auditing here is direct access to passwordhistory table which is never likely to happen -->
    <changeSet id="ECCO-565-remove-some-passwordhistoryaud" author="adamjhamer">
        <dropTable tableName="passwordhistory_AUD"/>
    </changeSet>

</databaseChangeLog>
