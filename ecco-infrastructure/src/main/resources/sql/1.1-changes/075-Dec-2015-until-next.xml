<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">

  <property name="now" value="CURRENT_TIMESTAMP()" dbms="h2"/>
  <property name="now" value="CURRENT_TIMESTAMP()" dbms="mssql"/>
  <property name="now" value="now()" dbms="mysql"/>
  <property name="now" value="sysdate" dbms="oracle"/>


    <changeSet id="ECCO-1741-add-review-svcRecId-nullable-cols" author="nealeu">
        <preConditions onFail="MARK_RAN">
            <not>
                <changeSetExecuted id="ECCO-1741-add-review-svcRecId" author="nealeu"
                    changeLogFile="classpath:sql/1.1-changes/075-Dec-2015-until-next.xml"/>
            </not>
        </preConditions>
        <addColumn tableName="reviews">
            <column name="serviceRecipientId" type="INT"/>
        </addColumn>
    </changeSet>
    <changeSet id="ECCO-1741-populate-svcRecId" author="nealeu">
        <update tableName="reviews">
            <column name="serviceRecipientId"
                valueComputed="(SELECT serviceRecipientId FROM referrals WHERE referrals.id = reviews.referralId)"/>
        </update>
        <addForeignKeyConstraint constraintName="fk_reviews_svcrec"
            baseTableName="reviews" baseColumnNames="serviceRecipientId"
            referencedTableName="servicerecipients" referencedColumnNames="id"
            deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
            referencesUniqueColumn="true"/>
    </changeSet>

    <changeSet id="ECCO-1741-add-notNull-constraint" author="nealeu">
        <preConditions onFail="MARK_RAN">
            <not>
                <changeSetExecuted id="ECCO-1741-add-review-svcRecId" author="nealeu"
                    changeLogFile="classpath:sql/1.1-changes/075-Dec-2015-until-next.xml"/>
            </not>
        </preConditions>
        <addNotNullConstraint tableName="reviews" columnName="serviceRecipientId" columnDataType="INT"/>
    </changeSet>

    <changeSet id="ECCO-1741-serviceRecipientId-idx" author="nealeu">
        <createIndex tableName="reviews" indexName="idx_reviews_srid">
            <column name="serviceRecipientId"/>
        </createIndex>
    </changeSet>

    <changeSet id="ECCO-1741-remove-referralId-col" author="nealeu">
        <dropForeignKeyConstraint baseTableName="reviews" constraintName="FK418FF41B2DAEC673"/>
        <dropColumn tableName="reviews" columnName="referralId"/>
    </changeSet>


    <changeSet id="ECCO-1751-add-svc-rec-created" author="nealeu">
        <addColumn tableName="servicerecipients">
            <column name="created" type="DATETIME"/>
        </addColumn>
    </changeSet>

    <changeSet id="ECCO-1751-populate-rsr-created" author="nealeu">
        <update tableName="servicerecipients">
            <column name="created"
                valueComputed="(SELECT created FROM referrals WHERE referrals.serviceRecipientId = servicerecipients.id)"/>
            <where>created IS NULL AND EXISTS (SELECT * FROM referrals WHERE referrals.serviceRecipientId = servicerecipients.id)</where>
        </update>
    </changeSet>
    <changeSet id="ECCO-1751-populate-wsr-created" author="nealeu">
        <update tableName="servicerecipients">
            <column name="created"
                valueComputed="(SELECT created FROM workers WHERE workers.serviceRecipientId = servicerecipients.id)"/>
            <where>created IS NULL AND EXISTS (SELECT * FROM workers WHERE workers.serviceRecipientId = servicerecipients.id)</where>
        </update>
    </changeSet>
    <changeSet id="ECCO-1751-populate-bsr-created" author="nealeu">
        <update tableName="servicerecipients">
            <column name="created" valueComputed="${now}"/>
            <where>created IS NULL AND EXISTS (SELECT * FROM bldg_fixed WHERE bldg_fixed.serviceRecipientId = servicerecipients.id)</where>
        </update>
    </changeSet>

    <changeSet id="ECCO-1751-add-notNull-constraint" author="nealeu">
        <addNotNullConstraint tableName="servicerecipients" columnName="created" columnDataType="DATETIME"/>
    </changeSet>

    <changeSet id="ECCO-1751-created-idx" author="nealeu">
        <createIndex tableName="servicerecipients" indexName="idx_svc_rec_created">
            <column name="created"/>
        </createIndex>
    </changeSet>

    <changeSet id="ECCO-1742-add-referralattachment-svcRecId-nullable-cols" author="nealeu">
        <preConditions onFail="MARK_RAN">
            <not>
                <changeSetExecuted id="ECCO-1742-add-referralattachment-svcRecId" author="nealeu"
                    changeLogFile="classpath:sql/1.1-changes/075-Dec-2015-until-next.xml"/>
            </not>
        </preConditions>
        <addColumn tableName="referralattachments">
            <column name="serviceRecipientId" type="INT"/>
        </addColumn>
    </changeSet>
    <changeSet id="ECCO-1742-populate-svcRecId" author="nealeu">
        <update tableName="referralattachments">
            <column name="serviceRecipientId"
                valueComputed="(SELECT serviceRecipientId FROM referrals WHERE referrals.id = referralattachments.referralId)"/>
        </update>
        <addForeignKeyConstraint constraintName="fk_referralattachments_svcrec"
            baseTableName="referralattachments" baseColumnNames="serviceRecipientId"
            referencedTableName="servicerecipients" referencedColumnNames="id"
            deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
            referencesUniqueColumn="true"/>
    </changeSet>

    <changeSet id="ECCO-1742-add-notNull-constraint" author="nealeu">
        <preConditions onFail="MARK_RAN">
            <not>
                <changeSetExecuted id="ECCO-1742-add-referralattachment-svcRecId" author="nealeu"
                    changeLogFile="classpath:sql/1.1-changes/075-Dec-2015-until-next.xml"/>
            </not>
        </preConditions>
        <addNotNullConstraint tableName="referralattachments" columnName="serviceRecipientId" columnDataType="INT"/>
    </changeSet>
    <changeSet id="ECCO-1742-serviceRecipientId-idx" author="nealeu">
        <createIndex tableName="referralattachments" indexName="idx_referralattachments_srid">
            <column name="serviceRecipientId"/>
        </createIndex>
    </changeSet>

    <changeSet id="ECCO-1742-remove-referralId-col" author="nealeu">
        <dropForeignKeyConstraint baseTableName="referralattachments" constraintName="FK50ACDBF32DAEC673"/>
        <dropColumn tableName="referralattachments" columnName="referralId"/>
    </changeSet>


    <changeSet id="ECCO-1747-add-events-svcRecId" author="nealeu">
        <addColumn tableName="events">
            <column name="serviceRecipientId" type="INT">
                <constraints nullable="true"/> <!-- events can have diff associations -->
            </column>
        </addColumn>
    </changeSet>
    <changeSet id="ECCO-1747-serviceRecipientId-idx" author="nealeu">
        <createIndex tableName="events" indexName="idx_events_srid">
            <column name="serviceRecipientId"/>
        </createIndex>
        <addForeignKeyConstraint constraintName="fk_events_svcrec"
            baseTableName="events" baseColumnNames="serviceRecipientId"
            referencedTableName="servicerecipients" referencedColumnNames="id"
            deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
            referencesUniqueColumn="true"/>
    </changeSet>
    <changeSet id="ECCO-1747-populate-svcRecId" author="nealeu">
        <update tableName="events">
            <column name="serviceRecipientId"
                valueComputed="(SELECT serviceRecipientId FROM referrals WHERE referrals.id = events.referralId)"/>
                <where>referralId IS NOT NULL</where>
        </update>
    </changeSet>
    <changeSet id="ECCO-1747-populate-svcRecIdForWorkers" author="nealeu">
        <update tableName="events">
            <column name="serviceRecipientId"
                valueComputed="(SELECT serviceRecipientId FROM workers WHERE workers.id = events.workerId)"/>
                <where>workerId IS NOT NULL</where>
        </update>
    </changeSet>

<!--     <changeSet id="ECCO-1747-remove-referralId-col" author="nealeu"> -->
<!--         <dropForeignKeyConstraint baseTableName="events" constraintName="xx"/> -->
<!--         <dropColumn tableName="events" columnName="referralId"/> -->
<!--     </changeSet> -->


    <changeSet id="ECCO-1744-add-referralactivities-svcRecId-nullable-col" author="nealeu">
        <preConditions onFail="MARK_RAN">
            <not>
                <changeSetExecuted id="ECCO-1744-add-referralactivities-svcRecId" author="nealeu"
                    changeLogFile="classpath:sql/1.1-changes/075-Dec-2015-until-next.xml"/>
            </not>
        </preConditions>
        <addColumn tableName="referralactivities">
            <column name="serviceRecipientId" type="INT"/>
        </addColumn>
    </changeSet>
    <changeSet id="ECCO-1744-populate-svcRecId" author="nealeu">
        <update tableName="referralactivities">
            <column name="serviceRecipientId"
                valueComputed="(SELECT serviceRecipientId FROM referrals WHERE referrals.id = referralactivities.referralId)"/>
        </update>
        <addForeignKeyConstraint constraintName="fk_referralactivities_svcrec"
            baseTableName="referralactivities" baseColumnNames="serviceRecipientId"
            referencedTableName="servicerecipients" referencedColumnNames="id"
            deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
            referencesUniqueColumn="true"/>
    </changeSet>

    <changeSet id="ECCO-1744-add-notNull-constraint" author="nealeu">
        <preConditions onFail="MARK_RAN">
            <not>
                <changeSetExecuted id="ECCO-1744-add-referralactivities-svcRecId" author="nealeu"
                    changeLogFile="classpath:sql/1.1-changes/075-Dec-2015-until-next.xml"/>
            </not>
        </preConditions>
        <addNotNullConstraint tableName="referralactivities" columnName="serviceRecipientId" columnDataType="INT"/>
    </changeSet>

    <changeSet id="ECCO-1744-serviceRecipientId-idx" author="nealeu">
        <createIndex tableName="referralactivities" indexName="idx_referralactivities_srid">
            <column name="serviceRecipientId"/>
        </createIndex>
    </changeSet>

    <changeSet id="ECCO-1744-remove-referralId-col" author="nealeu">
        <dropForeignKeyConstraint baseTableName="referralactivities" constraintName="FKAFAD974A2DAEC673"/>
        <dropColumn tableName="referralactivities" columnName="referralId"/>
    </changeSet>

    <changeSet id="ECCO-1787-clientStatus-meetingStatus" author="adamjhamer">
        <addColumn tableName="supportplancomments">
            <column name="clientStatusId" type="INT">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <addColumn tableName="supportplancomments">
            <column name="meetingStatusId" type="INT">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="ECCO-1787-clientStatus-meetingStatus-FK" author="adamjhamer">
        <addForeignKeyConstraint constraintName="fk_supportCS_lists" baseTableName="supportplancomments"
            baseColumnNames="clientStatusId" referencedTableName="cfg_list_definitions" referencedColumnNames="id" />
        <addForeignKeyConstraint constraintName="fk_supportMS_lists" baseTableName="supportplancomments"
            baseColumnNames="meetingStatusId" referencedTableName="cfg_list_definitions" referencedColumnNames="id" />
    </changeSet>

    <!-- this is basically part of a temporal database in disguise -->
    <changeSet id="ECCO-1792-singleValueHistory" author="adamjhamer">
        <createTable tableName="singlevaluehistory">
            <column defaultValueNumeric="0" name="id" type="INT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="serviceRecipientId" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="keyname" type="VARCHAR(50)"> <!-- typically Java entity name '.' fieldname -->
                <constraints nullable="false"/>
            </column>
            <column name="keyvalue" type="BIGINT"/> <!-- our current requirements are number, not string -->
            <column name="validTo" type="DATETIME">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="ECCO-1792-singleValueHistory-idx" author="adamjhamer">
        <comment>Index for srId/key pair</comment>
        <createIndex unique="false" tableName="singlevaluehistory" indexName="idx_singlevalue_srId_keyname">
            <column name="serviceRecipientId"/>
            <column name="keyname"/>
        </createIndex>
    </changeSet>

    <changeSet id="ECCO-1794-singleValueHistoryCommands" author="adamjhamer">
        <addColumn tableName="svcrec_commands">
            <column name="singleValueKey" type="VARCHAR(64)">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="ECCO-1792-singleValueHistory-validFrom" author="adamjhamer">
        <validCheckSum>7:e6077add26f84217dffb60a6e32c602e</validCheckSum>
        <renameColumn tableName="singlevaluehistory" oldColumnName="validTo" newColumnName="validFrom" columnDataType="DATETIME"/>
    </changeSet>
    <!-- we need to re-apply the notNull constraint (doesn't matter if already
         done in previous commit above) except for oracle who complains its already there
    -->
    <changeSet id="ECCO-1792-singleValueHistory-validFrom-notNullAgain" author="adamjhamer">
        <preConditions onFail="MARK_RAN">
            <not>
                <dbms type="oracle"/>
            </not>
        </preConditions>
        <addNotNullConstraint tableName="singlevaluehistory" columnName="validFrom" columnDataType="DATETIME"/>
    </changeSet>

    <changeSet id="ECCO-1962-singleValueHistory-validTo" author="adamjhamer">
        <addColumn tableName="singlevaluehistory">
            <column name="validTo" type="DATETIME">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <!-- we ONLY need this for one client who is on mysql -->
    <changeSet id="ECCO-1962-singleValueHistory-validTo-populate" author="adamjhamer">
        <preConditions onFail="MARK_RAN">
            <dbms type="mysql"/>
        </preConditions>
        <sql>
      update singlevaluehistory svh1
      set svh1.validTo = (
          -- select svh1.*,svh2.id,min(svh2.validFrom) as validTo
          -- from singlevaluehistory svh1
          -- left join singlevaluehistory svh2
          select min(svh2.validFrom)
          from (select * from singlevaluehistory) svh2
          where svh1.serviceRecipientId = svh2.serviceRecipientId
          and svh1.keyname=svh2.keyname
          and svh2.validFrom > svh1.validFrom
          group by svh1.serviceRecipientId, svh1.keyname, svh1.validFrom
      );
        </sql>
    </changeSet>

    <changeSet id="ECCO-1962-singleValueHistory-unique" author="adamjhamer">
        <comment>Index for srId/key pair</comment>
        <createIndex unique="true" tableName="singlevaluehistory" indexName="idx_singlevalue_srId_key_vF">
            <column name="serviceRecipientId"/>
            <column name="keyname"/>
            <column name="validFrom"/>
        </createIndex>
    </changeSet>

    <changeSet id="ECCO-1716-add-supportthreatactions-svcRecId" author="nealeu">
        <addColumn tableName="supportthreatactions">
            <column name="serviceRecipientId" type="INT"/>
        </addColumn>
    </changeSet>
    <changeSet id="ECCO-1716-populate-svcRecId" author="nealeu">
        <update tableName="supportthreatactions">
            <column name="serviceRecipientId"
                valueComputed="(SELECT serviceRecipientId FROM referrals WHERE referrals.id = supportthreatactions.referralId)"/>
        </update>
        <addForeignKeyConstraint constraintName="fk_ev_threatactions_svcrec"
            baseTableName="supportthreatactions" baseColumnNames="serviceRecipientId"
            referencedTableName="servicerecipients" referencedColumnNames="id"
            deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
            referencesUniqueColumn="true"/>
    </changeSet>

    <changeSet id="ECCO-1716-add-notNull-constraint" author="nealeu">
        <addNotNullConstraint tableName="supportthreatactions" columnName="serviceRecipientId" columnDataType="INT"/>
    </changeSet>
    <changeSet id="ECCO-1716-serviceRecipientId-idx" author="nealeu">
        <createIndex tableName="supportthreatactions" indexName="idx_ev_threatactions_srid">
            <column name="serviceRecipientId"/>
        </createIndex>
    </changeSet>


    <changeSet id="ECCO-1716-remove-referralId-col" author="nealeu">
        <dropForeignKeyConstraint baseTableName="supportthreatactions" constraintName="FK5E3BF2342DAEC673"/>
        <dropColumn tableName="supportthreatactions" columnName="referralId"/>
    </changeSet>

    <changeSet id="ECCO-1717-add-supporthractions-svcRecId" author="nealeu">
        <addColumn tableName="supporthractions">
            <column name="serviceRecipientId" type="INT"/>
        </addColumn>
    </changeSet>
    <changeSet id="ECCO-1717-populate-svcRecId" author="nealeu">
        <update tableName="supporthractions">
            <column name="serviceRecipientId"
                valueComputed="(SELECT serviceRecipientId FROM workers WHERE workers.id = supporthractions.workerId)"/>
        </update>
        <addForeignKeyConstraint constraintName="fk_ev_hractions_svcrec"
            baseTableName="supporthractions" baseColumnNames="serviceRecipientId"
            referencedTableName="servicerecipients" referencedColumnNames="id"
            deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
            referencesUniqueColumn="true"/>
    </changeSet>

    <changeSet id="ECCO-1717-add-notNull-constraint" author="nealeu">
        <addNotNullConstraint tableName="supporthractions" columnName="serviceRecipientId" columnDataType="INT"/>
    </changeSet>
    <changeSet id="ECCO-1717-serviceRecipientId-idx" author="nealeu">
        <createIndex tableName="supporthractions" indexName="idx_ev_hractions_srid">
            <column name="serviceRecipientId"/>
        </createIndex>
    </changeSet>

    <changeSet id="ECCO-1717-remove-referralId-col" author="nealeu">
        <dropForeignKeyConstraint baseTableName="supporthractions" constraintName="FK1E2235C4FF50C098"/>
        <dropColumn tableName="supporthractions" columnName="workerId"/>
    </changeSet>

    <changeSet id="ECCO-1748-add-supporthrcomments-svcRecId" author="nealeu">
        <addColumn tableName="supporthrcomments">
            <column name="serviceRecipientId" type="INT"/>
        </addColumn>
    </changeSet>
    <changeSet id="ECCO-1748-populate-svcRecId" author="nealeu">
        <update tableName="supporthrcomments">
            <column name="serviceRecipientId"
                valueComputed="(SELECT serviceRecipientId FROM workers WHERE workers.id = supporthrcomments.workerId)"/>
        </update>
        <addForeignKeyConstraint constraintName="fk_ev_hrcomments_svcrec"
            baseTableName="supporthrcomments" baseColumnNames="serviceRecipientId"
            referencedTableName="servicerecipients" referencedColumnNames="id"
            deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
            referencesUniqueColumn="true"/>
    </changeSet>

    <changeSet id="ECCO-1748-add-notNull-constraint" author="nealeu">
        <addNotNullConstraint tableName="supporthrcomments" columnName="serviceRecipientId" columnDataType="INT"/>
    </changeSet>

    <changeSet id="ECCO-1748-serviceRecipientId-idx" author="nealeu">
        <createIndex tableName="supporthrcomments" indexName="idx_ev_hrcomments_srid">
            <column name="serviceRecipientId"/>
        </createIndex>
    </changeSet>

    <changeSet id="ECCO-1748-remove-referralId-col" author="nealeu">
        <dropForeignKeyConstraint baseTableName="supporthrcomments" constraintName="FKE4F38A0DFF50C098"/>
        <dropColumn tableName="supporthrcomments" columnName="workerId"/>
    </changeSet>


    <changeSet id="ECCO-1748-add-supporthroutcomes-svcRecId" author="nealeu">
        <addColumn tableName="supporthroutcomes">
            <column name="serviceRecipientId" type="INT"/>
        </addColumn>
    </changeSet>
    <changeSet id="ECCO-1748-pop-outcomes-svcRecId" author="nealeu">
        <update tableName="supporthroutcomes">
            <column name="serviceRecipientId"
                valueComputed="(SELECT serviceRecipientId FROM workers WHERE workers.id = supporthroutcomes.workerId)"/>
        </update>
        <addForeignKeyConstraint constraintName="fk_ev_hroutcomes_svcrec"
            baseTableName="supporthroutcomes" baseColumnNames="serviceRecipientId"
            referencedTableName="servicerecipients" referencedColumnNames="id"
            deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
            referencesUniqueColumn="true"/>
    </changeSet>

    <changeSet id="ECCO-1748-add-outcomes-notNull" author="nealeu">
        <addNotNullConstraint tableName="supporthroutcomes" columnName="serviceRecipientId" columnDataType="INT"/>
    </changeSet>

    <changeSet id="ECCO-1748-outcomes-srId-idx" author="nealeu">
        <createIndex tableName="supporthroutcomes" indexName="idx_ev_hroutcomes_srid">
            <column name="serviceRecipientId"/>
        </createIndex>
    </changeSet>

    <changeSet id="ECCO-1748-remove-outcomes-Id-col" author="nealeu">
        <dropForeignKeyConstraint baseTableName="supporthroutcomes" constraintName="FKC508F7AFF50C098"/>
        <dropColumn tableName="supporthroutcomes" columnName="workerId"/>
    </changeSet>



    <changeSet id="ECCO-1748-add-supporthrwork-childId" author="nealeu">
        <addColumn tableName="supporthrwork">
            <column name="serviceRecipientId" type="INT"/>
        </addColumn>
    </changeSet>
    <changeSet id="ECCO-1748-pop-work-svcRecId" author="nealeu">
        <update tableName="supporthrwork">
            <column name="serviceRecipientId"
                valueComputed="(SELECT serviceRecipientId FROM workers WHERE workers.id = supporthrwork.workerId)"/>
        </update>
        <addForeignKeyConstraint constraintName="fk_ev_hrwork_svcrec"
            baseTableName="supporthrwork" baseColumnNames="serviceRecipientId"
            referencedTableName="servicerecipients" referencedColumnNames="id"
            deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
            referencesUniqueColumn="true"/>
    </changeSet>

    <changeSet id="ECCO-1748-add-work-notNull" author="nealeu">
        <addNotNullConstraint tableName="supporthrwork" columnName="serviceRecipientId" columnDataType="INT"/>
    </changeSet>
    <changeSet id="ECCO-1748-work-srId-idx" author="nealeu">
        <createIndex tableName="supporthrwork" indexName="idx_ev_hrwork_srid">
            <column name="serviceRecipientId"/>
        </createIndex>
    </changeSet>

    <changeSet id="ECCO-1748-remove-work-Id-col" author="nealeu">
        <dropForeignKeyConstraint baseTableName="supporthrwork" constraintName="FKE97767AAFF50C098"/>
        <dropColumn tableName="supporthrwork" columnName="workerId"/>
    </changeSet>



    <changeSet id="ECCO-1749-add-supportplananswers-svcRecId" author="nealeu">
        <addColumn tableName="supportplananswers">
            <column name="serviceRecipientId" type="INT"/>
        </addColumn>
    </changeSet>
    <changeSet id="ECCO-1749-pop-answers-svcRecId" author="nealeu">
        <update tableName="supportplananswers">
            <column name="serviceRecipientId"
                valueComputed="(SELECT serviceRecipientId FROM referrals WHERE referrals.id = supportplananswers.referralId)"/>
        </update>
        <addForeignKeyConstraint constraintName="fk_ev_needsanswers_svcrec"
            baseTableName="supportplananswers" baseColumnNames="serviceRecipientId"
            referencedTableName="servicerecipients" referencedColumnNames="id"
            deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
            referencesUniqueColumn="true"/>
    </changeSet>

    <changeSet id="ECCO-1749-add-work-notNull" author="nealeu">
        <addNotNullConstraint tableName="supportplananswers" columnName="serviceRecipientId" columnDataType="INT"/>
    </changeSet>
    <changeSet id="ECCO-1749-answers-srId-idx" author="nealeu">
        <createIndex tableName="supportplananswers" indexName="idx_ev_needsanswers_srid">
            <column name="serviceRecipientId"/>
        </createIndex>
    </changeSet>

    <changeSet id="ECCO-1749-remove-answers-Id-col" author="nealeu">
        <dropForeignKeyConstraint baseTableName="supportplananswers" constraintName="FK9E33D5FD2DAEC673"/>
        <dropColumn tableName="supportplananswers" columnName="referralId"/>
    </changeSet>


    <changeSet id="ECCO-1749-add-supportplancomments-svcRecId" author="nealeu">
        <addColumn tableName="supportplancomments">
            <column name="serviceRecipientId" type="INT">
                <constraints nullable="true"/>  <!-- Some referralId are null -->
            </column>
        </addColumn>
    </changeSet>
    <changeSet id="ECCO-1749-comments-srId-idx" author="nealeu">
        <createIndex tableName="supportplancomments" indexName="idx_ev_needscomments_srid">
            <column name="serviceRecipientId"/>
        </createIndex>
    </changeSet>
    <changeSet id="ECCO-1749-pop-comments-svcRecId" author="nealeu">
        <update tableName="supportplancomments">
            <column name="serviceRecipientId"
                valueComputed="(SELECT serviceRecipientId FROM referrals WHERE referrals.id = supportplancomments.referralId)"/>
        </update>
        <addForeignKeyConstraint constraintName="fk_ev_needscomments_svcrec"
            baseTableName="supportplancomments" baseColumnNames="serviceRecipientId"
            referencedTableName="servicerecipients" referencedColumnNames="id"
            deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
            referencesUniqueColumn="true"/>
    </changeSet>

    <changeSet id="ECCO-1749-remove-comments-Id-col" author="nealeu">
        <dropForeignKeyConstraint baseTableName="supportplancomments" constraintName="FK2225716C2DAEC673"/>
        <dropColumn tableName="supportplancomments" columnName="referralId"/>
    </changeSet>



    <changeSet id="ECCO-1749-add-supportplanoutcomes-svcRecId" author="nealeu">
        <addColumn tableName="supportplanoutcomes">
            <column name="serviceRecipientId" type="INT"/>
        </addColumn>
    </changeSet>
    <changeSet id="ECCO-1749-pop-outcomes-svcRecId" author="nealeu">
        <update tableName="supportplanoutcomes">
            <column name="serviceRecipientId"
                valueComputed="(SELECT serviceRecipientId FROM referrals WHERE referrals.id = supportplanoutcomes.referralId)"/>
        </update>
        <addForeignKeyConstraint constraintName="fk_ev_needsoutcomes_svcrec"
            baseTableName="supportplanoutcomes" baseColumnNames="serviceRecipientId"
            referencedTableName="servicerecipients" referencedColumnNames="id"
            deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
            referencesUniqueColumn="true"/>
    </changeSet>

    <changeSet id="ECCO-1749-add-outcomes-notNull" author="nealeu">
        <addNotNullConstraint tableName="supportplanoutcomes" columnName="serviceRecipientId" columnDataType="INT"/>
    </changeSet>
    <changeSet id="ECCO-1749-outcomes-srId-idx" author="nealeu">
        <createIndex tableName="supportplanoutcomes" indexName="idx_ev_needsoutcomes_srid">
            <column name="serviceRecipientId"/>
        </createIndex>
    </changeSet>

    <changeSet id="ECCO-1749-remove-outcomes-Id-col" author="nealeu">
        <dropForeignKeyConstraint baseTableName="supportplanoutcomes" constraintName="FK498276D92DAEC673"/>
        <dropColumn tableName="supportplanoutcomes" columnName="referralId"/>
    </changeSet>


    <changeSet id="ECCO-1749-add-supportplanrisks-svcRecId" author="nealeu">
        <addColumn tableName="supportplanrisks">
            <column name="serviceRecipientId" type="INT"/>
        </addColumn>
    </changeSet>
    <changeSet id="ECCO-1749-risks-srId-idx" author="nealeu">
        <createIndex tableName="supportplanrisks" indexName="idx_ev_needsrisks_srid">
            <column name="serviceRecipientId"/>
        </createIndex>
    </changeSet>
    <changeSet id="ECCO-1749-pop-risks-svcRecId" author="nealeu">
        <update tableName="supportplanrisks">
            <column name="serviceRecipientId"
                valueComputed="(SELECT serviceRecipientId FROM referrals WHERE referrals.id = supportplanrisks.referralId)"/>
        </update>
        <addForeignKeyConstraint constraintName="fk_ev_needsrisks_svcrec"
            baseTableName="supportplanrisks" baseColumnNames="serviceRecipientId"
            referencedTableName="servicerecipients" referencedColumnNames="id"
            deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
            referencesUniqueColumn="true"/>
    </changeSet>

    <changeSet id="ECCO-1749-remove-risks-Id-col" author="nealeu">
        <dropForeignKeyConstraint baseTableName="supportplanrisks" constraintName="FK63A7CF0C2DAEC673"/>
        <dropColumn tableName="supportplanrisks" columnName="referralId"/>
    </changeSet>



    <changeSet id="ECCO-1749-add-supportplanwork-svcRecId" author="nealeu">
        <addColumn tableName="supportplanwork">
            <column name="serviceRecipientId" type="INT"/>
        </addColumn>
    </changeSet>
    <changeSet id="ECCO-1749-work-srId-idx" author="nealeu">
        <createIndex tableName="supportplanwork" indexName="idx_ev_needswork_srid">
            <column name="serviceRecipientId"/>
        </createIndex>
    </changeSet>
    <changeSet id="ECCO-1749-work-srId-created-idx" author="nealeu">
        <createIndex tableName="supportplanwork" indexName="idx_ev_needswork_srid_created">
            <column name="serviceRecipientId"/>
            <column name="created"/>
        </createIndex>
    </changeSet>
    <changeSet id="ECCO-1749-pop-work-svcRecId" author="nealeu">
        <update tableName="supportplanwork">
            <column name="serviceRecipientId"
                valueComputed="(SELECT serviceRecipientId FROM referrals WHERE referrals.id = supportplanwork.referralId)"/>
        </update>
        <addForeignKeyConstraint constraintName="fk_ev_needswork_svcrec"
            baseTableName="supportplanwork" baseColumnNames="serviceRecipientId"
            referencedTableName="servicerecipients" referencedColumnNames="id"
            deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
            referencesUniqueColumn="true"/>
    </changeSet>

    <changeSet id="ECCO-1749-remove-work-Id-col" author="nealeu">
        <dropForeignKeyConstraint baseTableName="supportplanwork" constraintName="FK4549D6892DAEC673"/>
        <dropIndex tableName="supportplanwork" indexName="IDX_SUPPWRK_RID_CREATED"/>
        <dropColumn tableName="supportplanwork" columnName="referralId"/>
    </changeSet>



    <changeSet id="ECCO-1748-add-supportplanwork-childSvcRecId" author="nealeu">
        <addColumn tableName="supportplanwork">
            <column name="childSvcRecId" type="INT"/>
        </addColumn>
    </changeSet>
    <changeSet id="ECCO-1748-pop-work-childSvcRecId" author="nealeu">
        <update tableName="supportplanwork">
            <column name="childSvcRecId"
                valueComputed="(SELECT serviceRecipientId FROM referrals WHERE referrals.id = supportplanwork.childReferralId)"/>
        </update>
        <addForeignKeyConstraint constraintName="fk_ev_needs_work_childsvcrec"
            baseTableName="supportplanwork" baseColumnNames="childSvcRecId"
            referencedTableName="servicerecipients" referencedColumnNames="id"
            deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
            referencesUniqueColumn="true"/>
    </changeSet>

    <changeSet id="ECCO-1748-remove-work-childId-col" author="nealeu">
        <dropForeignKeyConstraint baseTableName="supportplanwork" constraintName="FK_suppwork_childRefId"/>
        <dropColumn tableName="supportplanwork" columnName="childReferralId"/>
    </changeSet>


    <changeSet id="ECCO-1750-add-supportthreatcomments-svcRecId" author="nealeu">
        <addColumn tableName="supportthreatcomments">
            <column name="serviceRecipientId" type="INT"/>
        </addColumn>
    </changeSet>
    <changeSet id="ECCO-1750-comments-srId-idx" author="nealeu">
        <createIndex tableName="supportthreatcomments" indexName="idx_ev_threatcomments_srid">
            <column name="serviceRecipientId"/>
        </createIndex>
    </changeSet>
    <changeSet id="ECCO-1750-pop-comments-svcRecId" author="nealeu">
        <update tableName="supportthreatcomments">
            <column name="serviceRecipientId"
                valueComputed="(SELECT serviceRecipientId FROM referrals WHERE referrals.id = supportthreatcomments.referralId)"/>
        </update>
        <addForeignKeyConstraint constraintName="fk_ev_threatcomments_svcrec"
            baseTableName="supportthreatcomments" baseColumnNames="serviceRecipientId"
            referencedTableName="servicerecipients" referencedColumnNames="id"
            deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
            referencesUniqueColumn="true"/>
    </changeSet>

    <changeSet id="ECCO-1750-remove-comments-Id-col" author="nealeu">
        <dropForeignKeyConstraint baseTableName="supportthreatcomments" constraintName="FKA8115B9D2DAEC673"/>
        <dropColumn tableName="supportthreatcomments" columnName="referralId"/>
    </changeSet>


    <changeSet id="ECCO-1750-add-supportthreatflags-svcRecId" author="nealeu">
        <addColumn tableName="supportthreatflags">
            <column name="serviceRecipientId" type="INT"/>
        </addColumn>
    </changeSet>
    <changeSet id="ECCO-1750-flags-srId-idx" author="nealeu">
        <createIndex tableName="supportthreatflags" indexName="idx_ev_threatflags_srid">
            <column name="serviceRecipientId"/>
        </createIndex>
    </changeSet>
    <changeSet id="ECCO-1750-pop-flags-svcRecId" author="nealeu">
        <update tableName="supportthreatflags">
            <column name="serviceRecipientId"
                valueComputed="(SELECT serviceRecipientId FROM referrals WHERE referrals.id = supportthreatflags.referralId)"/>
        </update>
        <addForeignKeyConstraint constraintName="fk_ev_threatflags_svcrec"
            baseTableName="supportthreatflags" baseColumnNames="serviceRecipientId"
            referencedTableName="servicerecipients" referencedColumnNames="id"
            deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
            referencesUniqueColumn="true"/>
    </changeSet>

    <changeSet id="ECCO-1750-remove-flags-Id-col" author="nealeu">
        <dropForeignKeyConstraint baseTableName="supportthreatflags" constraintName="FK5BC2797E2DAEC673"/>
        <dropColumn tableName="supportthreatflags" columnName="referralId"/>
    </changeSet>



    <changeSet id="ECCO-1750-add-supportthreatoutcomes-svcRecId" author="nealeu">
        <addColumn tableName="supportthreatoutcomes">
            <column name="serviceRecipientId" type="INT"/>
        </addColumn>
    </changeSet>
    <changeSet id="ECCO-1750-outcomes-srId-idx" author="nealeu">
        <createIndex tableName="supportthreatoutcomes" indexName="idx_threatoutcomes_srid">
            <column name="serviceRecipientId"/>
        </createIndex>
    </changeSet>
    <changeSet id="ECCO-1750-pop-outcomes-svcRecId" author="nealeu">
        <update tableName="supportthreatoutcomes">
            <column name="serviceRecipientId"
                valueComputed="(SELECT serviceRecipientId FROM referrals WHERE referrals.id = supportthreatoutcomes.referralId)"/>
        </update>
        <addForeignKeyConstraint constraintName="fk_ev_threatoutcomes_svcrec"
            baseTableName="supportthreatoutcomes" baseColumnNames="serviceRecipientId"
            referencedTableName="servicerecipients" referencedColumnNames="id"
            deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
            referencesUniqueColumn="true"/>
    </changeSet>

    <changeSet id="ECCO-1750-remove-outcomes-Id-col" author="nealeu">
        <dropForeignKeyConstraint baseTableName="supportthreatoutcomes" constraintName="FKCF6E610A2DAEC673"/>
        <dropColumn tableName="supportthreatoutcomes" columnName="referralId"/>
    </changeSet>


    <changeSet id="ECCO-1750-add-supportthreatwork-svcRecId" author="nealeu">
        <addColumn tableName="supportthreatwork">
            <column name="serviceRecipientId" type="INT"/>
        </addColumn>
    </changeSet>
    <changeSet id="ECCO-1750-work-srId-idx" author="nealeu">
        <createIndex tableName="supportthreatwork" indexName="idx_ev_threatwork_srid">
            <column name="serviceRecipientId"/>
        </createIndex>
    </changeSet>
    <changeSet id="ECCO-1750-work-srId-created-idx" author="nealeu">
        <createIndex tableName="supportthreatwork" indexName="idx_ev_threatwork_srid_created">
            <column name="serviceRecipientId"/>
            <column name="created"/>
        </createIndex>
    </changeSet>
    <changeSet id="ECCO-1750-pop-work-svcRecId" author="nealeu">
        <update tableName="supportthreatwork">
            <column name="serviceRecipientId"
                valueComputed="(SELECT serviceRecipientId FROM referrals WHERE referrals.id = supportthreatwork.referralId)"/>
        </update>
        <addForeignKeyConstraint constraintName="fk_ev_threatwork_svcrec"
            baseTableName="supportthreatwork" baseColumnNames="serviceRecipientId"
            referencedTableName="servicerecipients" referencedColumnNames="id"
            deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
            referencesUniqueColumn="true"/>
    </changeSet>


    <changeSet id="ECCO-1750-remove-work-Id-col" author="nealeu">
        <dropForeignKeyConstraint baseTableName="supportthreatwork" constraintName="FK7EDC813A2DAEC673"/>
        <dropIndex tableName="supportthreatwork" indexName="IDX_THRTWRK_RID_CREATED"/>
        <dropColumn tableName="supportthreatwork" columnName="referralId"/>
    </changeSet>


    <changeSet id="ECCO-1750-add-supportthreatwork-childSvcRecId" author="nealeu">
        <addColumn tableName="supportthreatwork">
            <column name="childSvcRecId" type="INT"/>
        </addColumn>
    </changeSet>
    <changeSet id="ECCO-1750-pop-work-childSvcRecId" author="nealeu">
        <update tableName="supportthreatwork">
            <column name="childSvcRecId"
                valueComputed="(SELECT serviceRecipientId FROM referrals WHERE referrals.id = supportthreatwork.childReferralId)"/>
        </update>
        <addForeignKeyConstraint constraintName="fk_ev_threatwork_childsvcrec"
            baseTableName="supportthreatwork" baseColumnNames="childSvcRecId"
            referencedTableName="servicerecipients" referencedColumnNames="id"
            deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
            referencesUniqueColumn="true"/>
    </changeSet>

    <changeSet id="ECCO-1750-remove-work-childId-col" author="nealeu">
        <dropForeignKeyConstraint baseTableName="supportthreatwork" constraintName="FK_riskwork_childRefId"/>
        <dropColumn tableName="supportthreatwork" columnName="childReferralId"/>
    </changeSet>
    <!-- TODO: ADD MISSING NOT NULL CONSTRAINTS on some of above -->

    <changeSet id="ECCO-1789-create-sr-serviceId-field" author="nealeu">
        <addColumn tableName="servicerecipients">
            <column name="serviceId" type="BIGINT"/>
        </addColumn>
    </changeSet>


    <changeSet id="ECCO-1789-populate-rsr-serviceId" author="nealeu">
        <update tableName="servicerecipients">
            <column name="serviceId"
                valueComputed="(SELECT referredServiceId FROM referrals WHERE referrals.serviceRecipientId = servicerecipients.id)"/>
            <where>serviceId IS NULL AND EXISTS (SELECT * FROM referrals WHERE referrals.serviceRecipientId = servicerecipients.id)</where>
        </update>
    </changeSet>
    <changeSet id="ECCO-1789-populate-wsr-serviceId" author="nealeu">
        <update tableName="servicerecipients">
            <column name="serviceId" valueNumeric="-200"/>
            <where>serviceId IS NULL AND EXISTS (SELECT * FROM workers WHERE workers.serviceRecipientId = servicerecipients.id)</where>
        </update>
    </changeSet>
    <changeSet id="ECCO-1789-populate-bsr-serviceId" author="nealeu">
        <update tableName="servicerecipients">
            <column name="serviceId" valueNumeric="-100"/>
            <where>serviceId IS NULL AND EXISTS (SELECT * FROM bldg_fixed WHERE bldg_fixed.serviceRecipientId = servicerecipients.id)</where>
        </update>
    </changeSet>

    <changeSet id="ECCO-1789-add-notNull-constraint" author="nealeu">
        <addNotNullConstraint tableName="servicerecipients" columnName="serviceId" columnDataType="BIGINT"/>
    </changeSet>


    <changeSet id="ECCO-1797-create-sr-projectId-field" author="nealeu">
        <addColumn tableName="servicerecipients">
            <column name="projectId" type="BIGINT"/>
        </addColumn>
    </changeSet>

     <changeSet id="ECCO-1789-del-orm_sId_cr_id-idx" author="nealeu">
        <!-- Only drop index if we created it and didn't already delete it -->
        <preConditions onFail="MARK_RAN">
            <and>
                <changeSetExecuted id="ECCO-1789-orm_sId_cr_id-idx" author="nealeu"
                    changeLogFile="classpath:sql/1.1-changes/075-Dec-2015-until-next.xml"/>
                <not>
                    <changeSetExecuted id="ECCO-1797-orm_sId_pId_id-idx" author="nealeu"
                    changeLogFile="classpath:sql/1.1-changes/075-Dec-2015-until-next.xml"/>
                </not>
            </and>
        </preConditions>
        <dropIndex tableName="servicerecipients" indexName="idx_svc_rec_orm_sId_cr"/>
    </changeSet>

     <changeSet id="ECCO-1797-orm_sId_pId_id-idx" author="nealeu">
        <validCheckSum>7:d0da1646a0953ef2b1b96b60dd086eaf</validCheckSum>
        <!-- Index incl id so we avoid bookmark lookup or is PK implied (if so it doesn't matter then)-->
        <createIndex tableName="servicerecipients" indexName="idx_svc_rec_orm_sId_pId">
            <column name="discriminator_orm"/>
            <column name="serviceId"/>
            <column name="projectId"/>
            <column name="created"/>
            <column name="id"/>
        </createIndex>
    </changeSet>

    <changeSet id="ECCO-1797-populate-rsr-projectId" author="nealeu">
        <update tableName="servicerecipients">
            <column name="projectId"
                valueComputed="(SELECT projectId FROM referrals WHERE referrals.serviceRecipientId = servicerecipients.id)"/>
            <where>projectId IS NULL AND EXISTS (SELECT * FROM referrals WHERE referrals.serviceRecipientId = servicerecipients.id)</where>
        </update>
    </changeSet>

    <changeSet id="ECCO-1789-svcrec-serviceId-fk" author="nealeu">
        <addForeignKeyConstraint constraintName="fk_svcrec_serviceId" baseTableName="servicerecipients"
            baseColumnNames="serviceId" referencedTableName="services" referencedColumnNames="id"
            referencesUniqueColumn="true" />
    </changeSet>

    <changeSet id="ECCO-1797-svcrec-projectId-fk" author="nealeu">
        <addForeignKeyConstraint constraintName="fk_svcrec_projectId" baseTableName="servicerecipients"
            baseColumnNames="projectId" referencedTableName="projects" referencedColumnNames="id"
            referencesUniqueColumn="true" />
    </changeSet>

    <changeSet id="ECCO-1789-drop-referredServiceId-col" author="nealeu">
        <dropForeignKeyConstraint baseTableName="referrals" constraintName="FKC8E0F876262EA9F1"/>
        <dropColumn tableName="referrals" columnName="referredServiceId"/>
    </changeSet>

    <changeSet id="ECCO-1797-drop-projectId-col" author="nealeu">
        <dropForeignKeyConstraint baseTableName="referrals" constraintName="FK_rfrl_projId_projs"/>
        <dropColumn tableName="referrals" columnName="projectId"/>
    </changeSet>


    <changeSet id="prevent-CRITICAL-issue-reducePageSize" author="adamjhamer">
        <update tableName="setting">
            <column name="keyvalue" value="10"/>
            <where>id in (40,41,42)</where>
        </update>
    </changeSet>

    <changeSet author="nealeu" id="DROP_DATABASECHANGELOG-PK">
        <preConditions onFail="MARK_RAN">
            <primaryKeyExists tableName="DATABASECHANGELOG"/>
        </preConditions>
        <dropPrimaryKey tableName="DATABASECHANGELOG" />
    </changeSet>

    <changeSet author="nealeu" id="DATABASECHANGELOG-PK">
        <addPrimaryKey columnNames="ID,AUTHOR,FILENAME" tableName="DATABASECHANGELOG"/>
    </changeSet>

    <changeSet id="ECCO-1827-create-svcrec_taskstatus-table" author="nealeu">
        <createTable tableName="svcrec_taskstatus">
            <!-- An entry only appears here when a task becomes available, is taken or completed -->
            <column name="serviceRecipientId" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="referralAspectId" type="BIGINT"><!-- i.e. referralAspectId -->
                <constraints nullable="false"/>
            </column>
            <column name="created" type="DATETIME"> <!-- this starts the clock -->
                <constraints nullable="false"/>
            </column>
            <column name="relevantGroupId" type="BIGINT"/>
            <column name="completed" type="DATETIME"/>
            <column name="assignedUserId" type="BIGINT"/>
        </createTable>
        <addForeignKeyConstraint constraintName="fk_sr_taskstatus_srid"
            baseTableName="svcrec_taskstatus" baseColumnNames="serviceRecipientId"
            referencedTableName="servicerecipients" referencedColumnNames="id"
            deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
            referencesUniqueColumn="true"/>
        <addForeignKeyConstraint constraintName="fk_sr_taskstatus_rfrlAspects"
            baseTableName="svcrec_taskstatus" baseColumnNames="referralAspectId"
            referencedTableName="referralaspects" referencedColumnNames="id"
            deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
            referencesUniqueColumn="true"/>
        <addForeignKeyConstraint constraintName="fk_sr_taskstatus_relGroupId"
            baseTableName="svcrec_taskstatus" baseColumnNames="relevantGroupId"
            referencedTableName="groups" referencedColumnNames="id"
            deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
            referencesUniqueColumn="true"/>
        <addForeignKeyConstraint constraintName="fk_sr_taskstatus_assignedUid"
            baseTableName="svcrec_taskstatus" baseColumnNames="assignedUserId"
            referencedTableName="users" referencedColumnNames="id"
            deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
            referencesUniqueColumn="true"/>
    </changeSet>

    <changeSet id="ECCO-1831-add_signature_uuid" author="nealeu">
        <comment>Add uuid to signature and supportplanwork</comment>
        <addColumn tableName="signature">
            <column name="uuid" type="CHAR(36)"/> <!-- will set nonnull after populating -->
        </addColumn>
        <addColumn tableName="supportplanwork">
            <column name="signatureUuid" type="CHAR(36)"/><!-- Remains nullable as it's optional -->
        </addColumn>
    </changeSet>
    <changeSet id="ECCO-1831-add_threatwrk_sigUuid" author="nealeu">
        <addColumn tableName="supportthreatwork">
            <column name="signatureUuid" type="CHAR(36)"/><!-- Remains nullable as it's optional -->
        </addColumn>
    </changeSet>
    <changeSet id="ECCO-1831-add_svcrec_signature_uuids" author="nealeu">
        <addColumn tableName="servicerecipients">
            <column name="dataProtectionSignatureId" type="CHAR(36)"/> <!-- Remains nullable as it's optional -->
        </addColumn>
        <addColumn tableName="servicerecipients">
            <column name="consentSignatureId" type="CHAR(36)"/> <!-- Remains nullable as it's optional -->
        </addColumn>
    </changeSet>

    <changeSet id="ECCO-1831-populate-signature-uuid" author="nealeu">
        <customChange class="com.ecco.infrastructure.liquibase.AddUuidColumnChange"
            tableName="signature" uniqueKeyColumns="id"
            newUuidColumn="uuid" />
    </changeSet>
    <changeSet id="ECCO-1831-add-notNull-constraint" author="nealeu">
        <addNotNullConstraint tableName="signature" columnName="uuid" columnDataType="CHAR(36)"/>
    </changeSet>

    <changeSet id="ECCO-1831-signature-uuid-PK" author="nealeu">
        <dropForeignKeyConstraint baseTableName="supportplanwork" constraintName="FK4549D68951665EE8"/>
        <dropPrimaryKey tableName="signature" />
        <addPrimaryKey columnNames="uuid" tableName="signature"/>
    </changeSet>
    <changeSet id="ECCO-1831-nonull-on-signature-uuid" author="nealeu">
        <validCheckSum>7:2efd5c6e46cebc6c6ae71d37c5a2336f</validCheckSum>
        <addForeignKeyConstraint constraintName="fk_suppWork_signatureUuid"
            baseTableName="supportplanwork" baseColumnNames="signatureUuid"
            referencedTableName="signature" referencedColumnNames="uuid"
            deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
            referencesUniqueColumn="true"/>
    </changeSet>
    <changeSet id="ECCO-1831-populate-supportplan-wrk.sigUuid" author="nealeu">
        <update tableName="supportplanwork">
            <column name="signatureUuid"
            valueComputed="(SELECT uuid from signature where signature.id = supportplanwork.signatureId)"/>
        </update>
    </changeSet>
    <changeSet id="ECCO-1831-populate-supportthreat-wrk.sigUuid" author="nealeu">
        <update tableName="supportthreatwork">
            <column name="signatureUuid"
            valueComputed="(SELECT uuid from signature where signature.id = supportthreatwork.signatureId)"/>
        </update>
    </changeSet>
    <changeSet id="ECCO-1831-populate-svcrec-consent.sigUuid" author="nealeu">
        <update tableName="servicerecipients">
            <column name="consentSignatureId"
            valueComputed=
"(SELECT sig.uuid FROM referrals r JOIN signature sig ON sig.id = r.consentsignatureId where r.servicerecipientid = servicerecipients.id)"/>
        </update>
    </changeSet>
    <changeSet id="ECCO-1831-populate-svcrec-dataprot.sigUuid" author="nealeu">
        <update tableName="servicerecipients">
            <column name="dataProtectionSignatureId"
            valueComputed=
"(SELECT sig.uuid FROM referrals r JOIN signature sig ON sig.id = r.dataprotectionsignatureId where r.servicerecipientid = servicerecipients.id)"/>
        </update>
    </changeSet>

    <!--
    mssql creates its own constraints for 'default' values which don't appear in information_schema
    see http://stackoverflow.com/questions/1430456/how-to-drop-sql-default-constraint-without-knowing-its-name
    -->
    <changeSet id="ECCO-1831-drop-DF-constraint-MSSQL" author="nealeu">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql"/>
        </preConditions>
        <comment>fixup: DF_signature_id' is dependent on column 'id'</comment>
        <sql>
        DECLARE @ObjectName NVARCHAR(100)
        SELECT @ObjectName = OBJECT_NAME([default_object_id]) FROM SYS.COLUMNS
        WHERE [object_id] = OBJECT_ID('[dbo].[signature]') AND [name] = 'id'
        EXEC('ALTER TABLE [dbo].[signature] DROP CONSTRAINT ' + @ObjectName)
        ;
        </sql>
    </changeSet>

    <changeSet id="ECCO-1831-drop-old-sigId-columns" author="nealeu">
        <dropColumn tableName="supportplanwork" columnName="signatureId"/>
        <dropColumn tableName="supportthreatwork" columnName="signatureId"/>
        <dropColumn tableName="referrals" columnName="dataprotectionsignatureId"/>
        <dropColumn tableName="referrals" columnName="consentsignatureId"/>
        <dropColumn tableName="signature" columnName="id"/>
    </changeSet>

    <changeSet id="ECCO-1674-supportthreatactions-increaseSize" author="adamjhamer">
        <modifyDataType tableName="supportthreatactions" columnName="hazard" newDataType="VARCHAR(500)"/>
        <modifyDataType tableName="supportthreatactions" columnName="intervention" newDataType="VARCHAR(500)"/>
    </changeSet>

    <changeSet id="ECCO-1833-service_types-hideOnList" author="adamjhamer">
        <addColumn tableName="servicetypes">
            <column name="hideOnList" type="BOOLEAN" defaultValueNumeric="0">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="ECCO-1834-service_types-hideOnNew" author="adamjhamer">
        <addColumn tableName="servicetypes">
            <column name="hideOnNew" type="BOOLEAN" defaultValueNumeric="0">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="ECCO-1834-hide-hr-bldg-services" author="neale">
        <update tableName="servicetypes">
            <column name="hideOnNew" valueBoolean="true"/>
            <where>id=-100 or id=-200</where>
        </update>
        <update tableName="servicetypes">
            <column name="hideOnList" valueBoolean="true"/>
            <where>id=-100 or id=-200</where>
        </update>
    </changeSet>

    <changeSet id="ECCO-1833-minimal-servicetype-child" author="neale" context="1.1-base-data,central-proc-servicetype">
        <update tableName="servicetypes">
            <column name="hideOnList" valueBoolean="true"/>
            <where>id=95</where>
        </update>
    </changeSet>
    <changeSet id="ECCO-1834-minimal-servicetype-child" author="neale" context="1.1-base-data,central-proc-servicetype">
        <update tableName="servicetypes">
            <column name="hideOnNew" valueBoolean="true"/>
            <where>id=95</where>
        </update>
    </changeSet>

    <changeSet id="ECCO-1863-add_bldg_addresses_uuid" author="nealeu">
        <addColumn tableName="bldg_addresses">
            <column name="uuid" type="CHAR(36)"/> <!-- nullable until populated -->
        </addColumn>
    </changeSet>
    <changeSet id="ECCO-1863-populate-bldg-addr-uuid" author="nealeu">
        <customChange class="com.ecco.infrastructure.liquibase.AddUuidColumnChange"
            tableName="bldg_addresses" uniqueKeyColumns="id"
            newUuidColumn="uuid" />
    </changeSet>
    <changeSet id="ECCO-1863-add-notNull-constraint" author="nealeu">
        <addNotNullConstraint tableName="bldg_addresses" columnName="uuid" columnDataType="CHAR(36)"/>
    </changeSet>
    <changeSet id="ECCO-1863-bldg-addr-uuid-idx" author="nealeu">
        <createIndex tableName="bldg_addresses" indexName="idx_bldg_addr_uuid">
            <column name="uuid"/>
        </createIndex>
    </changeSet>

    <changeSet id="ECCO-1876-add-sr-latestClientStatus" author="nealeu">
        <addColumn tableName="servicerecipients">
            <column name="latestClientStatusId" type="INT"> <!-- based on latest non-null CS from supportplancomments -->
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet id="ECCO-1876-sr-latestClientStatus-FK" author="nealeu">
        <addForeignKeyConstraint constraintName="fk_svcrec_latestCS" baseTableName="servicerecipients"
            baseColumnNames="latestClientStatusId" referencedTableName="cfg_list_definitions" referencedColumnNames="id" />
    </changeSet>

    <changeSet id="ECCO-1876-populate-sr-latestClientStatus" author="nealeu">
        <preConditions onFail="MARK_RAN">
            <not>
                <sqlCheck expectedResult="0">
                SELECT count(1) FROM supportplancomments WHERE clientStatusId IS NOT NULL
                </sqlCheck>
            </not>
        </preConditions>
        <sql>
        UPDATE servicerecipients sr
            JOIN (SELECT serviceRecipientid, clientStatusId FROM supportplancomments
            WHERE id IN
                (SELECT max(id) from supportplancomments
                WHERE clientStatusId IS NOT null
                GROUP BY serviceRecipientId)) pairs
            ON sr.id = pairs.servicerecipientId
        SET latestClientStatusId = pairs.clientStatusId;
        </sql>
    </changeSet>

    <changeSet id="ECCO-1877-transfer-multipleReferrals-aspects" author="adamjhamer">
        <update tableName="servicetypes">
            <column name="hideOnList" valueBoolean="true"/>
            <column name="hideOnNew" valueBoolean="true"/>
            <where>multipleReferrals=1</where>
        </update>
    </changeSet>

    <changeSet author="nealeu" id="ECCO-1890-travelMins">
        <addColumn tableName="supportplancomments">
            <column name="minsTravel" type="INT">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="ECCO-1775-questionanswer-column" author="adamjhamer">
        <addColumn tableName="svcrec_commands">
            <column name="questionDefId" type="BIGINT">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet id="ECCO-1775-questionanswer-FK" author="adamjhamer">
        <addForeignKeyConstraint
            constraintName="fk_svcrec_question"
            baseTableName="svcrec_commands" baseColumnNames="questionDefId"
            referencedTableName="questions" referencedColumnNames="id" />
    </changeSet>

    <changeSet id="ECCO-1775-evidencegroupkey-size" author="adamjhamer">
        <comment>need to cater for questionniare_general</comment>
        <modifyDataType tableName="svcrec_commands" columnName="evidencegroupkey" newDataType="VARCHAR(40)"/>
    </changeSet>

    <changeSet id="ECCO-1772-hact-mapping" author="adamjhamer">
        <createTable tableName="hactoutcomemappings">
            <column defaultValueNumeric="0" name="id" type="INT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="hactOutcomeDefCode" type="VARCHAR(10)">
                <constraints nullable="false"/>
            </column>
            <column name="actionDefId" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="ECCO-1772-hact-mapping-actions-fk" author="adamjhamer">
        <addForeignKeyConstraint
            constraintName="fk_hactmapping_actions"
            baseTableName="hactoutcomemappings" baseColumnNames="actionDefId"
            referencedTableName="actions" referencedColumnNames="id" />
    </changeSet>

    <changeSet id="ECCO-1772-hact-mapping-idx" author="adamjhamer">
        <comment>index for mapping from actionId</comment>
        <createIndex unique="false" tableName="hactoutcomemappings" indexName="idx_hactmappings">
            <column name="actionDefId"/>
            <column name="hactOutcomeDefCode"/>
        </createIndex>
    </changeSet>

    <changeSet id="ECCO-1772-hact-svb" author="adamjhamer">
        <createTable tableName="hactsocialvaluebank">
            <column defaultValueNumeric="0" name="id" type="INT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="hactOutcomeDefCode" type="VARCHAR(10)">
                <constraints nullable="false"/>
            </column>
            <column name="outcomeCategory" type="VARCHAR(30)">
                <constraints nullable="false"/>
            </column>
            <column name="outcomeName" type="VARCHAR(255)" defaultValue="-">
                <constraints nullable="false"/>
            </column>
            <column name="valueAnywhereUnknown" type="INT" defaultValueNumeric="0">
                <constraints nullable="false"/>
            </column>
            <column name="valueAnywhere24" type="INT" defaultValueNumeric="0">
                <constraints nullable="false"/>
            </column>
            <column name="valueAnywhere49" type="INT" defaultValueNumeric="0">
                <constraints nullable="false"/>
            </column>
            <column name="valueAnywhereHigher" type="INT" defaultValueNumeric="0">
                <constraints nullable="false"/>
            </column>
            <column name="valueLondonUnknown" type="INT" defaultValueNumeric="0">
                <constraints nullable="false"/>
            </column>
            <column name="valueLondon24" type="INT" defaultValueNumeric="0">
                <constraints nullable="false"/>
            </column>
            <column name="valueLondon49" type="INT" defaultValueNumeric="0">
                <constraints nullable="false"/>
            </column>
            <column name="valueLondonHigher" type="INT" defaultValueNumeric="0">
                <constraints nullable="false"/>
            </column>
            <column name="valueOutsideLondonUnknown" type="INT" defaultValueNumeric="0">
                <constraints nullable="false"/>
            </column>
            <column name="valueOutsideLondon24" type="INT" defaultValueNumeric="0">
                <constraints nullable="false"/>
            </column>
            <column name="valueOutsideLondon49" type="INT" defaultValueNumeric="0">
                <constraints nullable="false"/>
            </column>
            <column name="valueOutsideLondonHigher" type="INT" defaultValueNumeric="0">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="ECCO-1772-hact-svb-idx-constraint" author="adamjhamer">
        <comment>constraint for mapping from actionId</comment>
        <addUniqueConstraint tableName="hactsocialvaluebank" columnNames="hactOutcomeDefCode"
            constraintName="uniq_hactOutcomeDefCode"/>
    </changeSet>

    <changeSet id="ECCO-1772-hact-mapping-hactoutcome-fk" author="adamjhamer">
        <addForeignKeyConstraint
            constraintName="fk_hactmapping_hactoutcomes"
            baseTableName="hactoutcomemappings" baseColumnNames="hactOutcomeDefCode"
            referencedTableName="hactsocialvaluebank" referencedColumnNames="hactOutcomeDefCode" />
    </changeSet>

    <!-- this table has scope for multiple questionDefId rows (or using another table) -->
    <!-- but its usage is currently for a single question for a single HACT outcome -->
    <changeSet id="ECCO-1772-hact-evidence" author="adamjhamer">
        <createTable tableName="hactoutcomeevidence">
            <column defaultValueNumeric="0" name="id" type="INT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="hactOutcomeDefCode" type="VARCHAR(10)">
                <constraints nullable="false"/>
            </column>
            <column name="evidenceDescription" type="VARCHAR(255)"/>
            <column name="questionDefId" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="ECCO-1772-hact-evidence-hactoutcome-fk" author="adamjhamer">
        <addForeignKeyConstraint
            constraintName="fk_hactevidence_hactoutcomes"
            baseTableName="hactoutcomeevidence" baseColumnNames="hactOutcomeDefCode"
            referencedTableName="hactsocialvaluebank" referencedColumnNames="hactOutcomeDefCode" />
    </changeSet>
    <changeSet id="ECCO-1772-hact-evidence-question-fk" author="adamjhamer">
        <addForeignKeyConstraint
            constraintName="fk_hactevidence_questions"
            baseTableName="hactoutcomeevidence" baseColumnNames="questionDefId"
            referencedTableName="questions" referencedColumnNames="id" />
    </changeSet>

    <changeSet id="ECCO-1816-reserve-ra-questionnaireHact" author="adamjhamer">
        <insert tableName="referralaspects">
            <column name="id" valueNumeric="109"/>
            <column name="version" valueNumeric="0"/>
            <column name="display" valueBoolean="false"/>
            <column name="displayOverview" valueBoolean="false"/>
            <column name="isexternal" valueBoolean="false"/>
            <column name="friendlyName" value="hactQuestionnaire"/>
            <column name="internal" valueBoolean="true"/>
            <column name="name" value="hactQuestionnaire"/>
        </insert>
    </changeSet>

    <changeSet id="ECCO-1999-hact-evidence-rename" author="adamjhamer">
        <renameTable oldTableName="hactoutcomeevidence" newTableName="hactoutcomeevidencesurvey" />
    </changeSet>

    <changeSet id="ECCO-1999-hact-evidence-activities" author="adamjhamer">
        <createTable tableName="hactoutcomeevidenceactivities">
            <column defaultValueNumeric="0" name="id" type="INT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="hactOutcomeDefCode" type="VARCHAR(10)">
                <constraints nullable="false"/>
            </column>
            <column name="evidenceDescription" type="VARCHAR(255)"/>
            <column name="actionDefId" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="ECCO-1999-hact-evidenceactivity-hactoutcome-fk" author="adamjhamer">
        <addForeignKeyConstraint
            constraintName="fk_hactevidenceact_hactsvb"
            baseTableName="hactoutcomeevidenceactivities" baseColumnNames="hactOutcomeDefCode"
            referencedTableName="hactsocialvaluebank" referencedColumnNames="hactOutcomeDefCode" />
    </changeSet>
    <changeSet id="ECCO-1999-hact-evidenceactivity-question-fk" author="adamjhamer">
        <addForeignKeyConstraint
            constraintName="fk_hactevidenceact_actions"
            baseTableName="hactoutcomeevidenceactivities" baseColumnNames="actionDefId"
            referencedTableName="actions" referencedColumnNames="id" />
    </changeSet>

    <changeSet id="ECCO-1999-hact-evidenceactivity-unique" author="adamjhamer">
        <createIndex unique="true" tableName="hactoutcomeevidenceactivities" indexName="idx_hactevidenceactivity_uniq">
            <column name="hactOutcomeDefCode"/>
            <column name="actionDefId"/>
        </createIndex>
    </changeSet>

    <!-- revert all FKs for hactoutcomeevidencesurvey since mysql complains at removing just the uniq constraint -->
    <changeSet id="ECCO-1772-hact-evidence-hactoutcome-fk-REVERT" author="adamjhamer">
        <preConditions onFail="MARK_RAN">
            <changeSetExecuted id="ECCO-1772-hact-evidence-hactoutcome-fk" author="adamjhamer" changeLogFile="classpath:sql/1.1-changes/075-Dec-2015-until-next.xml"/>
        </preConditions>
        <dropForeignKeyConstraint constraintName="fk_hactevidence_hactoutcomes" baseTableName="hactoutcomeevidencesurvey"/>
    </changeSet>
    <changeSet id="ECCO-1772-hact-evidence-question-fk-REVERT" author="adamjhamer">
        <preConditions onFail="MARK_RAN">
            <changeSetExecuted id="ECCO-1772-hact-evidence-question-fk" author="adamjhamer" changeLogFile="classpath:sql/1.1-changes/075-Dec-2015-until-next.xml"/>
        </preConditions>
        <dropForeignKeyConstraint constraintName="fk_hactevidence_questions" baseTableName="hactoutcomeevidencesurvey"/>
    </changeSet>
    <changeSet id="ECCO-1999-hact-evidencesurvey-answer-fk-REVERT" author="adamjhamer">
        <preConditions onFail="MARK_RAN">
            <changeSetExecuted id="ECCO-1999-hact-evidencesurvey-answer-fk" author="adamjhamer" changeLogFile="classpath:sql/1.1-changes/075-Dec-2015-until-next.xml"/>
        </preConditions>
        <dropForeignKeyConstraint constraintName="fk_hactevidence_answers" baseTableName="hactoutcomeevidencesurvey"/>
    </changeSet>
    <changeSet id="ECCO-1999-hact-evidencesurvey-unique-REVERT" author="adamjhamer">
        <preConditions onFail="MARK_RAN">
            <changeSetExecuted id="ECCO-1999-hact-evidencesurvey-unique" author="adamjhamer" changeLogFile="classpath:sql/1.1-changes/075-Dec-2015-until-next.xml"/>
        </preConditions>
        <dropUniqueConstraint constraintName="idx_hactevidencesurvey_uniq" tableName="hactoutcomeevidencesurvey"/>
    </changeSet>
    <changeSet id="ECCO-1999-hact-evidencesurvey-answer-REVERT" author="adamjhamer">
        <preConditions onFail="MARK_RAN">
            <changeSetExecuted id="ECCO-1999-hact-evidencesurvey-answer-fk" author="adamjhamer" changeLogFile="classpath:sql/1.1-changes/075-Dec-2015-until-next.xml"/>
        </preConditions>
        <dropColumn columnName="valuableAnswerChoiceDefId" tableName="hactoutcomeevidencesurvey"/>
    </changeSet>

    <changeSet id="ECCO-1772-hact-evidence-hactoutcome-fk2" author="adamjhamer">
        <addForeignKeyConstraint
                constraintName="fk_hactevidence_hactoutcomes"
                baseTableName="hactoutcomeevidencesurvey" baseColumnNames="hactOutcomeDefCode"
                referencedTableName="hactsocialvaluebank" referencedColumnNames="hactOutcomeDefCode" />
    </changeSet>
    <changeSet id="ECCO-1772-hact-evidence-question-fk2" author="adamjhamer">
        <addForeignKeyConstraint
                constraintName="fk_hactevidence_questions"
                baseTableName="hactoutcomeevidencesurvey" baseColumnNames="questionDefId"
                referencedTableName="questions" referencedColumnNames="id" />
    </changeSet>
    <changeSet id="ECCO-1999-hact-evidencesurvey-answers2" author="adamjhamer">
        <createTable tableName="hactsurvey_valuableanswers">
            <column name="hactsurveyId" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="questionanswerchoiceId" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet id="ECCO-1999-hact-evidencesurvey-answers-fk2" author="adamjhamer">
        <addForeignKeyConstraint
                constraintName="fk_hactevidence_answers"
                baseTableName="hactsurvey_valuableanswers" baseColumnNames="questionanswerchoiceId"
                referencedTableName="questionanswerchoices" referencedColumnNames="id" />
    </changeSet>
    <changeSet id="ECCO-1999-hact-evidencesurvey-answers-fk-survey" author="adamjhamer">
        <addForeignKeyConstraint
                constraintName="fk_hactevidence_surveyId"
                baseTableName="hactsurvey_valuableanswers" baseColumnNames="hactsurveyId"
                referencedTableName="hactoutcomeevidencesurvey" referencedColumnNames="id" />
    </changeSet>
    <changeSet id="ECCO-1999-hact-evidencesurvey-unique2" author="adamjhamer">
        <createIndex unique="true" tableName="hactoutcomeevidencesurvey" indexName="idx_hactevidencesurvey_uniq">
            <column name="hactOutcomeDefCode"/>
            <column name="questionDefId"/>
        </createIndex>
    </changeSet>

    <changeSet id="ECCO-1874-commandq-userdevices-fk" author="nealeu">
        <addForeignKeyConstraint constraintName="fk_cmdqueue_userdevices"
            baseTableName="commandqueue" baseColumnNames="userdevices_id"
            referencedTableName="userdevices" referencedColumnNames="id"
            deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
            referencesUniqueColumn="true"/>
    </changeSet>

    <changeSet id="ECCO-1921-add-textMap-column" author="nealeu">
        <addColumn tableName="referrals">
            <column name="textMap" type="CLOB"/>
        </addColumn>
    </changeSet>

    <changeSet id="ECCO-1921-populate-textMap" author="nealeu">
        <customChange class="com.ecco.infrastructure.liquibase.StringHashMapToJsonChange"
            tableName="referrals" primaryKeyColumns="id"
            srcColumnName="customStringData" dstColumnName="textMap"/>
    </changeSet>

    <changeSet id="ECCO-1925-add-textMap-column" author="nealeu">
        <addColumn tableName="supportthreatoutcomes">
            <column name="textMap" type="CLOB"/>
        </addColumn>
    </changeSet>

    <changeSet id="ECCO-1925-populate-textMap" author="nealeu">
        <customChange class="com.ecco.infrastructure.liquibase.StringHashMapToJsonChange"
            tableName="supportthreatoutcomes" primaryKeyColumns="id"
            srcColumnName="customStringData" dstColumnName="textMap"/>
    </changeSet>

    <changeSet id="ECCO-1979-reportdefs-friendlyName-col" author="nealeu">
        <addColumn tableName="reportdefinitions">
            <column name="friendlyName" type="VARCHAR(127)"/>
        </addColumn>
    </changeSet>

    <changeSet id="ECCO-1985-format-postcode-mysql-oracle" author="nealeu">
        <preConditions onFail="MARK_RAN">
             <or>
                <dbms type="mysql"/>
                <dbms type="oracle" />
             </or>
        </preConditions>
        <sql>
            UPDATE contacts SET addresspostcode = REPLACE( UPPER(addresspostcode), ' ', '');
            UPDATE contacts SET addresspostcode = CASE
              WHEN LENGTH(addresspostcode) &lt; 5 THEN addresspostcode
              ELSE CONCAT(
                CONCAT( SUBSTR(addresspostcode, 1, LENGTH(addresspostcode) - 3), ' '),
                  SUBSTR(addresspostcode, LENGTH(addresspostcode) - 2))
            END;
        </sql>
    </changeSet>

    <changeSet id="ECCO-1985-format-postcode-mssql" author="nealeu">
        <validCheckSum>7:0cb9202fbb791e938952028ad00a47a4</validCheckSum>
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql"/>
        </preConditions>
        <sql dbms="mssql">
            UPDATE contacts SET addresspostcode = REPLACE( UPPER(addresspostcode), ' ', '');

            UPDATE contacts SET addresspostcode = CASE
              WHEN LEN(addresspostcode) &lt; 5 THEN addresspostcode
              ELSE
                LEFT(addresspostcode, LEN(addresspostcode) - 3)
                  + ' '
                  + RIGHT(addresspostcode, 3)
            END;
        </sql>
    </changeSet>

    <!-- multipleReferral / family relationship -->
    <changeSet id="ECCO-2097-create" author="adamjhamer">
        <addColumn tableName="servicetypes">
            <column name="primaryRelationshipId" type="BIGINT"/>
        </addColumn>
    </changeSet>
    <changeSet id="ECCO-2097-fk" author="adamjhamer">
        <addForeignKeyConstraint baseColumnNames="primaryRelationshipId" baseTableName="servicetypes"
                                 referencedTableName="servicetypes" referencedColumnNames="id"
                                 constraintName="FK_REL_ST"
                                 deferrable="false" initiallyDeferred="false"
                                 onDelete="NO ACTION" onUpdate="NO ACTION" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet id="ECCO-2097-defaultOldValue" author="adamjhamer">
        <addDefaultValue tableName="servicetypes" columnName="multipleReferrals" defaultValueBoolean="false"/>
    </changeSet>

    <changeSet id="ECCO-2110-col-supportplanaction.eventId" author="nealeu">
        <addColumn tableName="supportplanwork">
            <column name="eventId" type="VARCHAR(255)"/>
        </addColumn>
    </changeSet>
    <changeSet id="ECCO-2110-FK-supportplanaction.eventId" author="nealeu">
        <!-- base schema has been changed retrospectively to specify unique=true on item_uid to avoid ORA-02270 -->
        <addForeignKeyConstraint baseTableName="supportplanwork" baseColumnNames="eventId"
                                 referencedTableName="cosmo_item" referencedColumnNames="item_uid"
                                 constraintName="FK_supp_pl_wk_event"
                                 deferrable="false" initiallyDeferred="false"
                                 onDelete="NO ACTION" onUpdate="NO ACTION" referencesUniqueColumn="true"/>
    </changeSet>

    <!-- ALL MYSQL here because mysql systems happen to use comment as 'goalName'-->
    <changeSet id="ECCO-2132-supportplanactions-comments-to-goalName" author="adamjhamer">
        <preConditions onFail="MARK_RAN">
            <not>
                <dbms type="oracle"/>
            </not>
        </preConditions>
        <renameColumn tableName="supportplanactions" oldColumnName="ga_comment" newColumnName="goalName" columnDataType="CLOB"/>
    </changeSet>
    <changeSet id="ECCO-2132-supporthractions-comments-to-goalName" author="adamjhamer">
        <preConditions onFail="MARK_RAN">
            <not>
                <dbms type="oracle"/>
            </not>
        </preConditions>
        <renameColumn tableName="supporthractions" oldColumnName="ga_comment" newColumnName="goalName" columnDataType="CLOB"/>
    </changeSet>
    <changeSet id="ECCO-2132-supportthreatactions-comments-to-goalName" author="adamjhamer">
        <preConditions onFail="MARK_RAN">
            <not>
                <dbms type="oracle"/>
            </not>
        </preConditions>
        <renameColumn tableName="supportthreatactions" oldColumnName="ga_comment" newColumnName="goalName" columnDataType="CLOB"/>
    </changeSet>
    <!-- ALL MYSQL here because mysql systems happen to use comment as 'goalName' and so needs the 'goalPlan' comment -->
    <changeSet id="ECCO-2133-supportplanactions-add-goalPlan" author="adamjhamer">
        <preConditions onFail="MARK_RAN">
            <not>
                <dbms type="oracle"/>
            </not>
        </preConditions>
        <addColumn tableName="supportplanactions" >
            <column name="goalPlan" type="CLOB"/>
        </addColumn>
    </changeSet>
    <changeSet id="ECCO-2133-supporthractions-add-goalPlan" author="adamjhamer">
        <preConditions onFail="MARK_RAN">
            <not>
                <dbms type="oracle"/>
            </not>
        </preConditions>
        <addColumn tableName="supporthractions" >
            <column name="goalPlan" type="CLOB"/>
        </addColumn>
    </changeSet>
    <changeSet id="ECCO-2133-supportthreatactions-add-goalPlan" author="adamjhamer">
        <preConditions onFail="MARK_RAN">
            <not>
                <dbms type="oracle"/>
            </not>
        </preConditions>
        <addColumn tableName="supportthreatactions" >
            <column name="goalPlan" type="CLOB"/>
        </addColumn>
    </changeSet>

    <!-- ALL ORACLE here because oracle systems happen to use comment as 'goalPlan'-->
    <changeSet id="ECCO-2132-supportplanactions-comments-to-goalPlan" author="adamjhamer">
        <preConditions onFail="MARK_RAN">
            <dbms type="oracle"/>
        </preConditions>
        <renameColumn tableName="supportplanactions" oldColumnName="ga_comment" newColumnName="goalPlan" columnDataType="CLOB"/>
    </changeSet>
    <changeSet id="ECCO-2132-supporthrnactions-comments-to-goalPlan" author="adamjhamer">
        <preConditions onFail="MARK_RAN">
            <dbms type="oracle"/>
        </preConditions>
        <renameColumn tableName="supporthractions" oldColumnName="ga_comment" newColumnName="goalPlan" columnDataType="CLOB"/>
    </changeSet>
    <changeSet id="ECCO-2132-supportthreatactions-comments-to-goalPlan" author="adamjhamer">
        <preConditions onFail="MARK_RAN">
            <dbms type="oracle"/>
        </preConditions>
        <renameColumn tableName="supportthreatactions" oldColumnName="ga_comment" newColumnName="goalPlan" columnDataType="CLOB"/>
    </changeSet>
    <!-- ALL ORACLE here because oralce systems happen to use comment as 'goalPlan' and so needs the 'goalName' comment -->
    <changeSet id="ECCO-2133-supportplanactions-add-goalName" author="adamjhamer">
        <preConditions onFail="MARK_RAN">
            <dbms type="oracle"/>
        </preConditions>
        <addColumn tableName="supportplanactions" >
            <column name="goalName" type="VARCHAR(384)"/>
        </addColumn>
    </changeSet>
    <changeSet id="ECCO-2133-supporthractions-add-goalName" author="adamjhamer">
        <preConditions onFail="MARK_RAN">
            <dbms type="oracle"/>
        </preConditions>
        <addColumn tableName="supporthractions" >
            <column name="goalName" type="VARCHAR(384)"/>
        </addColumn>
    </changeSet>
    <changeSet id="ECCO-2133-supportthreatactions-add-goalName" author="adamjhamer">
        <preConditions onFail="MARK_RAN">
            <dbms type="oracle"/>
        </preConditions>
        <addColumn tableName="supportthreatactions" >
            <column name="goalName" type="VARCHAR(384)"/>
        </addColumn>
    </changeSet>

    <!-- reduce the size of goalName in mysql -->
    <changeSet id="ECCO-2133-supportplanactions-reduce-goalName" author="adamjhamer">
        <preConditions onFail="MARK_RAN">
            <not>
                <dbms type="oracle"/>
            </not>
        </preConditions>
        <modifyDataType tableName="supportplanactions" columnName="goalName" newDataType="VARCHAR(384)"/>
    </changeSet>
    <changeSet id="ECCO-2133-supporthractions-reduce-goalName" author="adamjhamer">
        <preConditions onFail="MARK_RAN">
            <not>
                <dbms type="oracle"/>
            </not>
        </preConditions>
        <modifyDataType tableName="supporthractions" columnName="goalName" newDataType="VARCHAR(384)"/>
    </changeSet>
    <changeSet id="ECCO-2133-supportthreatactions-reduce-goalName" author="adamjhamer">
        <preConditions onFail="MARK_RAN">
            <not>
                <dbms type="oracle"/>
            </not>
        </preConditions>
        <modifyDataType tableName="supportthreatactions" columnName="goalName" newDataType="VARCHAR(384)"/>
    </changeSet>

    <changeSet id="ECCO-1463-fin_invoices-table" author="bodeng">
        <createTable tableName="fin_invoices">
            <column defaultValueNumeric="0" name="id" type="INT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="serviceRecipientId" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="invoiceDate" type="DATE">
                <constraints nullable="false"/>
            </column>
            <column name="posted" type="BOOLEAN" defaultValueNumeric="0">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addForeignKeyConstraint constraintName="fk_fin_invoice_srid"
            baseTableName="fin_invoices" baseColumnNames="serviceRecipientId"
            referencedTableName="servicerecipients" referencedColumnNames="id"
            deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
            referencesUniqueColumn="true"/>
        <createIndex tableName="fin_invoices" indexName="ix_fin_invoice_srid_date">
            <column name="serviceRecipientId"/>
            <column name="invoiceDate"/>
        </createIndex>
    </changeSet>

    <changeSet id="ECCO-1463-add-invoice-id-to-supportplanwork" author="bodeng">
        <addColumn tableName="supportplanwork">
            <column name="invoiceId" type="INT"/>
        </addColumn>
        <addForeignKeyConstraint baseTableName="supportplanwork" baseColumnNames="invoiceId"
                                 constraintName="fk_supp_pl_wk_invoice" onDelete="SET NULL" onUpdate="RESTRICT"
                                 referencedTableName="fin_invoices" referencedColumnNames="id"/>
    </changeSet>
    <changeSet id="ECCO-1463-fin_invoicelines" author="bodeng">
        <createTable tableName="fin_invoicelines">
            <column name="lineUuid" type="CHAR(36)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_fin_invoicelines"/>
            </column>
            <column name="invoiceId" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="line_order" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="workUuid" type="CHAR(36)"/>
            <column name="plannedDate" type="DATE"/>
            <column name="plannedMinutes" type="INT"/>
            <column name="actualDate" type="DATE"/>
            <column name="actualMinutes" type="INT"/>
            <column name="type" type="VARCHAR(255)"/>
            <column name="description" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="amount" type="DECIMAL(9,2)">
                <constraints nullable="false"/>
            </column>
            <column name="taxRate" type="DECIMAL(5,2)"/>
        </createTable>
        <createIndex unique="true" tableName="fin_invoicelines" indexName="idx_fin_invoicelines_uniq">
            <column name="invoiceId"/>
            <column name="line_order"/>
        </createIndex>
        <addForeignKeyConstraint baseTableName="fin_invoicelines" baseColumnNames="invoiceId"
                                 constraintName="fk_fin_invoicelines_invid" onDelete="CASCADE" onUpdate="CASCADE"
                                 referencedTableName="fin_invoices" referencedColumnNames="id"/>
        <addForeignKeyConstraint baseTableName="fin_invoicelines" baseColumnNames="workUuid"
                                 constraintName="fk_fin_invoicelines_workid" onDelete="RESTRICT" onUpdate="RESTRICT"
                                 referencedTableName="supportplanwork" referencedColumnNames="uuid"/>
    </changeSet>

    <changeSet id="DEV-1350-invoiceline-date-datetime" author="nealeu">
        <modifyDataType tableName="fin_invoicelines" columnName="plannedDate" newDataType="DATETIME"/>
        <modifyDataType tableName="fin_invoicelines" columnName="actualDate" newDataType="DATETIME"/>
    </changeSet>

    <changeSet id="ECCO-2161-funding-fundingAmount" author="adamjhamer">
        <addColumn tableName="referrals">
            <column name="fundingAmount" type="DECIMAL(9,2)"/>
        </addColumn>
    </changeSet>
    <changeSet id="ECCO-2161-funding-fundingReview" author="adamjhamer">
        <addColumn tableName="referrals">
            <column name="fundingReviewDate" type="DATE"/>
        </addColumn>
    </changeSet>

    <!-- updated changeset to separate the addColumn from createIndex, since index fails on mssql when doing addNotNullConstraint -->
    <changeSet id="ECCO-1310-add-uuid-to-supportplanactions-2a" author="nealeu">
        <preConditions onFail="MARK_RAN">
            <not>
                <changeSetExecuted id="ECCO-1310-add-uuid-to-supportplanactions" author="nealeu" changeLogFile="classpath:sql/1.1-changes/075-Dec-2015-until-next.xml"/>
            </not>
        </preConditions>
        <addColumn tableName="supportplanactions">
            <column name="actionInstanceUuid" type="CHAR(36)">
                <constraints nullable="true"/> <!-- Need to set false after we've done the 'magic' of generating them all -->
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="ECCO-1310-populate-supportplanactions-uuid" author="default">
        <customChange class="com.ecco.infrastructure.liquibase.AddUuidColumnChange"
                      tableName="supportplanactions" primaryKeyColumns="id" uniqueKeyColumns="serviceRecipientId,actionId"
                      newUuidColumn="actionInstanceUuid" />
    </changeSet>

    <changeSet id="ECCO-1310-nonull-on-supportplanactions-uuid" author="nealeu">
        <addNotNullConstraint tableName="supportplanactions" columnName="actionInstanceUuid" columnDataType="CHAR(36)"/>
    </changeSet>

    <!-- updated changeset to separate the addColumn from createIndex, since index fails on mssql when isong addNotNullConstraint -->
    <changeSet id="ECCO-1310-add-uuid-to-supportplanactions-2b" author="nealeu">
        <preConditions onFail="MARK_RAN">
            <not>
                <changeSetExecuted id="ECCO-1310-add-uuid-to-supportplanactions" author="nealeu" changeLogFile="classpath:sql/1.1-changes/075-Dec-2015-until-next.xml"/>
            </not>
        </preConditions>
        <createIndex tableName="supportplanactions" indexName="idx_su_pl_acts_ac_inst_uuid">
            <column name="actionInstanceUuid"/>
        </createIndex>
    </changeSet>

    <changeSet id="ECCO-2207-svcrec_commands-index" author="nealeu">
        <validCheckSum>7:de4b0964c72ce7cf1657cef26e2bbedc</validCheckSum>
        <preConditions onFail="MARK_RAN">
            <not>
                <indexExists tableName="svcrec_commands" indexName="idx_svcrec_cmd_srid_created"/>
            </not>
        </preConditions>
        <comment>Want indexes by order commands were applied to database</comment>
        <output target="WARN">Creating index on svcrec_commands. This may take a while</output>
        <createIndex tableName="svcrec_commands" indexName="idx_svcrec_cmd_srid_created">
            <column name="serviceRecipientId"/>
            <column name="created"/>
            <column name="id"/>
        </createIndex>
    </changeSet>

    <changeSet id="ECCO-2207-drop-fk_svcerc_cmds-rid" author="nealeu">
        <dropForeignKeyConstraint baseTableName="svcrec_commands" constraintName="fk_referralcmds_referrals"/>
    </changeSet>

    <changeSet id="ECCO-2207-drop-svcrec_cmds-rid-idx" author="nealeu">
        <preConditions onFail="MARK_RAN">
            <indexExists tableName="svcrec_commands" indexName="idx_referralcommands_rid"/>
        </preConditions>
        <dropIndex tableName="svcrec_commands" indexName="idx_referralcommands_rid"/>
    </changeSet>

    <changeSet id="ECCO-2207-drop-svcrec_cmds-rid-col" author="nealeu">
        <dropColumn tableName="svcrec_commands" columnName="referralId"/>
    </changeSet>

    <changeSet id="ECCO-2221-idx_fk_suppthreatwrk_srid_uuid" author="nealeu" dbms="oracle">
        <preConditions onFail="MARK_RAN">
            <not>
                <indexExists tableName="supportthreatwork" indexName="idx_fk_suppthreatwrk_srid_uuid"/>
            </not>
        </preConditions>
        <output target="WARN">Creating index on supportthreatwork. This may take a while...</output>
        <createIndex tableName="supportthreatwork" indexName="idx_fk_suppthreatwrk_srid_uuid">
            <column name="serviceRecipientId"/>
            <column name="uuid"/>
        </createIndex>
    </changeSet>
    <changeSet id="ECCO-2221-idx_fk_suppthreatoutcomes_workuuid" author="nealeu" dbms="oracle">
        <preConditions onFail="MARK_RAN">
            <not>
                <indexExists tableName="supportthreatoutcomes" indexName="idx_fk_s_t_outcomes_workuuid"/>
            </not>
        </preConditions>
        <output target="WARN">Creating index on supportthreatoutcomes. This may take a while...</output>
        <createIndex tableName="supportthreatoutcomes" indexName="idx_fk_s_t_outcomes_workuuid">
            <column name="workUuid"/>
        </createIndex>
    </changeSet>

    <changeSet id="ECCO-2222-idx_fk_supportthreatflags_workuuid" author="nealeu" dbms="oracle">
        <preConditions onFail="MARK_RAN">
            <not>
                <indexExists tableName="supportthreatflags" indexName="idx_fk_s_t_flags_workuuid"/>
            </not>
        </preConditions>
        <output target="WARN">Creating index on supportthreatflags. This may take a while...</output>
        <createIndex tableName="supportthreatflags" indexName="idx_fk_s_t_flags_workuuid">
            <column name="workUuid"/>
        </createIndex>
    </changeSet>


    <changeSet id="ECCO-2222-idx_fk_supportthreatcomments_workuuid" author="nealeu" dbms="oracle">
        <preConditions onFail="MARK_RAN">
            <not>
                <indexExists tableName="supportthreatcomments" indexName="idx_fk_s_t_comments_workuuid"/>
            </not>
        </preConditions>
        <output target="WARN">Creating index on supportthreatcomments. This may take a while...</output>
        <createIndex tableName="supportthreatcomments" indexName="idx_fk_s_t_comments_workuuid">
            <column name="workUuid"/>
        </createIndex>
    </changeSet>

    <changeSet id="ECCO-2222-idx_fk_supportthreatactions_workuuid" author="nealeu" dbms="oracle">
        <preConditions onFail="MARK_RAN">
            <not>
                <indexExists tableName="supportthreatactions" indexName="idx_fk_s_t_actions_workuuid"/>
            </not>
        </preConditions>
        <output target="WARN">Creating index on supportthreatactions. This may take a while...</output>
        <createIndex tableName="supportthreatactions" indexName="idx_fk_s_t_actions_workuuid">
            <column name="workUuid"/>
        </createIndex>
    </changeSet>

    <changeSet id="ECCO-2222-idx_fk_supportplancomments_threatworkuuid" author="nealeu" dbms="oracle">
        <preConditions onFail="MARK_RAN">
            <not>
                <indexExists tableName="supportplancomments" indexName="idx_fk_s_t_comments_t_workuuid"/>
            </not>
        </preConditions>
        <output target="WARN">Creating index on supportplancomments. This may take a while...</output>
        <createIndex tableName="supportplancomments" indexName="idx_fk_s_t_comments_t_workuuid">
            <column name="threatWorkUuid"/>
        </createIndex>
    </changeSet>

    <!-- Shouldn't end up running on Oracle because no Activiti in use on Oracle -->
    <changeSet id="ECCO-2260-migrate-ACT_BIZ_KEY-to-srId" author="nealeu">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="ACT_RU_EXECUTION"/>
        </preConditions>
        <update tableName="ACT_RU_EXECUTION">
            <column name="BUSINESS_KEY_"
                valueComputed="(SELECT serviceRecipientId FROM referrals WHERE id = ACT_RU_EXECUTION.BUSINESS_KEY_)"/>
        </update>
    </changeSet>

    <changeSet id="ECCO-2259-add-svcrecs.currentTaskId" author="nealeu">
        <addColumn tableName="servicerecipients">
            <column name="currentTaskId" type="BIGINT">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <addForeignKeyConstraint constraintName="fk_svcrecs_currentTaskId"
                                 baseTableName="servicerecipients" baseColumnNames="currentTaskId"
                                 referencedTableName="referralaspects" referencedColumnNames="id"
                                 deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
                                 referencesUniqueColumn="true"/>
    </changeSet>

    <changeSet id="ECCO-2194-svcrec_commands_archive-table" author="adamjhamer">
        <createTable tableName="svcrec_commands_archive">
            <column name="uuid" type="CHAR(36)">
                <constraints nullable="true"/>
            </column>
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="created" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column name="commandCreated" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column name="commandname" type="VARCHAR(20)">
                <constraints nullable="false"/>
            </column>
            <column name="serviceRecipientId" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="body" type="CLOB">
                <constraints nullable="false"/>
            </column>
            <column name="userid" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="evidencegroupkey" type="VARCHAR(40)">
                <constraints nullable="true"/>
            </column>
            <column name="actiondefid" type="BIGINT">
                <constraints nullable="true"/>
            </column>
            <column name="areadefid" type="BIGINT">
                <constraints nullable="true"/>
            </column>
            <column name="taskName" type="VARCHAR(47)"/> <!-- current max(length(name)) in referralaspects is 32 -->
            <column name="singleValueKey" type="VARCHAR(64)">
                <constraints nullable="true"/>
            </column>
            <column name="questionDefId" type="BIGINT">
                <constraints nullable="true"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet id="ECCO-2194-svcrec_cmdarc-actionDef-FK" author="adamjhamer">
        <addForeignKeyConstraint
                constraintName="fk_svcrec_cmdarc_actiondef"
                baseTableName="svcrec_commands_archive" baseColumnNames="actiondefid"
                referencedTableName="actions" referencedColumnNames="id" />
    </changeSet>
    <changeSet id="ECCO-2194-svcrec_cmdarc-user-FK" author="adamjhamer">
        <addForeignKeyConstraint
                constraintName="fk_svcrec_cmdarc_user"
                baseTableName="svcrec_commands_archive" baseColumnNames="userid"
                referencedTableName="users" referencedColumnNames="id" />
    </changeSet>
    <changeSet id="ECCO-2194-questionanswer-FK" author="adamjhamer">
        <addForeignKeyConstraint
                constraintName="fk_svcrec_cmdarc_question"
                baseTableName="svcrec_commands_archive" baseColumnNames="questionDefId"
                referencedTableName="questions" referencedColumnNames="id" />
    </changeSet>
    <!-- MISS OUT the svcrec_commands_archive -> servicerecipients FK as this doesn't work when deleting data -->
    <changeSet id="ECCO-2194-svcrec_cmdarc-created-index" author="adamjhamer">
        <createIndex tableName="svcrec_commands_archive" indexName="idx_svcrec_cmdarc_created">
            <column name="created"/>
            <column name="id"/>
        </createIndex>
    </changeSet>
    <changeSet id="ECCO-2194-svcrec_cmdarc-srid-index" author="adamjhamer">
        <createIndex tableName="svcrec_commands_archive" indexName="idx_svcrec_cmdarc_srid_created">
            <column name="serviceRecipientId"/>
            <column name="created"/>
            <column name="id"/>
        </createIndex>
    </changeSet>
    <changeSet id="ECCO-2194-svcrec_cmdarc-uuid-index" author="adamjhamer">
        <createIndex tableName="svcrec_commands_archive" indexName="idx_svcrec_cmdarc_uuid">
            <column name="uuid"/>
        </createIndex>
    </changeSet>

    <changeSet id="ECCO-2259-add-svc-rec-taskIndex" author="nealeu">
        <addColumn tableName="servicerecipients">
            <column name="currentTaskIndex" type="INT" defaultValueNumeric="0">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="ECCO-2259-populate-svc-rec-taskIndex" author="nealeu">
        <!-- if we've already run this id, then treat this new checkSum as valid -->
        <validCheckSum>7:b3ef0009e00bf2c17cb638f7da00f7b0</validCheckSum>
        <update tableName="servicerecipients">
            <column name="currentTaskIndex"
                    valueComputed="(SELECT currentreferralaspect FROM referrals WHERE referrals.serviceRecipientId = servicerecipients.id)"/>
            <where>currentTaskIndex=0 AND EXISTS (SELECT * FROM referrals WHERE referrals.serviceRecipientId = servicerecipients.id)</where>
        </update>
    </changeSet>

    <!--<changeSet id="ECCO-2259-drop-currentreferralaspect-col" author="nealeu">-->
        <!--<dropColumn tableName="referrals" columnName="currentreferralaspect" />-->
    <!--</changeSet>-->
    <changeSet id="ECCO-2259-allow-currentreferralaspect-col" author="adamjhamer">
        <preConditions onFail="MARK_RAN">
            <columnExists tableName="referrals" columnName="currentreferralaspect"/>
        </preConditions>
        <dropNotNullConstraint tableName="referrals" columnName="currentreferralaspect" columnDataType="INT"/>
    </changeSet>

    <changeSet id="ECCO-1997-add-objectMap-column" author="adamjhamer">
        <addColumn tableName="referrals">
            <column name="objectMap" type="CLOB"/>
        </addColumn>
    </changeSet>
    <changeSet id="ECCO-1997-populate-objectMap" author="adamjhamer">
        <customChange class="com.ecco.infrastructure.liquibase.StringHashMapToJsonChange"
                      tableName="referrals" primaryKeyColumns="id"
                      srcColumnName="customObjectData" dstColumnName="objectMap"/>
    </changeSet>

    <changeSet id="ECCO-2250-add-textMap-clientdetails-column" author="adamjhamer">
        <addColumn tableName="clientdetails">
            <column name="textMap2" type="CLOB"/>
        </addColumn>
    </changeSet>
    <changeSet id="ECCO-2250-add-textMap-workers-column" author="adamjhamer">
        <addColumn tableName="workers">
            <column name="textMap2" type="CLOB"/>
        </addColumn>
    </changeSet>
    <changeSet id="ECCO-2250-populate-clientdetails-textMap" author="adamjhamer">
        <customChange class="com.ecco.infrastructure.liquibase.StringHashMapToJsonChange"
                      tableName="clientdetails" primaryKeyColumns="id"
                      srcColumnName="textMap" dstColumnName="textMap2"/>
    </changeSet>
    <changeSet id="ECCO-2250-populate-workers-textMap" author="adamjhamer">
        <customChange class="com.ecco.infrastructure.liquibase.StringHashMapToJsonChange"
                      tableName="workers" primaryKeyColumns="id"
                      srcColumnName="textMap" dstColumnName="textMap2"/>
    </changeSet>

    <changeSet id="ECCO-2250-add-dateMap-clientdetails-column" author="adamjhamer">
        <addColumn tableName="clientdetails">
            <column name="dateMap2" type="CLOB"/>
        </addColumn>
    </changeSet>
    <changeSet id="ECCO-2250-add-dateMap-workers-column" author="adamjhamer">
        <addColumn tableName="workers">
            <column name="dateMap2" type="CLOB"/>
        </addColumn>
    </changeSet>
    <changeSet id="ECCO-2250-add-dateMap-referrrals-column" author="adamjhamer">
        <addColumn tableName="referrals">
            <column name="dateMap2" type="CLOB"/>
        </addColumn>
    </changeSet>

    <!--
    StringHashMapToJsonChange is sufficient for saving dates because the ObjectMapper
    is configured with a dateFormat, and 'readValue' is not invoked which causes runtime
    type erasure
    -->
    <changeSet id="ECCO-2250-populate-clientdetails-dateMap" author="adamjhamer">
        <customChange class="com.ecco.infrastructure.liquibase.StringHashMapToJsonChange"
                      tableName="clientdetails" primaryKeyColumns="id"
                      srcColumnName="dateMap" dstColumnName="dateMap2"/>
    </changeSet>
    <changeSet id="ECCO-2250-populate-workers-dateMap" author="adamjhamer">
        <customChange class="com.ecco.infrastructure.liquibase.StringHashMapToJsonChange"
                      tableName="workers" primaryKeyColumns="id"
                      srcColumnName="dateMap" dstColumnName="dateMap2"/>
    </changeSet>
    <changeSet id="ECCO-2250-populate-referrals-dateMap" author="adamjhamer">
        <customChange class="com.ecco.infrastructure.liquibase.StringHashMapToJsonChange"
                      tableName="referrals" primaryKeyColumns="id"
                      srcColumnName="dateMap" dstColumnName="dateMap2"/>
    </changeSet>

    <changeSet id="ECCO-2242-add-dateOfDeath-clientdetails-column" author="adamjhamer">
        <addColumn tableName="clientdetails">
            <column name="dateOfDeath" type="DATE"/>
        </addColumn>
    </changeSet>
    <changeSet id="ECCO-2242-add-dateOfDeath-workers-column" author="adamjhamer">
        <addColumn tableName="workers">
            <column name="dateOfDeath" type="DATE"/>
        </addColumn>
    </changeSet>

    <changeSet id="ECCO-2242-populate-dateOfDeath-clientsonly" author="adamjhamer">
        <preConditions onFail="MARK_RAN">
            <not>
                <dbms type="mssql"/>
            </not>
        </preConditions>
        <customChange class="com.ecco.infrastructure.liquibase.JsonMapToFieldChange"
                      tableName="clientdetails" primaryKeyColumns="id"
                      srcColumnName="dateMap2" srcMapKey="date of death"
                      dstColumnName="dateOfDeath"
                      removeOriginal="true"/>
    </changeSet>

    <changeSet id="ECCO-2277-activitytype-groupsupport-table" author="adamjhamer">
        <createTable tableName="gsat_linkedquestiongroups">
            <column name="groupsupportactivitytypeId" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="questiongroupId" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="ECCO-2277-qg-pk" author="adamjhamer">
        <addPrimaryKey columnNames="groupsupportactivitytypeid, questiongroupId" tableName="gsat_linkedquestiongroups"/>
    </changeSet>
    <changeSet id="ECCO-2277-qg-fk1" author="adamjhamer">
        <addForeignKeyConstraint baseColumnNames="groupsupportactivitytypeId" baseTableName="gsat_linkedquestiongroups" constraintName="fk_gsatlqg_gsqg" referencedColumnNames="id" referencedTableName="grp_activitytypes"/>
    </changeSet>
    <changeSet id="ECCO-2277-qg-fk2" author="adamjhamer">
        <addForeignKeyConstraint baseColumnNames="questiongroupId" baseTableName="gsat_linkedquestiongroups" constraintName="fk_gsatlqg_questiongroups" referencedColumnNames="id" referencedTableName="questiongroups"/>
    </changeSet>

    <changeSet id="ECCO-2277-add-evidencegroup" author="adamjhamer">
        <validCheckSum>7:432e457e06282dd488ef0b7db15f6bf2</validCheckSum>
        <addColumn tableName="gsat_linkedquestiongroups">
            <column name="evidencegroupkey" type="VARCHAR(255)"/>
        </addColumn>
    </changeSet>
    <changeSet id="ECCO-2277-add-evidencegroup-notnull" author="adamjhamer">
        <addNotNullConstraint tableName="gsat_linkedquestiongroups" columnName="evidencegroupkey" columnDataType="VARCHAR(255)"/>
    </changeSet>

    <!-- unique constraint creates an index in oracle and mysql which is required for a foreign key -->
    <!-- see 20140218_liquibase_log_analysis.txt -->
    <changeSet id="ECCO-2277-referralaspects-notnull-name" author="adamjhamer">
        <addNotNullConstraint tableName="referralaspects" columnName="name" columnDataType="VARCHAR(255)"/>
    </changeSet>
    <changeSet id="ECCO-2277-referralaspects-uniq-name" author="adamjhamer">
        <addUniqueConstraint tableName="referralaspects" columnNames="name"/>
    </changeSet>

    <changeSet id="ECCO-2277-gsat_linkedquestiongroups-fk-name" author="adamjhamer">
        <addForeignKeyConstraint baseTableName="gsat_linkedquestiongroups" baseColumnNames="evidencegroupkey" constraintName="fk_gsatlqg_evidencegroup" referencedTableName="referralaspects"
                                 referencedColumnNames="name"/>
    </changeSet>

    <!-- Have 'workerattachment' mimic 'referralattachment' -->
    <changeSet author="adamjhamer" id="DEV-35-evidence-attachments-addShowOnWorker">
        <!-- update what's there with showOnWorker, since we've had no other working solution on evidence pages -->
        <validCheckSum>7:0dcd4d77e9450331e4674641fc4e84cf</validCheckSum>
        <addColumn tableName="workerattachments">
            <column name="showOnWorker" type="BOOLEAN" valueBoolean="true" />
        </addColumn>
    </changeSet>
    <changeSet author="adamjhamer" id="DEV-35-evidence-attachments-allowAny1">
        <addColumn tableName="workerattachments">
            <column name="evidencePage" type="VARCHAR(255)"/>
        </addColumn>
    </changeSet>
    <changeSet author="adamjhamer" id="DEV-35-evidence-attachments-allowAny2">
        <addColumn tableName="workerattachments">
            <column name="evidencePageGroup" type="VARCHAR(255)"/>
        </addColumn>
    </changeSet>

    <changeSet id="DEV-35-add-workerattachment-svcRecId-nullable-cols" author="adamjhamer">
        <addColumn tableName="workerattachments">
            <column name="serviceRecipientId" type="INT"/>
        </addColumn>
    </changeSet>
    <changeSet id="DEV-35-populate-svcRecId" author="adamjhamer">
        <update tableName="workerattachments">
            <column name="serviceRecipientId"
                    valueComputed="(SELECT serviceRecipientId FROM workers WHERE workers.id = workerattachments.workerId)"/>
        </update>
        <addForeignKeyConstraint constraintName="fk_workerattachments_svcrec"
                                 baseTableName="workerattachments" baseColumnNames="serviceRecipientId"
                                 referencedTableName="servicerecipients" referencedColumnNames="id"
                                 deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
                                 referencesUniqueColumn="true"/>
    </changeSet>

    <changeSet id="DEV-35-add-notNull-constraint" author="adamjhamer">
        <addNotNullConstraint tableName="workerattachments" columnName="serviceRecipientId" columnDataType="INT"/>
    </changeSet>
    <changeSet id="DEV-35-serviceRecipientId-idx" author="adamjhamer">
        <createIndex tableName="workerattachments" indexName="idx_workerattachments_srid">
            <column name="serviceRecipientId"/>
        </createIndex>
    </changeSet>
    <changeSet id="DEV-35-remove-workerId-col" author="adamjhamer">
        <dropForeignKeyConstraint baseTableName="workerattachments" constraintName="FK135EFD72FF50C098"/>
        <dropColumn tableName="workerattachments" columnName="workerId"/>
    </changeSet>


    <changeSet id="ECCO-2290-location" author="adamjhamer">
        <addColumn tableName="supportplancomments">
            <column name="locationId" type="INT">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="ECCO-2290-location-FK" author="adamjhamer">
        <addForeignKeyConstraint constraintName="fk_supportL_lists" baseTableName="supportplancomments"
                                 baseColumnNames="locationId" referencedTableName="cfg_list_definitions" referencedColumnNames="id" />
    </changeSet>

    <changeSet id="DEV-52-add-svc-rec-slaTasks" author="nealeu">
        <addColumn tableName="servicerecipients">
            <column name="nextDueSlaTaskId" type="BIGINT"/>
            <column name="nextSlaDueDate" type="DATE"/>
        </addColumn>
    </changeSet>

    <changeSet id="DEV-52-remove-referrals.created" author="nealeu">
        <dropColumn tableName="referrals" columnName="created"/>
    </changeSet>
    <changeSet id="DEV-52-remove-workers.created" author="nealeu">
        <dropColumn tableName="workers" columnName="created"/>
    </changeSet>

    <changeSet id="DEV-93-supportthreatactions-increaseSize" author="adamjhamer">
        <modifyDataType tableName="supportthreatactions" columnName="hazard" newDataType="VARCHAR(1500)"/>
        <modifyDataType tableName="supportthreatactions" columnName="intervention" newDataType="VARCHAR(1500)"/>
    </changeSet>

    <!-- outcome star: family - early years -->
    <changeSet id="DEV-122-outcomestar-example-qac-v2" author="adamjhamer" context="1.1-base-data">
        <insert tableName="questionanswerchoices">
            <column name="id" valueNumeric="313"/>
            <column name="version" valueNumeric="0"/>
            <column name="displayImage"/>
            <column name="hiddenDefault" valueBoolean="false"/>
            <column name="displayValue" value="1"/>
            <column name="value" value="01"/>
        </insert>
        <insert tableName="questionanswerchoices">
            <column name="id" valueNumeric="314"/>
            <column name="version" valueNumeric="0"/>
            <column name="displayImage"/>
            <column name="hiddenDefault" valueBoolean="false"/>
            <column name="displayValue" value="2"/>
            <column name="value" value="02"/>
        </insert>
        <insert tableName="questionanswerchoices">
            <column name="id" valueNumeric="315"/>
            <column name="version" valueNumeric="0"/>
            <column name="displayImage"/>
            <column name="hiddenDefault" valueBoolean="false"/>
            <column name="displayValue" value="3"/>
            <column name="value" value="03"/>
        </insert>
        <insert tableName="questionanswerchoices">
            <column name="id" valueNumeric="316"/>
            <column name="version" valueNumeric="0"/>
            <column name="displayImage"/>
            <column name="hiddenDefault" valueBoolean="false"/>
            <column name="displayValue" value="4"/>
            <column name="value" value="04"/>
        </insert>
        <insert tableName="questionanswerchoices">
            <column name="id" valueNumeric="317"/>
            <column name="version" valueNumeric="0"/>
            <column name="displayImage"/>
            <column name="hiddenDefault" valueBoolean="false"/>
            <column name="displayValue" value="5"/>
            <column name="value" value="05"/>
        </insert>
        <insert tableName="questionanswerchoices">
            <column name="id" valueNumeric="318"/>
            <column name="version" valueNumeric="0"/>
            <column name="displayImage"/>
            <column name="hiddenDefault" valueBoolean="false"/>
            <column name="displayValue" value="6"/>
            <column name="value" value="06"/>
        </insert>
        <insert tableName="questionanswerchoices">
            <column name="id" valueNumeric="319"/>
            <column name="version" valueNumeric="0"/>
            <column name="displayImage"/>
            <column name="hiddenDefault" valueBoolean="false"/>
            <column name="displayValue" value="7"/>
            <column name="value" value="07"/>
        </insert>
        <insert tableName="questionanswerchoices">
            <column name="id" valueNumeric="320"/>
            <column name="version" valueNumeric="0"/>
            <column name="displayImage"/>
            <column name="hiddenDefault" valueBoolean="false"/>
            <column name="displayValue" value="8"/>
            <column name="value" value="08"/>
        </insert>
        <insert tableName="questionanswerchoices">
            <column name="id" valueNumeric="321"/>
            <column name="version" valueNumeric="0"/>
            <column name="displayImage"/>
            <column name="hiddenDefault" valueBoolean="false"/>
            <column name="displayValue" value="9"/>
            <column name="value" value="09"/>
        </insert>
        <insert tableName="questionanswerchoices">
            <column name="id" valueNumeric="322"/>
            <column name="version" valueNumeric="0"/>
            <column name="displayImage"/>
            <column name="hiddenDefault" valueBoolean="false"/>
            <column name="displayValue" value="10"/>
            <column name="value" value="10"/>
        </insert>
    </changeSet>
    <changeSet id="DEV-122-outcomestar-example-q" author="adamjhamer" context="1.1-base-data">
        <insert tableName="questions">
            <column name="id" valueNumeric="826"/>
            <column name="version" valueNumeric="0"/>
            <column name="disabled" valueBoolean="false"/>
            <column name="name" value="physical health"/>
        </insert>
        <insert tableName="questions">
            <column name="id" valueNumeric="827"/>
            <column name="version" valueNumeric="0"/>
            <column name="disabled" valueBoolean="false"/>
            <column name="name" value="emotional health"/>
        </insert>
        <insert tableName="questions">
            <column name="id" valueNumeric="828"/>
            <column name="version" valueNumeric="0"/>
            <column name="disabled" valueBoolean="false"/>
            <column name="name" value="keeping your children safe"/>
        </insert>
        <insert tableName="questions">
            <column name="id" valueNumeric="829"/>
            <column name="version" valueNumeric="0"/>
            <column name="disabled" valueBoolean="false"/>
            <column name="name" value="social networks"/>
        </insert>
        <insert tableName="questions">
            <column name="id" valueNumeric="830"/>
            <column name="version" valueNumeric="0"/>
            <column name="disabled" valueBoolean="false"/>
            <column name="name" value="boundaries and routines"/>
        </insert>
        <insert tableName="questions">
            <column name="id" valueNumeric="831"/>
            <column name="version" valueNumeric="0"/>
            <column name="disabled" valueBoolean="false"/>
            <column name="name" value="child development"/>
        </insert>
        <insert tableName="questions">
            <column name="id" valueNumeric="832"/>
            <column name="version" valueNumeric="0"/>
            <column name="disabled" valueBoolean="false"/>
            <column name="name" value="home, money and work"/>
        </insert>
    </changeSet>
    <changeSet id="DEV-122-outcomestar-example-q-qac-v2" author="adamjhamer" context="1.1-base-data">
        <insert tableName="questions_questionanswrchoices">
            <column name="questionId" valueNumeric="826"/>
            <column name="questionanswerchoiceId" valueNumeric="313"/>
        </insert>
        <insert tableName="questions_questionanswrchoices">
            <column name="questionId" valueNumeric="826"/>
            <column name="questionanswerchoiceId" valueNumeric="314"/>
        </insert>
        <insert tableName="questions_questionanswrchoices">
            <column name="questionId" valueNumeric="826"/>
            <column name="questionanswerchoiceId" valueNumeric="315"/>
        </insert>
        <insert tableName="questions_questionanswrchoices">
            <column name="questionId" valueNumeric="826"/>
            <column name="questionanswerchoiceId" valueNumeric="316"/>
        </insert>
        <insert tableName="questions_questionanswrchoices">
            <column name="questionId" valueNumeric="826"/>
            <column name="questionanswerchoiceId" valueNumeric="317"/>
        </insert>
        <insert tableName="questions_questionanswrchoices">
            <column name="questionId" valueNumeric="826"/>
            <column name="questionanswerchoiceId" valueNumeric="318"/>
        </insert>
        <insert tableName="questions_questionanswrchoices">
            <column name="questionId" valueNumeric="826"/>
            <column name="questionanswerchoiceId" valueNumeric="319"/>
        </insert>
        <insert tableName="questions_questionanswrchoices">
            <column name="questionId" valueNumeric="826"/>
            <column name="questionanswerchoiceId" valueNumeric="320"/>
        </insert>
        <insert tableName="questions_questionanswrchoices">
            <column name="questionId" valueNumeric="826"/>
            <column name="questionanswerchoiceId" valueNumeric="321"/>
        </insert>
        <insert tableName="questions_questionanswrchoices">
            <column name="questionId" valueNumeric="826"/>
            <column name="questionanswerchoiceId" valueNumeric="322"/>
        </insert>
        <insert tableName="questions_questionanswrchoices">
            <column name="questionId" valueNumeric="827"/>
            <column name="questionanswerchoiceId" valueNumeric="313"/>
        </insert>
        <insert tableName="questions_questionanswrchoices">
            <column name="questionId" valueNumeric="827"/>
            <column name="questionanswerchoiceId" valueNumeric="314"/>
        </insert>
        <insert tableName="questions_questionanswrchoices">
            <column name="questionId" valueNumeric="827"/>
            <column name="questionanswerchoiceId" valueNumeric="315"/>
        </insert>
        <insert tableName="questions_questionanswrchoices">
            <column name="questionId" valueNumeric="827"/>
            <column name="questionanswerchoiceId" valueNumeric="316"/>
        </insert>
        <insert tableName="questions_questionanswrchoices">
            <column name="questionId" valueNumeric="827"/>
            <column name="questionanswerchoiceId" valueNumeric="317"/>
        </insert>
        <insert tableName="questions_questionanswrchoices">
            <column name="questionId" valueNumeric="827"/>
            <column name="questionanswerchoiceId" valueNumeric="318"/>
        </insert>
        <insert tableName="questions_questionanswrchoices">
            <column name="questionId" valueNumeric="827"/>
            <column name="questionanswerchoiceId" valueNumeric="319"/>
        </insert>
        <insert tableName="questions_questionanswrchoices">
            <column name="questionId" valueNumeric="827"/>
            <column name="questionanswerchoiceId" valueNumeric="320"/>
        </insert>
        <insert tableName="questions_questionanswrchoices">
            <column name="questionId" valueNumeric="827"/>
            <column name="questionanswerchoiceId" valueNumeric="321"/>
        </insert>
        <insert tableName="questions_questionanswrchoices">
            <column name="questionId" valueNumeric="827"/>
            <column name="questionanswerchoiceId" valueNumeric="322"/>
        </insert>
        <insert tableName="questions_questionanswrchoices">
            <column name="questionId" valueNumeric="828"/>
            <column name="questionanswerchoiceId" valueNumeric="313"/>
        </insert>
        <insert tableName="questions_questionanswrchoices">
            <column name="questionId" valueNumeric="828"/>
            <column name="questionanswerchoiceId" valueNumeric="314"/>
        </insert>
        <insert tableName="questions_questionanswrchoices">
            <column name="questionId" valueNumeric="828"/>
            <column name="questionanswerchoiceId" valueNumeric="315"/>
        </insert>
        <insert tableName="questions_questionanswrchoices">
            <column name="questionId" valueNumeric="828"/>
            <column name="questionanswerchoiceId" valueNumeric="316"/>
        </insert>
        <insert tableName="questions_questionanswrchoices">
            <column name="questionId" valueNumeric="828"/>
            <column name="questionanswerchoiceId" valueNumeric="317"/>
        </insert>
        <insert tableName="questions_questionanswrchoices">
            <column name="questionId" valueNumeric="828"/>
            <column name="questionanswerchoiceId" valueNumeric="318"/>
        </insert>
        <insert tableName="questions_questionanswrchoices">
            <column name="questionId" valueNumeric="828"/>
            <column name="questionanswerchoiceId" valueNumeric="319"/>
        </insert>
        <insert tableName="questions_questionanswrchoices">
            <column name="questionId" valueNumeric="828"/>
            <column name="questionanswerchoiceId" valueNumeric="320"/>
        </insert>
        <insert tableName="questions_questionanswrchoices">
            <column name="questionId" valueNumeric="828"/>
            <column name="questionanswerchoiceId" valueNumeric="321"/>
        </insert>
        <insert tableName="questions_questionanswrchoices">
            <column name="questionId" valueNumeric="828"/>
            <column name="questionanswerchoiceId" valueNumeric="322"/>
        </insert>
        <insert tableName="questions_questionanswrchoices">
            <column name="questionId" valueNumeric="829"/>
            <column name="questionanswerchoiceId" valueNumeric="313"/>
        </insert>
        <insert tableName="questions_questionanswrchoices">
            <column name="questionId" valueNumeric="829"/>
            <column name="questionanswerchoiceId" valueNumeric="314"/>
        </insert>
        <insert tableName="questions_questionanswrchoices">
            <column name="questionId" valueNumeric="829"/>
            <column name="questionanswerchoiceId" valueNumeric="315"/>
        </insert>
        <insert tableName="questions_questionanswrchoices">
            <column name="questionId" valueNumeric="829"/>
            <column name="questionanswerchoiceId" valueNumeric="316"/>
        </insert>
        <insert tableName="questions_questionanswrchoices">
            <column name="questionId" valueNumeric="829"/>
            <column name="questionanswerchoiceId" valueNumeric="317"/>
        </insert>
        <insert tableName="questions_questionanswrchoices">
            <column name="questionId" valueNumeric="829"/>
            <column name="questionanswerchoiceId" valueNumeric="318"/>
        </insert>
        <insert tableName="questions_questionanswrchoices">
            <column name="questionId" valueNumeric="829"/>
            <column name="questionanswerchoiceId" valueNumeric="319"/>
        </insert>
        <insert tableName="questions_questionanswrchoices">
            <column name="questionId" valueNumeric="829"/>
            <column name="questionanswerchoiceId" valueNumeric="320"/>
        </insert>
        <insert tableName="questions_questionanswrchoices">
            <column name="questionId" valueNumeric="829"/>
            <column name="questionanswerchoiceId" valueNumeric="321"/>
        </insert>
        <insert tableName="questions_questionanswrchoices">
            <column name="questionId" valueNumeric="829"/>
            <column name="questionanswerchoiceId" valueNumeric="322"/>
        </insert>
        <insert tableName="questions_questionanswrchoices">
            <column name="questionId" valueNumeric="830"/>
            <column name="questionanswerchoiceId" valueNumeric="313"/>
        </insert>
        <insert tableName="questions_questionanswrchoices">
            <column name="questionId" valueNumeric="830"/>
            <column name="questionanswerchoiceId" valueNumeric="314"/>
        </insert>
        <insert tableName="questions_questionanswrchoices">
            <column name="questionId" valueNumeric="830"/>
            <column name="questionanswerchoiceId" valueNumeric="315"/>
        </insert>
        <insert tableName="questions_questionanswrchoices">
            <column name="questionId" valueNumeric="830"/>
            <column name="questionanswerchoiceId" valueNumeric="316"/>
        </insert>
        <insert tableName="questions_questionanswrchoices">
            <column name="questionId" valueNumeric="830"/>
            <column name="questionanswerchoiceId" valueNumeric="317"/>
        </insert>
        <insert tableName="questions_questionanswrchoices">
            <column name="questionId" valueNumeric="830"/>
            <column name="questionanswerchoiceId" valueNumeric="318"/>
        </insert>
        <insert tableName="questions_questionanswrchoices">
            <column name="questionId" valueNumeric="830"/>
            <column name="questionanswerchoiceId" valueNumeric="319"/>
        </insert>
        <insert tableName="questions_questionanswrchoices">
            <column name="questionId" valueNumeric="830"/>
            <column name="questionanswerchoiceId" valueNumeric="320"/>
        </insert>
        <insert tableName="questions_questionanswrchoices">
            <column name="questionId" valueNumeric="830"/>
            <column name="questionanswerchoiceId" valueNumeric="321"/>
        </insert>
        <insert tableName="questions_questionanswrchoices">
            <column name="questionId" valueNumeric="830"/>
            <column name="questionanswerchoiceId" valueNumeric="322"/>
        </insert>
        <insert tableName="questions_questionanswrchoices">
            <column name="questionId" valueNumeric="831"/>
            <column name="questionanswerchoiceId" valueNumeric="313"/>
        </insert>
        <insert tableName="questions_questionanswrchoices">
            <column name="questionId" valueNumeric="831"/>
            <column name="questionanswerchoiceId" valueNumeric="314"/>
        </insert>
        <insert tableName="questions_questionanswrchoices">
            <column name="questionId" valueNumeric="831"/>
            <column name="questionanswerchoiceId" valueNumeric="315"/>
        </insert>
        <insert tableName="questions_questionanswrchoices">
            <column name="questionId" valueNumeric="831"/>
            <column name="questionanswerchoiceId" valueNumeric="316"/>
        </insert>
        <insert tableName="questions_questionanswrchoices">
            <column name="questionId" valueNumeric="831"/>
            <column name="questionanswerchoiceId" valueNumeric="317"/>
        </insert>
        <insert tableName="questions_questionanswrchoices">
            <column name="questionId" valueNumeric="831"/>
            <column name="questionanswerchoiceId" valueNumeric="318"/>
        </insert>
        <insert tableName="questions_questionanswrchoices">
            <column name="questionId" valueNumeric="831"/>
            <column name="questionanswerchoiceId" valueNumeric="319"/>
        </insert>
        <insert tableName="questions_questionanswrchoices">
            <column name="questionId" valueNumeric="831"/>
            <column name="questionanswerchoiceId" valueNumeric="320"/>
        </insert>
        <insert tableName="questions_questionanswrchoices">
            <column name="questionId" valueNumeric="831"/>
            <column name="questionanswerchoiceId" valueNumeric="321"/>
        </insert>
        <insert tableName="questions_questionanswrchoices">
            <column name="questionId" valueNumeric="831"/>
            <column name="questionanswerchoiceId" valueNumeric="322"/>
        </insert>
        <insert tableName="questions_questionanswrchoices">
            <column name="questionId" valueNumeric="832"/>
            <column name="questionanswerchoiceId" valueNumeric="313"/>
        </insert>
        <insert tableName="questions_questionanswrchoices">
            <column name="questionId" valueNumeric="832"/>
            <column name="questionanswerchoiceId" valueNumeric="314"/>
        </insert>
        <insert tableName="questions_questionanswrchoices">
            <column name="questionId" valueNumeric="832"/>
            <column name="questionanswerchoiceId" valueNumeric="315"/>
        </insert>
        <insert tableName="questions_questionanswrchoices">
            <column name="questionId" valueNumeric="832"/>
            <column name="questionanswerchoiceId" valueNumeric="316"/>
        </insert>
        <insert tableName="questions_questionanswrchoices">
            <column name="questionId" valueNumeric="832"/>
            <column name="questionanswerchoiceId" valueNumeric="317"/>
        </insert>
        <insert tableName="questions_questionanswrchoices">
            <column name="questionId" valueNumeric="832"/>
            <column name="questionanswerchoiceId" valueNumeric="318"/>
        </insert>
        <insert tableName="questions_questionanswrchoices">
            <column name="questionId" valueNumeric="832"/>
            <column name="questionanswerchoiceId" valueNumeric="319"/>
        </insert>
        <insert tableName="questions_questionanswrchoices">
            <column name="questionId" valueNumeric="832"/>
            <column name="questionanswerchoiceId" valueNumeric="320"/>
        </insert>
        <insert tableName="questions_questionanswrchoices">
            <column name="questionId" valueNumeric="832"/>
            <column name="questionanswerchoiceId" valueNumeric="321"/>
        </insert>
        <insert tableName="questions_questionanswrchoices">
            <column name="questionId" valueNumeric="832"/>
            <column name="questionanswerchoiceId" valueNumeric="322"/>
        </insert>
    </changeSet>
    <changeSet id="DEV-122-outcomestar-example-qg" author="adamjhamer" context="1.1-base-data">
        <insert tableName="questiongroups">
            <column name="discriminator_orm" value="support"/>
            <column name="id" valueNumeric="208"/>
            <column name="version" valueNumeric="0"/>
            <column name="disabled" valueBoolean="false"/>
            <column name="name" value="family - early years"/>
            <column name="headerText"/>
        </insert>
    </changeSet>
    <changeSet id="DEV-122-outcomestar-example-q-qg" author="adamjhamer" context="1.1-base-data">
        <insert tableName="questiongroups_questions">
            <column name="questiongroupId" valueNumeric="208"/>
            <column name="questionId" valueNumeric="826"/>
            <column name="orderby" valueNumeric="0"/>
        </insert>
        <insert tableName="questiongroups_questions">
            <column name="questiongroupId" valueNumeric="208"/>
            <column name="questionId" valueNumeric="827"/>
            <column name="orderby" valueNumeric="1"/>
        </insert>
        <insert tableName="questiongroups_questions">
            <column name="questiongroupId" valueNumeric="208"/>
            <column name="questionId" valueNumeric="828"/>
            <column name="orderby" valueNumeric="2"/>
        </insert>
        <insert tableName="questiongroups_questions">
            <column name="questiongroupId" valueNumeric="208"/>
            <column name="questionId" valueNumeric="829"/>
            <column name="orderby" valueNumeric="3"/>
        </insert>
        <insert tableName="questiongroups_questions">
            <column name="questiongroupId" valueNumeric="208"/>
            <column name="questionId" valueNumeric="830"/>
            <column name="orderby" valueNumeric="4"/>
        </insert>
        <insert tableName="questiongroups_questions">
            <column name="questiongroupId" valueNumeric="208"/>
            <column name="questionId" valueNumeric="831"/>
            <column name="orderby" valueNumeric="5"/>
        </insert>
        <insert tableName="questiongroups_questions">
            <column name="questiongroupId" valueNumeric="208"/>
            <column name="questionId" valueNumeric="832"/>
            <column name="orderby" valueNumeric="6"/>
        </insert>
    </changeSet>

    <changeSet id="DEV-122-outcomestar-wiring" author="adamjhamer" context="1.1-base-data">
        <insert tableName="servicetypes_questiongroups">
            <column name="servicetypeId" valueNumeric="99"/>
            <column name="questiongroupId" valueNumeric="208"/>
        </insert>
        <insert tableName="servicetypes_referralaspects">
            <column name="servicetypeId" valueNumeric="99"/>
            <column name="referralaspectId" valueNumeric="79"/>
            <column name="allowNext" valueBoolean="false"/>
            <column name="orderby" valueNumeric="20"/>
            <column name="version" valueNumeric="0"/>
        </insert>
        <insert tableName="st_referralaspectsettings">
            <column name="id" valueNumeric="322"/>
            <column name="name" value="questions"/>
            <column name="value" value="family - early years"/>
            <column name="referralaspectId" valueNumeric="79"/>
            <column name="servicetypeId" valueNumeric="99"/>
            <column name="version" valueNumeric="0"/>
        </insert>
        <insert tableName="st_referralaspectsettings">
            <column name="id" valueNumeric="323"/>
            <column name="name" value="titleRaw"/>
            <column name="value" value="family outcome star"/>
            <column name="referralaspectId" valueNumeric="79"/>
            <column name="servicetypeId" valueNumeric="99"/>
            <column name="version" valueNumeric="0"/>
        </insert>
        <insert tableName="st_referralaspectsettings">
            <column name="id" valueNumeric="324"/>
            <column name="name" value="actAs"/>
            <column name="value" value="questions"/>
            <column name="referralaspectId" valueNumeric="79"/>
            <column name="servicetypeId" valueNumeric="99"/>
            <column name="version" valueNumeric="0"/>
        </insert>
        <insert tableName="st_referralaspectsettings">
            <column name="id" valueNumeric="325"/>
            <column name="name" value="validateComment"/>
            <column name="value" value="allowNullComment"/>
            <column name="referralaspectId" valueNumeric="79"/>
            <column name="servicetypeId" valueNumeric="99"/>
            <column name="version" valueNumeric="0"/>
        </insert>
        <insert tableName="st_referralaspectsettings">
            <column name="id" valueNumeric="326"/>
            <column name="name" value="showSaveTypes"/>
            <column name="value" value="draft,final"/>
            <column name="referralaspectId" valueNumeric="79"/>
            <column name="servicetypeId" valueNumeric="99"/>
            <column name="version" valueNumeric="0"/>
        </insert>
        <insert tableName="st_referralaspectsettings">
            <column name="id" valueNumeric="327"/>
            <column name="name" value="sourcePageGroup"/>
            <column name="value" value="79"/>
            <column name="referralaspectId" valueNumeric="79"/>
            <column name="servicetypeId" valueNumeric="99"/>
            <column name="version" valueNumeric="0"/>
        </insert>
        <insert tableName="st_referralaspectsettings">
            <column name="id" valueNumeric="328"/>
            <column name="name" value="sourcePage"/>
            <column name="value" value="79"/>
            <column name="referralaspectId" valueNumeric="79"/>
            <column name="servicetypeId" valueNumeric="99"/>
            <column name="version" valueNumeric="0"/>
        </insert>
        <insert tableName="st_referralaspectsettings">
            <column name="id" valueNumeric="329"/>
            <column name="name" value="commentWidth"/>
            <column name="value" value="wide"/>
            <column name="referralaspectId" valueNumeric="79"/>
            <column name="servicetypeId" valueNumeric="99"/>
            <column name="version" valueNumeric="0"/>
        </insert>
        <insert tableName="st_referralaspectsettings">
            <column name="id" valueNumeric="330"/>
            <column name="name" value="showEvidenceStyleAs"/>
            <column name="value" value="officialOutcomeStar"/>
            <column name="referralaspectId" valueNumeric="79"/>
            <column name="servicetypeId" valueNumeric="99"/>
            <column name="version" valueNumeric="0"/>
        </insert>
    </changeSet>

    <changeSet id="DEV-142-taskNames_with_description" author="adamjhamer">
        <addColumn tableName="referralaspects">
            <column name="description" type="VARCHAR(512)">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet id="DEV-142-taskNames_update_descriptions" author="adamjhamer">
        <update tableName="referralaspects">
            <column name="description"
                    value="'from' organisation / professional / individual"/>
            <where>name='sourceWithIndividual'</where>
        </update>
        <update tableName="referralaspects">
            <column name="description"
                    value="'destination of referral' which includes region"/>
            <where>name='project'</where>
        </update>
        <update tableName="referralaspects">
            <column name="description"
                    value="'data protection' with signature"/>
            <where>name='dataProtection'</where>
        </update>
        <update tableName="referralaspects">
            <column name="description"
                    value="'details of referral' capturing info from the referral (eg economic status, eligibility)"/>
            <where>name='referralDetails'</where>
        </update>
        <update tableName="referralaspects">
            <column name="description"
                    value="'pending status' to indicate a status on screen when a referral can't be processed"/>
            <where>name='pendingStatus'</where>
        </update>
        <update tableName="referralaspects">
            <column name="description"
                    value="'referral comments' evidence page"/>
            <where>name='engagementComments'</where>
        </update>
        <update tableName="referralaspects">
            <column name="description"
                    value="'funding'"/>
            <where>name='funding'</where>
        </update>
        <update tableName="referralaspects">
            <column name="description"
                    value="'appropriate referral' which can also be configured to allocate a primary support worker"/>
            <where>name='referralAccepted'</where>
        </update>
        <update tableName="referralaspects">
            <column name="description"
                    value="'setup initial assessment'"/>
            <where>name='assessmentDate'</where>
        </update>
        <update tableName="referralaspects">
            <column name="description"
                    value="'initial assessment' capturing info from an assessment (eg economic status, eligibility)"/>
            <where>name='assessmentDetails'</where>
        </update>
        <update tableName="referralaspects">
            <column name="description"
                    value="'consent' with signature"/>
            <where>name='consent'</where>
        </update>
        <update tableName="referralaspects">
            <column name="description"
                    value="'emergency details'"/>
            <where>name='emergencyDetails'</where>
        </update>
        <update tableName="referralaspects">
            <column name="description"
                    value="'accept on service'"/>
            <where>name='decideFinal'</where>
        </update>
        <update tableName="referralaspects">
            <column name="description"
                    value="'start' and assign a primary worker"/>
            <where>name='start'</where>
        </update>
        <update tableName="referralaspects">
            <column name="description"
                    value="'start' on service automatically"/>
            <where>name='autoStart'</where>
        </update>
        <update tableName="referralaspects">
            <column name="description"
                    value="'waiting list' score"/>
            <where>name='waitingListCriteria'</where>
        </update>
        <update tableName="referralaspects">
            <column name="description"
                    value="'schedule reviews' to set up a regular review"/>
            <where>name='scheduleReviews'</where>
        </update>
        <update tableName="referralaspects">
            <column name="description"
                    value="'schedule next meeting' records the client's next appointment, and saves the state of the last (eg attended / did not attend / rescheduled)"/>
            <where>name='nextMeeting'</where>
        </update>
        <update tableName="referralaspects">
            <column name="description"
                    value="'needs assessment' - evidence screen"/>
            <where>name='needsAssessment'</where>
        </update>
        <update tableName="referralaspects">
            <column name="description"
                    value="'support plan' - evidence screen"/>
            <where>name='needsReduction'</where>
        </update>
        <update tableName="referralaspects">
            <column name="description"
                    value="'needs assessment' and 'support plan' combined - evidence screen"/>
            <where>name='needsAssessmentReduction'</where>
        </update>
        <update tableName="referralaspects">
            <column name="description"
                    value="'review' - review evidence screen allowing new needs and achievements"/>
            <where>name='needsAssessmentReductionReview'</where>
        </update>
        <update tableName="referralaspects">
            <column name="description"
                    value="'risk management' - risk evidence screen"/>
            <where>name='threatAssessmentReduction'</where>
        </update>
        <update tableName="referralaspects">
            <column name="description"
                    value="'risk management' - assessment risk evidence screen"/>
            <where>name='threatAssessment'</where>
        </update>
        <update tableName="referralaspects">
            <column name="description"
                    value="'risk management' - support risk evidence screen"/>
            <where>name='threatReduction'</where>
        </update>
        <update tableName="referralaspects">
            <column name="description"
                    value="'staff notes' area - does not appear on 'support history'"/>
            <where>name='supportStaffNotes'</where>
        </update>
        <update tableName="referralaspects">
            <column name="description"
                    value="'manager notes' area - for managers, does not appear on 'support history'"/>
            <where>name='managerNotes'</where>
        </update>
        <update tableName="referralaspects">
            <column name="description"
                    value="'allocate to services' closes the current referral and creates a new one in the relevant service(s)"/>
            <where>name='allocateToServices'</where>
        </update>
        <update tableName="referralaspects">
            <column name="description"
                    value="'schedule meetings' to indicate the days of the week for the client"/>
            <where>name='scheduleMeetings'</where>
        </update>
        <update tableName="referralaspects">
            <column name="description"
                    value="'close off' the referral"/>
            <where>name='close'</where>
        </update>
        <update tableName="referralaspects">
            <column name="description"
                    value="mark the flow as finishing here"/>
            <where>name='endFlow'</where>
        </update>
        <update tableName="referralaspects">
            <column name="description"
                    value="'relationship' tab, requires other configuration"/>
            <where>name='newMultipleReferral'</where>
        </update>
        <update tableName="referralaspects">
            <column name="description"
                    value="'service agreement' for the rota"/>
            <where>name='agreementOfAppointments'</where>
        </update>
        <update tableName="referralaspects">
            <column name="description"
                    value="'rota visit' to record a rota visit"/>
            <where>name='rotaVisit'</where>
        </update>
        <update tableName="referralaspects">
            <column name="description"
                    value="'initial sp data' to record SP initial data"/>
            <where>name='initial-sp_data'</where>
        </update>
        <update tableName="referralaspects">
            <column name="description"
                    value="'exit sp data' to record SP exit data"/>
            <where>name='exit-sp_data'</where>
        </update>
        <update tableName="referralaspects">
            <column name="description"
                    value="show the referral file"/>
            <where>name='referralView'</where>
        </update>

        <!-- questionnaire evidence pages -->
        <update tableName="referralaspects">
            <column name="description"
                    value="questionnaire evidence screen"/>
            <where>name='generalQuestionnaire'</where>
        </update>
        <update tableName="referralaspects">
            <column name="description"
                    value="questionnaire evidence screen"/>
            <where>name='iaptInitialAssessment'</where>
        </update>
        <update tableName="referralaspects">
            <column name="description"
                    value="questionnaire evidence screen"/>
            <where>name='iaptAttendance'</where>
        </update>
        <update tableName="referralaspects">
            <column name="description"
                    value="questionnaire evidence screen"/>
            <where>name='iaptCurrentView'</where>
        </update>
        <update tableName="referralaspects">
            <column name="description"
                    value="questionnaire evidence screen"/>
            <where>name='iaptSessions'</where>
        </update>
        <update tableName="referralaspects">
            <column name="description"
                    value="questionnaire evidence screen"/>
            <where>name='feedbackQuestionnaire'</where>
        </update>
        <update tableName="referralaspects">
            <column name="description"
                    value="questionnaire evidence screen"/>
            <where>name='iaptGoals'</where>
        </update>
        <update tableName="referralaspects">
            <column name="description"
                    value="questionnaire evidence screen"/>
            <where>name='iaptImpact'</where>
        </update>
        <update tableName="referralaspects">
            <column name="description"
                    value="questionnaire evidence screen"/>
            <where>name='iaptFeedback'</where>
        </update>
        <update tableName="referralaspects">
            <column name="description"
                    value="questionnaire evidence screen specifically for recording HACT data"/>
            <where>name='hactQuestionnaire'</where>
        </update>

    </changeSet>

    <!-- update any friendlyName keys into name keys
        its likely duplicate msg_key's exists on older systems, they can be fixed:
            delete from messages where msg_key='new name key';
            and redeployed
            delete from DATABASECHANGELOG where id='DEV-145-messages_update';
        HOWEVER, msg_key isn't unique and the system still functions with the duplicate
    -->
    <changeSet id="DEV-145-messages_update" author="adamjhamer">
        <update tableName="messages">
            <column name="msg_key"
                    value="referralView.projectAsAccommodation"/> <!-- new name key -->
            <where>msg_key='referralView.accommodation'</where> <!-- old friendlyName key -->
        </update>
        <update tableName="messages">
            <column name="msg_key"
                    value="referralView.decideFinal"/>
            <where>msg_key='referralView.assessmentAccepted'</where>
        </update>
        <update tableName="messages">
            <column name="msg_key"
                    value="referralView.assessmentDetails"/>
            <where>msg_key='referralView.assessment'</where>
        </update>
        <update tableName="messages">
            <column name="msg_key"
                    value="referralBreadcrumb.region"/>
            <where>msg_key='referralBreadcrumb.toRegion'</where>
        </update>
        <update tableName="messages">
            <column name="msg_key"
                    value="referralBreadcrumb.project"/>
            <where>msg_key='referralBreadcrumb.toProject'</where>
        </update>
        <update tableName="messages">
            <column name="msg_key"
                    value="referralBreadcrumb.source"/>
            <where>msg_key='referralBreadcrumb.from'</where>
        </update>
    </changeSet>

    <changeSet id="DEV-158-services_projects-clientGroupId" author="nealeu">
        <addColumn tableName="services_projects">
            <column name="clientGroupId" type="INT"/>
        </addColumn>
    </changeSet>

    <changeSet id="DEV-158-services_projects-FK_clientGroupId" author="nealeu">
        <addForeignKeyConstraint constraintName="FK_ser_prj_clientGroup"
                                 baseColumnNames="clientGroupId" baseTableName="services_projects"
                                 referencedColumnNames="id" referencedTableName="cfg_list_definitions"
                                 deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
                                 referencesUniqueColumn="true"/>
    </changeSet>

    <changeSet id="DEV-169-services_projects-companyId" author="nealeu">
        <addColumn tableName="services_projects">
            <column name="companyId" type="INT"/>
        </addColumn>
    </changeSet>

    <changeSet id="DEV-169-services_projects-FK_companyId" author="nealeu">
        <addForeignKeyConstraint constraintName="FK_ser_prj_companyId"
                                 baseColumnNames="companyId" baseTableName="services_projects"
                                 referencedColumnNames="id" referencedTableName="cfg_list_definitions"
                                 deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
                                 referencesUniqueColumn="true"/>
    </changeSet>

    <changeSet id="DEV-200-hact-deadweight" author="adamjhamer">
        <addColumn tableName="hactsocialvaluebank">
            <column defaultValueNumeric="0" name="percentDropYear1" type="INT">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="percentDropYear2" type="INT">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="percentDropYear3" type="INT">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="percentDropYear4" type="INT">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="percentDropYear5" type="INT">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="percentDropYear6" type="INT">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="percentDropYear7" type="INT">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="ECCO-2223-referrals-cId_srId-idx" author="nealeu">
        <preConditions onFail="MARK_RAN">
            <not>
                <indexExists indexName="idx_referrals_cId_srId"/>
            </not>
        </preConditions>
        <comment>so that we get srId from index by clientId without needing bookmark lookup</comment>
        <createIndex tableName="referrals" indexName="idx_referrals_cId_srId">
            <column name="clientId"/>
            <column name="serviceRecipientId"/>
        </createIndex>
    </changeSet>

    <changeSet id="DEV-203-sup_pl_wk_spg_date_idx" author="nealeu">
        <preConditions onFail="MARK_RAN">
            <not>
                <indexExists tableName="supportplanwork" columnNames="sourcepagegroup,workdate,serviceRecipientId"/>
            </not>
        </preConditions>
        <output target="WARN">Creating index on supportplanwork. This may take a while</output>
        <createIndex tableName="supportplanwork" indexName="idx_spw_grp_date_srId">
            <column name="sourcepagegroup"/>
            <column name="workdate"/>
            <column name="serviceRecipientId"/>
        </createIndex>
    </changeSet>

    <changeSet id="DEV-203-idx_rfrl_exited_scId" author="nealeu">
        <preConditions onFail="MARK_RAN">
            <not>
                <indexExists tableName="referrals" columnNames="exited,signpostedcommentid,serviceRecipientId"/>
            </not>
        </preConditions>
        <createIndex tableName="referrals" indexName="idx_rfrl_exited_scId">
            <column name="exited"/>
            <column name="signpostedcommentid"/>
            <column name="serviceRecipientId"/>
        </createIndex>
    </changeSet>
    <changeSet id="DEV-203-idx_rfrl_rcvd_srId" author="nealeu">
        <createIndex tableName="referrals" indexName="idx_rfrl_rcvd_srId">
            <column name="receiveddate"/>
            <column name="serviceRecipientId"/>
        </createIndex>
    </changeSet>

    <changeSet id="DEV-203-client-code-idx" author="nealeu">
        <createIndex tableName="clientdetails" indexName="idx_clientdetails_code">
            <column name="code"/>
        </createIndex>
    </changeSet>

    <changeSet id="DEV-203-cosmo_item-idx" author="nealeu">
        <createIndex tableName="cosmo_item" indexName="idx_itemtype_ownerid">
            <column name="itemtype"/>
            <column name="ownerid"/>
        </createIndex>
        <dropIndex tableName="cosmo_item" indexName="IDX_ITEMTYPE"/>
    </changeSet>

    <changeSet id="ECCO-176-intervalFrequency" author="adamjhamer">
        <addColumn tableName="appointmentschedules">
            <column name="intervalFrequency" type="INT" defaultValueNumeric="1" valueNumeric="1">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="DEV-241-add-taskstatus_uuid" author="nealeu">
        <addColumn tableName="svcrec_taskstatus">
            <column name="uuid" type="CHAR(36)" beforeColumn="serviceRecipientId">
                <constraints nullable="false" />
            </column>
        </addColumn>
        <addPrimaryKey tableName="svcrec_taskstatus" columnNames="uuid" constraintName="PK_sr_taskStatus"/>
    </changeSet>

    <changeSet id="DEV-241-add-taskstatus_version" author="nealeu">
        <addColumn tableName="svcrec_taskstatus">
            <column name="version" type="INT" afterColumn="uuid"/>
        </addColumn>
    </changeSet>

    <changeSet id="DEV-241-add-taskstatus_duedate" author="nealeu">
        <addColumn tableName="svcrec_taskstatus">
            <column name="dueDate" type="DATE" beforeColumn="completed" />
        </addColumn>
    </changeSet>

    <changeSet id="DEV-260-taskstatus_duedate-LocalDateTime2" author="adamjhamer">
        <modifyDataType tableName="svcrec_taskstatus" columnName="dueDate" newDataType="DATETIME"/>
    </changeSet>

    <changeSet id="DEV-241-add-taskstatus-idxs" author="nealeu">
        <createIndex tableName="svcrec_taskstatus" indexName="idx_taskstatus_sr_ra">
            <column name="serviceRecipientId"/>
            <column name="referralAspectId"/>
            <column name="dueDate"/>
            <column name="completed"/>
        </createIndex>
        <createIndex tableName="svcrec_taskstatus" indexName="idx_taskstatus_user_due">
            <column name="assigneduserid"/>
            <column name="dueDate"/>
            <column name="completed"/>
        </createIndex>
    </changeSet>

    <changeSet context="1.1-baseline" author="nealeu" id="1366042567388-135-fix" dbms="oracle">
        <!-- Do these renames after we've inserted various data -->
        <renameColumn tableName="users" oldColumnName="password" newColumnName="PASSWORD"/>
        <renameColumn tableName="users_AUD" oldColumnName="password" newColumnName="PASSWORD"/>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-36-fix" dbms="oracle">
        <renameColumn tableName="cosmo_users" oldColumnName="password" newColumnName="PASSWORD"/>
    </changeSet>

    <changeSet id="DEV-254-dropNull-taskstatus-raId" author="adamjhamer">
        <dropNotNullConstraint tableName="svcrec_taskstatus" columnName="referralAspectId" columnDataType="BIGINT"/>
    </changeSet>

    <changeSet id="DEV-261-taskstatus_description" author="adamjhamer">
        <addColumn tableName="svcrec_taskstatus">
            <column name="description" type="VARCHAR(512)"/>
        </addColumn>
    </changeSet>

    <changeSet id="DEV-199-enforce-service-name" author="adamjhamer">
        <addNotNullConstraint tableName="services" columnName="name" columnDataType="VARCHAR(255)"/>
    </changeSet>

    <changeSet id="DEV-182-ra-profile" author="nealeu">
        <insert tableName="referralaspects">
            <column name="id" valueNumeric="110"/>
            <column name="version" valueNumeric="0"/>
            <column name="display" valueBoolean="true"/>
            <column name="displayOverview" valueBoolean="true"/>
            <column name="isexternal" valueBoolean="false"/>
            <column name="friendlyName" value="myProfile"/>
            <column name="internal" valueBoolean="true"/>
            <column name="name" value="myProfile"/>
        </insert>
    </changeSet>

    <changeSet id="DEV-279-generated-to-isGenerated" author="nealeu">
        <renameColumn tableName="events" oldColumnName="generated" newColumnName="isGenerated" columnDataType="BOOLEAN"/>
    </changeSet>
    <changeSet id="DEV-279-generated-to-isGenerated-mysql-notnull" author="nealeu" dbms="mysql">
        <comment>Reapply non-null due to mysql dropping it on rename</comment>
        <addNotNullConstraint tableName="events" columnName="isGenerated" columnDataType="BOOLEAN"/>
    </changeSet>

    <changeSet id="DEV-280-ra-dailyRoutines" author="nealeu">
        <insert tableName="referralaspects">
            <column name="id" valueNumeric="111"/>
            <column name="version" valueNumeric="0"/>
            <column name="display" valueBoolean="true"/>
            <column name="displayOverview" valueBoolean="true"/>
            <column name="isexternal" valueBoolean="false"/>
            <column name="name" value="dailyRoutines"/>
            <column name="friendlyName" value="dailyRoutines"/>
            <column name="internal" valueBoolean="true"/>
        </insert>
    </changeSet>

    <changeSet id="DEV-182-ra-health-profile" author="adamjhamer">
        <insert tableName="referralaspects">
            <column name="id" valueNumeric="112"/>
            <column name="version" valueNumeric="0"/>
            <column name="display" valueBoolean="true"/>
            <column name="displayOverview" valueBoolean="true"/>
            <column name="isexternal" valueBoolean="false"/>
            <column name="friendlyName" value="my health profile"/>
            <column name="internal" valueBoolean="true"/>
            <column name="name" value="myHealthProfile"/>
        </insert>
    </changeSet>

    <changeSet id="DEV-182-ra-pb-plan" author="adamjhamer">
        <insert tableName="referralaspects">
            <column name="id" valueNumeric="113"/>
            <column name="version" valueNumeric="0"/>
            <column name="display" valueBoolean="true"/>
            <column name="displayOverview" valueBoolean="true"/>
            <column name="isexternal" valueBoolean="false"/>
            <column name="friendlyName" value="my positive behaviour plan"/>
            <column name="internal" valueBoolean="true"/>
            <column name="name" value="myPositiveBehaviourPlan"/>
        </insert>
    </changeSet>

    <changeSet id="DEV-182-ra-communication-passport" author="adamjhamer">
        <insert tableName="referralaspects">
            <column name="id" valueNumeric="114"/>
            <column name="version" valueNumeric="0"/>
            <column name="display" valueBoolean="true"/>
            <column name="displayOverview" valueBoolean="true"/>
            <column name="isexternal" valueBoolean="false"/>
            <column name="friendlyName" value="my communication passport"/>
            <column name="internal" valueBoolean="true"/>
            <column name="name" value="myCommunicationPassport"/>
        </insert>
    </changeSet>

    <changeSet id="DEV-182-ra-communication-pathway" author="adamjhamer">
        <insert tableName="referralaspects">
            <column name="id" valueNumeric="115"/>
            <column name="version" valueNumeric="0"/>
            <column name="display" valueBoolean="true"/>
            <column name="displayOverview" valueBoolean="true"/>
            <column name="isexternal" valueBoolean="false"/>
            <column name="friendlyName" value="my communication pathway"/>
            <column name="internal" valueBoolean="true"/>
            <column name="name" value="myCommunicationPathway"/>
        </insert>
    </changeSet>

    <changeSet id="DEV-182-ra-person-centred-plan" author="adamjhamer">
        <insert tableName="referralaspects">
            <column name="id" valueNumeric="116"/>
            <column name="version" valueNumeric="0"/>
            <column name="display" valueBoolean="true"/>
            <column name="displayOverview" valueBoolean="true"/>
            <column name="isexternal" valueBoolean="false"/>
            <column name="friendlyName" value="my person centred plan"/>
            <column name="internal" valueBoolean="true"/>
            <column name="name" value="myPersonCentredPlan"/>
        </insert>
    </changeSet>

    <changeSet id="DEV-421-ra-dietaryIntake-checklist" author="adamjhamer">
        <insert tableName="referralaspects">
            <column name="id" valueNumeric="117"/>
            <column name="version" valueNumeric="0"/>
            <column name="display" valueBoolean="true"/>
            <column name="displayOverview" valueBoolean="true"/>
            <column name="isexternal" valueBoolean="false"/>
            <column name="friendlyName" value="dietary intake"/>
            <column name="internal" valueBoolean="true"/>
            <column name="name" value="dietaryIntake"/>
        </insert>
    </changeSet>

    <changeSet author="adamjhamer" id="DEV-291-overseerAccess-hideByDefault">
        <update tableName="setting">
            <column name="keyvalue" value="admin,case-manager,duty,hr-volunteers,commissioner,client,demo,rota,overseer"/>
            <where>
                keyname='GroupsToExclude' and keyvalue='admin,case-manager,duty,hr-volunteers,commissioner,client,demo,rota'
            </where>
        </update>
    </changeSet>

    <changeSet author="adamjhamer" id="DEV-463-overseerAccess-hideByDefault">
        <update tableName="setting">
            <column name="keyvalue" value="admin,case-manager,duty,hr-volunteers,commissioner,client,demo,rota,overseer,overseer-history"/>
            <where>
                keyname='GroupsToExclude' and keyvalue='admin,case-manager,duty,hr-volunteers,commissioner,client,demo,rota,overseer'
            </where>
        </update>
    </changeSet>

    <changeSet id="ECCO-1921-drop-referrals-customStringData" author="nealeu">
        <dropColumn tableName="referrals" columnName="customStringData"/>
    </changeSet>

    <changeSet id="ECCO-1925-drop-supportthreatoutcomes-customStringData" author="nealeu">
        <dropColumn tableName="supportthreatoutcomes" columnName="customStringData"/>
    </changeSet>

    <changeSet author="adamjhamer" id="DEV-302-hours-can-be-null">
        <dropNotNullConstraint tableName="referrals" columnName="fundingHoursOfSupport" columnDataType="INT"/>
    </changeSet>

    <!-- Integer to BigDecimal -->
    <!-- NB this might break mssql if there is a default constraint - search for 'mssql creates its own constraints' -->
    <!-- but it might be possible to drop the constraint with a little knowledge of what it is - see 'remove-old-selfFunding-default' -->
    <changeSet author="adamjhamer" id="DEV-302-hours-can-be-fractional-nonOracle">
        <preConditions onFail="MARK_RAN">
            <!-- not oracle because liquibase has set it as NUMBER(10) and we can't restrict the number of digits (precision) -->
            <!-- we have to move and copy columns -->
            <not>
                <dbms type="oracle"/>
            </not>
        </preConditions>
        <!-- 9 digits is used because of a mysql storage boundary -->
        <!-- 12 digits would work for oracle, because it already has decimal(10,0) so would get no issues -->
        <modifyDataType tableName="referrals" columnName="fundingHoursOfSupport" newDataType="DECIMAL(9,2)"/>
        <modifyDataType tableName="referrals" columnName="fundingHoursOfSupport" newDataType="DECIMAL(9,2)"/>
    </changeSet>
    <changeSet author="adamjhamer" id="DEV-302-hours-can-be-fractional-oracle">
        <preConditions onFail="MARK_RAN">
            <dbms type="oracle"/>
        </preConditions>
        <renameColumn tableName="referrals" oldColumnName="fundingHoursOfSupport" newColumnName="fundingHoursOfSupport_OLD" columnDataType="INT"/>
        <addColumn tableName="referrals">
            <column name="fundingHoursOfSupport" type="DECIMAL(9,2)"/>
        </addColumn>
        <update tableName="referrals">
            <column name="fundingHoursOfSupport" valueComputed="fundingHoursOfSupport_OLD"/>
        </update>
    </changeSet>

    <changeSet id="DEV-326-evdnc_form_snapshot" author="adamjhamer">
        <createTable tableName="evdnc_form_snapshot">
            <column name="uuid" type="CHAR(36)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="created" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column name="evidenceTaskId" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="evidenceTaskGroupKey" type="VARCHAR(20)">
                <constraints nullable="false"/>
            </column>
            <column name="workDate" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column name="contactId" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="serviceRecipientId" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="json" type="CLOB"/>
        </createTable>
    </changeSet>
    <changeSet id="DEV-326-evdnc_form_snapshot-fk_contactId" author="adamjhamer">
        <addForeignKeyConstraint baseTableName="evdnc_form_snapshot" baseColumnNames="contactId" constraintName="fk_evdnc_frm_snp_contact" referencedTableName="contacts" referencedColumnNames="id" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet id="DEV-326-evdnc_form_snapshot-fk_srId" author="adamjhamer">
        <addForeignKeyConstraint baseTableName="evdnc_form_snapshot" baseColumnNames="serviceRecipientId" constraintName="fk_evdnc_frm_snp_sr" referencedTableName="servicerecipients" referencedColumnNames="id" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet id="DEV-326-evdnc_form_snapshot-fk_evidenceTaskId" author="adamjhamer">
        <addForeignKeyConstraint baseTableName="evdnc_form_snapshot" baseColumnNames="evidenceTaskId" constraintName="fk_evdnc_frm_snp_taskId" referencedTableName="referralaspects" referencedColumnNames="id" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencesUniqueColumn="false"/>
    </changeSet>
    <!-- at least for oracle, a constraint has nothing to do with an index -->
    <changeSet id="DEV-326-evdnc_form_snapshot-idx_srId" author="adamjhamer">
        <createIndex tableName="evdnc_form_snapshot" indexName="idx_evdnc_frm_snp_srId">
            <column name="serviceRecipientId"/>
        </createIndex>
    </changeSet>
    <changeSet id="DEV-326-evdnc_form_snapshot-idx_srId_created_grp" author="adamjhamer">
        <createIndex tableName="evdnc_form_snapshot" indexName="idx_evdnc_frm_snp_srId_crt_grp">
            <column name="serviceRecipientId"/>
            <column name="created"/>
            <column name="evidenceTaskGroupKey"/>
        </createIndex>
    </changeSet>

    <!-- TODO when DEV-302 done, we can delete -->
    <!--
    <changeSet author="adamjhamer" id="DEV-302-hours-can-be-fractional-oracle-removeOld">
        <preConditions onFail="MARK_RAN">
            <dbms type="oracle"/>
        </preConditions>
        <dropColumn tableName="referrals" columnName="fundingHoursOfSupport_OLD"/>
    </changeSet>
    -->


    <changeSet id="DEV-172-create-project-id-minus-2" author="nealeu">
        <insert tableName="projects">
            <column name="id" valueNumeric="-2"/>
            <column name="version" valueNumeric="0"/>
            <column name="name" value="unassigned"/>
        </insert>
    </changeSet>
    <changeSet id="DEV-172-replace-project-id-with-minus-2" author="nealeu">
        <update tableName="servicerecipients">
            <column name="projectId" valueNumeric="-2"/>
            <where>projectId IS NULL</where>
        </update>
    </changeSet>

    <changeSet id="DEV-172-add-missing-s_p" author="nealeu">
        <sql>
            INSERT INTO services_projects (serviceId, projectId) (select sr.serviceId, sr.projectId
            FROM servicerecipients sr
            LEFT JOIN services_projects sp ON sp.serviceId = sr.serviceId AND sp.projectId = sr.projectId
            WHERE sp.serviceId IS NULL
            GROUP BY sr.serviceId, sr.projectId, sp.serviceId, sp.projectId);
        </sql>
    </changeSet>
    <changeSet id="DEV-172-add-services_projects.id" author="nealeu">
        <addColumn tableName="services_projects">
            <column name="id" type="INT"/>
        </addColumn>
    </changeSet>
    <changeSet id="DEV-172-add-services_projects.version" author="nealeu">
        <addColumn tableName="services_projects">
            <column name="version" type="INT" defaultValueNumeric="0">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="DEV-172-populate-services_projects.id" author="nealeu">
        <customChange class="com.ecco.infrastructure.liquibase.PopulateSequenceChange"
                      tableName="services_projects" uniqueKeyColumns="serviceId,projectId"
                      newIdColumn="id" />
    </changeSet>

    <changeSet id="DEV-172-insert-bldg-wrkr" author="nealeu">
        <validCheckSum>7:ad20507e60337b52f0b603d93adb9f91</validCheckSum>
        <preConditions onFail="MARK_RAN">
            <!-- Assume we get either both or neither -->
            <sqlCheck expectedResult="0">
                SELECT count(1) FROM services_projects
                WHERE (serviceId = -100 OR serviceId = -200)
                AND projectId = -2
            </sqlCheck>
        </preConditions>
        <insert tableName="services_projects">
            <column name="id" valueNumeric="-100"/>
            <column name="serviceId" valueNumeric="-100"/>
            <column name="projectId" valueNumeric="-2"/>
        </insert>
        <insert tableName="services_projects">
            <column name="id" valueNumeric="-200"/>
            <column name="serviceId" valueNumeric="-200"/>
            <column name="projectId" valueNumeric="-2"/>
        </insert>
    </changeSet>

    <changeSet id="DEV-172-add-sr.serviceAllocationId" author="nealeu">
        <addColumn tableName="servicerecipients">
            <column name="serviceAllocationId" type="INT"/>
        </addColumn>
    </changeSet>
    <changeSet id="DEV-172-populate-sr.serviceAllocationId" author="nealeu">
        <update tableName="servicerecipients">
            <column name="serviceAllocationId" valueComputed=
    "(SELECT id FROM services_projects WHERE services_projects.serviceId = servicerecipients.serviceId AND services_projects.projectId = servicerecipients.projectId)"/>
        </update>
    </changeSet>

    <changeSet id="DEV-172-servicerecipients-serviceAllocationId-notnull" author="nealeu">
        <addNotNullConstraint tableName="servicerecipients" columnName="serviceAllocationId" columnDataType="INT"/>
    </changeSet>

    <changeSet id="DEV-172-drop-svc_projects-PK" author="nealeu">
        <dropForeignKeyConstraint baseTableName="services_projects" constraintName="FK6D52C33B83B122E2"/>
        <dropForeignKeyConstraint baseTableName="services_projects" constraintName="FK6D52C33BD13AA2AA"/>
        <dropPrimaryKey tableName="services_projects"/>
    </changeSet>

    <changeSet id="DEV-172-services_projects-Id-notnull" author="nealeu">
        <addNotNullConstraint tableName="services_projects" columnName="Id" columnDataType="INT"/>
    </changeSet>

    <changeSet id="DEV-172-new-services_projects-PK" author="nealeu">
        <addPrimaryKey tableName="services_projects" columnNames="id"/>
    </changeSet>

    <changeSet id="DEV-172-add-servicerecipients-FK_serviceAllocationId" author="nealeu">
        <addForeignKeyConstraint constraintName="FK_svcrec_svcAllocationId"
                                 baseColumnNames="serviceAllocationId" baseTableName="servicerecipients"
                                 referencedColumnNames="id" referencedTableName="services_projects"
                                 deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
                                 referencesUniqueColumn="true"/>
    </changeSet>

    <changeSet id="DEV-172-svcs_projects_nullable" author="nealeu">
        <dropNotNullConstraint tableName="services_projects" columnName="projectId" columnDataType="BIGINT"/>
    </changeSet>

    <changeSet id="DEV-172-replace-project-id-minus-2-with-null" author="nealeu">
        <update tableName="servicerecipients">
            <column name="projectId" valueComputed="NULL"/>
            <where>projectId = -2</where>
        </update>
        <update tableName="services_projects">
            <column name="projectId" valueComputed="NULL"/>
            <where>projectId = -2</where>
        </update>
    </changeSet>

    <changeSet id="DEV-xxx-create-s_p-null-project-for-basedata-services" author="nealeu" context="1.1-base-data">
        <insert tableName="services_projects">
            <column name="id" valueNumeric="-3"/> <!-- minus serviceId to avoid clash with generated ids -->
            <column name="serviceId" valueNumeric="3"/>
        </insert>
        <insert tableName="services_projects">
            <column name="id" valueNumeric="-4"/>
            <column name="serviceId" valueNumeric="4"/>
        </insert>
        <insert tableName="services_projects">
            <column name="id" valueNumeric="-5"/>
            <column name="serviceId" valueNumeric="5"/>
        </insert>
        <insert tableName="services_projects">
            <column name="id" valueNumeric="-6"/>
            <column name="serviceId" valueNumeric="6"/>
        </insert>
    </changeSet>

    <changeSet id="DEV-172-create-s_p-null-project-for-basedata-services" author="nealeu" context="1.1-base-data">
        <insert tableName="services_projects">
            <column name="id" valueNumeric="-1"/> <!-- minus serviceId to avoid clash with generated ids -->
            <column name="serviceId" valueNumeric="1"/>
        </insert>
        <insert tableName="services_projects">
            <column name="id" valueNumeric="-2"/>
            <column name="serviceId" valueNumeric="2"/>
        </insert>
        <insert tableName="services_projects">
            <column name="id" valueNumeric="-97"/>
            <column name="serviceId" valueNumeric="97"/>
        </insert>
        <insert tableName="services_projects">
            <column name="id" valueNumeric="-98"/>
            <column name="serviceId" valueNumeric="98"/>
        </insert>
        <insert tableName="services_projects">
            <column name="id" valueNumeric="-99"/>
            <column name="serviceId" valueNumeric="99"/>
        </insert>
    </changeSet>

    <changeSet id="DEV-172-allow-null-svcrec-serviceId" author="nealeu">
        <dropNotNullConstraint tableName="servicerecipients" columnName="serviceId" columnDataType="BIGINT"/>
    </changeSet>

    <changeSet id="DEV-235-services_projects-serviceGroupId" author="nealeu">
        <addColumn tableName="services_projects">
            <column name="serviceGroupId" type="INT"/>
        </addColumn>
    </changeSet>

    <changeSet id="DEV-235-services_projects-FK_serviceGroupId" author="nealeu">
        <addForeignKeyConstraint constraintName="FK_ser_prj_serviceGroup"
                                 baseColumnNames="serviceGroupId" baseTableName="services_projects"
                                 referencedColumnNames="id" referencedTableName="cfg_list_definitions"
                                 deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
                                 referencesUniqueColumn="true"/>
    </changeSet>

    <changeSet id="DEV-172-migrate-to-std-for-no-project" author="nealeu" context="1.1-base-data">
        <validCheckSum>7:f535543f8d410e8644b72131324b9560</validCheckSum>
        <update tableName="servicerecipients">
            <column name="serviceAllocationId" valueComputed="-serviceId"/>
            <where>projectId IS NULL AND serviceId >= 0 AND serviceId &lt; 1000</where>
        </update>
    </changeSet>
    <changeSet id="DEV-172-delete-dupl-ser_prj" author="nealeu" context="1.1-base-data">
        <validCheckSum>7:6e861c27979321e6565625eb689280cb</validCheckSum>
        <delete tableName="services_projects">
            <where><![CDATA[projectId IS NULL AND id <> -serviceId AND serviceId > 0 AND serviceId < 1000]]></where>
        </delete>
    </changeSet>

    <changeSet id="DEV-172-add-s_p-unq-constraint" author="nealeu">
        <addUniqueConstraint tableName="services_projects" columnNames="serviceId,projectId"
                             constraintName="UQ_ser_prj_sId_pId"/>
    </changeSet>

    <changeSet id="DEV-466-cleanUpError-hazardIntervention" author="adamjhamer">
        <preConditions onFail="MARK_RAN">
            <!-- oracle has just been done manually - it required a cast from clob to char (to_char(hazard) <> 'hazard'...) -->
            <not>
                <dbms type="oracle"/>
            </not>
            <!-- if we already updated the column before the correction (hence this DEV-466 changeSet is first) -->
            <changeSetExecuted id="DEV-351-varchar-to-text-updateColumns" author="adamjhamer"
                               changeLogFile="classpath:sql/1.1-changes/075-Dec-2015-until-next.xml"/>
            <!-- and the only data in all the table is 'hazard' and 'intervention' -->
            <sqlCheck expectedResult="0"><![CDATA[select count(*) from supportthreatactions where hazard <> 'hazard' and intervention <> 'intervention']]></sqlCheck>
        </preConditions>
        <update tableName="supportthreatactions">
            <column name="hazard" valueComputed="NULL"/>
        </update>
        <update tableName="supportthreatactions">
            <column name="intervention" valueComputed="NULL"/>
        </update>
    </changeSet>

    <changeSet id="DEV-351-varchar-to-text-addColumns" author="adamjhamer">
        <preConditions onFail="MARK_RAN">
            <not>
                <!-- original converstion not appropriate for all dbs -->
                <changeSetExecuted id="ECCO-351-varchar-to-text" author="adamjhamer"
                                   changeLogFile="classpath:sql/1.1-changes/075-Dec-2015-until-next.xml"/>
            </not>
        </preConditions>
        <addColumn tableName="supportthreatactions">
            <column name="hazardClob" type="CLOB"/>
        </addColumn>
        <addColumn tableName="supportthreatactions">
            <column name="interventionClob" type="CLOB"/>
        </addColumn>
    </changeSet>
    <changeSet id="DEV-351-varchar-to-text-updateColumns" author="adamjhamer">
        <validCheckSum>7:bf8e46315e583bd4ff7fbe419be2edb2</validCheckSum>
        <preConditions onFail="MARK_RAN">
            <not>
                <!-- original converstion not appropriate for all dbs -->
                <changeSetExecuted id="ECCO-351-varchar-to-text" author="adamjhamer"
                                   changeLogFile="classpath:sql/1.1-changes/075-Dec-2015-until-next.xml"/>
            </not>
        </preConditions>
        <update tableName="supportthreatactions">
            <column name="hazardClob" valueComputed="hazard"/>
        </update>
        <update tableName="supportthreatactions">
            <column name="interventionClob" valueComputed="intervention"/>
        </update>
    </changeSet>

    <changeSet id="DEV-351-varchar-to-text-deleteColumns" author="adamjhamer">
        <preConditions onFail="MARK_RAN">
            <not>
                <!-- original converstion not appropriate for all dbs -->
                <changeSetExecuted id="ECCO-351-varchar-to-text" author="adamjhamer"
                                   changeLogFile="classpath:sql/1.1-changes/075-Dec-2015-until-next.xml"/>
            </not>
        </preConditions>
        <dropColumn tableName="supportthreatactions" columnName="hazard"/>
        <dropColumn tableName="supportthreatactions" columnName="intervention"/>
    </changeSet>
    <changeSet id="DEV-351-varchar-to-text-renameColumns" author="adamjhamer">
        <preConditions onFail="MARK_RAN">
            <not>
                <!-- original converstion not appropriate for all dbs -->
                <changeSetExecuted id="ECCO-351-varchar-to-text" author="adamjhamer"
                                   changeLogFile="classpath:sql/1.1-changes/075-Dec-2015-until-next.xml"/>
            </not>
        </preConditions>
        <renameColumn tableName="supportthreatactions" oldColumnName="hazardClob" newColumnName="hazard" columnDataType="CLOB"/>
        <renameColumn tableName="supportthreatactions" oldColumnName="interventionClob" newColumnName="intervention" columnDataType="CLOB"/>
    </changeSet>

    <changeSet id="DEV-355-remove-duplicate-menu" author="adamjhamer">
        <delete tableName="cfg_menu_cfg_menuitem">
            <where>menuItems_id=60</where>
        </delete>
        <delete tableName="cfg_menuitem">
            <where>id=60</where>
        </delete>
    </changeSet>

    <changeSet id="DEV-172-drop-fk_svcrec_serviceId" author="nealeu">
        <dropForeignKeyConstraint baseTableName="servicerecipients" constraintName="fk_svcrec_serviceId"/>
    </changeSet>
    <changeSet id="DEV-172-drop-fk_svcrec_projectId" author="nealeu">
        <dropForeignKeyConstraint baseTableName="servicerecipients" constraintName="fk_svcrec_projectId"/>
    </changeSet>
    <changeSet id="DEV-172-drop-idx_svc_rec_orm_sId_pId" author="nealeu">
        <dropIndex tableName="servicerecipients" indexName="idx_svc_rec_orm_sId_pId"/>
    </changeSet>
    <changeSet id="DEV-172-drop-sr.serviceId" author="nealeu">
        <dropColumn tableName="servicerecipients" columnName="serviceId"/>
    </changeSet>
    <changeSet id="DEV-172-drop-sr.projectId" author="nealeu">
        <dropColumn tableName="servicerecipients" columnName="projectId"/>
    </changeSet>
    <changeSet id="DEV-172-orm_saId_id-idx" author="nealeu">
        <!-- Index incl id so we avoid bookmark lookup or is PK implied (if so it doesn't matter then)-->
        <createIndex tableName="servicerecipients" indexName="idx_svc_rec_orm_saId">
            <column name="discriminator_orm"/>
            <column name="serviceAllocationId"/>
            <column name="created"/>
            <column name="id"/>
        </createIndex>
    </changeSet>

    <changeSet id="DEV-420-sup_pl_wk_sp_srId_created_idx" author="nealeu">
        <preConditions onFail="MARK_RAN">
            <not>
                <indexExists tableName="supportplanwork" columnNames="sourcepage,serviceRecipientId,created"/>
            </not>
        </preConditions>
        <output target="WARN">Creating index on supportplanwork. This may take a while</output>
        <createIndex tableName="supportplanwork" indexName="idx_spw_sp_srId_cre">
            <column name="sourcepage"/>
            <column name="serviceRecipientId"/>
            <column name="created"/>
        </createIndex>
    </changeSet>

    <changeSet id="DEV-420-sup_thr_wk_sp_srId_created_idx" author="nealeu">
        <preConditions onFail="MARK_RAN">
            <not>
                <indexExists tableName="supportthreatwork" columnNames="sourcepage,serviceRecipientId,created"/>
            </not>
        </preConditions>
        <output target="WARN">Creating index on supportplanwork. This may take a while</output>
        <createIndex tableName="supportthreatwork" indexName="idx_stw_sp_srId_cre">
            <column name="sourcepage"/>
            <column name="serviceRecipientId"/>
            <column name="created"/>
        </createIndex>
    </changeSet>

    <changeSet id="DEV-460-ensure-s_p.id-correct-for-wrkr" author="nealeu">
        <!-- If don't have service allocation with correct value -->
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="0">
                SELECT count(1) FROM services_projects
                WHERE serviceId = -200
                AND id = -200
            </sqlCheck>
        </preConditions>
        <insert tableName="services_projects">
            <column name="id" valueNumeric="-200"/>
            <column name="serviceId" valueNumeric="-200"/>
        </insert>
        <update tableName="servicerecipients">
            <column name="serviceAllocationId" valueNumeric="-200"/>
            <where><![CDATA[serviceAllocationId = (SELECT id FROM services_projects WHERE serviceId = -200 AND id <> -200)]]></where>
        </update>
        <delete tableName="services_projects">
            <where><![CDATA[id <> -200 AND serviceId = -200]]></where>
        </delete>
    </changeSet>

    <changeSet id="DEV-460-ensure-s_p.id-exists-for-bldg" author="nealeu">
        <!-- If don't have an entry at all for bldg service -->
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="0">
                SELECT count(1) FROM services_projects
                WHERE serviceId = -100
            </sqlCheck>
        </preConditions>
        <insert tableName="services_projects">
            <column name="id" valueNumeric="-100"/>
            <column name="serviceId" valueNumeric="-100"/>
        </insert>
    </changeSet>

    <changeSet id="DEV-460-ensure-s_p.id-correct-for-bldg" author="nealeu">
        <!-- If now don't have correct entry for bldg service -->
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="0">
                SELECT count(1) FROM services_projects
                WHERE serviceId = -100
                AND id = -100
            </sqlCheck>
        </preConditions>
        <insert tableName="services_projects">
            <column name="id" valueNumeric="-100"/>
            <column name="serviceId" valueNumeric="-100"/>
        </insert>
        <update tableName="servicerecipients">
            <column name="serviceAllocationId" valueNumeric="-100"/>
            <where><![CDATA[serviceAllocationId = (SELECT id FROM services_projects WHERE serviceId = -100 AND id <> -100)]]></where>
        </update>
        <delete tableName="services_projects">
            <where><![CDATA[id <> -100 AND serviceId = -100]]></where>
        </delete>
    </changeSet>

    <changeSet id="DEV-479-hidden-to-requestedDelete" author="bodeng">
        <renameColumn tableName="clientdetails" oldColumnName="hidden" newColumnName="requestedDelete" columnDataType="datetime"/>
        <renameColumn tableName="workers" oldColumnName="hidden" newColumnName="requestedDelete" columnDataType="datetime"/>
        <renameColumn tableName="referrals" oldColumnName="hidden" newColumnName="requestedDelete" columnDataType="datetime"/>
    </changeSet>

    <changeSet id="ECCO-479-supportwork-deleteRequest" author="adamjhamer">
        <validCheckSum>7:4c7e2bf11e68fd851ce5f6306bc856a7</validCheckSum>
        <addColumn tableName="supporthrwork">
            <column name="requestedDelete" type="datetime"/>
        </addColumn>
        <addColumn tableName="supportthreatwork">
            <column name="requestedDelete" type="datetime"/>
        </addColumn>
        <addColumn tableName="supportplanwork">
            <column name="requestedDelete" type="datetime"/>
        </addColumn>
    </changeSet>
    <changeSet id="ECCO-479-form_snapshot-deleteRequest" author="adamjhamer">
        <addColumn tableName="evdnc_form_snapshot">
            <column name="requestedDelete" type="datetime"/>
        </addColumn>
    </changeSet>

    <changeSet id="DEV-538-referralaspects-type-replace-client" author="adamjhamer">
        <comment>delete before insert so we blow up if 'client' is configured somewhere</comment>
        <delete tableName="referralaspects">
            <where>id=1</where>
        </delete>
        <insert tableName="referralaspects">
            <column name="id" valueNumeric="1"/>
            <column name="version" valueNumeric="0"/>
            <column name="display" valueBoolean="false"/>
            <column name="displayOverview" valueBoolean="false"/>
            <column name="isexternal" valueBoolean="false"/>
            <column name="friendlyName" value="groupSupport"/>
            <column name="internal" valueBoolean="true"/>
            <column name="name" value="groupSupport"/>
        </insert>
    </changeSet>
    <changeSet id="DEV-538-referralaspects-type-add-myplan" author="adamjhamer">
        <insert tableName="referralaspects">
            <column name="id" valueNumeric="118"/>
            <column name="version" valueNumeric="0"/>
            <column name="display" valueBoolean="false"/>
            <column name="displayOverview" valueBoolean="false"/>
            <column name="isexternal" valueBoolean="false"/>
            <column name="friendlyName" value="myPlan"/>
            <column name="internal" valueBoolean="true"/>
            <column name="name" value="myPlan"/>
        </insert>
    </changeSet>

    <changeSet id="DEV-538-referralaspects-type" author="adamjhamer">
        <addColumn tableName="referralaspects">
            <column name="type" type="VARCHAR(50)"/>
        </addColumn>
    </changeSet>

    <changeSet id="DEV-538-referralaspects-type-populate-specifics" author="adamjhamer">
        <update tableName="referralaspects">
            <column name="type" value="DEPRECATED"/>
            <where>id in (1, 2, 13, 29, 39, 55, 64, 67, 72, 74, 76, 92, 93, 99, 100)</where>
        </update>
        <update tableName="referralaspects">
            <column name="type" value="AUDITONLY"/>
            <where>id in (118)</where>
        </update>
        <update tableName="referralaspects">
            <column name="type" value="EVIDENCE_SUPPORT"/>
            <where>id in (19, 27, 18, 31, 54, 94, 97, 103, 107)</where>
        </update>
        <update tableName="referralaspects">
            <column name="type" value="EVIDENCE_ROTA"/>
            <where>id in (101)</where>
        </update>
        <update tableName="referralaspects">
            <column name="type" value="EVIDENCE_RISK"/>
            <where>id in (28, 59, 60)</where>
        </update>
        <update tableName="referralaspects">
            <column name="type" value="AGREEMENT"/>
            <where>id in (63, 73)</where>
        </update>
        <update tableName="referralaspects">
            <column name="type" value="EVIDENCE_QUESTIONNAIRE"/>
            <where>id in (68, 69, 70, 71, 75, 77, 79, 86, 87, 88, 89, 90, 109, 110)</where>
        </update>
        <update tableName="referralaspects">
            <column name="type" value="EVIDENCE_CUSTOMFORM"/>
            <where>id in (112, 113, 114, 115, 116)</where>
        </update>
        <update tableName="referralaspects">
            <!-- checklist is currently no different to support in terms of settings -->
            <column name="type" value="EVIDENCE_CHECKLIST"/>
            <where>id in (108, 111, 117)</where>
        </update>
    </changeSet>
    <changeSet id="DEV-538-referralaspects-type-populate-rest" author="adamjhamer">
        <update tableName="referralaspects">
            <column name="type" value="DEDICATED_TASK"/>
            <where>type is null</where>
        </update>
    </changeSet>
    <changeSet id="DEV-538-referralaspects-type-notnull" author="adamjhamer">
        <addNotNullConstraint tableName="referralaspects" columnName="type" columnDataType="VARCHAR(50)"/>
    </changeSet>

    <changeSet id="DEV-553-referralaspects-add-customForms" author="adamjhamer">
        <insert tableName="referralaspects">
            <column name="id" valueNumeric="120"/>
            <column name="version" valueNumeric="0"/>
            <column name="display" valueBoolean="true"/>
            <column name="displayOverview" valueBoolean="true"/>
            <column name="isexternal" valueBoolean="false"/>
            <column name="internal" valueBoolean="true"/>
            <column name="type" value="EVIDENCE_CUSTOMFORM"/>
            <column name="friendlyName" value="customForm1"/>
            <column name="name" value="customForm1"/>
        </insert>
        <insert tableName="referralaspects">
            <column name="id" valueNumeric="121"/>
            <column name="version" valueNumeric="0"/>
            <column name="display" valueBoolean="true"/>
            <column name="displayOverview" valueBoolean="true"/>
            <column name="isexternal" valueBoolean="false"/>
            <column name="internal" valueBoolean="true"/>
            <column name="type" value="EVIDENCE_CUSTOMFORM"/>
            <column name="friendlyName" value="customForm2"/>
            <column name="name" value="customForm2"/>
        </insert>
        <insert tableName="referralaspects">
            <column name="id" valueNumeric="122"/>
            <column name="version" valueNumeric="0"/>
            <column name="display" valueBoolean="true"/>
            <column name="displayOverview" valueBoolean="true"/>
            <column name="isexternal" valueBoolean="false"/>
            <column name="internal" valueBoolean="true"/>
            <column name="type" value="EVIDENCE_CUSTOMFORM"/>
            <column name="friendlyName" value="customForm3"/>
            <column name="name" value="customForm3"/>
        </insert>
    </changeSet>
    <changeSet id="DEV-543-add_svcrec_agreement_signature_uuids" author="adamjhamer">
        <addColumn tableName="servicerecipients">
            <column name="agreementSignatureId" type="CHAR(36)"/> <!-- Remains nullable as it's optional -->
        </addColumn>
    </changeSet>
    <changeSet id="DEV-543-add_agreement_date" author="adamjhamer">
        <addColumn tableName="referrals">
            <column name="agreementsigned" type="DATETIME"/>
        </addColumn>
    </changeSet>
    <changeSet id="DEV-543-add_referralaspect-agreement" author="adamjhamer">
        <insert tableName="referralaspects">
            <column name="id" valueNumeric="123"/>
            <column name="version" valueNumeric="0"/>
            <column name="display" valueBoolean="true"/>
            <column name="displayOverview" valueBoolean="true"/>
            <column name="isexternal" valueBoolean="false"/>
            <column name="internal" valueBoolean="true"/>
            <column name="type" value="AGREEMENT"/>
            <column name="friendlyName" value="agreement"/>
            <column name="name" value="agreement"/>
        </insert>
    </changeSet>

    <changeSet id="DEV-588-add-gs.serviceAllocationId" author="adamjhamer">
        <addColumn tableName="grp_activities">
            <column name="serviceAllocationId" type="INT"/>
        </addColumn>
    </changeSet>
    <changeSet id="DEV-588-populate-gs.serviceAllocationId" author="adamjhamer">
        <update tableName="grp_activities">
            <column name="serviceAllocationId" valueComputed=
                    "(SELECT id FROM services_projects WHERE services_projects.serviceId = grp_activities.serviceId AND services_projects.projectId = grp_activities.projectId)"/>
            <where>projectId is not null</where>
        </update>
    </changeSet>
    <changeSet id="DEV-588-populate-gs.serviceAllocationId-nullProject" author="adamjhamer">
        <update tableName="grp_activities">
            <column name="serviceAllocationId" valueComputed=
                    "(SELECT id FROM services_projects WHERE services_projects.serviceId = grp_activities.serviceId AND services_projects.projectId is null)"/>
            <where>projectId is null</where>
        </update>
    </changeSet>
    <changeSet id="DEV-588-gs-serviceAllocationId-notnull" author="adamjhamer">
        <addNotNullConstraint tableName="grp_activities" columnName="serviceAllocationId" columnDataType="INT"/>
    </changeSet>
    <changeSet id="DEV-588-add-gs-fk_serviceAllocationId" author="adamjhamer">
        <addForeignKeyConstraint constraintName="FK_gs_svcAllocationId"
                                 baseColumnNames="serviceAllocationId" baseTableName="grp_activities"
                                 referencedColumnNames="id" referencedTableName="services_projects"
                                 deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
                                 referencesUniqueColumn="true"/>
    </changeSet>
    <!-- oracle's index that gets automatically created on mysql -->
    <changeSet id="DEV-588-add-idx_fk_serviceAllocationId" author="adamjhamer">
        <preConditions onFail="MARK_RAN">
            <dbms type="oracle"/>
        </preConditions>
        <createIndex tableName="grp_activities" indexName="idx_fk_gs_svcAllocationId">
            <column name="serviceAllocationId"/>
        </createIndex>
    </changeSet>

    <!-- drop the service not null, but keep the FK for now - the data should remain valid until we drop the column -->
    <changeSet id="DEV-588-allow-null-gs-serviceId" author="adamjhamer">
        <dropNotNullConstraint tableName="grp_activities" columnName="serviceId" columnDataType="BIGINT"/>
    </changeSet>

    <changeSet id="DEV-543-add_svcrec_agreement23_signature_uuids" author="adamjhamer">
        <addColumn tableName="servicerecipients">
            <column name="agreement2SignatureId" type="CHAR(36)"/> <!-- Remains nullable as it's optional -->
        </addColumn>
        <addColumn tableName="servicerecipients">
            <column name="agreement3SignatureId" type="CHAR(36)"/> <!-- Remains nullable as it's optional -->
        </addColumn>
    </changeSet>
    <changeSet id="DEV-543-add_agreement23_date" author="adamjhamer">
        <addColumn tableName="referrals">
            <column name="agreement2signed" type="DATETIME"/>
        </addColumn>
        <addColumn tableName="referrals">
            <column name="agreement3signed" type="DATETIME"/>
        </addColumn>
    </changeSet>
    <changeSet id="DEV-543-add_referralaspect-agreement23" author="adamjhamer">
        <insert tableName="referralaspects">
            <column name="id" valueNumeric="124"/>
            <column name="version" valueNumeric="0"/>
            <column name="display" valueBoolean="true"/>
            <column name="displayOverview" valueBoolean="true"/>
            <column name="isexternal" valueBoolean="false"/>
            <column name="internal" valueBoolean="true"/>
            <column name="type" value="AGREEMENT"/>
            <column name="friendlyName" value="agreement"/>
            <column name="name" value="agreement2"/>
        </insert>
        <insert tableName="referralaspects">
            <column name="id" valueNumeric="125"/>
            <column name="version" valueNumeric="0"/>
            <column name="display" valueBoolean="true"/>
            <column name="displayOverview" valueBoolean="true"/>
            <column name="isexternal" valueBoolean="false"/>
            <column name="internal" valueBoolean="true"/>
            <column name="type" value="AGREEMENT"/>
            <column name="friendlyName" value="agreement"/>
            <column name="name" value="agreement3"/>
        </insert>
    </changeSet>

    <changeSet id="DEV-???-drop-oldreports-tables" author="adamjhamer">
        <dropTable tableName="reportattachments"/>
        <dropTable tableName="reports"/>
    </changeSet>

    <changeSet author="adamjhamer" id="DEV-708-deleteevidence-hideByDefault-since-managersByDefault">
        <update tableName="setting">
            <column name="keyvalue" value="admin,case-manager,duty,hr-volunteers,commissioner,client,demo,rota,overseer,overseer-history,delete evidence"/>
            <where>
                keyname='GroupsToExclude' and keyvalue='admin,case-manager,duty,hr-volunteers,commissioner,client,demo,rota,overseer,overseer-history'
            </where>
        </update>
    </changeSet>

    <changeSet author="adamjhamer" id="DEV-714-admin-evidence-groupsToExclude">
        <update tableName="setting">
            <column name="keyvalue" value="admin,case-manager,duty,hr-volunteers,commissioner,client,demo,rota,overseer,overseer-history,admin evidence"/>
            <where>
                keyname='GroupsToExclude' and keyvalue='admin,case-manager,duty,hr-volunteers,commissioner,client,demo,rota,overseer,overseer-history,delete evidence'
            </where>
        </update>
    </changeSet>

    <!--
      - 1.1-onwards-changelog dictates the order of schema/data changes and 'securityDomainChangeLog' is first
      - so our attempt to put this changeset in 901-security-domain-2015-12-onwards-changes means this is ran early on.
      - As the first file ran, it doesn't update any records, since the subsequent files of portableData and addSeniorManager do the inserting
      - (some of it is in base-data and some of it is in 1.1-changes).
      - LiquibaseConfig shows that tests can use data of 'test-schema' or 'base-data', BUT that 1.1 changes are always ran through!
      - Therefore, we can use 1.1-changes in this file alone.
      - TODO We ideally want 'securityDomainChangeLog' to include schema and data for security, so put the portableData and addSeniorManager changes there
      - TODO and have changes always rolling on there with contexts of 'schema' and 'basedata' and maybe 'testdata'. Contexts of 'base' and 'changes' are not helpful.
    -->
    <changeSet author="adamjhamer" id="DEV-692-renameLoginAdmin-on-groups">
        <update tableName="group_authorities">
            <column name="authority" value="ROLE_ADMINLOGINLDAP"/>
            <where>
                authority='ROLE_LOGINADMIN'
            </where>
        </update>
    </changeSet>

    <changeSet author="adamjhamer" id="DEV-692-renameLogin-on-groups">
        <update tableName="group_authorities">
            <column name="authority" value="ROLE_ADMINLOGIN"/>
            <where>
                authority='ROLE_LOGIN'
            </where>
        </update>
    </changeSet>

    <changeSet author="nealeu" id="DEV-145-rename-live-activiti-assAcc-decideFinal">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="ACT_HI_TASKINST"/>
        </preConditions>
        <update tableName="ACT_HI_TASKINST">
            <column name="NAME_" value="decideFinal"/>
            <where>
                NAME_='assessmentAccepted'
            </where>
        </update>
        <update tableName="ACT_RU_TASK">
            <column name="NAME_" value="decideFinal"/>
            <where>
                NAME_='assessmentAccepted'
            </where>
        </update>
    </changeSet>
    <changeSet author="nealeu" id="DEV-145-rename-live-activiti-variables">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="ACT_RU_VARIABLE"/>
        </preConditions>
        <update tableName="ACT_RU_VARIABLE">
            <column name="TEXT_" value="sourceWithIndividual,dataProtection,emergencyDetails,deliveredBy,referralDetails,pendingStatus,referralAccepted,assessmentDate,funding,decideFinal,start,referralActivities,agreementOfAppointments,supportStaffNotes,generalQuestionnaire,needsAssessment,threatAssessmentReduction,scheduleReviews,needsReduction,rotaVisit,needsAssessmentReductionReview,close"/>
            <where>
                NAME_='tasksToShowRestricted' and TEXT_ like '%agreementOfAppointments%'
            </where>
        </update>
        <update tableName="ACT_RU_VARIABLE">
            <column name="TEXT_" value="emergencyDetails,agreementOfAppointments,supportStaffNotes,needsReduction,rotaVisit"/>
            <where>
                NAME_='tasksToShowClientView' and TEXT_ like '%agreementOfAppointments%'
            </where>
        </update>
    </changeSet>
    <changeSet author="nealeu" id="DEV-145-rename-live-activiti-from-case-notes-delivered-by">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="ACT_HI_TASKINST"/>
        </preConditions>
        <update tableName="ACT_HI_TASKINST">
            <column name="NAME_" value="sourceWithIndividual"/>
            <where>
                NAME_='from'
            </where>
        </update>
        <update tableName="ACT_RU_TASK">
            <column name="NAME_" value="sourceWithIndividual"/>
            <where>
                NAME_='from'
            </where>
        </update>
        <update tableName="ACT_HI_TASKINST">
            <column name="NAME_" value="supportStaffNotes"/>
            <where>
                NAME_='case notes'
            </where>
        </update>
        <update tableName="ACT_RU_TASK">
            <column name="NAME_" value="supportStaffNotes"/>
            <where>
                NAME_='case notes'
            </where>
        </update>
        <update tableName="ACT_HI_TASKINST">
            <column name="NAME_" value="deliveredBy"/>
            <where>
                NAME_='delivered by'
            </where>
        </update>
        <update tableName="ACT_RU_TASK">
            <column name="NAME_" value="deliveredBy"/>
            <where>
                NAME_='delivered by'
            </where>
        </update>
    </changeSet>

    <changeSet author="nealeu" id="DEV-145-rename-live-activiti-from-family-outcome-star-generalQu">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="ACT_HI_TASKINST"/>
        </preConditions>
        <update tableName="ACT_RU_VARIABLE">
            <column name="TEXT_" value="sourceWithIndividual,dataProtection,emergencyDetails,deliveredBy,referralDetails,pendingStatus,referralAccepted,assessmentDate,funding,decideFinal,start,referralActivities,agreementOfAppointments,supportStaffNotes,outcomeStarQ,needsAssessment,threatAssessmentReduction,scheduleReviews,needsReduction,rotaVisit,needsAssessmentReductionReview,close"/>
            <where>
                NAME_='tasksToShowRestricted' and TEXT_ like '%generalQuestionnaire%'
            </where>
        </update>
        <update tableName="ACT_HI_TASKINST">
            <column name="NAME_" value="generalQuestionnaire"/>
            <where>
                NAME_='family outcome star'
            </where>
        </update>
        <update tableName="ACT_RU_TASK">
            <column name="NAME_" value="generalQuestionnaire"/>
            <where>
                NAME_='family outcome star'
            </where>
        </update>
    </changeSet>

    <changeSet id="DEV-965-drop-contacts-resourceType" author="nealeu">
        <!-- This is unused so no need to migrate existing data -->
        <dropForeignKeyConstraint baseTableName="contacts" constraintName="fk_contacts_resourcetypes"/>
        <dropColumn tableName="contacts" columnName="resourceTypeId"/>
    </changeSet>

    <changeSet id="DEV-965-add-bldg-resourceType" author="nealeu">
        <addColumn tableName="bldg_fixed">
            <column name="resourceTypeId" type="INT"/>
        </addColumn>
        <addForeignKeyConstraint constraintName="fk_bldg_resourceType" baseTableName="bldg_fixed"
                                 baseColumnNames="resourceTypeId" referencedTableName="cfg_list_definitions" referencedColumnNames="id" />
    </changeSet>

    <changeSet id="DEV-965-set-bldg-resourceType" author="nealeu">
        <update tableName="bldg_fixed">
            <column name="resourceTypeId" valueNumeric="132"/> <!-- = "building" -->
            <where>resourceTypeId IS NULL</where>
        </update>
        <addNotNullConstraint tableName="bldg_fixed" columnName="resourceTypeId" columnDataType="INT"/>
    </changeSet>

    <changeSet id="DEV-9xx-bldg_commands-srId" author="nealeu">
        <addColumn tableName="bldg_commands">
            <column name="serviceRecipientId" type="INT"/>
        </addColumn>
        <update tableName="bldg_commands">
            <column name="serviceRecipientId"
                    valueComputed="(SELECT serviceRecipientId FROM bldg_fixed WHERE bldg_fixed.id = bldg_commands.buildingId)"/>
        </update>
    </changeSet>
    <changeSet id="DEV-9xx-bldg_fixed-srId-nonnull" author="nealeu">
        <addNotNullConstraint tableName="bldg_fixed" columnName="serviceRecipientId" columnDataType="INT"/>
    </changeSet>

    <changeSet id="DEV-977-drop-apptschedule-resourceType-fk" author="nealeu">
        <dropForeignKeyConstraint baseTableName="appointmentschedules" constraintName="fk_appointmentschdls_rsrctypes"/>
    </changeSet>
    <changeSet id="DEV-977-add-apptschedule-resourceType" author="nealeu">
        <modifyDataType tableName="appointmentschedules" columnName="resourceTypeId" newDataType="INT"/>
        <addForeignKeyConstraint constraintName="fk_appointmentschdls_rsrctypes"
                                 baseTableName="appointmentschedules" baseColumnNames="resourceTypeId"
                                 referencedTableName="cfg_list_definitions" referencedColumnNames="id" />
    </changeSet>

    <changeSet id="DEV-977-drop-resourcetypes-if-empty" author="nealeu">
        <preConditions onFail="WARN">
            <tableIsEmpty tableName="resourcetypes"/>
        </preConditions>
        <dropTable tableName="resourcetypes"/>
    </changeSet>


    <!-- TODO: Drop buildingId when happy with migration -->

    <!--
    select id, firstresponsemadeon, receiveddate, requiredacceptancedate, receivingservicedate, decisiondate, decisionmadeon ,decisionreferralmadeon from referrals;
    select created from servicerecipients;
        TODO: if receivedDate is not set, then we could use svcrec.created to say that referraldetails task is overdue

        <changeSet id="DEV-52-populate-svc-rec-slaTasks" author="nealeu">
            <update tableName="servicerecipients">
                <column name="nextSlaDueDate"
                        valueComputed="(see our dates on this)"/>
                <where>nextSlaDueDate=null</where>
            </update>
        </changeSet>
    -->

    <!-- TODO - requires manual intervention on migrating primaryRelationshipId from multipleReferrals first
    <changeSet id="ECCO-2097-drop" author="adamjhamer">
        <dropColumn tableName="servicetypes" columnName="mulitpleReferrals"/>
    </changeSet>
    -->



    <!-- TAKE NOTE: ARE YOU SURE it goes here, and not in the configDomain or securityDomainChangeLog -->
</databaseChangeLog>
