<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd">
    <changeSet id="ECCO-826-populate-cosmouser-names" author="bodeng">
        <update tableName="cosmo_users">
            <column name="firstname" valueComputed="(SELECT c.firstname FROM users u JOIN contacts c ON c.usersid = u.id WHERE u.username = cosmo_users.username)"/>
            <column name="lastname" valueComputed="(SELECT c.lastname FROM users u JOIN contacts c ON c.usersid = u.id WHERE u.username = cosmo_users.username)"/>
            <where>username NOT LIKE '#%'</where>
        </update>
    </changeSet>
    <changeSet id="ECCO-826-populate-cosmorsrc-names" author="bodeng">
        <update tableName="cosmo_users">
            <column name="firstname" valueComputed="(SELECT c.firstname FROM contacts c WHERE c.discriminator_orm = 'resource' AND c.email = cosmo_users.email)"/>
            <column name="lastname" valueComputed="(SELECT c.lastname FROM contacts c WHERE c.discriminator_orm = 'resource' AND c.email= cosmo_users.email)"/>
            <where>username LIKE '#%'</where>
        </update>
    </changeSet>
</databaseChangeLog>