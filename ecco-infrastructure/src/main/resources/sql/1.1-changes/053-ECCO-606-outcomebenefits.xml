<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd">

    <changeSet author="baseline" id="ECCO-606-actions_outcomebenefits">
        <createTable tableName="actions_outcomebenefits">
            <column name="version" type="INT"/>
            <column name="actionId" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="outcomebenefitId" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="baseline" id="ECCO-606-outcomebenefits">
        <createTable tableName="outcomebenefits">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="disabled" type="BIT">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="baseline" id="ECCO-606-pk1">
        <addPrimaryKey columnNames="actionId, outcomebenefitId" tableName="actions_outcomebenefits"/>
    </changeSet>
    <changeSet author="baseline" id="ECCO-606-fk2">
        <addForeignKeyConstraint baseColumnNames="actionId" baseTableName="actions_outcomebenefits" constraintName="FK21AF78CC4946CACA" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="actions" referencesUniqueColumn="false"/>
    </changeSet>
    <changeSet author="baseline" id="ECCO-606-fk3">
        <addForeignKeyConstraint baseColumnNames="outcomebenefitId" baseTableName="actions_outcomebenefits" constraintName="FK21AF78CCEB0AD588" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="outcomebenefits" referencesUniqueColumn="false"/>
    </changeSet>

</databaseChangeLog>
