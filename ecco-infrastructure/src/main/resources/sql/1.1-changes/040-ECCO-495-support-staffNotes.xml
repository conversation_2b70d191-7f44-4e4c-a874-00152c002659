<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd">

    <changeSet id="support-staffNotes" author="adamjhamer">
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="0">select count(1) from referralaspects where id=94 and name='supportStaffNotes'</sqlCheck>
        </preConditions>
        <insert tableName="referralaspects">
            <column name="id" valueNumeric="94"/>
            <column name="version" valueNumeric="0"/>
            <column name="display" valueNumeric="1"/>
            <column name="displayOverview" valueNumeric="1"/>
            <column name="external" valueNumeric="0"/>
            <column name="friendlyName" value="supportStaffNotes"/>
            <column name="internal" valueNumeric="1"/>
            <column name="name" value="supportStaffNotes"/>
        </insert>
    </changeSet>

</databaseChangeLog>
