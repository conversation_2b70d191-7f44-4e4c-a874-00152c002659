<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd">

    <changeSet author="nealeu" id="ECCO-300-add-orderby-to-qg_questions">
       <addColumn tableName="questiongroups_questions">
            <column name="orderby" type="int"/>
        </addColumn>
    </changeSet>

    <changeSet author="nealeu" id="ECCO-300-remove-dupl-from-qg_questions-mysql" context="1.1-base-data">
        <preConditions onFail="MARK_RAN">
            <not><dbms type="oracle"/></not>
            <sqlCheck expectedResult="2">select count(1) from questiongroups_questions where questiongroupId=101 AND questionId=701</sqlCheck>
        </preConditions>
        <delete tableName="questiongroups_questions">
            <where>questiongroupId=101 AND questionId=701 limit 1</where>
        </delete>
    </changeSet>

    <changeSet author="nealeu" id="ECCO-300-remove-dupl-from-qg_questions-oracle" context="1.1-base-data">
        <preConditions onFail="MARK_RAN">
            <dbms type="oracle"/>
            <sqlCheck expectedResult="2">select count(1) from questiongroups_questions where questiongroupId=101 AND questionId=701</sqlCheck>
        </preConditions>
        <delete tableName="questiongroups_questions">
            <where>questiongroupId=101 AND questionId=701 and rownum=1</where>
        </delete>
    </changeSet>


    <changeSet author="nealeu" id="ECCO-300-add-PK-to-qg_questions">
        <addPrimaryKey tableName="questiongroups_questions" columnNames="questiongroupId,questionId"/>
    </changeSet>


    <changeSet author="nealeu" id="ECCO-300-populate-orderby-in-qg_questions">
        <customChange class="com.ecco.infrastructure.liquibase.AddOrderColumnChange"
            tableName="questiongroups_questions"
            collectionColumnName="questiongroupId" entryColumnName="questionId" orderColumnName="orderby"/>
    </changeSet>
    <!--
    UPDATE ecco.questiongroups_questions SET orderby=questionId-1498 where questionGroupId = 200;
    UPDATE ecco.questiongroups_questions SET orderby=questionId-1514 where questionGroupId = 201;
    UPDATE ecco.questiongroups_questions SET orderby=questionId-1530 where questionGroupId = 202;
    UPDATE ecco.questiongroups_questions SET orderby=(questionId-100120)/2 where questionGroupId = 100119;
    UPDATE ecco.questiongroups_questions SET orderby=(questionId-100139)/2 where questionGroupId = 100138;
-->

</databaseChangeLog>
