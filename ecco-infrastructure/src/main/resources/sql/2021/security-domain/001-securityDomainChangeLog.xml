<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd"
        logicalFilePath="2021/security-domain">

    <!-- carer so that we can distinguish the menu item for them -->
    <changeSet id="DEV-2109-dailyChecksAccess" author="adamjhamer">
        <insert tableName="sec_groups">
            <!-- groups don't get changed really, so just continue with the next id -->
            <column name="id" valueNumeric="29"/>
            <column name="version" valueNumeric="1"/>
            <column name="group_name" value="daily checks"/>
        </insert>
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="29"/>
            <column name="authority" value="ROLE_USER"/>
        </insert>
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="29"/>
            <column name="authority" value="ROLE_STAFF"/>
        </insert>
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="29"/>
            <column name="authority" value="ROLE_DAILYCHECKS"/>
        </insert>
    </changeSet>

    <changeSet id="DEV-2148-flags-edit-permission-staff" author="adamjhamer" context="!test-data">
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="2"/> <!-- staff -->
            <column name="authority" value="ROLE_EDITEVIDENCEFLAG"/>
        </insert>
    </changeSet>
    <changeSet id="DEV-2148-flags-edit-permission-manager-senior" author="adamjhamer" context="!test-data">
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="1"/> <!-- manager -->
            <column name="authority" value="ROLE_EDITEVIDENCEFLAG"/>
        </insert>
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="11"/> <!-- senior manager -->
            <column name="authority" value="ROLE_EDITEVIDENCEFLAG"/>
        </insert>
    </changeSet>
    <!-- *** STOP:
     DO NOT ADD ANYTHING MORE HERE
     - USE A CHANGELOG
     in the correct YEAR folder
     *** -->

</databaseChangeLog>