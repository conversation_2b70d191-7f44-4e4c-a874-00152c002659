<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd"
    logicalFilePath="2024/config-domain"
    >

    <changeSet id="DEV-614-commands-draft-cfg" author="adamjhamer">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists columnName="draft" tableName="cfg_commands"/>
            </not>
        </preConditions>
        <addColumn tableName="cfg_commands">
            <column name="draft" type="boolean" defaultValueBoolean="false" valueBoolean="false">
                <constraints nullable="false"/>
            </column>
        </addColumn>
        <!--<createIndex tableName="cfg_commands" indexName="idx_cfgcmd_draft">
            <column name="draft"/>
            <column name="userId"/>
        </createIndex>-->
    </changeSet>

    <!-- HANDLES: (based on search for <createTable)
     - see configDomainChangeLog.xml
    -->

</databaseChangeLog>