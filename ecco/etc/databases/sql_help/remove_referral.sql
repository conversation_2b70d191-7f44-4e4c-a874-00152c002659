
-- find when was deleted
/*select r.id as rid,r.servicerecipientId as srid, r.clientId,c.firstName,c.lastName,r.hidden
from referrals r inner join clientdetails cd on r.clientId=cd.id inner join contacts c on cd.contactsId=c.id
where r.hidden is not null order by r.hidden desc;*/


-- =================
-- ===== SETUP =====
-- =================

-- RIDS TO DELETE

-- FOR ORACLE
    -- search group_concat(servicerecipientId SEPARATOR ',') replace LISTAGG(servicerecipientId, ',') within group (order by null)
    -- search group_concat(id SEPARATOR ',') replace LISTAGG(id, ',') within group (order by null)
    -- select group_concat(bytesId SEPARATOR ',') replace LISTAGG(bytesId, ',') within group (order by null)

-- FOR BULK
-- for lots of rid deletions we create tables for the ids, rather than pasting text
create table referral_archive (referralid int, rcode varchar(255), srId int, clientId int);
create table referral_archive_primary (referralid int);
create table referral_archive_events (eventid int, cal_uid varchar(255));
create table referral_archive_bytes (bytesId int);
create table referral_archive_family (referralId int);
create table referral_archive_clients (clientId int, usersId int, username varchar(255), contactsId int, cosmouserid int);

-- PREP excel the r-ids into statements, vi onto server, source it
    -- excel a column of r-id
    -- insert before with 'insert into referral_archive set referralid=' and after with ;
    -- vi into /var/lib/mysql-files/...-archive.sql
    -- chmod 777 ...-archive.sql
    -- mysql; use ...
    -- source /var/lib/mysql-files/...-archive.sql
    -- select count(*) from referral_archive;
update referral_archive a inner join referrals r on r.id=a.referralId set a.srId=r.serviceRecipientId, a.clientId=r.clientId, a.rcode=r.code;

-- CHECK empty - code is the id, else think again!
select * from referral_archive where referralId<>rcode;
-- if the report uses code, then join on code and see any differences
select id,code from referrals r inner join referral_archive a on r.code=a.referralid where r.id<>a.referralId;

-- unlink associated referrals (associated contacts are deleted later, but referrals need another run through)
insert into referral_archive_primary (referralid) select id from referrals where primaryReferralId in (select referralId from referral_archive);
insert into referral_archive_events (eventId, cal_uid) select id, cal_uid from events where serviceRecipientId in (select srId from referral_archive);
-- NB this covers supportworkattachments, since it has fileId which maps to GenericTypeAttachment
-- which is one of GenericTypeSupport/ThreatAttachment where fileId is ServiceRecipientAttachment, which is the svcrec_attachments
insert into referral_archive_bytes (bytesId) select bytesId from svcrec_attachments where servicerecipientId in (select srId from referral_archive);
insert into referral_archive_family (referralId) select id from referrals where parentReferralId in (select referralId from referral_archive);
-- clients to maybe delete
insert into referral_archive_clients (clientId) select distinct clientId from referral_archive;
-- means we take out those that aren't safe to delete
delete from referral_archive_clients where clientId in
    (select distinct r.clientId from referrals r inner join referral_archive a on r.clientId=a.clientId
    where r.id not in (select referralid from referral_archive));


-- =================
-- ==== DELETE =====
-- =================

-- back to client-backups/archive_referrals.sql
-- slave stop for more compute power (else falls over and hard to get back)

-- still referral, not srId
-- referral_to_gsat could be some activity demand
delete from referral_to_gsat where referralid in (select referralId from referral_archive);
delete from grp_activities_referrals where referralid in (select referralId from referral_archive);
delete from grp_attendance where referralid in (select referralId from referral_archive);
delete from arc_audits where referralid in (select referralId from referral_archive);
-- dependent details (these get deleted in referralcomments below)
update referrals set signpostedCommentId=null, exitCommentId=null where id in (select referralId from referral_archive);
-- dependent details get handled at the end
update referrals set primaryReferralId=null where primaryReferralId in (select referralId from referral_archive);
-- update ... parentReferralId
delete from referralcomments where referralid in (select referralId from referral_archive);

-- srId
delete from svcrec_attachments where servicerecipientId in (select srId from referral_archive);
delete from uploadfile where bytesid in (select bytesId from referral_archive_bytes);
-- too many - error on tmp table disk full! but rolls back to have space
    -- possibly use procedure, or check binlogs etc
    -- so do piecemeal, and clear bindlogs as go
delete from uploadbytes where id in (select bytesId from referral_archive_bytes);
    -- PIECEMEAL
    create table referral_archive_bytes2 like referral_archive_bytes;
    delete from referral_archive_bytes2;
    insert into referral_archive_bytes2 (bytesid) select bytesid from referral_archive_bytes limit 750 offset 0;
    delete from uploadbytes where id in (select bytesId from referral_archive_bytes2);
        -- OR try this - UNTESTED
        /*
        DROP PROCEDURE IF EXISTS DEL_BYTES;
        DELIMITER ;;

        CREATE PROCEDURE DEL_BYTES()
        BEGIN
        DECLARE len INT DEFAULT 0;
        DECLARE counter INT DEFAULT 0;
        SELECT COUNT(*) FROM referral_archive_bytes2 INTO len;
        SET counter=0;
        WHILE counter<len DO
            delete from uploadbytes where bytesId in (SELECT bytesid FROM referral_archive_bytes2 LIMIT counter,1);
            SET counter = counter + 1;
        END WHILE;
        End;
        ;;

        DELIMITER ;
        CALL DEL_BYTES();
        */

-- events are by srId which is the owner of the event - so other data will lose reference to it
delete from contacts_events where eventId in (select eventId from referral_archive_events);
delete from eventsrecurring where servicerecipientId in (select srId from referral_archive);
delete from events where servicerecipientId in (select srId from referral_archive);
-- CHECK empty, if not do the below in adding a referralId etc (untested)
select * from grp_commands;
    -- ALTER table grp_commands add column referralId int
    -- select SUBSTR(LOCATE('referralId', body), ;
    -- update grp_commands set referralId=
    -- delete from grp_commands where commandname in ('editattendance', 'clientInvitation') and body like '%referralId":....,%';
-- the files are already in svcrec_attachments
delete from evdnc_attachments where workUuid in (select uuid from evdnc_supportwork where servicerecipientId in (select srId from referral_archive));
delete from evdnc_supportoutcomes where servicerecipientId in (select srId from referral_archive);
delete from evdnc_supportactions where servicerecipientId in (select srId from referral_archive);
delete from evdnc_supportcomments where servicerecipientId in (select srId from referral_archive);
-- linked actions
delete from evdnc_supportwork_actions where workUuid in (select uuid from evdnc_supportwork where servicerecipientId in (select srId from referral_archive));
-- old style referralactivities
delete from referralactivities where servicerecipientId in (select srId from referral_archive);
delete from evdnc_supportanswers where servicerecipientId in (select srId from referral_archive);
delete from evdnc_supportwork where servicerecipientId in (select srId from referral_archive);
delete from evdnc_form_snapshots where servicerecipientId in (select srId from referral_archive);
delete from evdnc_form_comments where servicerecipientId in (select srId from referral_archive);
delete from evdnc_form_work where servicerecipientId in (select srId from referral_archive);
delete from evdnc_threatoutcomes where servicerecipientId in (select srId from referral_archive);
delete from evdnc_threatactions where servicerecipientId in (select srId from referral_archive);
delete from evdnc_threatcomments where servicerecipientId in (select srId from referral_archive);
delete from evdnc_threatwork_actions where workUuid in (select uuid from evdnc_threatwork where servicerecipientId in (select srId from referral_archive));
delete from evdnc_supportflags where servicerecipientId in (select srId from referral_archive);
delete from evdnc_threatflags where workUuid in (select uuid from evdnc_threatwork where servicerecipientId in (select srId from referral_archive));
delete from evdnc_threatflags where servicerecipientId in (select srId from referral_archive);
delete from evdnc_threatwork where servicerecipientId in (select srId from referral_archive);
delete from reviews where servicerecipientid in (select srId from referral_archive);

-- CHECK empty - no referrals without an srId - else sort/delete them
select * from referral_archive where srId is null;

delete from referrals where servicerecipientid in (select srId from referral_archive);
delete from addresshistory where servicerecipientid in (select srId from referral_archive);
-- old style stuff (now in 'plan' box?) - more structured support - like details of the school whilst receiving support to attend etc
delete from referralactivities where servicerecipientid in (select srId from referral_archive);
delete from singlevaluehistory where servicerecipientid in (select srId from referral_archive);
delete from svcrec_commands where servicerecipientid in (select srId from referral_archive);
delete from svcrec_commands_archive where servicerecipientid in (select srId from referral_archive);
-- networks of contacts
-- NB this may leave contacts orphaned, but no more so than would be anyway
delete from svcrec_contacts_assocs where serviceRecipientId in (select srId from referral_archive);
-- specific link from srId to contact
delete from svcrec_contacts where servicerecipientid in (select srId from referral_archive);
delete from svcrec_taskstatus where servicerecipientid in (select srId from referral_archive);

-- rota stuff
delete from svcrec_provides_attrs where servicerecipientid in (select srId from referral_archive);
delete from svcrec_requires_attrs where servicerecipientid in (select srId from referral_archive);
delete from cal_eventstatus where servicerecipientid in (select srId from referral_archive);

delete from servicerecipients where id in (select srId from referral_archive);

-- TODO remove primary / parent

-- TODO commandqueue - technically un-executed should be deleted
-- NB commandqueue will hold details, but this gets pruned
-- see cleanupOldExecutedCommands which does deleteExecutedEarlierThan 1 month
