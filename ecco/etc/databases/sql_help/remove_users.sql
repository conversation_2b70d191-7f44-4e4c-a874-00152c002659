
create table tmp_del_users (workerId int, usersId int, username varchar(255), contactsId int, cosmouserid int, sid int);

-- insert into tmp_del_users (username) values ('<usernames>');
-- insert into tmp_del_users (usersId) values ('<usersId>');

-- build up - usersId, username, contactsId, sid from ecco
update tmp_del_users a inner join users u on a.username=u.username
    set a.usersId=u.id;
update tmp_del_users a inner join users u on a.usersId=u.id inner join contacts c on c.usersId=u.id
    set a.usersId=c.usersId, a.username=u.username, a.contactsId=c.id;
update tmp_del_users a inner join cosmo_users cu on a.username=cu.username
    set a.cosmouserid=cu.id;
-- NB ownerid of link to system/home collection is the same, so just choose one we can get easily
update tmp_del_users a inner join contacts c on a.contactsId=c.id
    inner join cosmo_item ci on c.calendarId=ci.item_uid
    inner join cosmo_users cu on cu.id=ci.ownerid
set a.cosmouserid=cu.id
where a.cosmouserid is null;
update tmp_del_users a inner join users u on a.usersId=u.id
    inner join acl_sid au on au.principal=true and au.sid=u.username
    set a.sid=au.id;
-- select * from tmp_del_users;

-- will be double the number of users - 2 rows each
create table tmp_del_cosmoitem (itemid int);
insert into tmp_del_cosmoitem (itemid) select id from cosmo_item where ownerid in (select cosmouserid from tmp_del_users);

-- will be empty if no events
create table tmp_del_events (eventid int, cal_uid varchar(255));
insert into tmp_del_events (eventId, cal_uid) select id, cal_uid from events where contactId in (select contactsId from tmp_del_users)
                                                                                        and id not in (select eventId from tmp_del_events);

-- ACL delete/ordering
-- look around...
-- select * from acl_entry order by acl_object_identity, ace_order;
-- see the calculated row_number
/*SELECT a.acl_object_identity, a.ace_order, COUNT(*) row_number FROM acl_entry a
    JOIN acl_entry b ON a.acl_object_identity = b.acl_object_identity and a.ace_order >= b.ace_order
    GROUP BY a.acl_object_identity, a.ace_order
    ORDER BY a.acl_object_identity, a.ace_order;*/
-- BACKUP
CREATE TABLE tmp_acl like acl_entry;
INSERT INTO tmp_acl SELECT * FROM acl_entry;
-- select * from tmp_acl;



-- *** DELETE as per remove_all_client.sql

-- delete events (also refer to <remove_referral.sql>)
delete from contacts_events where eventId in (select eventId from tmp_del_events);
delete from events where id in (select eventId from tmp_del_events);
delete from contacts where id in (select contactsId from tmp_del_users);

-- delete cosmo calendar entries
-- NB 'homeCollection' refers to 'collection' which holds all our entries
delete from cosmo_collection_item where collectionid in (select itemId from tmp_del_cosmoitem);
delete from cosmo_collection_item where itemid in (select itemId from tmp_del_cosmoitem);
delete from cosmo_tombstones where itemid in (select itemId from tmp_del_cosmoitem);
delete from cosmo_event_stamp where stampid in (select id from cosmo_stamp where itemid in (select itemId from tmp_del_cosmoitem));
delete from cosmo_stamp where itemid in (select itemId from tmp_del_cosmoitem);

-- delete cosmo users
update cosmo_item set modifiesitemid=null where modifiesitemid in (select itemId from tmp_del_cosmoitem);
delete from cosmo_item where ownerid in (select cosmouserid from tmp_del_users);
delete from cosmo_users where id in (select cosmouserid from tmp_del_users);

-- ACL
    -- delete acl
    delete from acl_entry where sid in (select sid from tmp_del_users);
    delete from acl_sid where id in (select sid from tmp_del_users);

    -- remove the unique clashes
    update acl_entry set ace_order=-1*ace_order;
    UPDATE acl_entry a
    SET a.ace_order = (select COUNT(*)-1 from (select * from acl_entry) b where a.acl_object_identity = b.acl_object_identity and a.id >= b.id
                       GROUP BY a.acl_object_identity
                       ORDER BY a.acl_object_identity, a.id);
    -- select * from acl_entry order by acl_object_identity, ace_order
    -- testing
    -- CREATE TABLE tmp_acl like acl_entry;
    -- INSERT INTO tmp_acl SELECT * FROM acl_entry;
    -- alter table tmp_acl drop key acl_entry_unique_oid_order;
    -- alter table tmp_acl modify ace_order int(11) null;
    -- update tmp_acl set ace_order=null;
    /*update tmp_acl set ace_order=ace_order+1000;
    UPDATE tmp_acl a
    SET a.ace_order = (select COUNT(*)-1 from (select * from tmp_acl) b where a.acl_object_identity = b.acl_object_identity and a.id >= b.id
                       GROUP BY a.acl_object_identity
                       ORDER BY a.acl_object_identity, a.id);
    -- select * from tmp_acl order by acl_object_identity, ace_order
    */
-- ACL

-- delete ecco user (not ACLs as only deleting clients)
delete from group_members where member_id in (select usersId from tmp_del_users);
delete from group_members_AUD where member_id in (select usersId from tmp_del_users);
-- delete from authorities where username in (select username from tmp_del_users);
delete from usr_commands where userIdSubject in (select usersId from tmp_del_users);
delete from persistent_logins where username in (select username from tmp_del_users);
-- delete from arc_audits where usersId in (select usersId from tmp_del_users);
delete from passwordhistory where userId in (select usersId from tmp_del_users);
delete from userdevices where users_id in (select usersId from tmp_del_users);
delete from userdevices_AUD where users_id in (select usersId from tmp_del_users);
delete from revision where username in (select username from tmp_del_users);
delete from clnt_commands where clientid in (select clientId from tmp_del_users);
-- delete from audits where ...;
delete from users where id in (select usersId from tmp_del_users);
delete from users_AUD where id in (select usersId from tmp_del_users);

-- *** DELETE as per remove_all_client.sql
