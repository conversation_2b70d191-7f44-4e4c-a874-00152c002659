-- OUTCOME / RISK / ACTIONS
-- ========================
-- 'service id', 'service name', 'outcome id', 'outcome name', 'action id', 'action name'
-- inner joins on services table so we only see relevant outcomes
select sto.servicetypeId, st.name, o.id, o.name, a.id, a.name
INTO OUTFILE '/tmp/report_outcomeriskactions.csv' 
FIELDS TERMINATED BY ',' OPTIONALLY ENCLOSED BY '"' LINES TERMINATED BY '\n' 
from actions a inner join risks r on a.riskId=r.id inner join outcomes o on r.outcomeId=o.id inner join servicetypes_outcomesupports sto on sto.outcomeId=o.id inner join services s on s.servicetypeId=sto.servicetypeId inner join servicetypes st on st.id=sto.servicetypeId order by sto.servicetypeId, sto.outcomeId, r.id, a.id;
