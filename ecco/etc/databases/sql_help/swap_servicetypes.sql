
-- create new service type

-- create new service
insert into services (id, version, name, servicetypeId) values (1015, 0, 'Floating Support - PSI', 1004);
update services_projects set serviceId=1015 where serviceId=1001 and projectId=27;
update ldapgroupmapping set localid=1015 where id=2210;
commit;

-- update old service data
-- change any data from 1001 service to 1015 where project is 27
select r.id,r.referredServiceId,rp.projectId from referrals r inner join referralprojects rp on r.id=rp.referralId where rp.projectId in (27);
update referrals set referredserviceid=1015 where id in (select r.id from referrals r inner join referralprojects rp on r.id=rp.referralId where rp.projectId in (27));
-- double check count same on new service
select r.id,r.referredServiceId,rp.projectId from referrals r inner join referralprojects rp on r.id=rp.referralId where rp.projectId in (27);

-- ensure acl entry
-- service
select id from acl_object_identity order by id desc;
-- 69
select id from acl_entry order by id desc;
-- 520
select id from ldapgroupmapping order by id desc;
-- 3660

insert into acl_object_identity (id, object_id_class, object_id_identity, parent_object, owner_sid, entries_inheriting) values (69, 1, 1015, NULL, 1, 0);
-- acl entry for sysadmin on the service only
insert into acl_entry (id, acl_object_identity, ace_order, sid, mask, granting, audit_success, audit_failure) values (520, 69, 0, 2, 1, 1, 0, 0);
commit;
-- GBL_Sec_eccoLoc_Global
insert into ldapgroupmapping values (3660, 0, 'GBL_SEC_ECCOLOC_Global', null, 'com.ecco.dom.Service', 1015);
commit;
