<?xml version="1.0" encoding="UTF-8"?>

<dataset xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../dataset.xsd">

    <!-- as copied from ecco-web SpiderGraphTest
    /*
     * table of createDummySupport for reference to make it easier to read the tests
     * this table is all for one referral so the latest data is the latest action
     * genericActionId  workId  outcomeId   actionId    status      statusChange    calculated as latest
     * 1                1       1           5           1           1
     * 2                1       1           6           1           1
     * 3                1       1           7           1           1                   y
     * 4                1       1           8           1           1                   y
     * 5                1       2           9           1           1
     * 6                2       1           5           3           1
     * 13               9       2           12          3           1                   y [this entry has no prior entry of '1' for relevant]
     * 7                3       1           6           3           1
     * 8                4       2           9           3           1                   y [the only action achieved for outcome 2 which has a '1' relevant first]
     * 9                5       1           5           4           1                   y
     * 10               6       1           6           4           1                   y
     * 11               7       2           13          4           0                   y ['4' (unachieve) without a status change doesn't exist yet in the ui - but useful to test anyway]
     * 12               8       1           8           4           0                     [this can exist when a date is changed and the support plan screen shows as unachieved]
     */
    -->

    <supportplanactions workUuid="046ce030-0001-45c2-83f2-988b8678718e" created="2013-03-01 15:00:00" id="1" actionId="5" status="1" statusChange="true" serviceRecipientId="1" version="0" activity="0"/>
    <supportplanactions workUuid="046ce030-0001-45c2-83f2-988b8678718e" created="2013-03-01 15:00:00" id="2" actionId="6" status="1" statusChange="true" serviceRecipientId="1" version="0" activity="0"/>
    <supportplanactions workUuid="046ce030-0001-45c2-83f2-988b8678718e" created="2013-03-01 15:00:00" id="3" actionId="7" status="1" statusChange="true" serviceRecipientId="1" version="0" activity="0"/>
    <supportplanactions workUuid="046ce030-0001-45c2-83f2-988b8678718e" created="2013-03-01 15:00:00" id="4" actionId="8" status="1" statusChange="true" serviceRecipientId="1" version="0" activity="0"/>
    <supportplanactions workUuid="046ce030-0001-45c2-83f2-988b8678718e" created="2013-03-01 15:00:00" id="5" actionId="9" status="1" statusChange="true" serviceRecipientId="1" version="0" activity="0"/>
    <supportplanactions workUuid="046ce030-0002-45c2-83f2-988b8678718e" created="2013-03-02 15:00:00" id="6" actionId="5" status="3" statusChange="true" serviceRecipientId="1" version="0" activity="0"/>
    <supportplanactions workUuid="046ce030-0003-45c2-83f2-988b8678718e" created="2013-03-03 15:00:00" id="7" actionId="6" status="3" statusChange="true" serviceRecipientId="1" version="0" activity="0"/>
    <supportplanactions workUuid="046ce030-0004-45c2-83f2-988b8678718e" created="2013-03-04 15:00:00" id="8" actionId="9" status="3" statusChange="true" serviceRecipientId="1" version="0" activity="0"/>
    <supportplanactions workUuid="046ce030-0005-45c2-83f2-988b8678718e" created="2013-03-05 15:00:00" id="9" actionId="5" status="4" statusChange="true" serviceRecipientId="1" version="0" activity="0"/>
    <supportplanactions workUuid="046ce030-0006-45c2-83f2-988b8678718e" created="2013-03-06 15:00:00" id="10" actionId="6" status="4" statusChange="true" serviceRecipientId="1" version="0" activity="0"/>
    <supportplanactions workUuid="046ce030-0007-45c2-83f2-988b8678718e" created="2013-03-07 15:00:00" id="11" actionId="13" status="4" statusChange="false" serviceRecipientId="1" version="0" activity="0"/>
    <supportplanactions workUuid="046ce030-0008-45c2-83f2-988b8678718e" created="2013-03-08 15:00:00" id="12" actionId="8" status="4" statusChange="false" serviceRecipientId="1" version="0" activity="0"/>

    <!-- have an action saved directly as achieved -->
    <supportplanactions workUuid="046ce030-0009-45c2-83f2-988b8678718e" created="2013-03-03 15:00:00" id="13" actionId="12" status="3" statusChange="true" serviceRecipientId="1" version="0" activity="0"/>

    <outcomes discriminator_orm="standard" disabled="false" id="1" name="economic wellbeing" version="0"/>
    <outcomes discriminator_orm="standard" disabled="false" id="2" name="enjoy and achieve" version="0"/>

    <risks disabled="false" id="1" name="risk 1" outcomeId="1" version="0"/>
    <risks disabled="false" id="2" name="risk 2" outcomeId="1" version="0"/>
    <risks disabled="false" id="3" name="risk 3" outcomeId="1" version="0"/>

    <risks disabled="false" id="4" name="risk 4" outcomeId="2" version="0"/>
    <risks disabled="false" id="5" name="risk 5" outcomeId="2" version="0"/>

    <actions id="1" riskId="1" version="0"/>
    <actions id="2" riskId="1" version="0"/>
    <actions id="3" riskId="2" version="0"/>
    <actions id="4" riskId="2" version="0"/>
    <actions id="5" riskId="3" version="0"/>
    <actions id="6" riskId="3" version="0"/>
    <actions id="7" riskId="3" version="0"/>
    <actions id="8" riskId="3" version="0"/>

    <actions id="9" riskId="4" version="0"/>
    <actions id="10" riskId="4" version="0"/>
    <actions id="11" riskId="4" version="0"/>
    <actions id="12" riskId="4" version="0"/>
    <actions id="13" riskId="5" version="0"/>

</dataset>
