package com.ecco.viewModels;

public class ServiceTypeTaskDefinitionViewModel implements ViewModel {
    private Long id;
    private String name;
    private boolean allowNext;

    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Override
    public boolean isNewEntity() {
        return id == null;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setAllowNext(boolean allowNext) {
        this.allowNext = allowNext;
    }

    public boolean isAllowNext() {
        return allowNext;
    }

    @Override
    public int hashCode() {
        int hashCode = 0xa5e257e6;
        if (isAllowNext()) {
            hashCode ^= 0xf3721e66;
        }
        hashCode ^= id;
        hashCode ^= name.hashCode();
        return hashCode;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj.getClass() != getClass()) {
            return false;
        }

        ServiceTypeTaskDefinitionViewModel other = (ServiceTypeTaskDefinitionViewModel) obj;
        return ((id == null && other.id == null) || (id != null && id.equals(other.id)))
                && ((name == null && other.name == null) || (name != null && name.equals(other.name)))
                && allowNext == other.allowNext;
    }
}
