<html>
<head><title>ecco 3rd party licenses</title></head>
<body>
<pre>
NOTE: The list below is as yet unprocessed, but does cover the full ecco software product.

Self hosting clients should provide their own Tomcat 7 (or derivative) and Java 7 (either Oracle Java 7 or OpenJDK distributions)


Note: If you wish to use the included MySQL client library, you must use it with a commercially licensed server.


License Summary:
================
For all of the below, number of licenses, and restrictions are N/A, as they are all open source.

Software         Supplier       License    Purpose & Notes
--------         --------       -------    ---------------
<PERSON>era           Apache         ASL2       Atom Feed publishing and processing
Activiti         (Authors)      ASL2       Workflow engine
Apache Commons   Apache         ASL2       Software utilities
Asm      INRIA,France Telecom   BSD        Code generation
AspectJ          Eclipse        EPL1       Aspect-oriented programming support
Axiom            Apache         ASL2       Web-services/SOAP handling
Batik            Apache         ASL2       Graphics rendering
Bouncy Castle    Bouncy Castle  BCastle    Email and encryption tools
CGLIB            Apache         ASL2       Core - Code Generation
Castor           Ralf Joachim   ASL2       XML Mapping
Clickstream      OpenSymphony   MIT        Logging
Cosmo            OSAFoundation  ASL2       Calendaring & scheduling (OSAF now defunct - ecco maintain the code)
Dom4j            MetaStuff      BSD        XML processing
EhCache          Terracotta     ASL2       Data caching
EzMorph          (Authors)      ASL2       Data mapping
Grails           SpringSource   ASL2       Runtime framework
Guava (Google)   (Authors)      ASL2       Utilities (collections, threading, etc)
H2 Database      H2 Group       EPL1       Embedded Database (used for testing/demo)
HDIV             hdiv.org       ASL2       Website security hardening
Hibernate        JBoss          LGPL       Object-Relational database mapping
iCal4j           Ben Fortuna    ICAL4J     Calendar data format
ICU4J            IBM            ICU        Internationalisation
iText            Lowagie        MPL1.1     PDF generation (ver. 2.1.7 - last open source version)
Jackrabbit       Apache         ASL2       Calendar and data repository (part of Cosmo)
Jackson          FasterXML      ASL2       Data (JSON) transfer codec
Jasper Reports   Jaspersoft     LGPL       Visual Reports
JavaMail         Sun/Oracle     CDDL       Email send/receive
Javassist       Shigeru Chiba   MPL1.1     Bytecode processing
Jaxen            Werken         NBSD       XPath processing
JDOM             JDOM Project   JDOM       XML processing
JDT Core         Eclipse        EPL1       Java compiler
JFreeChart/JCommon JFree        LGLL       Graphical Charts
JodaTime     Stephen Colebourne ASL2       DateTime support
JsonLib          (Authors)      ASL2       Data encoding
JsonTools   S.D.I.-Consulting   LGPL       JSON data processing
JSRxAPIs         Oracle et al   CDDL       Standard Java APIs
Liquibase        Nathan Voxland ASL2       Database Schema Migration
Log4j            Apache         ASL2       Logging
MyBatis          MyBatis Team   ASL2       Database data mapping
MySQL Connector  Oracle         GPL        MySQL database connection (*must have commercial server license to use*)
OGNL             (Authors)      ASL2       Data access
Oracle JDBC      Oracle         OTNL       Oracle database connection
Parboiled      Mathias Doenitz  ASL2       Parser engine
Pegdown          (Authors)      ASL2       Markdown rendering
POI              Apache         ASL2       Microsoft document format processing
Quartz           Terracotta     ASL2       Timed event scheduling
SiteMesh         OpenSymphony   OSSL       Web page rendering
SLF4J            QOS.ch         MIT        Logging
Spring Framework SpringSource   ASL2       Core, Dependency Injection, Web, Security
SSLExt           Apache         ASL1.1     Security utils
Velocity         Apache         ASL2       Markup rendering
WSDL4J           IBM            CPL1       Web services utils
Woodstox         FasterXML      ASL2       XML processing
Xerces           Apache         ASL2       XML processing
XMLBeans         Apache         ASL2       XML data mapping


Licenses:
---------
ASL2    - The Apache Software License, Version 2.0
BCastle - Bouncy Castle License - MIT License
BSD     - BSD License
CDDL    - Common Development and Distribution License (CDDL) v1.0
CPL1    - Common Public License, Version 1.0
H2v1    - The H2 License, Version 1.0
ICAL4J  - ICAL4J license (NBSD equiv)
ICU     - ICU license (http://source.icu-project.org/repos/icu/icu/trunk/license.html)
JDOM    - JDOM license (ASL minus acknowledgements clause)
LGPL    - GNU Lesser General Public License
MIT     - MIT License
MPL1.1  - Mozilla Public License Version 1.1
NBSD    - New BSD License (3-clause)
OSSL    - OpenSymphony Software License v1.1



License Details for each included component (JAR file):
=======================================================

     (Public Domain) AntLR (antlr:antlr:2.7.6 - http://www.antlr.org/)
     (Public Domain) AOP alliance (aopalliance:aopalliance:1.0 - http://aopalliance.sourceforge.net)
     (Public Domain) Backport of JSR 166 (backport-util-concurrent:backport-util-concurrent:3.1 - http://backport-jsr166.sourceforge.net/)
     (The Apache Software License, Version 2.0) Batik AWT Utilities (batik:batik-awt-util:1.6-1 - no url defined)
     (The Apache Software License, Version 2.0) Batik Bridge (batik:batik-bridge:1.6-1 - no url defined)
     (The Apache Software License, Version 2.0) Batik CSS (batik:batik-css:1.6-1 - no url defined)
     (The Apache Software License, Version 2.0) Batik DOM (batik:batik-dom:1.6-1 - no url defined)
     (The Apache Software License, Version 2.0) Batik Ext (batik:batik-ext:1.6-1 - no url defined)
     (The Apache Software License, Version 2.0) Batik GUI Utilities (batik:batik-gui-util:1.6-1 - no url defined)
     (The Apache Software License, Version 2.0) Batik GVT (batik:batik-gvt:1.6-1 - no url defined)
     (The Apache Software License, Version 2.0) Batik Parser (batik:batik-parser:1.6-1 - no url defined)
     (The Apache Software License, Version 2.0) Batik Script (batik:batik-script:1.6-1 - no url defined)
     (The Apache Software License, Version 2.0) Batik SVG DOM (batik:batik-svg-dom:1.6-1 - no url defined)
     (The Apache Software License, Version 2.0) Batik Transcoder (batik:batik-transcoder:1.6-1 - no url defined)
     (The Apache Software License, Version 2.0) Batik Utilities (batik:batik-util:1.6-1 - no url defined)
     (The Apache Software License, Version 2.0) Batik XML (batik:batik-xml:1.6-1 - no url defined)
     (The Apache Software License, Version 2.0) Code Generation Library (cglib:cglib:2.2.2 - http://cglib.sourceforge.net/)
     (GNU Lesser General Public License, Version 2.1) (The Apache Software License, Version 2.0) Jackson-annotations (com.fasterxml.jackson.core:jackson-annotations:2.2.2 - http://wiki.fasterxml.com/JacksonHome)
     (GNU Lesser General Public License, Version 2.1) (The Apache Software License, Version 2.0) Jackson-core (com.fasterxml.jackson.core:jackson-core:2.2.2 - http://wiki.fasterxml.com/JacksonHome)
     (GNU Lesser General Public License, Version 2.1) (The Apache Software License, Version 2.0) jackson-databind (com.fasterxml.jackson.core:jackson-databind:2.2.2 - http://wiki.fasterxml.com/JacksonHome)
     (GNU Lesser General Public License, Version 2.1) (The Apache Software License, Version 2.0) Jackson-datatype-Guava (com.fasterxml.jackson.datatype:jackson-datatype-guava:2.2.2 - http://wiki.fasterxml.com/JacksonModuleGuava)
     (GNU Lesser General Public License, Version 2.1) (The Apache Software License, Version 2.0) Jackson-datatype-JODA (com.fasterxml.jackson.datatype:jackson-datatype-joda:2.2.2 - http://wiki.fasterxml.com/JacksonModuleJoda)
     (The Apache Software License, Version 2.0) Spring Test DBUnit (com.github.springtestdbunit:spring-test-dbunit:1.0.1 - https://springtestdbunit.github.com/spring-test-dbunit)
     (The Apache Software License, Version 2.0) FindBugs-jsr305 (com.google.code.findbugs:jsr305:2.0.1 - http://findbugs.sourceforge.net/)
     (The Apache Software License, Version 2.0) Guava: Google Core Libraries for Java (com.google.guava:guava:14.0 - http://code.google.com/p/guava-libraries/guava)
     (The H2 License, Version 1.0) H2 Database Engine (com.h2database:h2:1.3.170 - http://www.h2database.com)
     (ICU License http://source.icu-project.org/repos/icu/icu/trunk/license.html) ICU4J (com.ibm.icu:icu4j:3.8 - http://www-306.ibm.com/software/globalization/icu/)
     (Mozilla Public License v1.1) iText, a Free Java-PDF library (com.lowagie:itext:2.1.7 - http://www.lowagie.com/iText/)
     (The Apache Software License, Version 2.0) make-it-easy (com.natpryce:make-it-easy:3.1.0 - http://code.google.com/p/make-it-easy/)
     (Oracle Technical Network License) ojdbc6 (com.oracle:ojdbc6:11.2.0.3 - http://www.oracle.com/technetwork/licenses/distribution-license-152002.html)

     (GNU Lesser General Public License Version 2.1) Java JSON Tools - Core (com.sdicons.jsontools:jsontools-core:1.4 - http://jsontools.berlios.de)
     (The Apache Software License, Version 2.0) Commons BeanUtils (commons-beanutils:commons-beanutils:1.8.3 - http://commons.apache.org/beanutils/)
     (The Apache Software License, Version 2.0) Commons Chain (commons-chain:commons-chain:1.1 - http://jakarta.apache.org/commons/${pom.artifactId.substring(8)}/)
     (The Apache Software License, Version 2.0) Commons Codec (commons-codec:commons-codec:1.7 - http://commons.apache.org/codec/)
     (The Apache Software License, Version 2.0) Commons Collections (commons-collections:commons-collections:3.2.1 - http://commons.apache.org/collections/)
     (The Apache Software License, Version 2.0) Commons DBCP (commons-dbcp:commons-dbcp:1.4 - http://commons.apache.org/dbcp/)
     (The Apache Software License, Version 2.0) Commons Digester (commons-digester:commons-digester:2.1 - http://commons.apache.org/digester/)
     (The Apache Software License, Version 2.0) Commons FileUpload (commons-fileupload:commons-fileupload:1.2.2 - http://commons.apache.org/fileupload/)
     (The Apache Software License, Version 2.0) commons-id (commons-id:commons-id:0.1-dev - no url defined)
     (The Apache Software License, Version 2.0) Commons IO (commons-io:commons-io:2.4 - http://commons.apache.org/io/)
     (The Apache Software License, Version 2.0) Commons Lang (org.apache.commons:commons-lang3:2.6 - http://commons.apache.org/lang/)
     (The Apache Software License, Version 2.0) Logging (commons-logging:commons-logging-api:1.1 - http://jakarta.apache.org/commons/logging/)
     (The Apache Software License, Version 2.0) Commons Pool (commons-pool:commons-pool:1.5.4 - http://commons.apache.org/pool/)
     (The Apache Software License, Version 2.0) Validator (commons-validator:commons-validator:1.3.1 - http://jakarta.apache.org/commons/${pom.artifactId.substring(8)}/)
     (BSD) dom4j (dom4j:dom4j:1.6.1 - http://dom4j.org)
     (Eclipse Public License - v 1.0) jdtcore (eclipse:jdtcore:3.1.0 - no url defined)
     (The Apache Software License, Version 2.0) Javassist (javassist:javassist:3.11.0.GA - http://www.javassist.org/)
     (Common Development and Distribution License (CDDL) v1.0) JavaBeans(TM) Activation Framework (javax.activation:activation:1.1.1 - http://java.sun.com/javase/technologies/desktop/javabeans/jaf/index.jsp)
     (Common Development and Distribution License (CDDL) v1.0) JSR-250 Common Annotations for the JavaTM Platform (javax.annotation:jsr250-api:1.0 - http://jcp.org/aboutJava/communityprocess/final/jsr250/index.html)
     (Common Development and Distribution License (CDDL) v1.0) JavaMail API (javax.mail:mail:1.4.1 - https://glassfish.dev.java.net/javaee5/mail/)
     (Common Development and Distribution License (CDDL) v1.0) jstl (javax.servlet:jstl:1.2 - no url defined)
     (Common Development and Distribution License (CDDL) v1.0) servlet-api (javax.servlet:servlet-api:2.5 - no url defined)
     (Common Development and Distribution License (CDDL) v1.0) jsp-api (javax.servlet.jsp:jsp-api:2.1 - no url defined)
     (Common Development and Distribution License (CDDL) v1.0) Java Transaction API (javax.transaction:jta:1.1 - http://java.sun.com/products/jta)
     (The Apache Software License, Version 2.0) Bean Validation API (javax.validation:validation-api:1.0.0.GA - no url defined)
     (Common Development and Distribution License (CDDL) v1.0) (GNU General Public Library) Streaming API for XML (javax.xml.stream:stax-api:1.0-2 - no url defined)
     (http://jaxen.codehaus.org/license.html) jaxen (jaxen:jaxen:1.1.1 - http://jaxen.codehaus.org/)
     (JDOM license (ASL minus acknowledgements clause)) jdom (jdom:jdom:1.0 - no url defined)
     (GNU Lesser General Public Licence) jcommon (jfree:jcommon:1.0.15 - http://www.jfree.org/jcommon/)
     (GNU Lesser General Public Licence) jfreechart (jfree:jfreechart:1.0.12 - http://www.jfree.org/jfreechart/)
     (The Apache Software License, Version 2.0) Joda time (joda-time:joda-time:1.6 - http://joda-time.sourceforge.net)
     (Common Public License Version 1.0) JUnit (junit:junit:4.11 - http://junit.org)
     (The Apache Software License, Version 2.0) Apache Log4j (log4j:log4j:1.2.16 - http://logging.apache.org/log4j/1.2/)
     (The GNU General Public License, Version 2) MySQL java connector (mysql:mysql-connector-java:5.1.23 - http://dev.mysql.com/usingmysql/java/)
     (The Apache Software License, Version 2.0) Neko HTML (nekohtml:nekohtml:0.9.5 - no url defined)
     (The Apache Software License, Version 2.0) Ehcache Core (net.sf.ehcache:ehcache-core:2.3.2 - http://ehcache.org)
     (The Apache Software License, Version 2.0) ezmorph (net.sf.ezmorph:ezmorph:1.0.3 - http://ezmorph.sourceforge.net)
     (GNU Lesser General Public License) JasperReports (net.sf.jasperreports:jasperreports:4.7.1 - http://jasperreports.sourceforge.net)
     (The Apache Software License, Version 2.0) json-lib (net.sf.json-lib:json-lib:2.1-osaf20071017 - no url defined)
     (Java HTML Tidy License) JTidy (net.sf.jtidy:jtidy:r938 - http://jtidy.sourceforge.net)
     (The Apache Software License, Version 2.0) OGNL - Object Graph Navigation Library (ognl:ognl:2.7.2 - http://ognl.org)
     (MIT license) clickstream (opensymphony:clickstream:1.0.2 - no url defined)
     (The Apache Software License, Version 2.0) Activiti - BPMN Converter (org.activiti:activiti-bpmn-converter:5.12 - http://activiti.org/modules/activiti-bpmn-converter)
     (The Apache Software License, Version 2.0) Activiti - BPMN Model (org.activiti:activiti-bpmn-model:5.12 - http://activiti.org/modules/activiti-bpmn-model)
     (The Apache Software License, Version 2.0) Activiti - Engine (org.activiti:activiti-engine:5.12 - http://activiti.org/modules/activiti-engine)
     (The Apache Software License, Version 2.0) Activiti - Spring (org.activiti:activiti-spring:5.12 - http://activiti.org/activiti-spring)
     (The Apache Software License, Version 2.0) Abdera Core (org.apache.abdera:abdera-core:0.4.0-incubating - http://incubator.apache.org/abdera/abdera-core)
     (The Apache Software License, Version 2.0) I18N Libraries (org.apache.abdera:abdera-i18n:0.4.0-incubating - http://incubator.apache.org/abdera)
     (The Apache Software License, Version 2.0) Abdera Parser (org.apache.abdera:abdera-parser:0.4.0-incubating - http://incubator.apache.org/abdera/abdera-parser)
     (The Apache Software License, Version 2.0) Abdera Server (org.apache.abdera:abdera-server:0.4.0-incubating - http://incubator.apache.org/abdera/abdera-server)
     (The Apache Software License, Version 2.0) Commons Email (org.apache.commons:commons-email:1.2 - http://commons.apache.org/email/)
     (The Apache Software License, Version 2.0) Activation (org.apache.geronimo.specs:geronimo-activation_1.0.2_spec:1.1 - http://geronimo.apache.org/geronimo-activation_1.0.2_spec)
     (The Apache Software License, Version 2.0) Streaming API for XML (STAX API 1.0) (org.apache.geronimo.specs:geronimo-stax-api_1.0_spec:1.0.1 - http://geronimo.apache.org/specs/geronimo-stax-api_1.0_spec)
     (The Apache Software License, Version 2.0) jackrabbit-jcr-commons (org.apache.jackrabbit:jackrabbit-jcr-commons:1.0-osaf-20060508 - no url defined)
     (The Apache Software License, Version 2.0) jackrabbit-jcr-server (org.apache.jackrabbit:jackrabbit-jcr-server:1.0-osaf-20061023 - no url defined)
     (The Apache Software License, Version 2.0) jackrabbit-jcr-webdav (org.apache.jackrabbit:jackrabbit-jcr-webdav:1.0-osaf-20061023 - no url defined)
     (The Apache Software License, Version 2.0) jackrabbit-server (org.apache.jackrabbit:jackrabbit-server:1.0-osaf-20061023 - no url defined)
     (The Apache Software License, Version 2.0) Apache POI (org.apache.poi:poi:3.8 - http://poi.apache.org/)
     (The Apache Software License, Version 2.0) Apache POI (org.apache.poi:poi-ooxml:3.7 - http://poi.apache.org/)
     (The Apache Software License, Version 2.0) Apache POI (org.apache.poi:poi-ooxml-schemas:3.7 - http://poi.apache.org/)
     (The Apache Software License, Version 2.0) Apache Velocity (org.apache.velocity:velocity:1.5 - http://velocity.apache.org/engine/releases/velocity-1.5/)
     (The Apache Software License, Version 2.0) VelocityTools (org.apache.velocity:velocity-tools:2.0 - http://velocity.apache.org/tools/devel/)
     (The Apache Software License, Version 2.0) Axiom API (org.apache.ws.commons.axiom:axiom-api:1.2.1 - http://ws.apache.org/commons/axiom/modules/axiom-api/)
     (The Apache Software License, Version 2.0) Axiom Impl (org.apache.ws.commons.axiom:axiom-impl:1.2.1 - http://ws.apache.org/commons/axiom/modules/axiom-impl/)
     (The Apache Software License, Version 2.0) XmlBeans (org.apache.xmlbeans:xmlbeans:2.3.0 - http://xmlbeans.apache.org)
     (Eclipse Public License - v 1.0) AspectJ runtime (org.aspectj:aspectjrt:1.7.0 - http://www.aspectj.org)
     (Eclipse Public License - v 1.0) AspectJ weaver (org.aspectj:aspectjweaver:1.7.0 - http://www.aspectj.org)
     (Bouncy Castle Licence - MIT License) Bouncy Castle CMS and S/MIME API (org.bouncycastle:bcmail-jdk14:1.38 - http://www.bouncycastle.org/java.html)
     (Bouncy Castle Licence - MIT License) Bouncy Castle Provider (org.bouncycastle:bcprov-jdk14:1.38 - http://www.bouncycastle.org/java.html)
     (Bouncy Castle Licence - MIT License) Bouncy Castle OpenPGP API (org.bouncycastle:bctsp-jdk14:1.38 - http://www.bouncycastle.org/java.html)
     (The Apache Software License, Version 2.0) Cloud Foundry Runtime for Java/Spring (org.cloudfoundry:cloudfoundry-runtime:0.8.2 - no url defined)
     (The Apache Software License, Version 2.0) Castor (org.codehaus.castor:castor:1.2 - http://castor.org)
     (The Apache Software License, Version 2.0) Jackson (org.codehaus.jackson:jackson-core-asl:1.7.4 - http://jackson.codehaus.org)
     (The Apache Software License, Version 2.0) Data Mapper for Jackson (org.codehaus.jackson:jackson-mapper-asl:1.7.4 - http://jackson.codehaus.org)
     (The Apache Software License, Version 2.0) Woodstox (org.codehaus.woodstox:wstx-asl:3.2.1 - http://woodstox.codehaus.org)
     (GNU Lesser General Public License, Version 2.1) DbUnit Framework (org.dbunit:dbunit:2.4.8 - http://dbunit.sourceforge.net)
     (The Apache Software License, Version 2.0) cosmo-core (org.eccosolutions.osaf.cosmo:cosmo-core:1.4.0.M6 - http://github.com/eccosolutions/cosmo-core/cosmo-core)
     (The Apache Software License, Version 2.0) Grails (org.grails:grails-core:2.2.0.RC2 - http://grails.org/)
     (New BSD License) Hamcrest Core (org.hamcrest:hamcrest-core:1.3 - https://github.com/hamcrest/JavaHamcrest/hamcrest-core)
     (New BSD License) Hamcrest library (org.hamcrest:hamcrest-library:1.3 - https://github.com/hamcrest/JavaHamcrest/hamcrest-library)


     (The Apache Software License, Version 2.0) HDIV Configure (org.hdiv:hdiv-config:2.1.4.ECCO-SNAPSHOT - http://www.hdiv.org)
     (The Apache Software License, Version 2.0) HDIV Core (org.hdiv:hdiv-core:2.1.4.ECCO-SNAPSHOT - http://www.hdiv.org)
     (The Apache Software License, Version 2.0) HDIV jstl-taglibs-1.2 (org.hdiv:hdiv-jstl-taglibs-1.2:2.1.4.ECCO-SNAPSHOT - http://www.hdiv.org)
     (The Apache Software License, Version 2.0) HDIV Spring Mvc (org.hdiv:hdiv-spring-mvc:2.1.4.ECCO-SNAPSHOT - http://www.hdiv.org)


     (GNU Lesser General Public License) Hibernate Commons Annotations (org.hibernate:hibernate-commons-annotations:3.2.0.Final - http://hibernate.org)
     (GNU Lesser General Public License) Hibernate Core (org.hibernate:hibernate-core:3.6.8.Final - http://hibernate.org/hibernate-core)
     (GNU Lesser General Public License) Hibernate Entity Manager (org.hibernate:hibernate-entitymanager:3.6.8.Final - http://hibernate.org/hibernate-entitymanager)
     (GNU Lesser General Public License) Hibernate Envers (org.hibernate:hibernate-envers:3.6.8.Final - http://hibernate.org/hibernate-envers)
     (The Apache Software License, Version 2.0) Hibernate JPA 2 Metamodel Generator (org.hibernate:hibernate-jpamodelgen:1.2.0.Final - http://www.hibernate.org/subprojects/jpamodelgen.html)
     (The Apache Software License, Version 2.0) Hibernate Validator (org.hibernate:hibernate-validator:4.1.0.Final - http://validator.hibernate.org/hibernate-validator)
     (GNU Lesser General Public License) Hibernate Validator Legacy (org.hibernate:hibernate-validator-legacy:4.0.2.GA - http://validator.hibernate.org)
     (Eclipse Public License v1.0) JPA 2.0 API (org.hibernate.javax.persistence:hibernate-jpa-2.0-api:1.0.1.Final - http://hibernate.org)
     (MIT License) HttpUnit (org.httpunit:httpunit:1.7.2 - http://www.httpunit.org)

     (The Apache Software License, Version 2.0) User Type for Joda Time with Hibernate (org.jadira.usertype:usertype.jodatime:1.8 - http://usertype.sourceforge.net/usertype.jodatime/)
     (The Apache Software License, Version 2.0) User Type SPI Classes for Hibernate (org.jadira.usertype:usertype.spi:1.8 - http://usertype.sourceforge.net/usertype.spi/)

     (The Apache Software License, Version 2.0) Liquibase Core (org.liquibase:liquibase-core:2.0.5 - http://www.liquibase.org/liquibase-core)

     (iCal4j - BSD License) iCal4j (org.mnode.ical4j:ical4j:1.0.4 - http://ical4j.sourceforge.net)

     (The MIT License) Mockito (org.mockito:mockito-core:1.9.5 - http://www.mockito.org)

     (The Apache Software License, Version 2.0) MyBatis (org.mybatis:mybatis:3.1.1 - http://www.mybatis.org/core/)
     (MIT License) Objenesis (org.objenesis:objenesis:1.0 - http://objenesis.googlecode.com/svn/docs/index.html)

     (BSD) ASM Core (org.ow2.asm:asm:4.1 - http://asm.objectweb.org/asm/)
     (BSD) ASM Analysis (org.ow2.asm:asm-analysis:4.1 - http://asm.objectweb.org/asm-analysis/)
     (BSD) ASM Tree (org.ow2.asm:asm-tree:4.1 - http://asm.objectweb.org/asm-tree/)
     (BSD) ASM Util (org.ow2.asm:asm-util:4.1 - http://asm.objectweb.org/asm-util/)

     (The Apache Software License, Version 2.0) parboiled-core (org.parboiled:parboiled-core:1.1.5 - http://parboiled.org)
     (The Apache Software License, Version 2.0) parboiled-java (org.parboiled:parboiled-java:1.1.5 - http://parboiled.org)
     (The Apache Software License, Version 2.0) pegdown (org.pegdown:pegdown:1.4.1 - http://pegdown.org)

     (The Apache Software License, Version 2.0.0) Quartz Enterprise Job Scheduler (org.quartz-scheduler:quartz:1.7.3 - http://quartz-scheduler.org/quartz/)

     (MIT License) SLF4J API Module (org.slf4j:slf4j-api:1.5.10 - http://www.slf4j.org)
     (MIT License) SLF4J LOG4J-12 Binding (org.slf4j:slf4j-log4j12:1.5.10 - http://www.slf4j.org)

     (The Apache Software License, Version 2.0) Spring AOP (org.springframework:spring-aop:3.2.2.RELEASE - https://github.com/SpringSource/spring-framework)
     (The Apache Software License, Version 2.0) Spring Aspects (org.springframework:spring-aspects:3.2.2.RELEASE - https://github.com/SpringSource/spring-framework)
     (The Apache Software License, Version 2.0) Spring Beans (org.springframework:spring-beans:3.2.2.RELEASE - https://github.com/SpringSource/spring-framework)
     (The Apache Software License, Version 2.0) Spring Context (org.springframework:spring-context:3.2.2.RELEASE - https://github.com/SpringSource/spring-framework)
     (The Apache Software License, Version 2.0) Spring Context Support (org.springframework:spring-context-support:3.2.2.RELEASE - https://github.com/SpringSource/spring-framework)
     (The Apache Software License, Version 2.0) Spring Core (org.springframework:spring-core:3.2.2.RELEASE - https://github.com/SpringSource/spring-framework)
     (The Apache Software License, Version 2.0) Spring Expression Language (SpEL) (org.springframework:spring-expression:3.2.2.RELEASE - https://github.com/SpringSource/spring-framework)
     (The Apache Software License, Version 2.0) Spring JDBC (org.springframework:spring-jdbc:3.2.2.RELEASE - https://github.com/SpringSource/spring-framework)
     (The Apache Software License, Version 2.0) Spring Object/Relational Mapping (org.springframework:spring-orm:3.2.2.RELEASE - https://github.com/SpringSource/spring-framework)
     (The Apache Software License, Version 2.0) Spring Object/XML Marshalling (org.springframework:spring-oxm:3.2.2.RELEASE - https://github.com/SpringSource/spring-framework)
     (The Apache Software License, Version 2.0) Spring TestContext Framework (org.springframework:spring-test:3.2.2.RELEASE - https://github.com/SpringSource/spring-framework)
     (The Apache Software License, Version 2.0) Spring Transaction (org.springframework:spring-tx:3.2.2.RELEASE - https://github.com/SpringSource/spring-framework)
     (The Apache Software License, Version 2.0) Spring Web (org.springframework:spring-web:3.2.2.RELEASE - https://github.com/SpringSource/spring-framework)
     (The Apache Software License, Version 2.0) Spring Web MVC (org.springframework:spring-webmvc:3.2.2.RELEASE - https://github.com/SpringSource/spring-framework)
     (The Apache Software License, Version 2.0) Spring Data Core (org.springframework.data:spring-data-commons:1.5.0.RELEASE - http://www.springsource.org/spring-data/spring-data-commons)
     (The Apache Software License, Version 2.0) Spring Data JPA (org.springframework.data:spring-data-jpa:1.3.0.RELEASE - http://www.springframework.org/spring-data/jpa)
     (The Apache Software License, Version 2.0) Spring Hateoas (org.springframework.hateoas:spring-hateoas:0.6.0.RELEASE - http://github.com/SpringSource/spring-hateoas)
     (The Apache Software License, Version 2.0) Spring LDAP Core (org.springframework.ldap:spring-ldap-core:1.3.1.RELEASE - no url defined)
     (The Apache Software License, Version 2.0) spring-security-acl (org.springframework.security:spring-security-acl:3.1.3.RELEASE - http://springsource.org/spring-security)
     (The Apache Software License, Version 2.0) spring-security-config (org.springframework.security:spring-security-config:3.1.3.RELEASE - http://springsource.org/spring-security)
     (The Apache Software License, Version 2.0) spring-security-core (org.springframework.security:spring-security-core:3.1.3.RELEASE - http://springsource.org/spring-security)
     (The Apache Software License, Version 2.0) spring-security-ldap (org.springframework.security:spring-security-ldap:3.1.3.RELEASE - http://springsource.org/spring-security)
     (The Apache Software License, Version 2.0) spring-security-taglibs (org.springframework.security:spring-security-taglibs:3.1.3.RELEASE - http://springsource.org/spring-security)
     (The Apache Software License, Version 2.0) spring-security-web (org.springframework.security:spring-security-web:3.1.3.RELEASE - http://springsource.org/spring-security)
     (The Apache Software License, Version 2.0) Spring Security Kerberos Core (org.springframework.security.kerberos:spring-security-kerberos-core:1.0.0.CI-SNAPSHOT - http://static.springsource.org/spring-security/site/extensions/krb/index.html)

     (The Apache Software License, Version 2.0) Spring Binding (org.springframework.webflow:spring-binding:2.3.0.RELEASE - no url defined)
     (The Apache Software License, Version 2.0) Spring JavaScript (org.springframework.webflow:spring-js:2.3.0.RELEASE - no url defined)
     (The Apache Software License, Version 2.0) Spring JavaScript Resources (org.springframework.webflow:spring-js-resources:2.3.0.RELEASE - no url defined)
     (The Apache Software License, Version 2.0) Spring WS Core (org.springframework.ws:spring-ws-core:2.0.2.RELEASE - no url defined)
     (The Apache Software License, Version 2.0) Spring XML (org.springframework.ws:spring-xml:2.0.2.RELEASE - no url defined)

     (The Apache Software License, Version 2.0) Synyx Messagesource for Spring (org.synyx:messagesource:0.6.1 - http://messagesource.synyx.org/static)
     (The Apache Software License, Version 2.0) Unitils core module (org.unitils:unitils-core:3.3 - http://www.unitils.org/unitils-core/)

     (The Apache Software License, Version 2.0) oro (oro:oro:2.0.8 - no url defined)

     (The P6Spy Software License, Version 1.1) P6Spy (p6spy:p6spy:1.3 - http://www.p6spy.com/)

     (Mozilla Public License version 1.1) Rhino (rhino:js:1.6R5 - http://www.mozilla.org/rhino/)

     (Apache Software License, Version 1.1) sslext (sslext:sslext:1.2-0 - no url defined)

     (The Apache Software License, Version 2.0) StAX API (stax:stax-api:1.0.1 - http://stax.codehaus.org/)

     (Common Public License, Version 1.0) WSDL4J (wsdl4j:wsdl4j:1.6.1 - http://sf.net/projects/wsdl4j)

     (The Apache Software License, Version 2.0) Xalan Java Serializer (xalan:serializer:2.7.1 - http://xml.apache.org/xalan-j/)
     (The Apache Software License, Version 2.0) Xerces2-j (xerces:xercesImpl:2.10.0 - https://xerces.apache.org/xerces2-j/)
     (The Apache Software License, Version 2.0) xmlParserAPIs (xerces:xmlParserAPIs:2.6.1 - no url defined)
     (The Apache Software License, Version 2.0) (The SAX License) (The W3C License) XML Commons External Components XML APIs (xml-apis:xml-apis:1.4.01 - http://xml.apache.org/commons/components/external/)
     (The Apache Software License, Version 2.0) xmlParserAPIs (xml-apis:xmlParserAPIs:2.0.2 - no url defined)
</pre>
</body>
</html>
