<%@tag body-content="scriptless" pageEncoding="UTF-8" trimDirectiveWhitespaces="true"%>

<%@attribute name="noCollapse" description="set true to flatten dropdown" required="false" type="java.lang.Boolean" %>
<%@attribute name="adminMenuText" description="if supplied, we enable the admin action with this text"
    required="false" type="java.lang.String" %>

<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@taglib prefix="ecco" tagdir="/WEB-INF/tags" %>
<%@taglib prefix="requireJs" tagdir="/WEB-INF/tags/requireJs" %>
<%@taglib prefix="security" uri="http://www.springframework.org/security/tags" %>
<%@taglib prefix="s" uri="http://www.springframework.org/tags" %>


<s:eval var="username" htmlEscape="true" expression="T(com.ecco.security.SecurityUtil).getAuthenticatedUsernameOrNull()" />
<c:set var="username" value="${username != null ? username: '-'}"/>

<security:authorize var="canDoSysAdmin" access="hasAnyRole('ROLE_SYSADMIN')"/>
<security:authorize var="canDoAdmin" access="hasAnyRole('ROLE_ADMIN')"/>

<c:url var="baseUrl" value="/nav/secure/welcome.html"/>

<c:choose>
    <c:when test="${!noCollapse}">
        <li class="dropdown">
            <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                <i class="fa fa-user"></i>&nbsp;<span class="current-user">
                <c:out value="${username}"/></span>
                &nbsp;<i class="fa fa-caret-down"></i>
            </a>
            <ul class="dropdown-menu">
                <jsp:doBody/>
                <c:if test="${canDoAdmin && !empty adminMenuText || canDoSysAdmin}">
                    <requireJs:import modules="ecco-components"/>
                    <li><a class="menu-action-edit-mode" role="button" href="#">
                        <i class="fa fa-cog"></i> ${empty adminMenuText ? "config mode" : adminMenuText}</a></li>
                </c:if>
            </ul>
        </li>
    </c:when>
    <c:otherwise>
        <jsp:doBody/>

    <li class="dropdown">
        <a href="#" class="dropdown-toggle" data-toggle="dropdown">
            <i class="fa fa-user"></i>&nbsp;<span class="current-user"><c:out value="${username}"/></span>
            &nbsp;<b class="caret"></b>
        </a>
        <%--FIXME: This breaks with both noCollapse and doBody stuff.  The dropdown content goes in the tag body --%>
        <ul class="dropdown-menu">
            <c:if test="${canDoAdmin && !empty adminMenuText || canDoSysAdmin}">
                <requireJs:import modules="ecco-components"/>
                <li><a class="menu-action-edit-mode" role="button" href="#">
                    <i class="fa fa-cog"></i> ${empty adminMenuText ? "config mode" : adminMenuText}</a></li>
            </c:if>
        </ul>
    </li>
    </c:otherwise>
</c:choose>

