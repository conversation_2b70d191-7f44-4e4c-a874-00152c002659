<%@page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>

<%@ include file="/WEB-INF/views/includes.jsp" %>

<c:set var="actionTmp" value="${actionName}"/>

<c:if test="${action == 'wizard'}">
    <c:if test="${empty actionText}">
        <c:set var="actionText" value="next"/>
    </c:if>
    <c:if test="${empty actionName}">
        <c:set var="actionTmp" value="_eventId_next"/>
    </c:if>
    <input type="submit" class="button hidden-print" name="${actionTmp}" value="${actionText}"/>
</c:if>

<c:if test="${action == 'add'}">
    <c:if test="${empty actionText}">
        <spring:message code="add" var="actionText"/>
    </c:if>
    <c:if test="${empty actionName}">
        <c:set var="actionTmp" value="_eventId_save"/>
    </c:if>
    <input type="submit" class="button hidden-print" name="${actionTmp}" value="${actionText}"/>
</c:if>
<c:if test="${action == 'edit'}">
    <c:if test="${empty actionText}">
        <spring:message code="update" var="actionText"/>
    </c:if>
    <c:if test="${empty actionName}">
        <c:set var="actionTmp" value="_eventId_save"/>
    </c:if>
    <input type="submit" class="button hidden-print" name="${actionTmp}" value="${actionText}"/>
    <%-- when we had our edit/save blockUI --%>
    <%--
    <input class="button jsHide" type="submit" name="_finish" value="<fmt:message key="update"/>" />
    <a href="#" style="display: none;" class="button editSaveCancel"><fmt:message key="cancel"/></a>
    <a href="#" id="test" class="button jsShow editSaveToggle"><fmt:message key="edit"/></a>
    --%>
</c:if>
<c:if test="${action == 'search'}">
    <c:if test="${empty actionText}">
        <spring:message code="find" var="actionText"/>
    </c:if>
    <c:if test="${empty actionName}">
        <c:set var="actionTmp" value="_eventId_search"/>
    </c:if>
    <input type="submit" class="button hidden-print" name="${actionTmp}" value="${actionText}"/>
</c:if>
