<%@page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" trimDirectiveWhitespaces="true"%>

<%@include file="/WEB-INF/views/includes.jsp"%>
<%@include file="/WEB-INF/views/reference-data.jsp"%>

<jsp:useBean id="dateToShow" scope="request" type="java.lang.String"/>
<jsp:useBean id="demandType" scope="request" type="java.lang.String"/>
<jsp:useBean id="resourceTypeCode" scope="request" class="java.lang.String"/>
<jsp:useBean id="resourceFilter" scope="request" class="java.lang.String"/>
<jsp:useBean id="demandFilter" scope="request" class="java.lang.String"/>

<%@taglib prefix="page" tagdir="/WEB-INF/tags/page"%>
<%@taglib prefix="requireJs" tagdir="/WEB-INF/tags/requireJs"%>

<!DOCTYPE html>
<html>
    <head>
        <page:page-head-meta/>
        <!--
        Java Version             <c:out value="${applicationProperties.javaVersion}"/>
        Build CommitId:          <c:out value="${applicationProperties.gitCommitId}"/>
        Build Branch:            <c:out value="${applicationProperties.gitBranch}"/>
        Build CommitTime:        <c:out value="${applicationProperties.gitCommitTime}"/>
        -->

        <link rel="icon" href="${applicationProperties.resourceRootPath}themes/ecco/images/favicon.ico">
        <link rel="shortcut icon" href="${applicationProperties.resourceRootPath}themes/ecco/images/favicon.ico">

        <link href="${applicationProperties.resourceRootPath}bootstrap/css/bootstrap.min.css" rel="stylesheet"/>
        <link href="${applicationProperties.resourceRootPath}bootstrap/css/bootstrap-modal-bs3patch.css" rel="stylesheet"/>
        <link href="${applicationProperties.resourceRootPath}bootstrap/css/bootstrap-modal.css" rel="stylesheet"/>
        <link href="${applicationProperties.resourceRootPath}bootstrap/css/bootstrap-theme.min.css" rel="stylesheet"/>
        <link href="${applicationProperties.resourceRootPath}css/common/common.css" rel="stylesheet"/>
        <link href="${applicationProperties.resourceRootPath}css/common/house_v2.css" rel="stylesheet"/>
        <link href="${applicationProperties.resourceRootPath}css/common/banner.css" rel="stylesheet"/>
        <link href="${applicationProperties.resourceRootPath}font-awesome/css/font-awesome.min.css" rel="stylesheet">
        <link href="${applicationProperties.resourceRootPath}css/rota/rota.css" rel="stylesheet"/>
        <link href="${applicationProperties.resourceRootPath}css/jqueryui/jqueryui.css" rel="stylesheet"/>
        <link href="${applicationProperties.resourceRootPath}css/jqueryui/jquery-popbox.css" rel="stylesheet"/>

        <c:url var="apiPath" value="/api/"/>
        <requireJs:init root="${applicationProperties.resourceRootPath}" apiPath="${apiPath}" devMode="${devMode}"/>
        <requireJs:import modules="rota/rota-DELETED controls/banner-titlebar bootstrap"/>
    </head>

    <body class="" data-session-len="${empty sessionLength ? 0 : sessionLength}">
        <page:page-banner>
            <page:page-menus-bs quickGuideId="quickGuide" helpId="rota-overview">
                <c:if test="${param.weekView ne null}"><%-- any value for weekView gives true - dayView is just to hack the URL --%>
                    <li><a href="?dayView=true${param.builder ne null ? '&builder=true' : ''}">
                        <i class="fa fa-calendar-o"></i> day view</a></li>
                </c:if>
                <c:if test="${param.weekView eq null}">
                    <li><a href="?weekView=true${param.builder ne null ? '&builder=true' : ''}">
                        <i class="fa fa-calendar"></i> week view</a></li>
                </c:if>
                <c:if test="${param.builder ne null}"> <%-- on runbuilder so switch to rota (HACK: builder and path)--%>
                    <li><a href="../rota?${param.weekview ne null ? 'weekView=true' : 'dayView=true'}">
                        <i class="fa fa-calendar-o"></i> rota</a></li>
                </c:if>
                <c:if test="${requestScope['javax.servlet.forward.request_uri'].indexOf('building') > -1}">
                    <c:if test="${param.builder eq null}"> <%-- on rota so switch to runbuilder --%>
                        <li><a href="rota/carerun?builder=true&${param.weekview ne null ? 'weekView=true' : 'dayView=true'}">
                            <i class="fa fa-calendar-o"></i> run builder</a></li>
                    </c:if>
                </c:if>
            </page:page-menus-bs>

        </page:page-banner>
        <page:page-titlebar>
            <div class="clearfix">
                <div class="pull-left" id="status"></div>
                <div class="pull-right" id="rota-sr-popup"></div>
                <h4 class="pull-right" id="rota-label" style="width:260px; height: 38px; text-align: right"></h4>
            </div>
            <div id="controls"></div>
            <div id="properties">
                <em>Click on a <fmt:message key="rota.demandType.${demandType}.displayName" /> or activity to see details and actions here</em>
            </div>
            <div id="controls-filters"></div>
        </page:page-titlebar>
        <div id="content">
            <div id="availability-demandType" data-demandType="${demandType}"></div> <%-- NB so availability knows what to load in ddl --%>
            <div id="rota" data-date="<c:out value="${dateToShow}"/>"
                data-resourceFilter="<c:out value="${resourceFilter}"/>"
                data-demandFilter="<c:out value="${demandFilter}"/>"
                ${empty param.weekView ? "" : 'data-weekview' }></div>
        </div>
        <page:modal title="Rota Administration" id="quickGuide">
            <h3>Service Agreements</h3>
            <p>For a client to have appointments scheduled using the rota, they must have a
            service agreement (with an end date) covering the date.  Once a client has a service agreement,
            their requirements can be recorded either as a regular schedule (e.g. Mon & Tues from 3-3:30pm),
            and/or appointment requests can be added as <em>ad-hoc</em> appointments.
            </p>
            <h4>ad-hoc appointments</h4>
            <p>You can create an ad-hoc appointment for a client on any date covered by a service
            agreement (in the client's referral). <strong>Note: The service agreement must always have an end
            date after the date for which you want to create an appointment.</strong>
        </page:modal>
    </body>
</html>

