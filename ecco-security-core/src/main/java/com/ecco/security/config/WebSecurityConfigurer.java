package com.ecco.security.config;

import com.azure.spring.aad.webapp.AADWebSecurityConfigurerAdapter;
import com.ecco.security.SecurityUtil;
import com.ecco.security.authentication.MagicTokenAuthenticationProvider;
import com.ecco.security.service.CustomDaoAuthenticationProvider;
import com.ecco.security.service.MfaAuthentication;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.core.annotation.Order;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authorization.AuthorizationDecision;
import org.springframework.security.authorization.AuthorizationManager;
import org.springframework.security.config.annotation.ObjectPostProcessor;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.oauth2.client.oidc.userinfo.OidcUserRequest;
import org.springframework.security.oauth2.client.userinfo.OAuth2UserService;
import org.springframework.security.oauth2.core.oidc.user.OidcUser;
import org.springframework.security.web.access.ExceptionTranslationFilter;
import org.springframework.security.web.access.intercept.FilterSecurityInterceptor;
import org.springframework.security.web.access.intercept.RequestAuthorizationContext;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
import org.springframework.security.web.authentication.switchuser.SwitchUserFilter;
import org.springframework.web.context.request.RequestContextListener;
import org.springframework.web.servlet.handler.HandlerMappingIntrospector;


/**
 * This is applicationContext-security.xml for spring-boot (specifically, security:http name="defaultHttpSecurity")
 * However, we don't configure page-based security, as WebAppSecurityConfigurer does that.
 *
 * Used by spring's WebSecurityConfigurerAdapter (in this case, AADWebSecurityConfigurerAdapter, for 365).
 * Used in common med-security spring.factories.
 */
@Configuration
@Import(SecurityComponentConfig.class)
@Order(95)
//@EnableGlobalAuthentication was replaced by med-security spring.factories (org.springframework.boot.autoconfigure.EnableAutoConfiguration, see e56a4148)
public class WebSecurityConfigurer extends AADWebSecurityConfigurerAdapter {

    @Autowired
    private CustomDaoAuthenticationProvider eccoAuthenticationProvider;

    @Autowired
    private MagicTokenAuthenticationProvider magicTokenAuthenticationProvider;

    @Autowired
    private SwitchUserFilter switchUserProcessingFilter;

    @Autowired
    @Qualifier("webSuccessHandler")
    private AuthenticationSuccessHandler webSuccessHandler;

    @Autowired
    private OAuth2UserService<OidcUserRequest, OidcUser> eccoEnhancingOidcUserService;

    public WebSecurityConfigurer() {
        super();
        //setTrustResolver(new MfaTrustResolver());
    }
//
//    @Bean
//    public AuthenticationTrustResolver trustResolver() {
//        return new MfaTrustResolver();
//    }

/*
    @Bean
    AuthorizationManager<RequestAuthorizationContext> mfaAuthorizationManager() {
        return (authentication, context) -> new AuthorizationDecision(authentication.get() instanceof MfaAuthentication);
    }
*/

    /**
     * Allows Azure /login to work, since spring security needs access to the request outside DispatcherServlet.
     */
    @Bean
    @ConditionalOnMissingBean(RequestContextListener.class)
    public RequestContextListener requestContextListener() {
        return new RequestContextListener();
    }

    @Override
    @Bean
    public AuthenticationManager authenticationManagerBean() throws Exception {
        return super.authenticationManagerBean();
    }

    @Bean
    public AuthenticationProvider mfaPassthruAuthenticationProvider() {
        return new AuthenticationProvider() {
            @Override
            public Authentication authenticate(Authentication authentication) throws AuthenticationException {
                if (authentication instanceof MfaAuthentication) {
                    // TODO: deal with code
//                    return ((MfaAuthentication) authentication).getFirst();
                    return authentication;
                }
                else
                    return eccoAuthenticationProvider.authenticate(authentication);
            }

            @Override
            public boolean supports(Class<?> authentication) {
                return MfaAuthentication.class.isAssignableFrom(authentication) || eccoAuthenticationProvider.supports(authentication);
            }

            @Override
            public String toString() {
                return super.toString().concat("-MFA");
            }
        };
    }

    @Override
    protected void configure(AuthenticationManagerBuilder auth) throws Exception {
        auth.authenticationProvider(mfaPassthruAuthenticationProvider());
        auth.authenticationProvider(magicTokenAuthenticationProvider);
    }

    @Bean
    public AuthenticationSuccessHandler mfaPassthruAuthenticationHandler() {
        var mfaHandler = new MfaAuthenticationHandler("/nav/mfa/validate");
        return (request, response, authentication) -> {
            var user = SecurityUtil.getUser(authentication);
            if (!user.isMfaRequired()) {
                this.webSuccessHandler.onAuthenticationSuccess(request, response, authentication);
            } else {
                mfaHandler.onAuthenticationSuccess(request, response, authentication);
            }
        };
    }

    @Bean
    AuthorizationManager<RequestAuthorizationContext> mfaAuthorizationManager() {
        return (authentication,
                context) -> new AuthorizationDecision(authentication.get() instanceof MfaAuthentication);
    }

    // see https://github.com/spring-projects/spring-framework/issues/20848
    // mvcMatchers ties mvc paths with security - it requires this bean which should be provided in WebMvcConfigurationSupport
    // but due to our wiring it is not provided, but seems to be the only piece missing
    // FIXME: This should be provided elsewhere by @EnableMvcSecurity and we should separate different forms of
    //  authentication config with the endpoint config
    @Bean(name = "mvcHandlerMappingIntrospector")
    @ConditionalOnMissingBean
    public HandlerMappingIntrospector mvcHandlerMappingIntrospector() {
        return new HandlerMappingIntrospector();
    }

    @Override
    protected void configure(HttpSecurity http) throws Exception {

        // @formatter:off
        http
            .cors()
        .and()
                // TODO: I think this stuff is okay as it's not dependent on which servlet you are in
            .csrf().disable() // We can disable this because we're using SameSite:Strict by default but override with -Dcookie.insecure=true

            // check this works by a hit in RequestMatcherDelegatingAuthorizationManager#check...for loop, or MvcRequestMatcher#matcher
            // see https://docs.spring.io/spring-security/reference/servlet/authorization/authorize-http-requests.html
            .authorizeHttpRequests((authorize) -> authorize
                    .mvcMatchers("/nav/mfa/validate").access(mfaAuthorizationManager()) // // for spring boot single context
                    // needed for ecco-servlet since RequestMatcherDelegatingAuthorizationManager strips the servlet path
                    // due to skipServletPathDetermination
                    .mvcMatchers("/mfa/validate").access(mfaAuthorizationManager()) // for web.xml ecco-servlet
                    .antMatchers(
                            "/",
                            "/p/**", // public, see PublicPageController which is wired with /p
                            "/ui/**", // FIXME Hack for OfflineResourceController
                            "/r/**", // FIXME
                            "/nav/r/refer", // IN USE?? allow specific case - inbound referral
                            "/nav/r/refer/**",
                            "/nav/p/r/refer", // IN-USE? allow specific case - inbound referral /nav/p/r/refer (NB for incidents we don't use nav)
                            "/nav/p/r/refer/**",
                            "/nav/secure/login.html",
                            "/nav/secure/login.old.html",
                            "/nav/secure/changePassword.html",
                            "/nav/secure/logoutSuccess.html",
                            "/nav/secure/logoutExpiredSuccess.html",
                            "/nav/secure/**error**.html",
                            "/magic/**"
                    ).permitAll()
                    .antMatchers("/**/welcome/settings/**").hasRole("ADMIN")
//                        .access("isFullyAuthenticated() and hasAnyRole('ADMIN')")
                    .antMatchers("/nav/secure/admin/switchUser**")
                        .hasAnyRole("SWITCHUSER", "PREVIOUS_ADMINISTRATOR")
                    .antMatchers( "/nav/secure/admin/**")
                            .hasAnyRole("ADMIN", "SYSADMIN")
        //            .access("isFullyAuthenticated() and hasAnyRole('ADMIN', 'SYSADMIN')")
                    .antMatchers( "**/welcome/admin/users/**")
                            .hasRole("ADMINLOGIN")
        //            .access("isFullyAuthenticated() and hasAnyRole('ADMINLOGIN')")
                    .antMatchers( "**/welcome/admin/**")
                        // ADMIN should cover SYSADMIN also
                        .hasAnyRole("ADMIN", "DELETEREFERRAL", "ADMINGROUPSUPPORT")
        //            .access("isFullyAuthenticated() and hasAnyRole('ADMIN', 'DELETEREFERRAL', 'ADMINGROUPSUPPORT')")
                    .antMatchers(
                            "/nav/r/**", "/nav/w/**",
                            "/nav/secure/**", "/dynamic/secure/**", "/online/**", "/nav/**" // TODO: move /dynamic/ and /online/ to /nav/
                    ).authenticated()
                .anyRequest().denyAll()
            )

            .formLogin()
//                .successForwardUrl("/yay-loggedIn")
//                .failureForwardUrl("/oh-failed")
                .loginPage("/nav/secure/login.html")
                .loginProcessingUrl("/nav/secure/j_acegi_security_check")
                .usernameParameter("j_username")
                .passwordParameter("j_password")
                .failureUrl("/nav/secure/login.html?login_error=1")
                .successHandler(mfaPassthruAuthenticationHandler())
//        .and()
//            .anonymous().authorities("ROLE_ANONYMOUS").key("doesNotMatter").principal("anonymousUser")
        .and()
            .logout()
                .logoutUrl("/nav/secure/logout.html")
                .logoutSuccessUrl("/nav/secure/logoutSuccess.html")
        .and()

        .exceptionHandling((exceptions) -> exceptions
            .withObjectPostProcessor(new ObjectPostProcessor<ExceptionTranslationFilter>() {
                @Override
                public <O extends ExceptionTranslationFilter> O postProcess(O filter) {
                    filter.setAuthenticationTrustResolver(new MfaTrustResolver());
                    return filter;
                }
            })
            //.accessDeniedHandler(exceptionHandling()) // default AccessDeniedHandlerImpl
            .accessDeniedPage("/nav/secure/checkedErrorAccessDenied.html")
        )
        .addFilterAfter(switchUserProcessingFilter, FilterSecurityInterceptor.class);

        // oauth2Login()
        super.configure(http);
        http.oauth2Login()
            // success uses the existing SavedRequestAwareAuthenticationSuccessHandler
            // but can be ocerriden with .defaultSuccessUrl() / .successHandler(eccoAuthenticationSuccessHandler365)
            // Have to use own bean name here because com.azure.spring.aad.webapp.AADWebAppConfiguration.oidcUserService is unconditional
            // prior to https://github.com/Azure/azure-sdk-for-java/blame/5bc550c9a5de4f8ee93a5b3141500ee34be3850d/sdk/spring/spring-cloud-azure-autoconfigure/src/main/java/com/azure/spring/cloud/autoconfigure/aad/configuration/AadWebApplicationConfiguration.java#L38
            .userInfoEndpoint().oidcUserService(eccoEnhancingOidcUserService)
        ;

        // @formatter:off
    }
}
