package com.ecco.dom.submissions;

import javax.persistence.CascadeType;
import javax.persistence.DiscriminatorColumn;
import javax.persistence.DiscriminatorType;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.Inheritance;
import javax.persistence.InheritanceType;
import javax.persistence.JoinColumn;
import javax.persistence.Lob;
import javax.persistence.OneToOne;
import javax.persistence.Table;

import com.ecco.dom.upload.UploadedBytes;
import com.ecco.infrastructure.entity.AbstractLongKeyedEntity;
import org.hibernate.annotations.DiscriminatorOptions;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;
import org.hibernate.annotations.Type;
import org.joda.time.DateTime;

@Entity
@Table(name="submissions")
@Inheritance(strategy=InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name="discriminator_orm", discriminatorType=DiscriminatorType.STRING)
@DiscriminatorOptions(force = true)
public abstract class Submission extends AbstractLongKeyedEntity {

    @Type(type="org.jadira.usertype.dateandtime.joda.PersistentDateTime")
    DateTime submitted;
    @Lob
    String responseRaw;
    String responseStatus;
    @OneToOne(cascade={CascadeType.PERSIST, CascadeType.REMOVE, CascadeType.DETACH, CascadeType.REFRESH}, orphanRemoval=true, fetch=FetchType.LAZY, optional=false)
    @JoinColumn(name="bytesId", insertable=true, updatable=false)
    @NotFound(action=NotFoundAction.EXCEPTION)
    UploadedBytes uploadedBytes;

    public DateTime getSubmitted() {
        return submitted;
    }
    public void setSubmitted(DateTime submitted) {
        this.submitted = submitted;
    }
    public String getResponseRaw() {
        return responseRaw;
    }
    public void setResponseRaw(String responseRaw) {
        this.responseRaw = responseRaw;
    }
    public String getResponseStatus() {
        return responseStatus;
    }
    public void setResponseStatus(String responseStatus) {
        this.responseStatus = responseStatus;
    }

    // see UploadedFile
    public UploadedBytes getUploadedBytes() {
        return uploadedBytes;
    }
    public void setUploadedBytes(UploadedBytes bytes) {
        this.uploadedBytes = bytes;
    }

}
