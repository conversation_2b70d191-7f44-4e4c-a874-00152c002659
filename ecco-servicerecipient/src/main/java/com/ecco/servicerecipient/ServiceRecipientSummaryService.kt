package com.ecco.servicerecipient

import com.ecco.infrastructure.annotations.ReadOnlyTransaction
import org.springframework.cache.annotation.CacheEvict
import org.springframework.cache.annotation.Cacheable

const val CACHE_SERVICERECIPIENTSUMMARY: String = "serviceRecipientSummary"

@ReadOnlyTransaction
interface ServiceRecipientSummaryService {

    fun findAndVerifyAccess(srId: Int): ServiceRecipientSummary

    // default is 5 min TTL so will only ever get that stale if we forget to evict
    @Cacheable(cacheNames = [CACHE_SERVICERECIPIENTSUMMARY]) // NOTE: Requires open
    fun findOne(srId: Int): ServiceRecipientSummary

    @CacheEvict(CACHE_SERVICERECIPIENTSUMMARY)
    fun invalidateCacheFor(srId: Int) {}
}