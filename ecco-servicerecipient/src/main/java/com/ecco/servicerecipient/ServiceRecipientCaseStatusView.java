package com.ecco.servicerecipient;

import org.joda.time.DateTime;
import org.joda.time.LocalDate;

public interface ServiceRecipientCaseStatusView {

    class Support {

        public static boolean isClosed(ServiceRecipientCaseStatusView input) {
            return input.isExited() || input.isSignposted();
        }
        public static boolean isExited(ServiceRecipientCaseStatusView input) {
            return input.isExited();
        }
        public static boolean isSignposted(ServiceRecipientCaseStatusView input) {
            return input.isSignposted();
        }

        /**
         * see Referral#getReceivedDate()
         * -see ReferralToViewModel#acceptedStates
         */
        public static String getStatusMessageKey(ServiceRecipientCaseStatusView input) {
            // This duplicates logic from referrals-list.tag
            if (input.isExited()) {
                return "status.exited";
            } else if (input.isFinalDecision()) {
                if (input.isAcceptedOnService()) {
                    if (input.getReceivingServiceLocalDate() == null || LocalDate.now().isBefore(input.getReceivingServiceLocalDate())) {
                        return "status.toStart";
                    }
                    return "status.started";
                } else {
                    // see acceptOnServiceState and appropriateReferralState for the differentiator
                    return "status.signposted";
                }
            } else if (input.isAcceptedReferral()) {
                return "status.forAssessment";
            } else if (input.isPending()) {
                return "status.pending";
            } else {
                return "status.incomplete";
            }
        }

        /**
         * see Referral#getReceivedDate()
         * Statuses obtainable (copied from Referral.java)
         *     if (acceptedReferral && !decision && !acceptedOnService) then referral acceptance was made
         *     if (!acceptedReferral && decision && !acceptedOnService) then final signpost was made at referral stage
         *     if (acceptedReferral && decision && !acceptedOnService) then final signpost was made
         *     if (acceptedReferral && decision && acceptedOnService) then final acceptance was made
         * This is simpler to get the state than {@link ServiceRecipientCaseStatusView}
         * @return a two-element array for appropriateReferralState and acceptOnServiceState
         */
        public static AcceptState[] acceptedStates(boolean finalDecision, Boolean isReferralDecision, boolean acceptedReferral, boolean acceptedOnService) {
            if (!finalDecision) {
                if (!acceptedReferral) {
                    return new AcceptState[]{AcceptState.UNSET, AcceptState.UNSET};
                }
                else {
                    return new AcceptState[]{AcceptState.ACCEPTED, AcceptState.UNSET};
                }
            }
            else if (acceptedOnService) {
                return new AcceptState[]{AcceptState.ACCEPTED, AcceptState.ACCEPTED};
            }
            else if (acceptedReferral) {
                return new AcceptState[]{AcceptState.ACCEPTED, AcceptState.SIGNPOSTED};
            }
            else {
                // we can reject at either stage but only once: 'appropriate referral' or 'accept on service'
                // the only way to differentiate between two boolean false's is
                // to use the referralDecisionMadeOn which is left independently
                // (decisionMadeOn is always set regardless of the stage used to signpost)
                if (Boolean.TRUE.equals(isReferralDecision)) {
                    return new AcceptState[]{AcceptState.SIGNPOSTED, AcceptState.UNSET};
                } else {
                    return new AcceptState[]{AcceptState.UNSET, AcceptState.SIGNPOSTED};
                }
            }

        }
    }

    default AcceptState[] acceptedStates() {
        return Support.acceptedStates(isFinalDecision(), this.isReferralDecision(), isAcceptedReferral(), isAcceptedOnService());
    }

    LocalDate getReceivedDate();
    LocalDate getDecisionReferralMadeOn();
    LocalDate getDecisionMadeOn();
    DateTime getDecisionMadeOnDT();

    //Long getReferralId();
    Integer getServiceRecipientId();
    String getClientDisplayName();

    default boolean isExited() {
        return this.getExitedDate() != null;
    }

    default boolean isSignposted() {
        // matches anyRejected in ReferralStatusCommonPredicates
        return this.isFinalDecision() && !isAcceptedOnService();
    }

    LocalDate getExitedDate();

    //DaysOfWeek getMeetingDays();

    LocalDate getReceivingServiceLocalDate();

    boolean isAcceptedOnService();

    Boolean isAcceptedReferral();

    Boolean isReferralDecision();

    boolean isFinalDecision();

    boolean isPending();

    boolean isRequestedDelete();
}
