package com.ecco.dto;

import static java.lang.Boolean.TRUE;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.ecco.serviceConfig.viewModel.ProjectViewModel;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor // Couldn't do this with @Builder, so delombok'd @Builder
public class ServiceViewModel {

    /** Ignored when POSTing a new entity */
    public Integer id;

    public String name;

    public Long serviceTypeId;

    public List<ProjectViewModel> projects;

    public Map<String, Object> parameters;

    public Boolean disabled;

    public static ServiceViewModelBuilder builder() {
        return new ServiceViewModelBuilder();
    }

    public void addProject(ProjectViewModel p) {
        if (projects == null) {
             projects = new ArrayList<>();
        }
        projects.add(p);
    }

    @JsonIgnore
    public boolean isAllowInboundReferral() {
        return parameters != null && TRUE.equals(parameters.get("allowInboundReferrals"));
    }

    public static class ServiceViewModelBuilder {
        private Integer id;
        private String name;
        private Long serviceTypeId;
        private List<ProjectViewModel> projects;
        private Map<String, Object> parameters;
        private Boolean disabled;

        ServiceViewModelBuilder() {
        }

        public ServiceViewModelBuilder id(Integer id) {
            this.id = id;
            return this;
        }

        public ServiceViewModelBuilder name(String name) {
            this.name = name;
            return this;
        }

        public ServiceViewModelBuilder serviceTypeId(Long serviceTypeId) {
            this.serviceTypeId = serviceTypeId;
            return this;
        }

        public ServiceViewModelBuilder projects(List<ProjectViewModel> projects) {
            this.projects = projects;
            return this;
        }

        public ServiceViewModelBuilder parameters(Map<String, Object> parameters) {
            this.parameters = parameters;
            return this;
        }

        public ServiceViewModelBuilder disabled(Boolean disabled) {
            this.disabled = disabled;
            return this;
        }

        public ServiceViewModel build() {
            return new ServiceViewModel(id, name, serviceTypeId, projects, parameters, disabled);
        }
    }
}
