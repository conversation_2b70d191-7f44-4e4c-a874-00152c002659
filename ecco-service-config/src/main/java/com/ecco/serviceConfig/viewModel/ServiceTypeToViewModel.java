package com.ecco.serviceConfig.viewModel;

import static java.util.Collections.emptyList;

import com.ecco.infrastructure.entity.IdName;
import java.util.List;
import java.util.function.Function;

import org.jspecify.annotations.NonNull;

import org.jspecify.annotations.Nullable;
import org.springframework.beans.factory.annotation.Configurable;
import org.springframework.beans.factory.aspectj.AnnotationBeanConfigurerAspect;

import com.ecco.serviceConfig.dom.ServiceType;

@Configurable
public
final class ServiceTypeToViewModel implements Function<ServiceType, ServiceTypeViewModel> {

    {
        injectServices();
    }

    private static final OutcomeToViewModel outcomeToViewModel = new OutcomeToViewModel();
    private static final RiskAreaToViewModel riskAreaToViewModel = new RiskAreaToViewModel();
    private static final TaskDefinitionEntryToViewModel taskDefEntryToViewModel = new TaskDefinitionEntryToViewModel();


    public ServiceTypeToViewModel() {
    }

    @Override
    public @NonNull ServiceTypeViewModel apply(@Nullable ServiceType input) {
        if (input == null) {
            throw new NullPointerException("input ServiceType must not be null");
        }
        ServiceTypeViewModel result = new ServiceTypeViewModel();
        result.id = input.getId() == null ? null : input.getId().intValue();
        result.name = input.getName();
        result.primaryRelationshipId = input.getPrimaryRelationshipId();
        result.hideOnList = input.isHideOnList();
        result.hideOnNew = input.isHideOnNew();

        // TODO we just need the ids associated here, not the whole mapping
        result.questionGroups = input.getQuestionGroupsSupport().stream().map(this::idNameToViewModel).toList();
        result.supportOutcomes = input.getOutcomeSupports().stream().map(this::idNameToViewModel).toList();
        result.riskAreas = input.getOutcomeThreats().stream().map(riskAreaToViewModel).toList();

        result.wizardTasks = input.getWizardTasks();
        result.processKey = input.getWorkflowProcessKey();

        result.taskDefinitionEntries = input.getTaskDefinitions() == null
                ? emptyList()
                : input.getTaskDefinitions().stream().map(taskDefEntryToViewModel).toList();

        // Let each one know what index they are
        List<TaskDefinitionEntryViewModel> taskDefinitionEntries = result.taskDefinitionEntries;
        for (int i = 0; i < taskDefinitionEntries.size(); i++) {
            taskDefinitionEntries.get(i).taskDefIndex = i;
        }

        return result;
    }

    private IdNameViewModel idNameToViewModel(IdName idName) {
        var result = new IdNameViewModel();
        result.id = idName.getId();
        result.name = idName.getName();
        result.disabled = idName.isDisabled();
        return result;
    }

    private void injectServices() {
        // NOTE: This expects to find @Configurable on the class
        AnnotationBeanConfigurerAspect.aspectOf().configureBean(this);
    }
}