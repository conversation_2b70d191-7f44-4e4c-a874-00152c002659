package com.ecco.serviceConfig.viewModel;

import static java.util.stream.Collectors.toList;

import java.util.function.Function;

import com.ecco.serviceConfig.dom.ActionGroup;
import com.ecco.serviceConfig.dom.OutcomeThreat;

public class RiskAreaToViewModel implements Function<OutcomeThreat, RiskAreaViewModel> {

    private static final Function<ActionGroup, RiskActionGroupViewModel> actionGroupToViewModel = new RiskActionGroupToViewModel();

    @Override
    public RiskAreaViewModel apply(OutcomeThreat input) {
        RiskAreaViewModel result = new RiskAreaViewModel();
        result.id = input.getId();
        result.uuid = input.getUuid();
        result.name = input.getName();
        result.disabled = input.isDisabled();

        result.actionGroups = input.getActionGroups().stream().map(actionGroupToViewModel).collect(toList());

        return result;
    }

}
