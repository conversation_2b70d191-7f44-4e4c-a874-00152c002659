package com.ecco.serviceConfig.viewModel;

import com.ecco.infrastructure.entity.IdNameWithCode;
import com.google.common.base.Function;

import org.jspecify.annotations.Nullable;

public final class IdNameWithCodeToViewModel<T extends IdNameWithCode> implements Function<T, IdNameCodeViewModel>,
    java.util.function.Function<T, IdNameCodeViewModel> {

    @Nullable
    @Override
    public IdNameCodeViewModel apply(@Nullable IdNameWithCode input) {
        if (input == null) {
            throw new NullPointerException("input IdNameWithCode must not be null");
        }

        IdNameCodeViewModel vm = new IdNameCodeViewModel();
        vm.id = input.getId() == null ? null : input.getId().longValue();
        vm.code = input.getCode();
        vm.name = input.getName();
        vm.disabled = input.isDisabled();

        return vm;
    }

}