package com.ecco.serviceConfig.hact.viewModel;

import java.util.function.Function;

import com.ecco.serviceConfig.hact.dom.HactOutcomeEvidenceSurvey;
import org.jspecify.annotations.Nullable;

public class HactOutcomeEvidenceSurveyToViewModel implements Function<HactOutcomeEvidenceSurvey, HactOutcomeEvidenceSurveyViewModel> {

    @Override
    @Nullable
    public HactOutcomeEvidenceSurveyViewModel apply(@Nullable HactOutcomeEvidenceSurvey input) {
        if (input == null) {
            throw new NullPointerException("input HactOutcomeEvidence must not be null");
        }

        HactOutcomeEvidenceSurveyViewModel vm = new HactOutcomeEvidenceSurveyViewModel();
        vm.id = input.getId();
        vm.hactOutcomeDefCode = input.getHactOutcomeDefCode();
        vm.evidenceDescription = input.getEvidenceDescription();
        vm.questionDefId = input.getQuestionDefId();
        vm.valuableAnswerChoiceDefIds = input.getValuableAnswerChoiceDefIds();
        return vm;
    }

}
