package com.ecco.serviceConfig.dom;

import javax.persistence.Entity;
import javax.persistence.Lob;
import javax.persistence.Table;
import javax.persistence.Transient;

import org.springframework.beans.factory.annotation.Autowired;
import com.ecco.infrastructure.markdown.MarkdownProcessor;
import com.ecco.infrastructure.spring.data.ConfigurableTableGeneratedIdPersistable;

/**
 * Allows us to provide fixed information that is associated with an EvidenceGroup (QG/Outcome), and/or a
 * TaskDefinition
 */
@Entity
@Table(name="evdnc_guidance")
public class EvidenceGuidance extends ConfigurableTableGeneratedIdPersistable {

    private static final long serialVersionUID = 1L;

    @Transient
    @Autowired
    transient private MarkdownProcessor markdownProcessor;

    @Lob
    private String guidanceMarkdown;


    public String getGuidanceMarkdown() {
        return guidanceMarkdown;
    }

    /**
     * Allow editing via PUT /api/config/guidance/{id}/guidanceMarkdown
     * (or do we do stuff that's more of a command queueing approach - and make this a command)
     */
    public void setGuidanceMarkdown(String guidanceMarkdown) {
        this.guidanceMarkdown = guidanceMarkdown;
    }

    public String getGuidanceAsHtml() {
        return markdownProcessor.getAsHtml(guidanceMarkdown);
    }
}
