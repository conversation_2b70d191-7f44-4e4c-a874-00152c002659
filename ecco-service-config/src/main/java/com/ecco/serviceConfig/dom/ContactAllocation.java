package com.ecco.serviceConfig.dom;

import com.ecco.dom.ContactImpl;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.io.Serializable;

@javax.persistence.Entity
@Table(name="svccat_contacts")
@EqualsAndHashCode
@IdClass(ContactAllocation.CompositeKey.class)
@Data
public class ContactAllocation implements Serializable {

    @Id
    int serviceAllocationId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name="serviceAllocationId", insertable = false, updatable = false)
    ServiceCategorisation allocation;

    @Id
    long contactId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name="contactId", insertable = false, updatable = false)
    ContactImpl contact;

    protected ContactAllocation() {
    }

    public ContactAllocation(int serviceAllocationId, long contactId) {
        this.serviceAllocationId = serviceAllocationId;
        this.contactId = contactId;
    }

    @EqualsAndHashCode
    public static class CompositeKey implements Serializable {
        public int serviceAllocationId;
        public long contactId;
    }
}
