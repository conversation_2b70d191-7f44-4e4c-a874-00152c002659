package com.ecco.integration.ql;

import com.ecco.dto.ClientDefinition;
import com.ecco.dto.ClientEvent;
import com.ecco.integration.api.EccoIntApiActor;
import org.joda.time.LocalDate;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.test.context.TestPropertySource;

import java.net.URI;
import java.net.URISyntaxException;
import java.time.Instant;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.springframework.boot.test.context.SpringBootTest.WebEnvironment.RANDOM_PORT;

/**
 * Test the behaviour of our Spring Boot app.
 *
 * @see <a href="http://docs.spring.io/spring-boot/docs/current/reference/html/boot-features-testing.html">Spring Boot Reference</a>
 *
 * This test retrieves a record from the Liquibase schema, which can be found at changelog-master.xml.
 * A {@link com.ecco.dto.ClientDefinition} exemplar object is constructed to represent a search, with
 * some search parameters, which is used to query the URL "/ql/", to see if a single ClientDefinition
 * is returned, and to check that it has a required set of parameters.
 */
@SpringBootTest(webEnvironment = RANDOM_PORT, classes = Application.class)
@TestPropertySource(locations="classpath:test.properties")
// TODO: Extract abstract superclass for teh test behaviour which will be same for all integrations
public class ApplicationTests {

    private static final String GENDER = "FEMALE";
    private static final String DISABILITY_NO = "NO";
    private static final String FIRST_LANGUAGE = "ENGLISH";
    private static final String ETHNIC_ORIGIN = "GYPSY";
    private static final String NATIONALITY = "BRITISH"; // British
    private static final String RELIGION = "CHRISTIAN";
    private static final String SEXUALITY = "HETERO";
    private static final String POST_CODE = "TE1 2ST";
    private LocalDate dob;

    // Test API
    private TestRestTemplate template = new TestRestTemplate();
    private EccoIntApiActor actor = new EccoIntApiActor(template.getRestTemplate());

    @Value("${local.server.port}")
    private int httpPort;

    @BeforeEach
    public void setUp() {

        final DateTimeFormatter dtf = DateTimeFormat.forPattern("yyyy-MM-dd");
        String dateInString = "1980-02-24";
        dob = dtf.parseLocalDate(dateInString);
    }

    @Test
    public void shouldFindMeganBryce() throws URISyntaxException  {
        ClientDefinition exemplar = ClientDefinition.BuilderFactory.create()
                .firstName("Megan")
                .lastName("Bryce")
                .genderKey(GENDER)
                .build();

        Iterable<ClientDefinition> queryClients = actor.queryClients("the source", getUri(), exemplar);
        assertThat(queryClients).hasSize(1);
        ClientDefinition client = queryClients.iterator().next();
        assertIsFullMeganBryceRecord(client);
    }

    @Test
    @Disabled("Requires QL to be configured in application-ql-test.properties")
    public void shouldCreatePaulaFrankensteinAndOneEvent() throws URISyntaxException  {
        ClientDefinition exemplar = ClientDefinition.BuilderFactory.create()
                .firstName("Paula")
                .lastName("Frankenstein")
                .genderKey(GENDER)
                .externalClientSource("the source") // which source to create the client on
                .build();

        ClientDefinition createdClient = actor.createClient(getUri(), exemplar);
        assertThat(createdClient).isNotNull();
        assertThat(createdClient.getExternalClientRef()).isEqualTo("TODO: I made this up");

        ClientEvent event = new ClientEvent(
                createdClient.getExternalClientRef(),
                ClientEvent.NoteType.Contact,
                "Support Work",
                "Support work comments",
                Instant.now(),
                UUID.randomUUID());
        actor.createEvent(getUri(), event);
    }


    public void assertIsFullMeganBryceRecord(ClientDefinition client) {
        assertThat(client.getExternalClientRef()).isEqualTo("11111");
        assertThat(client.getFirstName()).isEqualTo("Megan");
        assertThat(client.getLastName()).isEqualTo("Bryce");
        assertThat(client.getGenderKey()).isEqualTo(GENDER);
        assertThat(client.getBirthDate()).isEqualTo(dob);
        assertThat(client.getNi()).isEqualTo("TG703265U");
        assertThat(client.getFirstLanguageKey()).isEqualTo(FIRST_LANGUAGE);
        assertThat(client.getDisabilityKey()).isEqualTo(DISABILITY_NO);
        assertThat(client.getEthnicOriginKey()).isEqualTo(ETHNIC_ORIGIN);
        //assertThat(client.getNationalityKey()).isEqualTo(NATIONALITY);
        assertThat(client.getReligionKey()).isEqualTo(RELIGION);
        assertThat(client.getSexualOrientationKey()).isEqualTo(SEXUALITY);
        assertThat(client.getPostCode()).isEqualTo(POST_CODE);
        // NB line1 and line2 are used so the user can sort out line1 - ideally, since unique may involve both
        assertThat(client.getAddress()).isEqualTo(new String[]{"Flat 1","Test House"});
    }

    @Test
    public void shouldFindMeganBrownWithNoDisabilityMapping() throws URISyntaxException  {
        ClientDefinition exemplar = ClientDefinition.BuilderFactory.create()
                .firstName("Megan")
                .lastName("Brown")
                .genderKey(GENDER)
                .build();

        Iterable<ClientDefinition> queryClients = actor.queryClients("the source", getUri(), exemplar);
        assertThat(queryClients).hasSize(1);
        ClientDefinition client = queryClients.iterator().next();
        assertThat(client.getExternalClientRef()).isEqualTo("11113");
        assertThat(client.getFirstName()).isEqualTo("Megan");
        assertThat(client.getLastName()).isEqualTo("Brown");
        assertThat(client.getGenderKey()).isEqualTo(GENDER);
        assertThat(client.getBirthDate()).isEqualTo(dob);
        assertThat(client.getNi()).isEqualTo("TG703265Z");
        assertThat(client.getFirstLanguageKey()).isEqualTo(FIRST_LANGUAGE);
        assertThat(client.getDisabilityKey()).isNull();
        assertThat(client.getEthnicOriginKey()).isEqualTo(ETHNIC_ORIGIN);
        //assertThat(client.getNationalityKey()).isEqualTo(NATIONALITY);
        assertThat(client.getReligionKey()).isEqualTo(RELIGION);
        assertThat(client.getSexualOrientationKey()).isEqualTo(SEXUALITY);
        assertThat(client.getPostCode()).isEqualTo(POST_CODE);
        assertThat(client.getAddress()).isEqualTo(new String[]{"Flat 2","Test House"});
    }

    private URI getUri() throws URISyntaxException {
        return new URI("http",null,"localhost",httpPort,"/ql/", null, null );
    }

}
