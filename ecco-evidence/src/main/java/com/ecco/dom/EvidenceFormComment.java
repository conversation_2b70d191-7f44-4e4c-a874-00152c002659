package com.ecco.dom;

import com.ecco.evidence.event.EvidenceCommentSavedEvent;
import com.ecco.infrastructure.bus.MessageBus;
import com.querydsl.core.annotations.QueryInit;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Configurable;
import org.springframework.beans.factory.aspectj.AnnotationBeanConfigurerAspect;
import org.springframework.context.ApplicationEvent;

import javax.persistence.*;
import java.time.Instant;
import java.util.UUID;

import static com.ecco.infrastructure.hibernate.AntiProxyUtils.identifier;

@Entity
@Table(name="evdnc_form_comments")
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@Getter
@Setter
@Configurable
public class EvidenceFormComment extends EvidenceComment implements ServiceRecipientId {

    private static final long serialVersionUID = 1L;

    {
        injectServices(); // This class is instantiated by Hibernate, so not a managed Spring bean.
    }

    public Object readResolve() {
        injectServices();
        return this;
    }

    private void injectServices() {
        // NOTE: This expects to find @Configurable on the class
        AnnotationBeanConfigurerAspect.aspectOf().configureBean(this);
    }

    public static class Builder {

        private final EvidenceFormComment c;

        public Builder(EvidenceFormComment c) {
            this.c = c;
        }

        public EvidenceFormComment build() {
            return c;
        }
    }

    public static Builder builder(int serviceRecipientId) {
        EvidenceFormComment c = new EvidenceFormComment(serviceRecipientId);
        return new Builder(c);
    }

    @Autowired
    @Transient
    protected transient MessageBus<ApplicationEvent> messageBus;

    @QueryInit("*.*.*")
    @OneToOne(fetch=FetchType.LAZY, targetEntity= EvidenceFormWork.class)
    @JoinColumn(name="workUuid", columnDefinition="CHAR(36)")
    protected EvidenceFormWork work;

    public EvidenceFormComment(Integer serviceRecipientId) {
        super(serviceRecipientId);
    }

    @Override
    @Transient
    public UUID getWorkId() {
        return identifier(work);
    }

    @Override
    public void setWork(BaseWorkEvidence work) {
        this.work = (EvidenceFormWork) work;
    }

    @PostPersist
    void notifyNewCommentCreated() {
        messageBus.publishAfterTxEnd(new EvidenceCommentSavedEvent(this, getServiceRecipientId()));
    }

    @Override
    public Instant getRelevantInstant() {
        return Instant.ofEpochMilli(work.getWorkDate().getMillis());
    }
}
