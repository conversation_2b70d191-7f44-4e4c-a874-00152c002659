package com.ecco.dom.commands;

import java.util.UUID;

import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;
import javax.persistence.Column;
import javax.persistence.Entity;

import org.joda.time.Instant;


@Entity
public class GoalCommand extends ServiceRecipientEvidenceCommand {

    @Nullable
    @Column(nullable = true)
    protected Long actionDefId;

    /**
     * @deprecated Do not use. Required by JPA/Hibernate.
     */
    @Deprecated
    public GoalCommand() {
        super();
    }

    public GoalCommand(@Nullable UUID uuid, @NonNull Instant remoteCreationTime,
                       long userId, @NonNull String body, int serviceRecipientId,
                       @Nullable Long actionDefId, @NonNull String evidenceGroupKey, String taskName) {
        super(uuid, remoteCreationTime, userId, body, serviceRecipientId, taskName, evidenceGroupKey);
        this.actionDefId = actionDefId;
    }

    public long getActionDefId() {
        return actionDefId;
    }
}
