package com.ecco.dom;

import com.ecco.config.dom.FormDefinition;
import com.ecco.dom.servicerecipients.BaseServiceRecipient;
import com.ecco.infrastructure.Created;
import com.ecco.infrastructure.entity.AbstractIntKeyedEntity;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;
import org.hibernate.annotations.Type;
import org.joda.time.DateTime;

import org.jspecify.annotations.NonNull;
import javax.persistence.*;
import java.util.UUID;

import static com.ecco.infrastructure.hibernate.AntiProxyUtils.identifier;

/**
 * A work item for recording custom form data
 */
@Entity
@Table(name="evdnc_form_snapshots")
@Data
@NoArgsConstructor(access = AccessLevel.PROTECTED) // for hibernate, protected in superclass
@EqualsAndHashCode(callSuper = true)
public class EvidenceFormSnapshot extends AbstractIntKeyedEntity implements EvidenceData, Created, ServiceRecipientId {

    // see BaseServiceRecipientEvidence - but without the workDate
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "serviceRecipientId", insertable = false, updatable = false)
    @NotFound(action = NotFoundAction.EXCEPTION)
    private BaseServiceRecipient serviceRecipient;

    @Column
    private Integer serviceRecipientId;

    /** NOTE: This is defaulted to now by {@link com.ecco.infrastructure.hibernate.CreatedInterceptor} if null */
    @Column
    @Type(type="org.jadira.usertype.dateandtime.joda.PersistentDateTime")
    protected DateTime created;
    // see BaseServiceRecipientEvidence - but without the workDate

    @NonNull
    @OneToOne(fetch=FetchType.EAGER, targetEntity=EvidenceFormWork.class, optional = false)
    @JoinColumn(name="workUuid", columnDefinition="CHAR(36)")
    protected EvidenceFormWork work;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "formDefinitionUuid", insertable = false, updatable = false)
    private FormDefinition formDefinition;

    @Column(name="formDefinitionUuid", columnDefinition="CHAR(36)")
    @Type(type="org.hibernate.type.UUIDCharType")
    private UUID formDefinitionUuid;

    @Override
    public UUID getWorkId() {
        return identifier(work);
    }

    @Lob
    @Column
    private String json;

    public EvidenceFormSnapshot(int serviceRecipientId) {
        this.serviceRecipientId = serviceRecipientId;
    }

    @Override
    public int compareTo(EvidenceDataAcceptor o) {
        return getWorkId().compareTo(o.getWorkId());
    }

}
