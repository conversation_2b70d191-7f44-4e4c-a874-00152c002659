package com.ecco.dom;

import com.ecco.infrastructure.entity.AbstractLongKeyedEntity;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.Table;

@Entity
@Table(name="referralactivityworker")
@NamedQueries(@NamedQuery(name = ReferralActivityWorker.BULK_UPDATE_INDIVIDUAL_QUERY, query = "update ReferralActivityWorker set individual = :newContact where individual = :oldContact"))
public class ReferralActivityWorker extends AbstractLongKeyedEntity {
    public static final String BULK_UPDATE_INDIVIDUAL_QUERY = "referralActivityWorker.bulkUpdateIndividual";

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "referralActivityId")
    private ServiceRecipientActivity referralActivity;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "supportWorkerId")
    private Individual individual;

    private String calendarEntryId;

    public ReferralActivityWorker() {}

    public ReferralActivityWorker(ServiceRecipientActivity referralActivity, Individual individual, String calendarEntryId) {
        this.referralActivity = referralActivity;
        this.individual = individual;
        this.calendarEntryId = calendarEntryId;
    }

    public ServiceRecipientActivity getReferralActivity() {
        return referralActivity;
    }

    public void setReferralActivity(ServiceRecipientActivity referralActivity) {
        this.referralActivity = referralActivity;
    }

    public Individual getIndividual() {
        return individual;
    }

    public void setIndividual(Individual individual) {
        this.individual = individual;
    }

    public String getCalendarEntryId() {
        return calendarEntryId;
    }

    public void setCalendarEntryId(String calendarEntryId) {
        this.calendarEntryId = calendarEntryId;
    }

}
