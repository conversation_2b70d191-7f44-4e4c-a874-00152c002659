package com.ecco.dom;

import org.jspecify.annotations.Nullable;

import javax.persistence.Column;
import javax.persistence.DiscriminatorValue;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;

/**
 * 'contact' events are contacts events - basically they are the users own events or staff/other.
 * NB This replaces the CustomEventImpl 'free' events but has the benefit of indexing of the contact.
 */
@javax.persistence.Entity
@DiscriminatorValue("contact")
public class CustomEventWithContact extends CustomEventImpl {

    private static final long serialVersionUID = 1L;


    @ManyToOne(optional=false)
    @JoinColumn(name="contactId", insertable=false, updatable=false)
    private Individual contact;

    @Column
    @Nullable
    private Long contactId;


    protected CustomEventWithContact() {
        // for hibernate etc
    }

    public CustomEventWithContact(long contactId) {
        this.contactId = contactId;
    }


    public Individual getContact() {
        return contact;
    }

    @Override
    public Integer getServiceRecipientId() {
        return null;
    }

    @Override
    public Long getContactId() {
        return contactId;
    }

}
