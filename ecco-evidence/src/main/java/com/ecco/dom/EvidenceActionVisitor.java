package com.ecco.dom;

import java.util.List;

public interface EvidenceActionVisitor {

    void visit(EvidenceAction e);
    /*
    void visit(GenericTypeOutcome e);
    //void visit(GenericTypeRisk e);
    void visit(GenericTypeAction e);
    void visit(GenericTypeComment e);
    //void visit(OutcomeDataPoint e);
    */

    //public int getSortDirection();
    List<WorkDataPoint> getWorkDataPoints();

}
