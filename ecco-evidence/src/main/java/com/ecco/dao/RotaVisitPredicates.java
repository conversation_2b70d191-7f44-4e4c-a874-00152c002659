package com.ecco.dao;

import com.querydsl.core.types.dsl.BooleanExpression;

import static com.ecco.dom.QEvidenceSupportWork.evidenceSupportWork;

/**
 * Defines specifications for {@link EvidenceSupportWorkRepository#findAll(com.querydsl.core.types.Predicate)} and friends.
 *
 * @since 14/10/2016
 */
public class RotaVisitPredicates {
    private static final long ROTAVISIT_PAGESOURCE = 101L;

    public static BooleanExpression rotaVisits() {
        // visits will also have an eventId, but the 'pageSource' ensures we limit the search to 'visits'
        return evidenceSupportWork.taskDefId.eq(ROTAVISIT_PAGESOURCE);
    }

    public static BooleanExpression uninvoicedRotaVisit() {
        return evidenceSupportWork.invoiceId.isNull()
                // visits will also have an eventId, but the 'pageSource' ensures we limit the search to 'visits'
                .and(evidenceSupportWork.taskDefId.eq(ROTAVISIT_PAGESOURCE));
    }

    public static BooleanExpression uninvoicedRotaVisit(Integer serviceRecipientId) {
        return evidenceSupportWork.serviceRecipientId.eq(serviceRecipientId)
                .and(evidenceSupportWork.invoiceId.isNull())
                // visits will also have an eventId, but the 'pageSource' ensures we limit the search to 'visits'
                .and(evidenceSupportWork.taskDefId.eq(ROTAVISIT_PAGESOURCE));
    }
}
