package com.ecco.service;

import com.ecco.config.dom.ExternalSystem;
import com.ecco.config.repositories.ExternalSystemRepository;
import com.ecco.config.service.ExternalSystemService;
import com.ecco.dto.ClientDefinition;
import com.ecco.dto.DelegateResponse;
import com.google.common.collect.ImmutableList;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import org.jspecify.annotations.NonNull;
import java.net.URI;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.ecco.service.ClientDetailService.DEFAULT_CLIENT_SOURCE_SYSTEM;
import static java.util.Collections.singletonList;
import static org.hamcrest.Matchers.*;
import static org.junit.Assert.assertThat;
import static org.mockito.Mockito.*;

/**
 * @since 09/07/2014
 */
@RunWith(MockitoJUnitRunner.class)
public class ClientDetailServiceImplTest {

    private static final String GENDER = "118";

    @InjectMocks
    private ClientDetailServiceImpl clientDetailService;

    @Mock
    private
    ExternalSystemRepository externalSystemRepository;

    @Mock
    private
    SearchClientService searchClientService;

    private final ExternalSystem demoSystem = new ExternalSystem() {
        @NonNull
        @Override
        public String getName() {
            return "demo";
        }

        @Override
        public ExternalSystemService.@NonNull ApiType getApiType() {
            return ExternalSystemService.ApiType.DEMO;
        }

        @NonNull
        @Override
        public URI getUri() {
            return URI.create("https://some.server/some/path/");
        }
    };

    @Before
    public void setup() {
        clientDetailService.setDelegates(singletonList(new MemoizingDemoClientSource()));
    }

    @Test
    public void givenDemoExternalSystemWhenQueryingClientsThenDemoClientsReturned() {
        when(externalSystemRepository.findByClientSourceTrue()).thenReturn(ImmutableList.of(demoSystem));
        final ClientDefinition exemplar = ClientDefinition.BuilderFactory.create().lastName("Stewart").firstName("James").build();

        final Map<String,DelegateResponse<Iterable<ClientDefinition>>> result = clientDetailService.queryClientsByExample(exemplar, true);
        assertThat("Result has demo system and ecco keys", result.keySet(), containsInAnyOrder(demoSystem.getName(), DEFAULT_CLIENT_SOURCE_SYSTEM));
        ImmutableList<ClientDefinition> demoResult = ImmutableList.copyOf(Objects.requireNonNull(result.get(demoSystem.getName()).getPayload()));
        assertThat("Result has single value for demo", demoResult.size(), equalTo(1));
        ImmutableList<ClientDefinition> eccoResult = ImmutableList.copyOf(Objects.requireNonNull(result.get(DEFAULT_CLIENT_SOURCE_SYSTEM).getPayload()));
        assertThat("Result has no values for ecco", eccoResult.size(), equalTo(0));
    }

    @Test(expected = IllegalArgumentException.class)
    public void whenNoLastNameSpecifiedThenQueryFails() {
        final ClientDefinition exemplar = ClientDefinition.BuilderFactory.create().firstName("James").build();

        try {
            clientDetailService.queryClientsByExample(exemplar, true);
        } finally {
            verifyNoInteractions(searchClientService); // should intercept in advance of calling this
            verifyNoInteractions(externalSystemRepository);
        }
    }

    @Test(expected = IllegalArgumentException.class)
    public void whenNoFirstNameSpecifiedThenQueryFails() {
        final ClientDefinition exemplar = ClientDefinition.BuilderFactory.create().lastName("Stewart").build();

        try {
            clientDetailService.queryClientsByExample(exemplar, true);
        } finally {
            Mockito.verify(searchClientService).getLocalClientMatches(same(exemplar)); // we delegate to local search for acceptable params here
            verifyNoInteractions(externalSystemRepository);
        }
    }

    @Test
    public void givenLocalMatchOnlyWhenQueryingClientsThenLocalClientReturned() {
        when(externalSystemRepository.findByClientSourceTrue()).thenReturn(ImmutableList.of(demoSystem));
        final String lastName = "Zoological Society";
        final String firstName = "London";
        final ClientDefinition exemplar = ClientDefinition.BuilderFactory.create().lastName(lastName).firstName(firstName).build();
        final ClientDefinition found = ClientDefinition.BuilderFactory.create().localClientId(100024L).lastName(lastName).firstName(firstName)
                .genderKey(GENDER).build();

        List internalAnswers = ImmutableList.of(found);
        when(searchClientService.getLocalClientMatches(same(exemplar))).thenReturn(internalAnswers);
        when(searchClientService.secureLocalClientMatches(same(internalAnswers))).thenReturn(ImmutableList.of(found));

        final Map<String, DelegateResponse<Iterable<ClientDefinition>>> result = clientDetailService.queryClientsByExample(exemplar, true);

        ImmutableList<ClientDefinition> demoResult = ImmutableList.copyOf(Objects.requireNonNull(result.get(demoSystem.getName()).getPayload()));
        assertThat("Result has no values for demo", demoResult.size(), equalTo(0));
        ImmutableList<ClientDefinition> eccoResult = ImmutableList.copyOf(Objects.requireNonNull(result.get(DEFAULT_CLIENT_SOURCE_SYSTEM).getPayload()));
        assertThat("Result has one value for ecco", eccoResult.size(), equalTo(1));
    }

    @Test
    // Test that the security logic is invoked. We'd like an API test for the security logic to actually
    // test the querydsl in secureLocalClientMatches - but currently we don't have an ACL test system.
    public void givenLocalMatchOnlyWhenQueryingClientsThenSecureLocalClientReturned() {
        when(externalSystemRepository.findByClientSourceTrue()).thenReturn(ImmutableList.of(demoSystem));
        final String lastName = "Zoological Society";
        final String firstName = "London";
        final ClientDefinition exemplar = ClientDefinition.BuilderFactory.create().lastName(lastName).firstName(firstName).build();
        final ClientDefinition found1 = ClientDefinition.BuilderFactory.create().localClientId(100024L).lastName(lastName).firstName(firstName)
                .genderKey(GENDER).build();
        final ClientDefinition found2 = ClientDefinition.BuilderFactory.create().localClientId(100026L).lastName(lastName.concat("notAvailable")).firstName(firstName)
                .genderKey(GENDER).build();

        List internalAnswers = ImmutableList.of(found1, found2);
        when(searchClientService.getLocalClientMatches(same(exemplar))).thenReturn(internalAnswers);
        when(searchClientService.secureLocalClientMatches(same(internalAnswers))).thenReturn(ImmutableList.of(found1));

        final Map<String, DelegateResponse<Iterable<ClientDefinition>>> result = clientDetailService.queryClientsByExample(exemplar, true);

        ImmutableList<ClientDefinition> eccoResult = ImmutableList.copyOf(Objects.requireNonNull(result.get(DEFAULT_CLIENT_SOURCE_SYSTEM).getPayload()));
        assertThat("Result has one value for ecco", eccoResult.size(), equalTo(1));
        assertThat("Single result doesn't have correct details", eccoResult.get(0).getLastName(), not(equalTo(lastName.concat("notAvailable"))));
    }
}
