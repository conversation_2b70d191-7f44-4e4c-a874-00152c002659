These allow us to connect through Cisco VPNs where a file transfer can be achieved through pasting to the RDP.


Usage
-----
```
connect to vpn using 'network' indicator
sudo route add ************ tun0
/usr/local/bin/xfreerdp +drives +clipboard /size:800x600 /d:Bah /u:ecchamera /p:PASSWORD /v:************ /cert-ignore
```
then see windows drive 'media on xxx' for the files on local machine
paste files by copying to the window


VPN - install
-------------
1) Install Cisco plugin:
```
sudo atp-get install network-manager-vpnc
```
If a 'group password' is needed, it can be extracted using info from http://baheyeldin.com/cisco/converting-cisco-easy-vpn-pcf-files-linux-vpnc-configuration-format.html, or using an internet page https://www.unix-ag.uni-kl.de/~massar/bin/cisco-decode.
Once installed, the Network Manager gui (wireless symbol) will have 'connect to -> vpn, cisco' which can be configured accordingly.

Debug using
```
less /var/log/syslog | grep NetworkManager
```

2) Add the network route so not all traffic goes through the vpn (eg internet requests!) see https://help.ubuntu.com/community/VPNClient#line-203
 ```
route -> all routes -> edit VPN connection, ipv4, routes, UNtick 'use this connection only for resources on network'
sudo route add <target IP you want to reach> <interface that you want the target to be reached>
```

Resources
---------
 - alternative VPN client, vpnc, gives error
http://colans.net/blog/installing-vpn-pcf-files-ubuntu-1304
sudo apt-get install vpnc
pcf2vpnc 3P-Ecco-IPSEC.PCF 3P-Ecco-IPSEC.conf
chmod 600 3P-Ecco-IPSEC.conf
sudo vpnc /media/truecrypt1/accounts/bh/3P-Ecco-IPSEC.conf
sudo vpnc-disconnect
error: "firewall remote end behind a nat device - check configured username and password"

 - ubuntu cisco login
http://www.cisco.com/c/dam/en/us/td/docs/security/vpn_client/cisco_vpn_client/vpn_client46/administration/guide/vcA_original.pdf
http://baheyeldin.com/cisco/converting-cisco-easy-vpn-pcf-files-linux-vpnc-configuration-format.html
https://bugs.launchpad.net/ubuntu/+source/network-manager-vpnc/+bug/1207918
http://iamthelinuxsysadmin.blogspot.ca/2011/03/cisco-vpn-on-ubuntu-1010-64bit-howto.html
http://colans.net/blog/installing-vpn-pcf-files-ubuntu-1304

 -	limit the traffic to the vpn
http://www.enterprisenetworkingplanet.com/linux_unix/article.php/3823781/vpnc-Connects-Linux-and-Cisco-VPNs.htm
diagnostic of it when connected: https://help.ubuntu.com/community/VPNClient


RDP - install
-------------
Used latest verion via compiling for file transfer to work
```
mkdir freerdp-src
git clone https://github.com/FreeRDP/FreeRDP
cd FreeRDP
git tag -l
git checkout tags/1.1.0-beta1
follow https://github.com/FreeRDP/FreeRDP/wiki/Compilation
```
Use this command to run (see https://github.com/FreeRDP/FreeRDP/wiki/CommandLineInterface):
```
/usr/local/bin/xfreerdp +drives +clipboard /size:800x600 /d:Bah /u:ecchamera /p:password /v:************ /cert-ignore
```

Resources
---------
 - (reminna) remote desktop protocol BUT doesn't file transfer
 - pre packaged freedp instructions
sudo apt-get install freerdp-x11
xfreerdp +drives /u:username /v:server_address:server_port (DOESN'T WORK - too modern syntax)
xfreerdp -d Bah -u ecchamera -p password --plugin cliprdr --plugin rdpdr --data disk:HOME:/home/<USER>/Desktop ************
xfreerdp -g 800x600 -d Bah -u ecchamera -p password --plugin rdpdr --data disk:HOME:/home/<USER>/Desktop -- ************
