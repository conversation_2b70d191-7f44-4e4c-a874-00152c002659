import {Client, PersonSearchCriteriaDto} from "../client-dto";
import {SparseArray, StringToObjectMap} from "@eccosolutions/ecco-common";
import {DelegateResponse} from "../dto";
import {ClientRepository} from "./ClientRepository";
import {ApiClient} from "../web-api";

export class ClientAjaxRepository implements ClientRepository {
    private cache: SparseArray<Promise<Client>> = {};
    private cacheByServiceRecipientId: SparseArray<Promise<Client>> = {};

    constructor(private apiClient: ApiClient) {}

    findOneClient(clientId: number): Promise<Client | null> {
        if (!clientId) return Promise.resolve(null);

        return this.apiClient.get<Client>(`clients/${clientId}/`);
    }

    /** Beware of this on pages where you modify client - this is really only for reporting */
    findOneClientCached(clientId: number): Promise<Client | null> {
        if (!clientId) return Promise.resolve(null);

        let client = this.cache[clientId];
        if (!client) {
            client = this.apiClient.get<Client>(`clients/${clientId}/`);
            this.cache[clientId] = client;
        }
        return client;
    }

    /**
     * Get the client details from external source.
     * NB This is not the other domains, like flags etc.
     * @param externalSourceName
     * @param externalRef
     */
    findOneExternalClientBySourceAndRef(
        externalSourceName: string,
        externalRef: string
    ): Promise<Client> {
        return this.apiClient.get<Client>(
            encodeURI(`clients/fromExternal/${externalSourceName}/${externalRef}`)
        );
    }

    findOneClientByReferralId(referralId: number): Promise<Client> {
        return this.apiClient.get<Client>(`referrals/${referralId}/client/`);
    }

    findOneClientByServiceRecipientId(srId: number): Promise<Client> {
        return this.findOneClientCachedByServiceRecipientId(srId).then(
            clientOrNull => clientOrNull!
        );
    }

    findOneClientByCode(clientCode: string): Promise<Client[]> {
        return this.apiClient.get<Client[]>(`clients/byCode/${clientCode}/`);
    }

    findOneClientCachedByServiceRecipientId(serviceRecipientId: number): Promise<Client | null> {
        if (!serviceRecipientId) return Promise.resolve(null);

        let clientQ = this.cacheByServiceRecipientId[serviceRecipientId];
        if (!clientQ) {
            clientQ = this.apiClient.get<Client>(`serviceRecipients/${serviceRecipientId}/client/`);
            clientQ.then(client => {
                this.cache[client.clientId!!] = clientQ; // allow other methods to benefit
                return client;
            });
            this.cacheByServiceRecipientId[serviceRecipientId] = clientQ;
        }
        return clientQ;
    }

    findAllClientsByServiceRecipientId(srIds: number[] | string[]): Promise<Client[]> {
        return this.apiClient.get<Client[]>("clients/", {
            query: {
                srId: srIds.join(",")
            }
        });
    }

    findAllClientsByClientId(clientIds: number[] | string[]): Promise<Client[]> {
        return this.apiClient.get<Client[]>("clients/", {
            query: {
                id: clientIds.join(",")
            }
        });
    }

    findAllClients(): Promise<Client[]> {
        return this.apiClient.get<Client[]>("clients/");
    }

    findAllClientsByCriteria(
        criteria: PersonSearchCriteriaDto,
        localOnly = false
    ): Promise<StringToObjectMap<DelegateResponse<Client[]>>> {
        if (!criteria.lastName && !localOnly) {
            // TODO: See what we can do to allow more flexible search on remote data sources - allow each source
            //   to return whether it was able to search on the criteria.
            console.warn("switching to local client search - no lastname supplied");
            localOnly = true;
        }

        return this.apiClient.post<StringToObjectMap<DelegateResponse<Client[]>>>(
            `clients/${localOnly ? "local" : "all"}/query`,
            criteria
        );
    }

    saveClient(client: Client): Promise<any> {
        return this.apiClient.post<any>("clients/", client);
    }

    public deleteByIdWithLastName(clientId: number, lastName: string) {
        return this.apiClient.del(`clients/${clientId}/`, {lastName: lastName});
    }
}
