import {entries} from "@softwareventures/object";
import {Config} from "jest";
import {clientEnv} from "./env";
import {Options, ResolvedOptions, resolveTestOptions} from "./options/options";

export type JestConfig = () => Promise<Config>;

export function jestConfig(options: Options): JestConfig {
    let config: Config | null = null;

    return async () => {
        // For some reason this function is called twice, so memoize the result.
        if (config == null) {
            config = await jestConfigFromResolvedOptions(resolveTestOptions(options));
        }

        return config;
    };
}

async function jestConfigFromResolvedOptions(options: ResolvedOptions<"test">): Promise<Config> {
    for (const [key, value] of entries(await clientEnv(options))) {
        process.env[key] = value;
    }

    const srcGlob = escapeMicroMatchGlob(options.paths.src);

    return {
        rootDir: options.paths.root,
        roots: [options.paths.src],
        collectCoverageFrom: [`${srcGlob}/**/*.{c,m}{js,ts}{x,}`, `!${srcGlob}/**/*.d.ts`],
        setupFiles: ["ecco-webpack-config/jest/jsdom-polyfill"],
        setupFilesAfterEnv: [],
        testMatch: [
            `${srcGlob}/**/__tests__/**/*.{c,m,}{js,ts}{x,}`,
            `${srcGlob}/**/*.{spec,test}.{c,m,}{js,ts}{x,}`
        ],
        testEnvironment: "jsdom",
        transform: {
            "^.+\\.[cm]?[tj]s$": "ecco-webpack-config/jest/esbuild-ts-transform",
            "^.+\\.[cm]?[tj]sx$": "ecco-webpack-config/jest/esbuild-tsx-transform",
            "^.+\\.css$": "ecco-webpack-config/jest/css-transform",
            "^(?!.*\\.([cm]?[tj]sx?|css|json)$)": "ecco-webpack-config/jest/file-transform"
        },
        transformIgnorePatterns: [
            "[/\\\\]node_modules[/\\\\].+\\.(js|jsx|mjs|cjs|ts|tsx)$",
            "^.+\\.module\\.(css|sass|scss)$"
        ],
        modulePaths: [],
        moduleNameMapper: {
            "^.+\\.module\\.(css|sass|scss)$": "ecco-webpack-config/jest/identity-obj-proxy"
        },
        moduleFileExtensions: [
            "web.js",
            "js",
            "web.ts",
            "ts",
            "web.tsx",
            "tsx",
            "json",
            "web.jsx",
            "jsx",
            "node"
        ],
        injectGlobals: false,
        resetMocks: true
    };
}

function escapeMicroMatchGlob(glob: string): string {
    return glob.replace(/[*!+()|\[\]{}]/u, c => `\\${c}`);
}