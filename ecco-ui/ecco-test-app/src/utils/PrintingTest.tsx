import * as React from "react";
import {Grid} from "@eccosolutions/ecco-mui";
import {PrintIcon} from "ecco-components/layout/printing";
import {
    PrintableResourceLine,
    PrintResourceLineIcon
} from "ecco-components/rota/PrintableResourceLine";
import {useRota} from "ecco-components/data/entityLoadHooks";
import {EccoDate} from "@eccosolutions/ecco-common";
import {LoadingSpinner} from "ecco-components/Loading";

const PrintingTest = () => {

    let date = EccoDate.parseIso8601("2020-11-09");
    const {rota} = useRota(date, date.addDays(2), "careruns:all", "buildings:1011", true, false);
    if (!rota) { return <LoadingSpinner/> }

    const resource = rota.getResources()[0];

    return (
        <Grid container>
            <Grid item xs={12}>
                <PrintResourceLineIcon
                    resource={resource}
                    title="2020-11-09 night shift @ Centre Parcs"
                />
            </Grid>
            <Grid item xs={12}>
                Static: <PrintIcon printView={<h4>This'll print</h4>} />
            </Grid>
            <Grid item xs={12}>
                <h2>Appointments shifts starting on 9th-11th Nov</h2>
                <PrintableResourceLine resource={resource} />
            </Grid>
            <Grid item xs={12}>
                <h2>Appointments on 9th-11th Nov (ignoring shifts)</h2>
                <PrintableResourceLine alignByRuns={false} resource={resource} />
            </Grid>
        </Grid>
    );
}

export default PrintingTest;
