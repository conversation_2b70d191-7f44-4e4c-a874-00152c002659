import {useIndividual, useServiceRecipient} from "../data/entityLoadHooks";
import PersonIcon from "@material-ui/icons/Person";
import {Avatar, makeStyles} from "@eccosolutions/ecco-mui";
import * as React from "react";
import {Individual} from "ecco-dto";
import {useServicesContext} from "../ServicesContext";

// directly from https://mui.com/material-ui/react-avatar/
// NB adapted for stringAvatar backgrounColor (mui 4)

function stringToColor(string: string) {
    let hash = 0;
    let i;

    /* eslint-disable no-bitwise */
    for (i = 0; i < string.length; i += 1) {
        hash = string.charCodeAt(i) + ((hash << 5) - hash);
    }

    let color = '#';

    for (i = 0; i < 3; i += 1) {
        const value = (hash >> (i * 8)) & 0xff;
        color += `00${value.toString(16)}`.slice(-2);
    }
    /* eslint-enable no-bitwise */

    return color;
}

export function stringAvatar(name: string) {
    return {
        style: {
            backgroundColor: stringToColor(name)
        },
        children: `${name.split(" ")[0][0]}${name.split(" ")[1][0]}`
    };
}

export const useStyles = makeStyles(theme => ({
    root: {
        "display": "flex",
        "& > *": {
            margin: theme.spacing(1)
        }
    },
    small: {
        width: theme.spacing(3),
        height: theme.spacing(3)
    },
    large: {
        width: theme.spacing(8),
        height: theme.spacing(8)
    },
    pic: {
        width: theme.spacing(12),
        height: theme.spacing(12)
    }
}));

export function MuiContactAvatar({
    contact,
    asIcon
}: {
    contact?: Individual | null;
    asIcon: boolean;
}) {
    const classes = useStyles();
    const size = asIcon ? classes.root : contact && contact.avatarId ? classes.pic : classes.large;
    const {apiClient} = useServicesContext();
    return contact && contact.avatarId ? (
        <Avatar
            src={`${apiClient.getWebApiUrl()}secure/images/${contact.avatarId}`}
            alt="photo"
            className={size}
        />
    ) : contact ? (
        <Avatar
            {...stringAvatar(`${contact.firstName || "-"} ${contact.lastName || "-"}`)}
            className={size}
        />
    ) : (
        <Avatar className={size}>
            <PersonIcon />
        </Avatar>
    );
}

export function SrAvatarImage({srId, asIcon}: {srId: number; asIcon: boolean}) {
    const {serviceRecipient} = useServiceRecipient(srId);
    const {contact} = useIndividual(serviceRecipient ? serviceRecipient.contactId : null);
    return <MuiContactAvatar contact={contact} asIcon={asIcon} />;
}
