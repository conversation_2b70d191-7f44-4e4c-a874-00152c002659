import * as React from "react";
import {ClassAttributes} from "react";
import {EccoDate, EccoDateTime, EccoTime, StringToObjectMap} from "@eccosolutions/ecco-common";
import {Box, Grid, IconButton} from "@eccosolutions/ecco-mui";
import {DateRangePickerEccoDate, TimePickerEccoTime} from "@eccosolutions/ecco-mui-controls";

import {createButtonGroup, numberInput, showReactInModal} from "ecco-components-core";
import {Uuid} from "@eccosolutions/ecco-crypto";

import {ScheduleData} from "ecco-rota";
import * as ReactDOM from "react-dom"



interface FormProps extends ClassAttributes<TargetScheduleForm> {
    data: ScheduleData;
    allowMultipleTimes: boolean
}
interface FormState {
    nextDateTime: EccoDateTime | null; // From targetDateTime
    start: EccoDate | null;
    times: (EccoTime | null)[];
    end: EccoDate | null;
    days: StringToObjectMap<boolean>;
    week: number | null;
}
const daysOrderedTargetSchedule = ["mon", "tues", "wed", "thurs", "fri", "sat", "sun"];

const fillRow = (child: any) => <Grid item xs={12}>{child}</Grid>;

const notNull = (it:any) => it != null

/**
 * Form to manipulate cron schedules. Used by targetSchedules only currently.
 */
class TargetScheduleForm extends React.Component<FormProps, FormState> {
    constructor(props: FormProps) {
        super(props);
        this.extractStateFromSchedule(props.data);
    }

    public getData(): ScheduleData {
        return ScheduleData.fromTargetSchedule(
            this.state.nextDateTime,
            this.constructScheduleFromState()
        );
    }

    override render() {
        const times = this.state.times.length ? this.state.times : [null]; // For ensuring we show first entry
        return (
            <Box m={4}>
                <Grid container>
                    <DateRangePickerEccoDate
                        start={this.state.start}
                        onStartChange={start => this.setState({start})}
                        end={this.state.end}
                        onEndChange={end => this.setState({end})}
                        renderer={el => (
                            <Grid item sm={6} xs={12}>
                                {el}
                            </Grid>
                        )}
                    />
                    {times.map((time, idx) => (
                        <>
                            <Grid item sm={6} xs={12}>
                                <TimePickerEccoTime
                                    label="time"
                                    time={time}
                                    onTimeChange={time => {
                                        const times = this.state.times.slice();
                                        if (!time) {
                                            times.splice(idx, 1);
                                        } else {
                                            times.splice(idx, 1, time);
                                        }
                                        this.setState({
                                            times: times.sort((a, b) => {
                                                if (a == null) return 1;
                                                if (b == null) return -1;
                                                return a
                                                    .formatHoursMinutes()
                                                    .localeCompare(b.formatHoursMinutes());
                                            })
                                        });
                                    }}
                                />
                                {this.props.allowMultipleTimes &&
                                    time &&
                                    idx == times.length - 1 && (
                                        <IconButton
                                            className="fa fa-plus"
                                            onClick={() =>
                                                this.setState({times: [...this.state.times, null]})
                                            }
                                        />
                                    )}
                            </Grid>
                            <Grid item xs />
                        </>
                    ))}
                    {fillRow(
                        createButtonGroup(
                            "days",
                            "days",
                            this.state.days,
                            (day: string, value: boolean) =>
                                this.setState(state => {
                                    state.days!![day] = value;
                                    return state;
                                })
                        )
                    )}
                    {this.getData().isNonStandard() && (
                        <Grid item sm={6} xs={12}>
                            {numberInput(
                                "week",
                                "every (weeks)",
                                o => this.setState({week: o.week}),
                                {week: this.state.week},
                                undefined,
                                undefined,
                                1,
                                4
                            )}
                        </Grid>
                    )}
                </Grid>
            </Box>
        );
    }

    private extractStateFromSchedule(data: ScheduleData) {
        this.state = {
            nextDateTime: data.getNextDue(),
            start: data.getStart(),
            times: data.getTimes(),
            end: data.getEnd(),
            days: data.getDaysMap(),
            week: data.getWeek()
        };
    }

    // NB schedule format - see BaseGoalUpdateCommandViewModel.java
    private constructScheduleFromState(): string {
        // TODO: Surely we can have this in a scheduleData instance and just update each value?
        let schedule = "";
        if (this.state.start) {
            schedule += "start:" + this.state.start.formatIso8601() + " ";
        }
        if (this.state.days) {
            const selectedDays = daysOrderedTargetSchedule.filter(day => this.state.days[day]);
            if (selectedDays.length > 0) {
                schedule += "days:" + selectedDays.join(",");
            }
        }

        if (this.state.times) {
            schedule +=
                " times:" +
                this.state.times
                    .filter(notNull)
                    .map(t => t!.formatHoursMinutes())
                    .join(",");
        }

        if (this.state.end) {
            schedule += " end:" + this.state.end.formatIso8601();
        }

        if (this.state.week) {
            schedule += " week:" + this.state.week;
        }

        return schedule;
    }
}

interface ScheduleProps {
    readOnly: boolean;
    title: string;
    instanceState: ScheduleData;
    onChange: (data: ScheduleData) => void;
    allowMultipleTimes?: boolean;
    withStartEnd?: boolean;
}

interface ScheduleState {
    statusText: string | null;
    title: string
}

/**
 * Form to manipulate cron schedules. Used by targetSchedules and read-only by AppointmentSchedulesTable
 */
export class CronScheduleStatus extends React.Component<ScheduleProps, ScheduleState> {
    static asElement(props: ScheduleProps): Element {
        const element = document.createElement("span");
        ReactDOM.render(<CronScheduleStatus {...props} />, element);
        return element;
    }

    private scheduleFormData: ScheduleData; // TODO: Remove and have all state hoisted
    private readonly targetScheduleForm = React.createRef<TargetScheduleForm>();

    public constructor(props: ScheduleProps) {
        super(props);
        //props.instanceState.bus.addHandler(() => this.forceUpdate());
        this.scheduleFormData = this.props.instanceState;
        this.state = {
            statusText: this.scheduleText(),
            title: this.getTitle()
        };
    }

    private scheduleText() {
        return this.scheduleFormData && this.scheduleFormData.asText(this.props.withStartEnd);
    }
    private getTitle() {
        return (
            this.props.title +
            (this.scheduleFormData ? ": " + this.scheduleFormData.asText(true) : "")
        );
    }

    /**
     * Returns the schedule without the 'end' date since that is part of the command, but not part of the database.
     */
    getScheduleForCmd() {
        return this.scheduleFormData.getScheduleForCmd();
    }

    private editSchedule() {
        const form = (
            <TargetScheduleForm
                key={"schedule-" + Uuid.randomV4().toString()} // TODO: Consider whether to pull up the data from the form and modal.  In fact we should do this for the whole smart step
                data={this.scheduleFormData}
                allowMultipleTimes={
                    (this.props.allowMultipleTimes && !this.props.readOnly) || false
                }
                ref={this.targetScheduleForm}
            />
        );
        const onCompleted = () => {
            if (this.targetScheduleForm.current) {
                this.scheduleFormData = this.targetScheduleForm.current.getData();
                this.setState({
                    statusText: this.scheduleText(),
                    title: this.getTitle()
                });
                this.props.onChange(this.scheduleFormData);
            }
        };
        showReactInModal("edit schedule", form, {
            onAction: onCompleted,
            action: "update",
            maxWidth: "sm"
        });
    }

    override render() {
        const muiTableCell = 0.75;
        return (
            <span
                title={this.state.title}
                className="target-schedule"
                onClick={() => !this.props.readOnly && this.editSchedule()}
            >
                <i
                    className={
                        "clickable-image fa fa-clock-o fa-lg" +
                        (!!this.state.statusText ? " active" : "")
                    }
                    style={{opacity: muiTableCell}}
                />
                <span>{this.state.statusText}</span>
            </span>
        );
    }
}
