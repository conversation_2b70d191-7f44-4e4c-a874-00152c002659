import {amber, green} from '@eccosolutions/ecco-mui';
import CloseIcon from '@material-ui/icons/Close';
import * as React from "react";
import {createStyles, IconButton, makeStyles, Snackbar, SnackbarContent, Theme} from "@eccosolutions/ecco-mui";

/*
** This may need to change post demo to handle many more options but it is simple enough for demoware.
*/
function FeedbackMessage(props: any) {
    // open by default, which requires a check in the parent component.
    // this breaks with the usual material ui patterns
    const [open, setOpen] = React.useState(true);
    const classes = useStyles();

    function onClose() {
        // post demo, worth considering if this should be async or not...
        // the power can be given to the parent component
        if (props.onClose) {
            props.onClose();
        }
        setOpen(false);
    }

    return (
        <Snackbar
            anchorOrigin={{
                vertical: 'bottom',
                horizontal: 'left'
            }}
            open={open}
            autoHideDuration={6000}
            onClick={onClose}
        >
            <SnackbarContent
                className={props.type === 'success' ? classes.successFeedback : classes.errorFeedback}
                message={<span id="message-id">{props.feedbackMessage}</span>}
                action={[
                    <IconButton key="close" aria-label="close" color="inherit" onClick={onClose}>
                        <CloseIcon />
                    </IconButton>
                ]}
            />
        </Snackbar>
    );
}

const useStyles = makeStyles((_theme: Theme) =>
    createStyles({
        successFeedback: {
            backgroundColor: green[800]
        },
        errorFeedback: {
            backgroundColor: amber[800]
        }
    })
);
export default FeedbackMessage;
