import * as React from "react";
import {FC, ReactNode, useEffect} from "react";
import {Checkbox, MenuItem} from "@eccosolutions/ecco-mui";
import {AdminMode} from "@eccosolutions/ecco-common";
import {useAppBarContext} from "../AppBarBase";
import {useStorage} from "ecco-components-core";

type Props = {
    title: ReactNode
    storageType: "sessionStorage" | "localStorage"
    storageKey: string
    onChange: (enabled: boolean) => void
    variant: "fa-cog" | "checkbox"
}

export const ToggleMenuItem: FC<Props> = ({title, variant,
                                              storageType, storageKey, onChange}) => {

    const [enabled, setEnabled] = useStorage(storageType, storageKey, 'n')
    const checked = enabled == 'y'

    function toggleEnabled() {
        const newValue = enabled == "y" ? "n" : "y";
        setEnabled(newValue);
        onChange(newValue == "y");
    }

    return (
        <MenuItem
            onClick={() => toggleEnabled()}>

            {variant == "checkbox"
                ? <><Checkbox checked={checked}/> &nbsp;{title}</>
                : <><i className={`fa fa-cog ${checked ? "fa-spin" : ""}`}/> &nbsp;{title}</>
            }

        </MenuItem>);
};

export const AdminModeMenuItem: FC<{name: string}> = ({name}) => <ToggleMenuItem
    title={name} storageType="sessionStorage" storageKey="admin-mode"
    variant="fa-cog"
    onChange={enabled => {AdminMode.bus.fire(new AdminMode(enabled))}}
/>;

export function useAdminModeMenu(name: string, condition = true) {
    const appBarContext = useAppBarContext();
    if (!appBarContext) {
        return;
    }
    const {setExtraMenuItems} = appBarContext;
    useEffect(() => {
        if (condition) {
            setExtraMenuItems(<AdminModeMenuItem name={name} />);
            return () => setExtraMenuItems(null);
        }
        return;
    }, []);
}



/** e.g. name="use new file view" key="preview.referral.offline" */
export const PreviewFeatureMenuItem: FC<{name: string, storageKey: string}> = ({name, storageKey}) => <ToggleMenuItem
    title={name} storageType="localStorage" storageKey={storageKey}
    variant="checkbox"
    onChange={() => {}}
/>;

export function usePreviewFeatureMenu(name: string, storageKey: string) {
    const appBarContext = useAppBarContext();

    useEffect(() => {
        if (appBarContext) {
            appBarContext.setExtraMenuItems(
                <PreviewFeatureMenuItem name={name} storageKey={storageKey} />
            );
            return () => appBarContext.setExtraMenuItems(null);
        }
        return () => {};
    }, []);

    const [toggle, _] = useStorage("localStorage", storageKey, "n");
    return toggle == "y";
}
