import {FC, useEffect, useState} from "react";
import * as React from "react";
import {SearchBar} from "../SearchBar";
import {Grid} from "@eccosolutions/ecco-mui";
import {EccoDate} from "@eccosolutions/ecco-common";
import {DatePickerEccoDate} from "@eccosolutions/ecco-mui-controls";

export interface SearchCriteria {
    page: number | null;
    searchText: string | null;
    dateFrom: EccoDate | null;
    dateTo: EccoDate | null;
}

export const AuditHistoryFilterBar: FC<{onChange: (criteria: SearchCriteria) => void}> = props => {
    const [searchCriteria, setSearchCriteria] = useState<SearchCriteria>({
        page: null,
        searchText: null,
        dateFrom: null,
        dateTo: null
    });

    useEffect(() => {
        props.onChange(searchCriteria);
    }, [searchCriteria]);

    const onChangeSearchBar = (searchText: string | null) => {
        // create a new object only if required, else the new object will trigger a load
        if (searchText != searchCriteria.searchText) {
            setSearchCriteria({...searchCriteria, searchText: searchText});
        }
    };

    const DateFrom = () => {
        return (
            <DatePickerEccoDate
                name={`search-from-date`}
                label="from"
                onChange={date => setSearchCriteria({...searchCriteria, dateFrom: date})}
                value={searchCriteria.dateFrom}
            />
        );
    };

    const DateTo = () => {
        return (
            <DatePickerEccoDate
                name={`search-to-date`}
                label="to"
                onChange={date => setSearchCriteria({...searchCriteria, dateTo: date})}
                value={searchCriteria.dateTo}
            />
        );
    };

    return (
        <Grid container spacing={2}>
            <Grid item xs={12}>
                <SearchBar searchText={searchCriteria.searchText} onChange={onChangeSearchBar} />
            </Grid>
            <Grid item xs={6}>
                <DateFrom />
            </Grid>
            <Grid item xs={6}>
                <DateTo />
            </Grid>
        </Grid>
    );
};
