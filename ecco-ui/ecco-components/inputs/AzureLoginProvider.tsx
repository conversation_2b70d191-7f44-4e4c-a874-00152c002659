import * as React from "react";
import {Button, Grid} from "@eccosolutions/ecco-mui";
import {FC} from "react";
import {applicationRootPath, remoteHost} from "application-properties";
import {usePromise} from "../data/entityLoadHooks";
import {SessionDataAjaxRepository} from "ecco-dto";
import {apiClient} from "../environment";
import signInSvg from "../images/sign-in-with-microsoft.svg";

function usePublicConfig() {
    const publicConfigRepository = new SessionDataAjaxRepository(apiClient)
    const {resolved, error, loading} = usePromise(
            () => publicConfigRepository.getPublicConfig(),[]);
    return {publicConfig: resolved, error, loading};
}

export const AzureLoginProvider: FC = () => {
    const {publicConfig, loading} = usePublicConfig();

    const loginUrl = new URL(`${applicationRootPath}oauth2/authorization/azure`, remoteHost ? remoteHost.slice(0, -1) : window.location.href).href;
    const azureEnabled = (loading || !publicConfig) ? false : publicConfig.hasLoginProvider('azure')

    const AzureLogin = () =>
        <Grid item xs={12} style={{textAlign: "center"}}>
            <Button
                    onClick={() => window.location.href = loginUrl}
                    color="primary"
            >
                <img alt="Sign in with Microsoft" src={signInSvg}/>
            </Button>
        </Grid>

    return azureEnabled ? <AzureLogin/> : null
};
