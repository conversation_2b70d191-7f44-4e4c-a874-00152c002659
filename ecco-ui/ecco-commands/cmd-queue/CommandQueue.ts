import {bus, <PERSON><PERSON><PERSON><PERSON>, Result} from "@eccosolutions/ecco-common";
import {CommandDto, Mergeable} from "ecco-dto/command-dto";
import {Command, OfflineCommand} from "../commands";
import {CommandRepository} from "./CommandRepository";
import {CommandDtoSentEvent} from "./commands";
import {MergeableCommand} from "./MergeableCommand";
import {AddressHistoryCommand} from "../address/commands";


let getGlobalCommandRepository: (() => CommandRepository) | null = null;

export function setGlobalCommandRepository(getCommandRepository: () => CommandRepository) {
    getGlobalCommandRepository = getCommandRepository;
}

/**
 * This is currently only fired in flushCommands: commandQueueFlushedEventBus.fire().
 * It's currently only passed to usePromise by useLatestCommands
 * so that the promise is reloaded when the bus is fired (although we also have useWorkflow for task changes).
 */
export const commandQueueFlushedEventBus = bus<void>();

function getCommandRepository() {
    console.assert(
        !!getGlobalCommandRepository,
        "You cannot call getCommandRepository() before setGlobalCommandRepository() has been called"
    );
    return getGlobalCommandRepository!();
}

/** Command adapter to give us a command has sync toDto() from the other way around, and
 * for good measure gives us an empty getEffects for signature compatibility */
// TODO make the return explicit - and return a new MergeableCommand type that actually extends from Command?
function adapt<DTO extends CommandDto>(command: MergeableCommand): AdaptedCommand {
    const dto = command.toDto();
    dto.commandUri = command.getCommandTargetUri();
    return {
        toCommandDto: () => dto,
        getEffects: () => [] as never[],
        // hasChanges is required for addCommand below
        // 'command' might have a hasChanges, but there is no interface
        // - so just migrate to Command to restore this functionality
        hasChanges: () => true
    };
}

/** An adaptation of the old-style MergeableCommand, which has partial
 * compatibility with the new Command type.
 *
 * As a hack, instances of this type get cast to Command and passed around as
 * if they are a real Command.
 *
 * TODO Make a new MergeableCommand type that actually extends from Command? */
interface AdaptedCommand extends OfflineCommand {
    toCommandDto(): CommandDto;
    getEffects(): any[]; // DJCs original was never[] which would imply impl must throw or never return
    hasChanges(): true;
}

export function isMergeable(command: any): command is Mergeable {
    return (<Mergeable>command).canMerge != undefined;
}
export function isMergeableCommand(command: any): command is MergeableCommand {
    return (<MergeableCommand>command).toDto != undefined;
}
export function isNewStyleCommand(cmd: any): cmd is Command {
    return (<Command>cmd).toCommandDto !== undefined;
}

/**
 * A Queue that supports merging a command into a single previous command when adding, such that operations
 * that simplify can be stripped to a simple command for submitting and seeing in the history.
 * Most importantly is that we want to be able to have some changes cancel each other out.
 *
 * We can then do things like emit events for the queue length to enable and disable submit buttons.
 */
export class CommandQueue {
    private readonly commandQueueRepository: CommandRepository;
    /**
     *
     * @param commandQueueRepository - Don't provide this in .tsx files as you should get it via ServicesContextProvider having set it
     */
    constructor(commandQueueRepository = getCommandRepository()) {
        // Comes from global command repository ... EccoAPI
        this.commandQueueRepository = commandQueueRepository!;
    }

    private queue = new Array<Command>();

    /** Keep track of what has been queued to add, but not yet added */
    private pendingAdditionsCount = 0;

    private commandSubmittedEventBus = bus<Command>();

    private commandDtoSentEventBus = bus<CommandDtoSentEvent>();

    private pendingOperations = Promise.resolve();

    /** Useful if you are using preSubmitCommandsHook so want to start from empty each time */
    public clear(): void {
        this.queue = [];
    }

    /** Add a queue */
    public addQueue(queue: CommandQueue) {
        this.pendingAdditionsCount++;

        this.pendingOperations = this.pendingOperations // Wait our pending operations
            .then(() => queue.getCommands()) // Then add commands from other queue
            .then(commands => {
                commands.forEach(cmd => this.applyToQueue(cmd));
                this.pendingAdditionsCount--;
            });
    }

    /**
     * Add the command, merging it with at most one previous commands (if merged, the previous command is
     * replaced with this one.
     */
    private applyToQueue(cmd: Command) {
        // TODO: put hasChanges() on MergeableCommand and test it here before adding - may break stuff!
        // NB cmd may be the result of the 'adapt' method - see 'addCommand' below.
        // This method returns a new object - losing our ability to see the merge methods
        // It would be worth checking where merge does not work, that the relevant addCommand has been migrated
        // to pass a promise, not the actual object - which is typically cmd = this.workUuid.then((id) => new Command...
        if (isMergeable(cmd)) {
            for (let i = 0; i < this.queue.length; i++) {
                const prev = this.queue[i];
                if (isMergeable(prev) && cmd.canMerge(prev)) {
                    const merged = cmd.merge(prev);
                    if (merged) {
                        this.queue[i] = merged;
                    } else {
                        // null result so remove
                        this.queue.splice(i, 1);
                    }
                    return;
                }
            }
        }
        // didn't merge
        this.queue.push(cmd);
    }

    /**
     * Add the command to the end of the queue
     * Note that most commands are still MergeableCommand (which is NOT a Command).
     * The new Command approach is driven by showing changes offline - it has no work promise in it
     * but we are given a promise to the queue, which typically resolves the workUuid to get the command.
     */
    public addCommand<T extends OfflineCommand>(
        possiblyLegacyCmd: T | Promise<AddressHistoryCommand> | MergeableCommand
    ) {
        this.pendingAdditionsCount++;

        const cmd: OfflineCommand | Promise<AddressHistoryCommand> =
            isMergeable(possiblyLegacyCmd) && !isNewStyleCommand(possiblyLegacyCmd)
                ? // if its a MergeableCommand (original offline style commands), then we
                  // 'adapt' it by returning a NEW object with toDto() in it.
                  // This loses the original command object (eg for 'instanceof'
                  // class which only occurs now in the graph code, search
                  // for 'instanceof command')
                  adapt(<MergeableCommand>possiblyLegacyCmd)
                : possiblyLegacyCmd;

        this.pendingOperations = this.pendingOperations
            .then(() => cmd) // resolve command if needed
            .then(cmd => {
                if (cmd.hasChanges()) {
                    this.applyToQueue(cmd as Command);
                    this.pendingAdditionsCount--;
                }
            });

        return this;
    }

    public addCommandSubmittedEventHandler(handler: EventHandler<Command>): void {
        if (handler) {
            this.commandSubmittedEventBus.addHandler(handler);
        }
    }

    public removeCommandSubmittedEventHandler(handler: EventHandler<Command>): void {
        this.commandSubmittedEventBus.removeHandler(handler);
    }

    /** Event is the dto in handler: (event) => void */
    public addCommandDtoSentEventHandler(handler: EventHandler<CommandDtoSentEvent>): void {
        this.commandDtoSentEventBus.addHandler(handler);
    }

    /** Event is the dto in handler: (event) => void */
    public removeCommandDtoSentEventHandler(handler: EventHandler<CommandDtoSentEvent>): void {
        this.commandDtoSentEventBus.removeHandler(handler);
    }

    public getCommands(): Promise<Command[]> {
        return this.waitPendingOperations().then(() => this.queue.slice(0)); // Make a copy.
    }

    public getCommandsNoPending() {
        if (this.pendingAdditionsCount > 0) {
            throw Error("There are pending operations");
        }
        return this.queue.slice(0);
    }

    private removeFirst(): void {
        // removed pendingOperations check as this is now internal and we always wait first
        this.queue.splice(0, 1);
    }

    /**
     * NB This is the size of commands passed in, not necessarily commands going to the server
     * since not all commands will have 'hasChanges'.
     */
    public size(): number {
        return this.queue.length + this.pendingAdditionsCount;
    }

    private waitPendingOperations(): Promise<void> {
        return this.pendingOperations;
    }

    private submitCommand(command: Command): Promise<any> {
        return this.commandQueueRepository.sendCommand(command);
    }

    public flushCommands(reloadEvent = true): Promise<void> {
        if (this.size() == 0) {
            // All gets resolved if needed
            console.log(
                "commandQueue is empty - possibly okay, but could be DEV error - don't use a promise in emitCommands!"
            );
        }

        const handleCommand = (): Promise<void> => {
            const command = this.queue[0];
            if (command) {
                return this.submitCommand(command).then((returnDto: Result) => {
                    // returnDto is '{"id":"...","message":"command applied"}'
                    // TODO handle a duplicate? be nice to have a message to the user for BaseCommandHandler's 'command ignored'
                    this.removeFirst();
                    this.commandSubmittedEventBus.fire(command);
                    this.commandDtoSentEventBus.fire(new CommandDtoSentEvent(command, returnDto));
                    return handleCommand();
                });
            } else {
                if (reloadEvent) {
                    // For now allow this to be disabled where flush(false) is called
                    // This used to be ReloadEvent.bus.fire() back in Apr 2023, 578395f3
                    commandQueueFlushedEventBus.fire();
                }
                return Promise.resolve();
            }
        };

        return this.pendingOperations.then(() => handleCommand());
    }
}
export default CommandQueue;
