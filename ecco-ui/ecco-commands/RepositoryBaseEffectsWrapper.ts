
import {CommandDtoRepository} from "./CommandDtoRepository";
import {CommandDtoConverter} from "./CommandDtoConverter";
import { Command, CommandEffect } from "./commands";
import {DomainObject} from "./DomainObject";
import { collectionSubscription, CollectionSubscription, objectSubscription, ObjectSubscription }
        from "./subscriptions";
import {Observable} from "rxjs";

export abstract class RepositoryBaseEffectsWrapper<TCommand extends Command, TCommandDto> {
    constructor(
        private commandDtoRepository: CommandDtoRepository<TCommandDto>,
        private commandDtoConverter: CommandDtoConverter<TCommand, TCommandDto>
    ) {}

    public submitCommand(command: TCommand): Promise<TCommandDto> {
        return this.commandDtoRepository.submitCommand(this.commandDtoConverter.toDto(command));
    }

    protected objectSubscription<T extends DomainObject>(
        key: string,
        snapshot: Promise<T>
    ): ObjectSubscription<T> {
        return objectSubscription(key, snapshot, this.findPendingEffects());
    }

    protected collectionSubscription<T extends DomainObject>(
        predicate: (domainObject: T) => boolean,
        snapshots: Promise<ReadonlyArray<T>>
    ): CollectionSubscription<T> {
        return collectionSubscription(predicate, snapshots, this.findPendingEffects());
    }

    private findPendingEffects(): Observable<CommandEffect<any>> {
        return this.commandDtoRepository
            .findPendingCommands()
            .map(command => this.commandDtoConverter.fromDto(command))
            .concatMap(command => command.getEffects());
    }
}

