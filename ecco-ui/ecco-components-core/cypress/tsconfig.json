{"compilerOptions": {"rootDirs": ["."], "jsx": "react", "target": "ES2020", "lib": ["ES2020", "dom"], "types": ["cypress", "@testing-library/cypress"], "moduleResolution": "Node", "allowSyntheticDefaultImports": true, "paths": {"@cypress/react": ["../../../../node_modules/@cypress/react/dist/index.d.ts"]}, "noEmit": true}, "references": [{"path": "../tsconfig.json"}], "include": ["**/*.ts", "**/*.tsx"]}