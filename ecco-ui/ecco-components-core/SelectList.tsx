import * as React from "react";
import Creatable from "react-select/creatable";
import Select from "react-select";
import {Props as CreatableProps} from "react-select/src/Creatable";
import {OptionTypeBase} from "react-select/src/types";

/**
 * Expose only the props from react-select that we want - as an anti-corruption layer.
 * These properties work for Select (SelectProps) and Creatable (CreatableProps).
 */
interface Props<O extends OptionTypeBase>
    extends Pick<
        CreatableProps<O>,
        | "isClearable"
        | "isDisabled"
        | "options"
        | "value"
        | "onChange"
        | "getOptionLabel"
        | "getOptionValue"
        | "getNewOptionData"
        | "defaultInputValue"
        | "defaultValue"
        | "placeholder"
        | "isMulti"
    > {}

/**
 * Creatable needs 'getNewOptionData' unless we just use the default structure value/label.
 * See https://github.com/JedWatson/react-select/issues/2630
 */
export interface CreatableListOptions {
    value: string;
    label: string;
}

interface ListType<O extends OptionTypeBase> extends Props<O> {
    createNew?: boolean;
}

/**
 * Anti-corruption layer for react-select Creatable.
 * NB Offers ZERO VALIDATION: https://github.com/JedWatson/react-select/issues/1453
 * TODO: Consider migrating to https://material-ui.com/components/autocomplete/#creatable
 */
export function SelectList<ENTRY extends OptionTypeBase>(props: ListType<ENTRY>) {
    return props.createNew ? (
        <Creatable className="react-select" {...props} isClearable={true} />
    ) : (
        <Select className="react-select" {...props} isClearable={true} />
    );
}
