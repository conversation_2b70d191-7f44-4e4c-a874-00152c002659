package com.ecco.dom.contacts;

import com.ecco.dom.LongKeyedEntity;

import java.time.LocalDate;

public interface Contact extends LongKeyedEntity {

    String getDisplayName();
    String getPhoneNumber();
    String getMobileNumber();
    String getEmail();
    //public String getFirstName();
    //public String getLastName();
    boolean getIsUser();
    // NB ContactServiceImpl.updateAddress saves the addressLocation and address, since Nov 2020
    // NB there are still places which reference 'address' - also see DEV-525
    Address getAddress();
    LocalDate getArchived();

}
