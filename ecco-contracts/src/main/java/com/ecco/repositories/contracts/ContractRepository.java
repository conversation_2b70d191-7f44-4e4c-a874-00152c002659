package com.ecco.repositories.contracts;

import com.ecco.dom.contracts.Contract;
import com.ecco.dom.contracts.RateCard;
import com.ecco.infrastructure.spring.data.CrudRepositoryWithFindOne;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface ContractRepository extends CrudRepositoryWithFindOne<Contract, Integer> {

    @Query("SELECT c.rateCards FROM Contract c where c.id = ?1")
    List<RateCard> findRateCardsByContractId(int contractId);

    List<Contract> findByContractTypeIdOrderByIdDesc(int contractTypeId);

}
