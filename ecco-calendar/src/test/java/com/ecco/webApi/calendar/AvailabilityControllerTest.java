package com.ecco.webApi.calendar;

import com.ecco.calendar.core.Availability;
import com.ecco.calendar.core.CalendarService;
import com.ecco.infrastructure.rest.hateoas.ApiLinkTo;
import org.joda.time.DateTime;
import org.joda.time.Interval;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.springframework.hateoas.RepresentationModel;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.net.URI;

import static com.ecco.webApi.calendar.AvailabilityResourceAssembler.*;
import static org.joda.time.DateTimeConstants.DAYS_PER_WEEK;
import static org.joda.time.DateTimeConstants.MONDAY;
import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;
import static org.springframework.hateoas.IanaLinkRelations.NEXT_VALUE;
import static org.springframework.hateoas.IanaLinkRelations.PREV_VALUE;
import static org.springframework.hateoas.IanaLinkRelations.SELF_VALUE;

public class AvailabilityControllerTest {
    private static final String BASE_URI = "http://localhost/api/calendar/availability/";
    MockHttpServletRequest request;
    CalendarService calendarService;
    AvailabilityController controller;
    private static final String CAL1_ID = "Cal1";
    private static final String CAL1_NAME = "A User";
    private static final DateTime START_OF_WEEK = new DateTime().withDayOfWeek(MONDAY).withMillisOfDay(0);
    private static final DateTime END_OF_WEEK = START_OF_WEEK.plusDays(DAYS_PER_WEEK);
    private static final Interval THIS_WEEK = new Interval(START_OF_WEEK, END_OF_WEEK);
    private static final Interval NEXT_WEEK = new Interval(END_OF_WEEK, THIS_WEEK.toPeriod());
    private static final Interval LAST_WEEK = new Interval(THIS_WEEK.toPeriod(), START_OF_WEEK);
    private static final DateTimeFormatter YYYYMMDD = DateTimeFormat.forPattern("yyyyMMdd");

    @SuppressWarnings("unused")
    private static final ApiLinkTo initStaticApiLinkTo = new ApiLinkTo("/api", null);

    @Before
    public void setUp() {
        calendarService = mock(CalendarService.class);
        controller = new AvailabilityController();
        controller.setCalendarService(calendarService);
        request = new MockHttpServletRequest();
        ServletRequestAttributes requestAttributes = new ServletRequestAttributes(request);
        RequestContextHolder.setRequestAttributes(requestAttributes);
    }

    @Test
    public void givenNoAvailabilityWhenGettingThenEmptyResourceReturned() {
        Availability availability = new Availability(null, CAL1_ID, CAL1_NAME, THIS_WEEK);
        when(calendarService.findAvailability(CAL1_ID, THIS_WEEK, true)).thenReturn(availability);

        final AvailabilityResource resource = controller.getAvailability(CAL1_ID, START_OF_WEEK, END_OF_WEEK);

        assertEquals(START_OF_WEEK, resource.getDtStart());
        assertEquals(END_OF_WEEK, resource.getDtEnd());
        assertEquals(0, resource.getAvailables().size());
        assertEquals(BASE_URI + CAL1_ID + "/" + YYYYMMDD.print(START_OF_WEEK) + "-" + YYYYMMDD.print(END_OF_WEEK), resource.getRequiredLink(SELF_VALUE).getHref());
    }

    @Test
    public void whenGettingThenRelatedResourcesAreDiscoverable() {
        Availability availability = new Availability(null, CAL1_ID, CAL1_NAME, THIS_WEEK);
        when(calendarService.findAvailability(CAL1_ID, THIS_WEEK, true)).thenReturn(availability);

        final AvailabilityResource resource = controller.getAvailability(CAL1_ID, START_OF_WEEK, END_OF_WEEK);
        assertEquals(BASE_URI + CAL1_ID, resource.getRequiredLink(REL_COLLECTION).getHref());
        assertEquals(BASE_URI + CAL1_ID + "/" + YYYYMMDD.print(LAST_WEEK.getStart()) + "-" + YYYYMMDD.print(LAST_WEEK.getEnd()), resource.getRequiredLink(PREV_VALUE).getHref());
        assertEquals(BASE_URI + CAL1_ID + "/" + YYYYMMDD.print(NEXT_WEEK.getStart()) + "-" + YYYYMMDD.print(NEXT_WEEK.getEnd()), resource.getRequiredLink(NEXT_VALUE).getHref());
    }

    @Test
    public void whenGettingBaseUriThenUsefulResourcesAreDiscoverable() {
        final ResponseEntity<RepresentationModel> response = controller.getAvailabilityResources(CAL1_ID);
        final RepresentationModel resource = response.getBody();
        assertEquals(HttpStatus.MULTIPLE_CHOICES, response.getStatusCode());
        assertEquals(BASE_URI + CAL1_ID, resource.getRequiredLink(SELF_VALUE).getHref());
        assertEquals(URI.create(BASE_URI + CAL1_ID + "/" + YYYYMMDD.print(START_OF_WEEK) + "-" + YYYYMMDD.print(END_OF_WEEK)),
                response.getHeaders().getLocation());
        assertEquals(BASE_URI + CAL1_ID + "/" + YYYYMMDD.print(START_OF_WEEK) + "-" + YYYYMMDD.print(END_OF_WEEK), resource.getRequiredLink(REL_WEEK).getHref());
        assertEquals(BASE_URI + CAL1_ID + "/" + YYYYMMDD.print(new DateTime()) + "-" + YYYYMMDD.print(new DateTime().plusDays(1)), resource.getRequiredLink(REL_DAY).getHref());
        assertEquals(BASE_URI + CAL1_ID + "/" + YYYYMMDD.print(new DateTime().withDayOfMonth(1)) + "-" + YYYYMMDD.print(new DateTime().plusMonths(1).withDayOfMonth(1)), resource.getRequiredLink(REL_MONTH).getHref());
    }

    @Test
    public void givenSomeAvailabilityWhenGettingThenPopulatedResourceReturned() {
        Availability availability = new Availability(null, CAL1_ID, CAL1_NAME, THIS_WEEK);
        final Interval interval1 = new Interval(START_OF_WEEK.plusHours(9), START_OF_WEEK.plusHours(18));
        availability.addAvailableInterval(interval1);
        when(calendarService.findAvailability(CAL1_ID, THIS_WEEK, true)).thenReturn(availability);

        final AvailabilityResource resource = controller.getAvailability(CAL1_ID, START_OF_WEEK, END_OF_WEEK);

        assertEquals(1, resource.getAvailables().size());
        assertEquals(interval1.getStart(), resource.getAvailables().get(0).getDtStart());
        assertEquals(interval1.getEnd(), resource.getAvailables().get(0).getDtEnd());
    }

    @Test
    public void whenPostingThenLocationHeaderReturned() {
        final AvailabilityResource resource = new AvailabilityResource();
        resource.setDtStart(START_OF_WEEK);
        resource.setDtEnd(END_OF_WEEK);

        final ResponseEntity<AvailabilityResource> response = controller.updateAvailability(CAL1_ID, resource);

        assertEquals(HttpStatus.CREATED, response.getStatusCode());
        assertEquals(URI.create(BASE_URI + CAL1_ID + "/" + YYYYMMDD.print(START_OF_WEEK) + "-" + YYYYMMDD.print(END_OF_WEEK)),
                response.getHeaders().getLocation());
    }

    @Test
    public void whenPostingThenCalendarUpdated() {
        final AvailabilityResource resource = new AvailabilityResource();
        resource.setDtStart(START_OF_WEEK);
        resource.setDtEnd(END_OF_WEEK);

        controller.updateAvailability(CAL1_ID, resource);

        ArgumentCaptor<Availability> argument = ArgumentCaptor.forClass(Availability.class);
        verify(calendarService).updateAvailability(argument.capture());
        assertEquals(CAL1_ID, argument.getValue().getCalendarId());
        assertEquals(THIS_WEEK, argument.getValue().getInterval());
        assertEquals(0, argument.getValue().getAvailableIntervals().size());
    }

    @Test
    public void whenPostingWithSomeAvailabilityThenCalendarUpdatedWithSameAvailability() {
        final AvailabilityResource resource = new AvailabilityResource();
        resource.setDtStart(START_OF_WEEK);
        resource.setDtEnd(END_OF_WEEK);
        final AvailableResource interval1 = new AvailableResource();
        interval1.setDtStart(START_OF_WEEK.plusHours(9));
        interval1.setDtEnd(START_OF_WEEK.plusHours(18));
        resource.getAvailables().add(interval1);

        controller.updateAvailability(CAL1_ID, resource);

        ArgumentCaptor<Availability> argument = ArgumentCaptor.forClass(Availability.class);
        verify(calendarService).updateAvailability(argument.capture());
        assertEquals(1, argument.getValue().getAvailableIntervals().size());
    }

    @Test(expected = RuntimeException.class)
    public void givenCalendarIdIsInvalidWhenPostingThenExceptionThrown() {
        doThrow(new RuntimeException()).when(calendarService).updateAvailability(any(Availability.class));

        final AvailabilityResource resource = new AvailabilityResource();
        resource.setDtStart(START_OF_WEEK);
        resource.setDtEnd(END_OF_WEEK);

        controller.updateAvailability(CAL1_ID, resource);
    }

    @Test
    public void whenExceptionHandlerInvokedThenErrorStatusReturned() {
        final ResponseEntity<Void> response = controller.handleRuntimeException(new RuntimeException());

        assertEquals(HttpStatus.Series.SERVER_ERROR, response.getStatusCode().series());
    }
}
