package com.ecco.webApi.calendar;

import com.ecco.calendar.CombinedEntry;
import com.ecco.calendar.core.Entry;
import com.ecco.calendar.core.webapi.EventResource;

import org.joda.time.DateTime;
import org.junit.Test;
import java.net.URI;

import static org.junit.Assert.*;

/**
 * Test the EventResourceAssembler which assembles an EventResource from a CombinedEntry of ical's Entry and ecco's EventEntry.
 * The assembler is fairly rudimentary, its the EntryConverterTest which does the work.
 */
public class EventResourceAssemblerTest {
    @Test
    public void whenEntryHasNoEndDateThenConversionSucceeds() {
        Entry entry = new Entry();
        entry.setAllDay(true);
        entry.setCalendarIdUserReferenceUri("chilopod");
        entry.setDescription("hahn");
        entry.setStart(new DateTime().minusDays(2).withTime(0, 0, 0, 0));
        entry.setEnd(null);
        entry.setLocation("spectralness");
        entry.setTitle("oidium");
        entry.setManagedByUri(URI.create("entity://treasonableness/" + hashCode()));
        CombinedEntry ce = new CombinedEntry();
        ce.setIcalEntry(entry);
        final EventResource resource = new EventResourceAssembler(null, EventResourceAssemblerTest.class)
            .toModel(ce);
        assertNull(resource.getEnd());
        assertNotNull(resource.getStart());
        assertTrue(resource.isAllDay());
    }
    @Test
    public void whenEntryHasEndDateThenConversionSucceeds() {
        Entry entry = new Entry();
        entry.setAllDay(true);
        entry.setCalendarIdUserReferenceUri("chilopod");
        entry.setDescription("hahn");
        entry.setStart(new DateTime().minusDays(2).withTime(0, 0, 0, 0));
        entry.setEnd(entry.getStart().plusDays(2));
        entry.setLocation("spectralness");
        entry.setTitle("oidium");
        entry.setManagedByUri(URI.create("entity://treasonableness/" + hashCode()));
        CombinedEntry ce = new CombinedEntry();
        ce.setIcalEntry(entry);
        final EventResource resource = new EventResourceAssembler(null, EventResourceAssemblerTest.class)
                .toModel(ce);
        assertEquals(entry.getStart().plusDays(2).toLocalDateTime(), resource.getEnd());
        assertNotNull(resource.getStart());
        assertTrue(resource.isAllDay());
    }
}
