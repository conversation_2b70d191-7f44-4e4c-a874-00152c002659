package com.ecco.workflow;

/**
 * Defines the workflow mode for existing referrals, when there is the option
 * of using Activiti.
 *
 * For new referrals, a workflow process instance will be created, but what
 * happens next should be determined by the mode here.
 *
 */
public enum WorkflowMode {
    /** Force use of legacy workflow even if an Activiti process instance exists */
    FORCE_LEGACY,

    /** Use Activiti for where a process instance exists, else use legacy */
    WORKFLOW_IF_EXISTS,

    /** Always use Activiti. Auto-create process instance when doesn't exist */
    WORKFLOW_WITH_MIGRATION
}
