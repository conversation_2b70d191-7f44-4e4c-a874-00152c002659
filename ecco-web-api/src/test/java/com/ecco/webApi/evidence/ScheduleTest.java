package com.ecco.webApi.evidence;

import org.junit.Test;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;

import static org.assertj.core.api.Assertions.assertThat;

public class ScheduleTest {

    @Test
    public void correctlyParseValidFormats() {

        {
            var s = Schedule.from("start:" + LocalDate.now().format(DateTimeFormatter.ISO_DATE).concat(" days:Mon,Tue,Thur"));
            assertThat(s.getDays()).isEqualTo("Mon,<PERSON><PERSON>,Thur");
        }
        {
            Schedule startOnly = Schedule.from("start:2012-11-11");
            assertThat(startOnly.getStart()).isEqualTo(LocalDate.of(2012, 11, 11));
            assertThat(startOnly.getDays()).isEqualTo(null);
        }
        {
            Schedule startAndDays = Schedule.from("start:2012-11-11 days:*");
            assertThat(startAndDays.getStart()).isEqualTo(LocalDate.of(2012, 11, 11));
            assertThat(startAndDays.getDays()).isEqualTo("*");
        }
        {
            Schedule startAndtimeAndEnd = Schedule.from("start:2012-11-11 times:12:55 end:2019-06-04");
            assertThat(startAndtimeAndEnd.getStart()).isEqualTo(LocalDate.of(2012, 11, 11));
            assertThat(startAndtimeAndEnd.getDays()).isEqualTo(null);
            assertThat(startAndtimeAndEnd.getTimes()[0]).isEqualTo(LocalTime.of(12, 55));
        }
        {
            Schedule startAndtimeAndEnd = Schedule.from("start:2012-11-11 times:12:55,15:25 end:2019-06-04");
            assertThat(startAndtimeAndEnd.getStart()).isEqualTo(LocalDate.of(2012, 11, 11));
            assertThat(startAndtimeAndEnd.getDays()).isEqualTo(null);
            assertThat(startAndtimeAndEnd.getTimes()[0]).isEqualTo(LocalTime.of(12, 55));
            assertThat(startAndtimeAndEnd.getTimes()[1]).isEqualTo(LocalTime.of(15, 25));
        }
        {
            Schedule startAndtimeAndEnd = Schedule.from("start:2012-11-11 times:12:55 end:2019-06-04 week:3");
            assertThat(startAndtimeAndEnd.getStart()).isEqualTo(LocalDate.of(2012, 11, 11));
            assertThat(startAndtimeAndEnd.getDays()).isEqualTo(null);
            assertThat(startAndtimeAndEnd.getTimes()[0]).isEqualTo(LocalTime.of(12, 55));
            assertThat(startAndtimeAndEnd.getEnd()).isEqualTo(LocalDate.of(2019, 6, 4));
            assertThat(startAndtimeAndEnd.getWeek()).isEqualTo(3);
        }
    }

    @Test
    public void correctlyScheduleNextDateWhenEditingSchedule() {
        // tuesday
        var workInstantTues29Sep1500 = LocalDateTime.of(2020, 9, 29, 15, 0);
        var workDateTues29Sep = workInstantTues29Sep1500.toLocalDate();
        var action = Schedule.SettingUpOrRecording.SETTING_UP;
        {
            // start
            Schedule s = Schedule.from("start:2020-09-29");
            LocalDateTime n = s.nextDateTimeAfter(workInstantTues29Sep1500, action);
            assertThat(n).isNull();
        }
        {
            // start
            // late start
            Schedule s = Schedule.from("start:2020-10-02");
            LocalDateTime n = s.nextDateTimeAfter(workInstantTues29Sep1500, action);
            assertThat(n).isEqualTo(LocalDate.of(2020, 10, 2).atStartOfDay());
        }
        {
            // start + days
            // late start
            Schedule s = Schedule.from("start:2020-10-02 days:*");
            LocalDateTime n = s.nextDateTimeAfter(workInstantTues29Sep1500, action);
            assertThat(n).isEqualTo(LocalDate.of(2020, 10, 2).atStartOfDay());
        }
        {
            // start + days
            Schedule s = Schedule.from("start:2020-09-29 days:Wed");
            LocalDateTime n = s.nextDateTimeAfter(workInstantTues29Sep1500, action);
            assertThat(n).isEqualTo(LocalDateTime.of(2020, 9, 30, 0, 0));
        }
        {
            // start + times
            // 4pm is after now, so we expect 4pm to be scheduled
            Schedule s = Schedule.from("start:2020-09-29 times:16:00");
            LocalDateTime n = s.nextDateTimeAfter(workInstantTues29Sep1500, action);
            assertThat(n).isEqualTo(LocalDateTime.of(2020, 9, 29, 16, 0));
        }
        {
            // start + times
            // 2pm is before 3pm, with no schedule left - so null
            Schedule s = Schedule.from("start:2020-09-29 times:14:00");
            LocalDateTime n = s.nextDateTimeAfter(workInstantTues29Sep1500, action);
            assertThat(n).isNull();
        }
        {
            // start + times + days
            // 2pm is before 3pm, but we have days so expect tomorrow
            Schedule s = Schedule.from("start:2020-09-29 days:* times:14:00");
            LocalDateTime n = s.nextDateTimeAfter(workInstantTues29Sep1500, action);
            assertThat(n).isEqualTo(LocalDateTime.of(2020, 9, 30, 14, 0));
        }
        {
            // start + times + days
            // 2pm is before 3pm, but we have Wed so expect tomorrow
            Schedule s = Schedule.from("start:2020-09-29 days:Wed times:14:00");
            LocalDateTime n = s.nextDateTimeAfter(workInstantTues29Sep1500, action);
            assertThat(n).isEqualTo(LocalDateTime.of(2020, 9, 30, 14, 0));
        }
        {
            // start + times + days
            // 2pm is before 3pm, but we have Sun,Mon so expect Sunday
            Schedule s = Schedule.from("start:2020-09-29 days:Sun,Mon times:14:00");
            LocalDateTime n = s.nextDateTimeAfter(workInstantTues29Sep1500, action);
            assertThat(n).isEqualTo(LocalDateTime.of(2020, 10, 4, 14, 0));
        }
        {
            // start + times + days + end
            // as above, this is to test ending the 4th doesn't affect the 4th next due
            Schedule s = Schedule.from("start:2020-09-29 days:Sun,Mon times:14:00 end:2020-10-04");
            LocalDateTime n = s.nextDateTimeAfter(workInstantTues29Sep1500, action);
            assertThat(n).isEqualTo(LocalDateTime.of(2020, 10, 4, 14, 0));
        }
        {
            // start + times + days + end
            // as above, this is to test ending the 3rd means there's no more schedules left
            Schedule s = Schedule.from("start:2020-09-29 days:Sun,Mon times:14:00 end:2020-10-03");
            LocalDateTime n = s.nextDateTimeAfter(workInstantTues29Sep1500, action);
            assertThat(n).isNull();
        }
        {
            // start + times + days + end = week 1
            // as above, with every week
            Schedule s = Schedule.from("start:2020-09-29 days:Sun,Mon times:14:00 end:2020-10-03 week:1");
            LocalDateTime n = s.nextDateTimeAfter(workInstantTues29Sep1500, action);
            assertThat(n).isNull();
        }
        {
            // start + times + days + end
            // early start in July
            Schedule s = Schedule.from("start:2020-07-29 days:Sun,Mon times:14:00 end:2020-11-03");
            LocalDateTime n = s.nextDateTimeAfter(workInstantTues29Sep1500, action);
            assertThat(n).isEqualTo(LocalDateTime.of(2020, 10, 4, 14, 0));
        }
        {
            // start + days + week:2
            // 'every weeks 2' means wk1 (29/09 - 06/10), wk3 (13/10 - 20/10)
            Schedule s = Schedule.from("start:2020-09-29 days:Sun,Mon week:2");
            LocalDateTime firstSunNoTime = workDateTues29Sep.atStartOfDay().with(TemporalAdjusters.next(DayOfWeek.SUNDAY));
            // Sun 4th is in the first week after today
            LocalDateTime n = s.nextDateTimeAfter(workInstantTues29Sep1500, action);
            assertThat(n).isEqualTo(firstSunNoTime);
            // Mon 5th is still in the first week after today
            LocalDateTime n2 = s.nextDateTimeAfter(workInstantTues29Sep1500.with(TemporalAdjusters.next(DayOfWeek.MONDAY)), action);
            assertThat(n2).isEqualTo(firstSunNoTime.plusDays(1));
            // Sun 18th is the first Sun after 6th
            LocalDateTime n3 = s.nextDateTimeAfter(workInstantTues29Sep1500.plusWeeks(1), action);
            assertThat(n3).isEqualTo(firstSunNoTime.plusWeeks(2));
        }
        {
            // start + times + days + end
            // start next mon at 10am, and expect next mon to be the first date
            Schedule s = Schedule.from("start:2021-03-08 days:Mon times:10:00 end:2021-08-07");
            var fewDaysBefore = LocalDateTime.of(2021, 3, 4, 14, 0);
            LocalDateTime n = s.nextDateTimeAfter(fewDaysBefore, action);
            assertThat(n).isEqualTo(LocalDateTime.of(2021, 3, 8, 10, 0));
        }
    }

    @Test
    public void correctlyScheduleNextDateTimeWhenEditingMultiTimeSchedule() {
        // tuesday
        var workInstantTues29Sep1500 = LocalDateTime.of(2020, 9, 29, 15, 0);
        var action = Schedule.SettingUpOrRecording.SETTING_UP;
        {
            // start + times
            // 4pm is after now, so we expect 4pm to be scheduled
            Schedule s = Schedule.from("start:2020-09-29 times:16:00,12:00");
            LocalDateTime n = s.nextDateTimeAfter(workInstantTues29Sep1500, action);
            assertThat(n).isEqualTo(LocalDateTime.of(2020, 9, 29, 16, 0));
        }
        {
            // start + times
            Schedule s = Schedule.from("start:2020-09-29 times:10:00,12:00");
            LocalDateTime n = s.nextDateTimeAfter(workInstantTues29Sep1500, action);
            assertThat(n).isNull();
        }
        {
            // start + times + days
            Schedule s = Schedule.from("start:2020-09-29 days:Mon times:10:00,12:00");
            LocalDateTime n = s.nextDateTimeAfter(workInstantTues29Sep1500, action);
            assertThat(n).isEqualTo(LocalDateTime.of(2020, 10, 5, 10, 0));
        }
    }

    @Test
    public void correctlyScheduleNextDateTimeWhenCompletingWorkForMultiTimeSchedule() {
        // tuesday
        var checkCompleted29Sep1500 = LocalDateTime.of(2020, 9, 29, 15, 0);
        var action = Schedule.SettingUpOrRecording.RECORDING;
        {
            // start + times
            // due later (4pm is after now) still available
            Schedule s = Schedule.from("start:2020-09-29 times:12:00,16:00");
            LocalDateTime n = s.nextDateTimeAfter(checkCompleted29Sep1500, action);
            assertThat(n).isEqualTo(LocalDateTime.of(2020, 9, 29, 16, 0));
        }
        {
            // start + times
            Schedule s = Schedule.from("start:2020-09-29 times:10:00,12:00");
            LocalDateTime n = s.nextDateTimeAfter(checkCompleted29Sep1500, action);
            assertThat(n).isNull();
        }
        {
            // start + times + days
            Schedule s = Schedule.from("start:2020-09-29 days:Mon times:10:00,12:00");
            LocalDateTime n = s.nextDateTimeAfter(checkCompleted29Sep1500, action);
            assertThat(n).isEqualTo(LocalDateTime.of(2020, 10, 5, 10, 0));
        }
    }

    @Test
    public void correctlyScheduleNextDateWhenCompletingWork() {
        // tuesday
        var checkCompleted29Sep1500 = LocalDateTime.of(2020, 9, 29, 15, 0);
        var startDate29Sep = checkCompleted29Sep1500.toLocalDate();
        var action = Schedule.SettingUpOrRecording.RECORDING;
        {
            // start
            Schedule s = Schedule.from("start:2020-09-29");
            LocalDateTime n = s.nextDateTimeAfter(checkCompleted29Sep1500, action);
            assertThat(n).isNull();
        }
        {
            // start
            // late start (oct) still available
            Schedule s = Schedule.from("start:2020-10-02");
            LocalDateTime n = s.nextDateTimeAfter(checkCompleted29Sep1500, action);
            assertThat(n).isEqualTo(LocalDate.of(2020, 10, 2).atStartOfDay());
        }
        {
            // start + days
            // late start (oct) still available next day
            Schedule s = Schedule.from("start:2020-10-02 days:*");
            LocalDateTime n = s.nextDateTimeAfter(checkCompleted29Sep1500, action);
            assertThat(n).isEqualTo(LocalDate.of(2020, 10, 2).atStartOfDay());
        }
        {
            // start + days
            // due later (tomorrow) still available
            Schedule s = Schedule.from("start:2020-09-29 days:Wed");
            LocalDateTime n = s.nextDateTimeAfter(checkCompleted29Sep1500, action);
            assertThat(n).isEqualTo(LocalDateTime.of(2020, 9, 30, 0, 0));
        }
        {
            // start + days
            // due today, tomorrow still available
            Schedule s = Schedule.from("start:2020-09-29 days:*");
            LocalDateTime n = s.nextDateTimeAfter(checkCompleted29Sep1500, action);
            assertThat(n).isEqualTo(LocalDateTime.of(2020, 9, 30, 0, 0));
        }
        {
            // start + days
            // due today, tomorrow still available
            Schedule s = Schedule.from("start:2020-09-28 days:mon,tues,wed");
            LocalDateTime n = s.nextDateTimeAfter(checkCompleted29Sep1500.plusDays(1), action);
            assertThat(n).isEqualTo(LocalDateTime.of(2020, 10, 5, 0, 0));
        }
        {
            // start + times
            // due later (4pm is after now) still available
            Schedule s = Schedule.from("start:2020-09-29 times:16:00");
            LocalDateTime n = s.nextDateTimeAfter(checkCompleted29Sep1500, action);
            assertThat(n).isEqualTo(LocalDateTime.of(2020, 9, 29, 16, 0));
        }
        {
            // start + times
            // overdue (2pm is before 3pm), so next check null
            Schedule s = Schedule.from("start:2020-09-29 times:14:00");
            LocalDateTime n = s.nextDateTimeAfter(checkCompleted29Sep1500, action);
            assertThat(n).isNull();
        }
        {
            // start + times + days
            // overdue (2pm is before 3pm), so next check due tomorrow
            Schedule s = Schedule.from("start:2020-09-29 days:* times:14:00");
            LocalDateTime n = s.nextDateTimeAfter(checkCompleted29Sep1500, action);
            assertThat(n).isEqualTo(LocalDateTime.of(2020, 9, 30, 14, 0));
        }
        {
            // start + times + days
            // overdue (2pm is before 3pm), so next check due tomorrow
            Schedule s = Schedule.from("start:2020-09-29 days:Wed times:14:00");
            LocalDateTime n = s.nextDateTimeAfter(checkCompleted29Sep1500, action);
            assertThat(n).isEqualTo(LocalDateTime.of(2020, 9, 30, 14, 0));
        }
        {
            // start + times + days
            // due later (2pm is before 3pm but we have Sun,Mon so expect Sunday) still available
            Schedule s = Schedule.from("start:2020-09-29 days:Sun,Mon times:14:00");
            LocalDateTime n = s.nextDateTimeAfter(checkCompleted29Sep1500, action);
            assertThat(n).isEqualTo(LocalDateTime.of(2020, 10, 4, 14, 0));
        }
        {
            // start + times + days + end
            // as above, with end available
            Schedule s = Schedule.from("start:2020-09-29 days:Sun,Mon times:14:00 end:2020-10-04");
            LocalDateTime n = s.nextDateTimeAfter(checkCompleted29Sep1500, action);
            assertThat(n).isEqualTo(LocalDateTime.of(2020, 10, 4, 14, 0));
        }
        {
            // start + times + days + end
            // as above, with end not available
            Schedule s = Schedule.from("start:2020-09-29 days:Sun,Mon times:14:00 end:2020-10-03");
            LocalDateTime n = s.nextDateTimeAfter(checkCompleted29Sep1500, action);
            assertThat(n).isNull();
        }
        {
            // start + times + days + end = week 1
            // as above, with every week but ended
            Schedule s = Schedule.from("start:2020-09-29 days:Sun,Mon times:14:00 end:2020-10-03 week:1");
            LocalDateTime n = s.nextDateTimeAfter(checkCompleted29Sep1500, action);
            assertThat(n).isNull();
        }
        {
            // start + times + days + end
            // early start to test getting the next date from 'now'
            Schedule s = Schedule.from("start:2020-07-29 days:Sun,Mon times:14:00 end:2020-11-03");
            LocalDateTime n = s.nextDateTimeAfter(checkCompleted29Sep1500, action);
            assertThat(n).isEqualTo(LocalDateTime.of(2020, 10, 4, 14, 0));
        }
        {
            // start + days + week:2
            // 'every weeks 2' means wk1 (29/09 - 06/10), wk3 (13/10 - 20/10)
            Schedule s = Schedule.from("start:2020-09-29 days:Sun,Mon week:2");
            LocalDateTime firstSunNoTime = startDate29Sep.atStartOfDay().with(TemporalAdjusters.next(DayOfWeek.SUNDAY));
            // due later, still available - Sun 4th is in the first week after today
            LocalDateTime n = s.nextDateTimeAfter(checkCompleted29Sep1500, action);
            assertThat(n).isEqualTo(firstSunNoTime);
            // due later, still available - Mon 5th is still in the first week after today, the next check is Sun 11th
            LocalDateTime n2 = s.nextDateTimeAfter(checkCompleted29Sep1500.with(TemporalAdjusters.next(DayOfWeek.MONDAY)), action);
            assertThat(n2).isEqualTo(firstSunNoTime.plusDays(7));
            // due later, still available - Sun 18th is the first Sun after 6th
            LocalDateTime n3 = s.nextDateTimeAfter(checkCompleted29Sep1500.plusWeeks(1), action);
            assertThat(n3).isEqualTo(firstSunNoTime.plusWeeks(2));
        }
    }
}