package com.ecco.webApi.contacts;

import com.ecco.dao.ClientRepository;
import com.ecco.dao.commands.ClientCommandRepository;
import com.ecco.dom.ClientDetail;
import com.ecco.dom.clients.ClientCommand;
import com.ecco.repositories.incidents.IncidentCommandRepository;
import com.ecco.repositories.incidents.IncidentRepository;
import com.ecco.service.ReferralService;
import com.ecco.webApi.controllers.CreateClientCommandHandler;
import com.ecco.webApi.incidents.IncidentFromViewModel;
import com.ecco.webApi.users.UserBaseController;
import com.ecco.webApi.viewModels.Result;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.Instant;
import org.jspecify.annotations.NonNull;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static com.ecco.security.SecurityUtil.getAuthenticatedUserIdOrNull;

/**
 * Underlying delegate for doing client operations internally, away from the request based security
 */
@RequiredArgsConstructor
public class ClientWebService {

    private final ObjectMapper objectMapper;

    private final ClientRepository clientRepository;
    private final ClientCommandRepository clientCommandRepository;
    private final ReferralService referralService;
    private final ClientFromViewModel clientFromViewModel;

    private final IncidentRepository incidentRepository;
    private final IncidentFromViewModel incidentFromViewModel;
    private final IncidentCommandRepository incidentCommandRepository;

    @NonNull
    public Result checkExists(ClientViewModel clientViewModel) {

        // code is good to check unique
        if (StringUtils.isNotBlank(clientViewModel.code)) {
            List<ClientDetail> matchingClients = clientRepository.findAllByCode(clientViewModel.code);
            if (matchingClients.size() > 1) {
                return new Result(matchingClients.get(0).getId());
            }
        }

        // email is good to check unique
        // DISABLED without going live - email is a good choice but people put junk in here (eg inbound referral)
        // and duplicates will exist across clients
        /*if (StringUtils.isNotBlank(clientViewModel.email)) {
            List<ClientDetail> matchingClients = clientRepository.findAllByContact_Email(clientViewModel.email);
            if (matchingClients.size() > 1) {
                return new Result(matchingClients.get(0).getId());
            }
        }*/

        // ni is good to check unique
        // no spaces - see ClientDetailAbstract.setNi
        var trimmedNi = org.springframework.util.StringUtils.trimAllWhitespace(clientViewModel.ni);
        if (StringUtils.isNotEmpty(trimmedNi)) {
            Optional<ClientDetail> matchingClient = clientRepository.findFirstByNiIgnoreCaseOrderByCreatedDesc(trimmedNi);
            if (matchingClient.isPresent()) {
                return new Result(matchingClient.get().getId());
            }
        }

        return new Result("client not found");
    }

    /**
     * Create a client without an audit (but typically the audit is created on the referral).
     * Called by all places:
     *      - inbound referrals
     *      - ClientDetailNew (from inside ImportWizard)
     *      - ReferralAggregateHandler#syncClientToServer (enforceUniqueClient = true)
     * @param clientViewModel dto to create
     * @param enforceUniqueClient Matching client details should not exist
     * @return result containing the created id
     */
    @NonNull
    public Result create(ClientViewModel clientViewModel, boolean enforceUniqueClient) {
        var clientId = CreateClientCommandHandler.createClient(clientViewModel, enforceUniqueClient,
                this.clientFromViewModel, this.clientRepository, this.referralService);

        Long userIdOrNull = getAuthenticatedUserIdOrNull();
        createClientCommand(clientViewModel, clientId, userIdOrNull);

        return new Result(clientId);
    }

    @SneakyThrows
    private void createClientCommand(ClientViewModel viewModel, long clientId, Long userId) {
        var userIdOrExternal = userId != null ? userId : UserBaseController.EXTERNAL_USERID;
        var jsonBody = objectMapper.writeValueAsString(viewModel);
        var cmd = new ClientCommand(
                UUID.randomUUID(),
                Instant.now(),
                userIdOrExternal,
                jsonBody,
                clientId);
        clientCommandRepository.save(cmd);
    }

    // NB this is handled already in CreateIncidentCommandHandler - EXCEPT for an audit it inc_commands
    // which we could create (using below createIncidentCommand), but may not be a need
    /*
    @Nonnull
    public Result create(IncidentViewModel incidentViewModel) {
        var incidentId = CreateIncidentCommandHandler.createIncident(incidentViewModel,
                this.incidentFromViewModel, this.incidentRepository);

        Long userIdOrNull = getAuthenticatedUserIdOrNull();
        createIncidentCommand(incidentViewModel, incidentId, userIdOrNull);

        return new Result(incidentId);
    }

    @SneakyThrows
    private void createIncidentCommand(IncidentViewModel viewModel, int incidentId, Long userId) {
        var userIdOrExternal = userId != null ? userId : UserBaseController.EXTERNAL_USERID;
        var jsonBody = objectMapper.writeValueAsString(viewModel);
        var cmd = new IncidentCommand(
                UUID.randomUUID(),
                Instant.now(),
                userIdOrExternal,
                jsonBody,
                incidentId);
        incidentCommandRepository.save(cmd);
    }
    */

}