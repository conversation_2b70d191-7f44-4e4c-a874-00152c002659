package com.ecco.webApi.contacts;

import org.jspecify.annotations.Nullable;

import com.ecco.webApi.evidence.BaseCommandViewModel;
import com.ecco.dto.ChangeViewModel;
import lombok.NoArgsConstructor;
import org.springframework.web.util.UriComponentsBuilder;

@NoArgsConstructor
public class ContactCommandViewModel extends BaseCommandViewModel {

    public int contactId;

    public String operation;

    @Nullable
    public ChangeViewModel<String> companyName; // for Agency only

    @Nullable
    public ChangeViewModel<String> firstName;

    @Nullable
    public ChangeViewModel<String> lastName;

    @Nullable
    public ChangeViewModel<String> jobTitle;

    @Nullable
    public ChangeViewModel<String> title;

    @Nullable
    public ChangeViewModel<String> mobileNumber;

    @Nullable
    public ChangeViewModel<String> phoneNumber;

    @Nullable
    public ChangeViewModel<String> archived;

    public ContactCommandViewModel(int contactId) {
        super(UriComponentsBuilder
                        .fromUriString("/contacts/{contactId}/commands/")
                        .buildAndExpand(contactId)
                        .toString());
        this.contactId = contactId;
    }
}
