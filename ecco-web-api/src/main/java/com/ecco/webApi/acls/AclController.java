package com.ecco.webApi.acls;

import com.ecco.dom.Project;
import com.ecco.dom.Service;
import com.ecco.security.acl.dom.AclObjectIdentity;
import com.ecco.security.service.UserManagementService;
import com.ecco.serviceConfig.EntityRestrictionService;
import com.ecco.webApi.controllers.BaseWebApiController;
import com.ecco.webApi.serviceConfig.ServiceViewModel;
import com.ecco.webApi.serviceConfig.ServicesService;
import com.ecco.webApi.viewModels.Result;
import com.ecco.security.dao.acl.AclEntryRepository;
import com.ecco.security.dao.acl.AclObjectIdentityRepository;
import com.ecco.dom.BaseEntity;
import com.ecco.security.dto.AclEntryDto;
import com.ecco.security.dto.AclExtractor;
import com.ecco.security.acl.AclHandler;
import lombok.RequiredArgsConstructor;

import org.springframework.composed.web.rest.json.GetJson;
import org.springframework.composed.web.rest.json.PostJson;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.acls.domain.PrincipalSid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;

import static java.util.stream.Collectors.toList;

@PreAuthorize("hasRole('ROLE_ADMINLOGIN')")
@RestController
@RequiredArgsConstructor
public class AclController extends BaseWebApiController {

    private final boolean enableAcls;
    private final boolean enableAclsConfig;
    private final AclHandler aclHandler;
    private final AclEntryRepository entryRepository;
    private final EntityRestrictionService entityRestrictionService;
    private final AclObjectIdentityRepository objectIdentityRepository;
    private final UserManagementService userManagementService;
    private final ServicesService servicesSerivce;

    private final AclEntryDtoToViewModel aclEntryDtoToViewModel = new AclEntryDtoToViewModel();
    private final AclEntryDtoFromViewModel aclEntryDtoFromViewModel = new AclEntryDtoFromViewModel();


    /** Determine if acls are configured
     */
    @GetJson("/acls/enabled/")
    public Boolean isEnabled() {
        return this.enableAcls ? Boolean.TRUE : Boolean.FALSE;
    }

    @GetJson("/acls/enabledConfig/")
    public Boolean isEnabledConfig() {
        return this.enableAclsConfig ? Boolean.TRUE : Boolean.FALSE;
    }

    /**
     * Its possible that acls don't need to be created for all services and projects, but in reality this is what we want.
     * This can be run whenever services/projects change to ensure the acl table is up to date
     */
    @PostJson("/acls/autoAcls/")
    public Result ensureAcls() {
        entityRestrictionService.ensureAcls();
        return new Result("created");
    }

    /**
     * Create acl's for all users by default, if there are no permissions already
     */
    @PostJson("/acls/autoAclEntries/")
    public Result ensureAclEntries() {
        int count = entryRepository.count();

        if (count > 0) {
            return new Result(new IllegalArgumentException("cannot call this method with existing ACL data"));

        } else {
            entityRestrictionService.ensureAcls();

            List<ServiceViewModel> services = servicesSerivce.findAll().collect(toList());

            // we loop for each user to mimic the data-import, but it would probably be better to do by services,
            // because thats what updateAclEntrys does (gathering usernames to process together)
            // TODO fails for large numbers of users, as does data-import
            userManagementService.findUsersNotThirdParty().forEach(username -> {

                log.info("ACL autoAclEntries processing of: " + username);

                List<AclEntryViewModel> acls = aclHandler.getAclEntries()
                        .stream().map(aclEntryDtoToViewModel::apply).collect(toList());
                AclEntriesViewModel vm = new AclEntriesViewModel();
                vm.setAcls(acls);

                for (ServiceViewModel svc : services) {
                    if (svc.id > 0) {
                        AclEntryViewModel svcVm = new AclEntryViewModel(username, svc.id.longValue(), Service.class.getName(), 1);
                        vm.getAcls().add(svcVm);
                    }
                }
                AclEntryViewModel allProject = new AclEntryViewModel(username, -1L, Project.class.getName(), 1);
                vm.getAcls().add(allProject);

                updateAclEntrys(vm);
            });

            return new Result("created");
        }
    }

    @GetJson("/acls/entriesByUser/{username}/")
    public Set<AclEntryViewModel> findByUsername(@PathVariable String username) {
        Set<AclEntryDto> aclEntrys = aclHandler.getAclEntries(new PrincipalSid(username));
        Set<AclEntryViewModel> avms = new HashSet<>();
        for (AclEntryDto aclEntryDto : aclEntrys) {
            avms.add(aclEntryDtoToViewModel.apply(aclEntryDto));
        }
        return avms;
    }

    @GetJson("/acls/usersBySecuredObject/{className}/{objectId}/")
    public List<String> findUserBySecuredObject(@PathVariable String className, @PathVariable Long objectId) {
        AclObjectIdentity aclObjectIdentity = objectIdentityRepository.findByObjectIdClass_ClassNameAndObjectIdIdentity(className, objectId);

        return aclObjectIdentity.getAclEntries().stream().map(entry -> entry.getSid().getSid()).collect(toList());
    }

    @GetJson("/acls/entries/")
    public Set<AclEntryViewModel> findByObjects() {
        Set<AclEntryDto> aclEntrys = aclHandler.getAclEntries();
        Set<AclEntryViewModel> avms = new HashSet<>();
        for (AclEntryDto aclEntryDto : aclEntrys) {
            avms.add(aclEntryDtoToViewModel.apply(aclEntryDto));
        }
        return avms;
    }


    @PostJson("/acls/entries/")
    public Result updateAclEntrys(@RequestBody AclEntriesViewModel vm) {
        // asserts handled in the contructor of the AclEntryViewModel - which may cause errors to not propogate?

        Set<AclEntryDto> aes = new HashSet<>();
        for (AclEntryViewModel avm : vm.acls) {
            AclEntryDto ae = aclEntryDtoFromViewModel.apply(avm);
            aes.add(ae);
        }

        Collection<AclExtractor<? extends BaseEntity<Long>>> aclExtractors = aclHandler.aclExtractors();
        aclHandler.updateAclEntryDifferences(aclExtractors, aes, vm.usernamesToClear);

        return new Result("updated");
    }

    @PostJson("/acls/entries/add/")
    public Result addAclEntrys(@RequestBody AclEntriesViewModel vm) {

        Set<AclEntryDto> aes = new HashSet<>();
        for (AclEntryViewModel avm : vm.acls) {
            AclEntryDto ae = aclEntryDtoFromViewModel.apply(avm);
            aes.add(ae);
        }

        aclHandler.addAclEntries(aes);

        return new Result("updated");
    }

    @PostJson("/acls/entries/remove/")
    public Result removeAclEntries(@RequestBody AclEntriesViewModel vm) {

        Set<AclEntryDto> aes = new HashSet<>();
        for (AclEntryViewModel avm : vm.acls) {
            AclEntryDto ae = aclEntryDtoFromViewModel.apply(avm);
            aes.add(ae);
        }

        aclHandler.removeAclEntries(aes);

        return new Result("updated");
    }
}
