package com.ecco.webApi.acls;

import java.util.List;

public class AclEntriesViewModel {

    List<AclEntryViewModel> acls;
    List<String> usernamesToClear;

    public AclEntriesViewModel() {}

    public AclEntriesViewModel(List<AclEntryViewModel> acls, List<String> usernamesToClear) {
        this.acls = acls;
        this.usernamesToClear = usernamesToClear;
    }

    public List<AclEntryViewModel> getAcls() {
        return acls;
    }
    public void setAcls(List<AclEntryViewModel> acls) {
        this.acls = acls;
    }
    public List<String> getUsernamesToClear() {
        return usernamesToClear;
    }
    public void setUsernamesToClear(List<String> usernamesToClear) {
        this.usernamesToClear = usernamesToClear;
    }

}
