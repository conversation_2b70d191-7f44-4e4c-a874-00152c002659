package com.ecco.webApi.clients;

import com.ecco.dao.commands.ServiceRecipientCommandRepository;
import com.ecco.dom.commands.DeleteRequestServiceRecipientCommand;
import com.ecco.service.ReferralService;
import com.ecco.webApi.CommandResult;
import com.ecco.webApi.evidence.ServiceRecipientCommandHandler;
import com.ecco.webApi.viewModels.DeleteRequestServiceRecipientCommandViewModel;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.jspecify.annotations.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.io.Serializable;


@Component
public class DeleteRequestServiceRecipientCommandHandler extends ServiceRecipientCommandHandler<
        DeleteRequestServiceRecipientCommandViewModel, DeleteRequestServiceRecipientCommand, @NonNull DeleteServiceRecipientParams> {

    private final ReferralService referralService;

    @Autowired
    public DeleteRequestServiceRecipientCommandHandler(ObjectMapper objectMapper,
                                                       ServiceRecipientCommandRepository serviceRecipientCommandRepository,
                                                       ReferralService referralService) {
        super(objectMapper, serviceRecipientCommandRepository, DeleteRequestServiceRecipientCommandViewModel.class);
        this.referralService = referralService;
    }

    @Override
    protected CommandResult handleInternal(@NonNull Authentication auth, @NonNull DeleteServiceRecipientParams params,
                                           @NonNull DeleteRequestServiceRecipientCommandViewModel viewModel) {
        // do nothing...
        // either we just record the fact we have a delete-request command
        // or we record we want to revoke, and toggle as much as we want until the actual deletion
        // so the verification goes in the deletion command

        // however, we maintain the previous solution of 'hidden' until none are left and we can
        // then replace the 'find hidden referrals' with 'find request deletions'
        if (viewModel.revoke) {
            referralService.unhideReferralByServiceRecipientId(params.serviceRecipientId);
        } else {
            referralService.hideReferralByServiceRecipientId(params.serviceRecipientId);
        }
        return null;
    }

    @NonNull
    @Override
    protected DeleteRequestServiceRecipientCommand createCommand(Serializable targetId, @NonNull DeleteServiceRecipientParams params,
                                                                 @NonNull String requestBody,
                                                                 @NonNull DeleteRequestServiceRecipientCommandViewModel viewModel,
                                                                 long userId) {
        Assert.state(params.serviceRecipientId == viewModel.serviceRecipientId, "serviceRecipientId in body must match URI");

        return new DeleteRequestServiceRecipientCommand(viewModel.uuid, viewModel.timestamp, userId, requestBody,
                params.serviceRecipientId);
    }

}
