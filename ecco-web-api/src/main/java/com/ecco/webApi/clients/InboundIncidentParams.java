package com.ecco.webApi.clients;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.util.UUID;

@Data
@NoArgsConstructor
public class InboundIncidentParams {

    public UUID apiKey;

    @Nonnull
    public InboundIncidentResource dto;

    @Nullable
    protected UUID formDefinitionUuid;

    @Nullable
    protected JsonNode formData;

}
