package com.ecco.webApi.serviceConfig;

import com.ecco.dom.Service;
import com.ecco.infrastructure.entity.AbstractLongKeyedEntity;
import com.ecco.serviceConfig.dom.*;
import com.ecco.serviceConfig.repositories.QuestionGroupRepository;
import com.ecco.serviceConfig.repositories.QuestionRepository;
import com.ecco.serviceConfig.repositories.ServiceRepository;
import com.ecco.serviceConfig.service.RepositoryBasedServiceTypeService;
import com.ecco.serviceConfig.viewModel.QuestionGroupToViewModel;
import com.ecco.serviceConfig.viewModel.QuestionGroupViewModel;
import com.ecco.serviceConfig.viewModel.QuestionToViewModel;
import com.ecco.serviceConfig.viewModel.QuestionViewModel;
import com.ecco.webApi.controllers.BaseWebApiController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.StreamSupport;

import static java.util.stream.Collectors.toList;
import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.web.bind.annotation.RequestMethod.GET;

/**
 * Allows JSON export/import of collections.  Any id's in the JSON are used to to indicate entries that are the
 * same in a many-to-many relationship (no checking of values is done).  We set these ids to collectionId and allow
 * Hibernate to assign PKs as normal.
 */
@RestController
public class QuestionGroupController extends BaseWebApiController {

    private final QuestionGroupRepository questionGroupRepository;
    private final QuestionRepository questionRepository;
    private final ServiceRepository serviceRepository;
    private final RepositoryBasedServiceTypeService serviceTypeService;

    private final QuestionGroupToViewModel questionGroupToViewModel = new QuestionGroupToViewModel();
    private final QuestionGroupFromViewModel questionGroupFromViewModel = new QuestionGroupFromViewModel();
    private final QuestionToViewModel questionToViewModel = new QuestionToViewModel();


    @Autowired
    public QuestionGroupController(QuestionGroupRepository questionGroupRepository,
            QuestionRepository questionRepository,
            ServiceRepository serviceRepository, RepositoryBasedServiceTypeService serviceTypeService) {
        this.questionGroupRepository = questionGroupRepository;
        this.questionRepository = questionRepository;
        this.serviceRepository = serviceRepository;
        this.serviceTypeService = serviceTypeService;
    }

    @RequestMapping(value = "/questions/", method = GET, produces = APPLICATION_JSON_VALUE)
    public Iterable<QuestionViewModel> findAllQuestions() {
        Iterable<Question> questions = questionRepository.findAll();
        return StreamSupport.stream(questions.spliterator(), false).map(questionToViewModel).collect(toList());
    }

    @RequestMapping(value = "/questions/{id}", method = GET, produces = APPLICATION_JSON_VALUE)
    public QuestionViewModel findOneQuestion(@PathVariable long id) {
        return questionToViewModel.apply(questionRepository.findById(id).orElse(null));
    }

    @RequestMapping(value = "/questionGroups/{id}", method = GET, produces = APPLICATION_JSON_VALUE)
    public QuestionGroupViewModel findOne(@PathVariable long id) {
        return questionGroupToViewModel.apply(questionGroupRepository.findById(id).orElse(null));
    }

    @RequestMapping(value = "/questionGroups/byName/{name}/", method = GET, produces = APPLICATION_JSON_VALUE)
    public QuestionGroupViewModel findOne(@PathVariable String name) {
        return questionGroupToViewModel.apply(questionGroupRepository.findOneByName(name));
    }

    @RequestMapping(value = "/questionGroups/", method = GET, produces = APPLICATION_JSON_VALUE)
    public Iterable<QuestionGroupViewModel> findAll() {
        return questionGroupRepository.findAll().stream().map(questionGroupToViewModel).collect(toList());
    }

    @RequestMapping(value = "/questionGroups/byServiceId/{serviceId}", method = GET, produces = APPLICATION_JSON_VALUE)
    public Iterable<QuestionGroupViewModel> findAllByService(@PathVariable long serviceId) {
        Service s = serviceRepository.findById(serviceId).orElseThrow(NullPointerException::new);
        ServiceType st = serviceTypeService.findOne(s.getServiceTypeId());
        Set<QuestionGroupSupport> questionGroupsByService = st.getQuestionGroupsSupport();
        return questionGroupsByService.stream().map(questionGroupToViewModel).collect(toList());
    }

    @RequestMapping(value = "/questionGroups/byServiceName/{serviceName}", method = GET, produces = APPLICATION_JSON_VALUE)
    public Iterable<QuestionGroupViewModel> findAllByService(@PathVariable String serviceName) {
        Service s = serviceRepository.findOneByName(serviceName);
        ServiceType st = serviceTypeService.findOne(s.getServiceTypeId());
        Set<QuestionGroupSupport> questionGroupsByService = st.getQuestionGroupsSupport();
        return questionGroupsByService.stream().map(questionGroupToViewModel).collect(toList());
    }

    @RequestMapping(value = "/questionGroups/", method = RequestMethod.POST, consumes = APPLICATION_JSON_VALUE)
    @ResponseStatus(HttpStatus.CREATED)
    public Map<String, Long> create(@RequestBody QuestionGroupViewModel questionGroupViewModel) {

        log.debug("create: entered");

        final QuestionGroupSupport questionGroup = questionGroupFromViewModel.apply(questionGroupViewModel);

        log.debug("create: questionGroup translated from view model");

        // NOTE: Any collections need converting to java.lang versions, not Guava transform() results
        Comparator<AbstractLongKeyedEntity> comparator = new ClassnameCollectionIdOrderingComparator();
        TreeMap<AbstractLongKeyedEntity, AbstractLongKeyedEntity> deduplication = new TreeMap<>(comparator);

        // merge done before persist modified anything in deduplication map
        var qns = questionGroup.getQuestions().stream().map(QuestionGroupQuestion::getQuestion).collect(toList());
        for (Question question : qns) {
            mergeWhereNecessary(question, deduplication);
        }
        log.debug("create: deduplication complete, test attribute 'headerText': " + questionGroup.getHeaderText());

        questionRepository.saveAll(qns);
        questionGroupRepository.save(questionGroup);

        log.debug("create: save complete, id: " + questionGroup.getId());

        return Collections.singletonMap("id", questionGroup.getId());
    }

    private void mergeWhereNecessary(Question question, TreeMap<AbstractLongKeyedEntity, AbstractLongKeyedEntity> deduplication) {
        HashSet<QuestionAnswerChoice> newQACs = new HashSet<QuestionAnswerChoice>();
        for (QuestionAnswerChoice questionAnswerChoice : question.getChoices()) {
            QuestionAnswerChoice existing = (QuestionAnswerChoice) deduplication.get(questionAnswerChoice);
            if (existing != null) {
                newQACs.add(existing);
            }
            else {
                newQACs.add(questionAnswerChoice);
                deduplication.put(questionAnswerChoice, questionAnswerChoice);
            }
        }
        question.setChoices(newQACs);

        HashSet<QuestionAnswerFree> newQAFs = new HashSet<QuestionAnswerFree>();
        for (QuestionAnswerFree questionAnswerFree : question.getFreeTypes()) {
            QuestionAnswerFree existing = (QuestionAnswerFree) deduplication.get(questionAnswerFree);
            if (existing != null) {
                newQAFs.add(existing);
            }
            else {
                newQAFs.add(questionAnswerFree);
                deduplication.put(questionAnswerFree, questionAnswerFree);
            }
        }
        question.setFreeTypes(newQAFs);
    }


    @RequestMapping(value = "/questionGroups/{id}", method = RequestMethod.DELETE)
    @ResponseStatus(HttpStatus.OK)
    public Map<String, Long> delete(@PathVariable long id) {
        questionGroupRepository.deleteById(id);
        return Collections.singletonMap("id", id);
    }
}
