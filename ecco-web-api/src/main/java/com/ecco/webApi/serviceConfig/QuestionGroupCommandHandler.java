package com.ecco.webApi.serviceConfig;

import com.ecco.infrastructure.dom.ConfigCommand;
import com.ecco.serviceConfig.dom.QuestionGroup;
import com.ecco.serviceConfig.dom.QuestionGroupSupport;
import com.ecco.serviceConfig.dom.QuestionnaireCommand;
import com.ecco.serviceConfig.repositories.ConfigCommandRepository;
import com.ecco.serviceConfig.repositories.QuestionGroupRepository;
import com.ecco.webApi.CommandResult;
import com.ecco.webApi.evidence.BaseCommandHandler;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import java.io.Serializable;

@Component
public class QuestionGroupCommandHandler extends BaseCommandHandler<QuestionGroupCommandViewModel, Long,
            ConfigCommand, @Nullable Void> {

    @NonNull
    private final QuestionGroupRepository questionGroupRepository;

    @Autowired
    public QuestionGroupCommandHandler(ObjectMapper objectMapper, ConfigCommandRepository configCommandRepository,
                                       QuestionGroupRepository questionGroupRepository) {
        super(objectMapper, configCommandRepository, QuestionGroupCommandViewModel.class);
        this.questionGroupRepository = questionGroupRepository;
    }

    @Override
    protected CommandResult handleInternal(@NonNull Authentication auth, @Nullable Void params, @NonNull QuestionGroupCommandViewModel viewModel) {

        switch (viewModel.operation) {
            case QuestionnaireBaseCommandViewModel.OPERATION_ADD:
                this.addQuestion(auth, viewModel);
                break;

            case QuestionnaireBaseCommandViewModel.OPERATION_UPDATE:
                this.updateQuestion(auth, viewModel);
                break;

            case QuestionnaireBaseCommandViewModel.OPERATION_REMOVE:
                this.removeQuestion(auth, viewModel);
                break;

            default:
                throw new IllegalArgumentException("invalid operation: " + viewModel.operation);
        }
        return null;
    }

    private void addQuestion(Authentication auth, QuestionGroupCommandViewModel cmdVM) {
        QuestionGroup questionGroup = new QuestionGroupSupport();
        this.applyChanges(questionGroup, cmdVM);
        this.questionGroupRepository.save(questionGroup);
    }

    private void updateQuestion(Authentication auth,  QuestionGroupCommandViewModel cmdVM) {
        QuestionGroup questionGroup = this.questionGroupRepository.findById(cmdVM.id.longValue()).orElse(null);
        this.applyChanges(questionGroup, cmdVM);
        this.questionGroupRepository.save(questionGroup);
    }

    private void removeQuestion(Authentication auth, QuestionGroupCommandViewModel cmdVM) {
        long questionGroupId = cmdVM.id.longValue();
        QuestionGroup questionGroup = this.questionGroupRepository.findById(questionGroupId).orElse(null);
        this.questionGroupRepository.delete(questionGroup);
    }

    private void applyChanges(QuestionGroup questionGroup, QuestionGroupCommandViewModel cmdVM) {
        if (cmdVM.hasChanges()) {
            if (cmdVM.nameChange != null) {
                warnIfPrevValueDoesntMatch(cmdVM, cmdVM.nameChange, questionGroup.getName(), "name");
                questionGroup.setName(cmdVM.nameChange.to);
            }
        }
    }

    @NonNull
    @Override
    protected ConfigCommand createCommand(Serializable targetId, @Nullable Void params, @NonNull String requestBody,
                                          @NonNull QuestionGroupCommandViewModel viewModel, long userId) {
        return new QuestionnaireCommand(viewModel.uuid, viewModel.timestamp, userId, requestBody);
    }

}
