package com.ecco.webApi.serviceConfig;

import static java.util.stream.Collectors.toSet;

import java.util.function.Function;

import org.jspecify.annotations.Nullable;

import com.ecco.serviceConfig.dom.Question;
import com.ecco.serviceConfig.viewModel.QuestionViewModel;

public class QuestionFromViewModel implements Function<QuestionViewModel, Question> {

    private static final QuestionChoiceFromViewModel questionChoicesFromViewModel = new QuestionChoiceFromViewModel();

    private static final QuestionAnswerFreeFromViewModel freeTypesFromViewModel = new QuestionAnswerFreeFromViewModel();

    @Override
    @Nullable
    public Question apply(@Nullable QuestionViewModel input) {
        if (input == null) {
            throw new NullPointerException("input Question must not be null");
        }

        Question question = new Question();
        question.setName(input.name);
        question.setAnswerType(input.answerType);

        question.setChoices(input.choices.stream().map(questionChoicesFromViewModel).collect(toSet()));
        question.setFreeTypes(input.freeTypes.stream().map(freeTypesFromViewModel).collect(toSet()));
        return question;
    }

}
