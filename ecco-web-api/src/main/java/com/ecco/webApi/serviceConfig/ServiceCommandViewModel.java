package com.ecco.webApi.serviceConfig;

import java.util.HashSet;

import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;

import org.springframework.web.util.UriComponentsBuilder;

import com.ecco.webApi.evidence.BaseCommandViewModel;
import com.ecco.dto.ChangeViewModel;

public class ServiceCommandViewModel extends BaseCommandViewModel {

    @Nullable
    public Long serviceId;

    @Nullable
    public Long serviceTypeId;

    @NonNull
    public String operation;

    @Nullable
    public ChangeViewModel<String> name;

    @Nullable
    public HashSet<Long> projectsToAdd;

    @Nullable
    public HashSet<Long> projectsToRemove;

    ServiceCommandViewModel() {
        super();
    }

    /**
     * @param operation "add" or "update"
     * @param serviceId null when doing add, required when doing update.
     * @param serviceTypeId required when doing add, null when doing update.
     */
    public ServiceCommandViewModel(String operation, Long serviceId, Long serviceTypeId) {
        super(UriComponentsBuilder
                .fromUriString("config/service/")
                .buildAndExpand(serviceId).toUriString());
        this.operation = operation;
        this.serviceId = serviceId;
        this.serviceTypeId = serviceTypeId;
    }

    public ServiceCommandViewModel changeName(String from, String to) {
        this.name = ChangeViewModel.create(from, to);
        return this;
    }

}
