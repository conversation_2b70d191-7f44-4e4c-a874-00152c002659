package com.ecco.webApi.serviceConfig;

import com.ecco.serviceConfig.viewModel.RiskAreaViewModel;
import com.ecco.webApi.evidence.BaseCommandViewModel;
import org.springframework.web.util.UriComponentsBuilder;

import org.jspecify.annotations.NonNull;

/**
 * Base command for configuration operations on questionnaires
 */
public class RiskAreaCloneCommandViewModel extends BaseCommandViewModel {

    private RiskAreaViewModel riskAreaViewModel;

    // we don't have a remove or update option, so for now, its always changed
    public boolean hasChanges() {
        return true;
    }

    /** only for Cglib/Hibernate etc */
    @Deprecated
    protected RiskAreaCloneCommandViewModel() {
        super();
    }

    public RiskAreaCloneCommandViewModel(@NonNull RiskAreaViewModel riskAreaViewModel) {
        super(UriComponentsBuilder
                .fromUriString("config/riskArea/clone/")
                .toUriString());
        this.riskAreaViewModel = riskAreaViewModel;
    }

    public RiskAreaViewModel getRiskAreaViewModel() {
        return riskAreaViewModel;
    }

}
