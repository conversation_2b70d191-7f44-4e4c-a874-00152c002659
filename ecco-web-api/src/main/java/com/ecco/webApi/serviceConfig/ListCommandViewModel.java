package com.ecco.webApi.serviceConfig;

import com.ecco.webApi.evidence.BaseCommandViewModel;
import com.ecco.dto.ChangeViewModel;

import java.util.HashSet;
import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;

public class ListCommandViewModel extends BaseCommandViewModel {

    @NonNull
    public static final String OPERATION_ADD = "add";
    @NonNull
    public static final String OPERATION_UPDATE = "update";
    @NonNull
    public static final String OPERATION_REMOVE = "remove";

    @NonNull
    public String operation;
    @Nullable public Integer id;
    @Nullable public ChangeViewModel<String> nameChange;
    @Nullable
    public ChangeViewModel<Long> serviceIdChange;
    @Nullable
    public HashSet<Long> servicesToAdd;
    @Nullable
    public HashSet<Long> servicesToRemove;


    public static ListCommandViewModel addActivityType(@NonNull String name) {
        return new ListCommandViewModel("config/activityType/", OPERATION_ADD, null)
                .changeName(null, name);
    }

    public static BaseCommandViewModel updateWithService(Long activityTypeId, Long serviceId) {
        return new ListCommandViewModel("config/activityType/", OPERATION_UPDATE, activityTypeId.intValue())
                .addService(serviceId);
    }

    public static ListCommandViewModel addProject(@NonNull String name, Long serviceId) {
        return new ListCommandViewModel("config/project/", OPERATION_ADD, null)
                .changeName(null, name)
                .changeServiceId(null, serviceId);
    }

    /** only for Cglib/Hibernate etc */
    @Deprecated
    protected ListCommandViewModel() {
        super();
    }

    public ListCommandViewModel(@NonNull String commandUri, @NonNull String operation, Integer id) {
        super(commandUri);
        this.operation = operation;
        this.id = id;
    }

    private ListCommandViewModel changeName(String from, String to) {
        nameChange = ChangeViewModel.create(from, to);
        return this;
    }

    private ListCommandViewModel changeServiceId(Long from, Long to) {
        serviceIdChange = ChangeViewModel.create(from, to);
        return this;
    }

    public BaseCommandViewModel addService(Long serviceId) {
        if (servicesToAdd == null) {
            servicesToAdd = new HashSet<Long>();
        }
        servicesToAdd.add(serviceId);
        return this;
    }

    public BaseCommandViewModel removeService(Long serviceId) {
        if (servicesToRemove == null) {
            servicesToRemove = new HashSet<Long>();
        }
        servicesToRemove.add(serviceId);
        return this;
    }

    @Override
    public String toString() {
        return "ListCommandViewModel [operation=" + operation + ", id=" + id + ", nameChange=" + nameChange
                + ", serviceIdChange=" + serviceIdChange + "]";
    }


}
