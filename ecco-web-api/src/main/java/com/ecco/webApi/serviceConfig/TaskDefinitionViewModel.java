package com.ecco.webApi.serviceConfig;

import com.fasterxml.jackson.annotation.JsonRawValue;
import com.fasterxml.jackson.databind.JsonNode;
import org.jspecify.annotations.Nullable;

public class TaskDefinitionViewModel {

    /**
     * id of the task
     */
    public long id;

    public String type;

    /**
     * unique name of the task
     */
    public String name;

    /** A description of what the task does */
    public String description;

    /**
     * Whether this task gets displayed on screen - it might be a processing task only
     */
    public boolean display;

    /**
     * Whether this task shows on the referralOverview screen - it might just be a wizard item
     */
    public boolean displayOverview;

    public String metadata;

    @Nullable
    @JsonRawValue
//    @JsonInclude(value=Include.NON_NULL) // doesn't work we get "metadata": , instead of it being omitted;
    public String getMetadata() {
        return metadata;
    }

    /** see http://stackoverflow.com/a/11452577 */
    public void setMetadata(JsonNode json) {
        this.metadata = json.toString();
    }


}
