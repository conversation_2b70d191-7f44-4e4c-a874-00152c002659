package com.ecco.webApi.serviceConfig;

import static java.util.stream.Collectors.toList;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import org.jspecify.annotations.NonNull;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

import com.ecco.serviceConfig.dom.ServiceType_TaskDefinitionSetting;
import com.ecco.webApi.CommandResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import com.ecco.infrastructure.dom.ConfigCommand;
import com.ecco.serviceConfig.dom.ServiceType_TaskDefinition;
import com.ecco.serviceConfig.dom.ServiceType_TaskDefinition_MultiId;
import com.ecco.serviceConfig.dom.TaskDefinitionEntryCommand;
import com.ecco.serviceConfig.repositories.ConfigCommandRepository;
import com.ecco.serviceConfig.repositories.TaskDefinitionRepository;
import com.ecco.serviceConfig.repositories.TaskDefinitionEntryRepository;
import com.ecco.serviceConfig.service.ServiceTypeService;
import com.ecco.webApi.evidence.BaseCommandHandler;
import com.fasterxml.jackson.databind.ObjectMapper;

@Component
public class TaskDefinitionEntrySettingCommandHandler extends BaseCommandHandler<TaskDefinitionEntrySettingCommandViewModel, Long,
            ConfigCommand, @NonNull TaskDefinitionEntrySettingParams> {

    @PersistenceContext
    private EntityManager entityManager;

    @NonNull
    private final TaskDefinitionEntryRepository taskDefinitionEntryRepository;
    @NonNull
    private final TaskDefinitionRepository taskDefinitionRepository;
    @NonNull
    private final ServiceTypeService serviceTypeService;
    private final List<TaskDefinitionEntrySettingTransientHandler> taskDefTransientHandlers = new ArrayList<>();
    private final List<TaskDefinitionEntrySettingSpecificHandler> taskDefSpecificHandlers = new ArrayList<>();


    @Autowired
    public TaskDefinitionEntrySettingCommandHandler(ObjectMapper objectMapper,
                                                    @NonNull TaskDefinitionEntryRepository taskDefinitionEntryRepository,
                                                    ConfigCommandRepository configCommandRepository,
                                                    @NonNull TaskDefinitionRepository taskDefinitionRepository,
                                                    @NonNull ServiceTypeService serviceTypeService,
                                                    TaskDefinitionEntrySettingTransientOutcomesByUuidHandler outcomesByUuidHandler,
                                                    TaskDefinitionEntrySettingSpecificOutcomesHandler outcomesHandler,
                                                    TaskDefinitionEntrySettingSpecificOutcomesByIdHandler outcomesByIdHandler,
                                                    TaskDefinitionEntrySettingSpecificQuestionGroupsHandler questionGroupsHandler,
                                                    TaskDefinitionEntrySettingSpecificQuestionGroupsByIdHandler questionGroupsByIdHandler) {
        super(objectMapper, configCommandRepository, TaskDefinitionEntrySettingCommandViewModel.class);
        this.taskDefinitionEntryRepository = taskDefinitionEntryRepository;
        this.taskDefinitionRepository = taskDefinitionRepository;
        this.serviceTypeService = serviceTypeService;

        taskDefTransientHandlers.add(outcomesByUuidHandler);

        taskDefSpecificHandlers.add(outcomesHandler);
        taskDefSpecificHandlers.add(outcomesByIdHandler);
        taskDefSpecificHandlers.add(questionGroupsHandler);
        taskDefSpecificHandlers.add(questionGroupsByIdHandler);
    }

    @Override
    protected CommandResult handleInternal(@NonNull Authentication auth, @NonNull TaskDefinitionEntrySettingParams params, @NonNull TaskDefinitionEntrySettingCommandViewModel viewModel) {

        for (TaskDefinitionEntrySettingTransientHandler transientHandler : taskDefTransientHandlers) {
            if (transientHandler.canHandle(params)) {
                params = transientHandler.newParams(params);
                viewModel = transientHandler.newViewModel(viewModel);
            }
        }

        if (!viewModel.hasChanges()) {
            return null;
        }

        this.handleTaskSetting(auth, params, viewModel);
        return null;
    }

    protected void handleTaskSetting(Authentication auth, @NonNull TaskDefinitionEntrySettingParams params, TaskDefinitionEntrySettingCommandViewModel viewModel) {
        if (!viewModel.hasChanges()) {
            return;
        }

        long taskDefinitionId = taskDefinitionRepository.findOneByNameIgnoreCase(params.taskName).getId();
        ServiceType_TaskDefinition_MultiId multiId = ServiceType_TaskDefinition_MultiId.create(entityManager, params.serviceTypeId, taskDefinitionId);
        ServiceType_TaskDefinition serviceTypeTaskDefinitionEntry = taskDefinitionEntryRepository.findById(multiId).orElseThrow(NullPointerException::new);
        List<ServiceType_TaskDefinitionSetting> filtered = serviceTypeTaskDefinitionEntry.getSettings().stream()
                .filter(input -> params.settingName.equals(input.getName())).collect(toList());
        Assert.state(filtered.size() < 2,
                "This task entry setting name exists more than once (causing unpredictability): " + params.settingName +
                " for servicetypeId " + params.serviceTypeId + " and taskDefinitionId " + taskDefinitionId);

        /* querydsl alternative - but in the end the saving needed to be on ServiceType_TaskDefinition (detached entity warnings otherwise)
        QServiceType_TaskDefinitionSetting settingQ = QServiceType_TaskDefinitionSetting.serviceType_TaskDefinitionSetting;
        ServiceType_TaskDefinition_MultiId multiId = ServiceType_TaskDefinition.create(params.serviceTypeId, taskDefinitionId).getMultiId();
        BooleanExpression isMultiId = settingQ.serviceType_TaskDefinition.multiId.eq(multiId);
        BooleanExpression settingName = settingQ.name.eq(params.settingName);
        Iterable<ServiceType_TaskDefinitionSetting> settings = taskDefinitionEntrySettingRepository.findAll(isMultiId.and(settingName));
        */

        if (filtered.size() == 1) {
            ServiceType_TaskDefinitionSetting setting = filtered.get(0);
            applyChanges(setting, viewModel);
        } else {
            ServiceType_TaskDefinitionSetting setting = new ServiceType_TaskDefinitionSetting();
            setting.setName(params.settingName);
            setting.setServiceType_TaskDefinition(serviceTypeTaskDefinitionEntry);
            applyChanges(setting, viewModel);
            serviceTypeTaskDefinitionEntry.addSetting(setting);
        }
        taskDefinitionEntryRepository.save(serviceTypeTaskDefinitionEntry);
        serviceTypeService.evictOneDtoInCache(Math.toIntExact(viewModel.serviceTypeId));
    }

    private void applyChanges(ServiceType_TaskDefinitionSetting taskDefinitionEntrySetting, TaskDefinitionEntrySettingCommandViewModel viewModel) {
        warnIfPrevValueDoesntMatch(viewModel, viewModel.valueChange, taskDefinitionEntrySetting.getValue(), "value");

        // we apply the special handlers before updating the setting as this the setting is the definitive 'from' source
        // and the view model provides the 'to'
        for (TaskDefinitionEntrySettingSpecificHandler taskDefinitionEntrySettingSpecificHandler : taskDefSpecificHandlers) {
            if (taskDefinitionEntrySettingSpecificHandler.canHandle(taskDefinitionEntrySetting)) {
                taskDefinitionEntrySettingSpecificHandler.applyChanges(taskDefinitionEntrySetting, viewModel);
            }
        }

        taskDefinitionEntrySetting.setValue(viewModel.valueChange.to);
    }

    @NonNull
    @Override
    protected ConfigCommand createCommand(Serializable targetId, @NonNull TaskDefinitionEntrySettingParams params, @NonNull String requestBody,
                                          @NonNull TaskDefinitionEntrySettingCommandViewModel viewModel, long userId) {
        Assert.state(params.serviceTypeId == viewModel.serviceTypeId, "serviceTypeId in body must match URI");
        Assert.state(params.taskName.equals(viewModel.taskName), "taskName in body must match URI");
        Assert.state(params.settingName.equals(viewModel.settingName), "settingName in body must match URI");

        return new TaskDefinitionEntryCommand(viewModel.uuid, viewModel.timestamp, userId, requestBody);
    }

}
