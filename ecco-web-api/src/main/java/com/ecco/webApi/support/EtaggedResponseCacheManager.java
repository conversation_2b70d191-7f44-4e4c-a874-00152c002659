package com.ecco.webApi.support;

import com.aayushatharva.brotli4j.Brotli4jLoader;
import com.aayushatharva.brotli4j.encoder.BrotliOutputStream;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.http.MediaType;
import org.springframework.web.context.request.WebRequest;

import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Objects;
import java.util.concurrent.Callable;
import java.util.function.Supplier;
import java.util.zip.GZIPOutputStream;

import static com.ecco.infrastructure.web.RequestUtils.cacheForXSecs;


@RequiredArgsConstructor
public class EtaggedResponseCacheManager {
    private enum Encoder {
        BR, GZIP, NONE
    }
    private static final boolean brotliAvailable;
    static {
         brotliAvailable = Brotli4jLoader.isAvailable();
    }

    private final ObjectMapper objectMapper;
    private final CacheManager cacheManager;

    public <T> void getFromCacheWithEtagHandling(
            @Nullable WebRequest request,
            @Nullable HttpServletResponse response,
            String cacheName,
            String key,
            int httpCacheSeconds,
            Supplier<T> viewModelSupplier
    ) throws IOException {

        // With gzip locally for UAT we get 133kB for config/global against a resource size of 1.4Mb
        // FIXME: Default to br in cache and then if not br header, then decompress it to send it
        EtaggedBytes etaggedResult = null;

        var encodeWith = Encoder.BR;

        if (request != null) {
            String acceptEncoding = request.getHeader("Accept-Encoding");

            if (acceptEncoding != null) {
                var allowsBr = EtaggedResponseCacheManager.brotliAvailable && acceptEncoding.contains("br");
                var allowsGzip = acceptEncoding.contains("gzip");
                encodeWith = allowsBr ? Encoder.BR : allowsGzip ? Encoder.GZIP : Encoder.NONE;
                if (encodeWith == Encoder.NONE) {
                    etaggedResult = getCachedResult(cacheName, key, viewModelSupplier);
                }
            }
        }

        if (etaggedResult == null) {
            etaggedResult = getGzippedCachedResult(cacheName, key + "-" + encodeWith.name().toLowerCase(), encodeWith, viewModelSupplier);
        }

        // Allows method to be called with null request & response to just freshen the cache
        if (request == null || response == null || request.checkNotModified(etaggedResult.getEtag())) {
            return;
        }
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        var encoding = etaggedResult.getEncoding();
        if (encoding != null) {
            response.setHeader("Content-Encoding", encoding);
        }
        cacheForXSecs(httpCacheSeconds, response);
        response.getOutputStream().write(etaggedResult.getPayload()); // Note: flushes headers, so Spring can't add more (I think)
    }

    @NonNull
    private <T> EtaggedBytes getGzippedCachedResult(String cacheName, String key, Encoder encodeWith, Supplier<T> viewModelSupplier) {
        Callable<EtaggedBytes> cachedValueSupplier = () -> {
            try (var bytes = new ByteArrayOutputStream();
                 var zipper = encodeWith == Encoder.GZIP
                         ? new GZIPOutputStream(bytes)
                         : new BrotliOutputStream(bytes);
            ) {
                objectMapper.writeValue(zipper, viewModelSupplier.get());
                return new EtaggedBytes(bytes.toByteArray(), encodeWith.name().toLowerCase());
            }
        };

        Cache cache = cacheManager.getCache(cacheName);
        assert cache != null;
        return Objects.requireNonNull(cache.get(key, cachedValueSupplier));
    }

    @NonNull
    private <T> EtaggedBytes getCachedResult(String cacheName, String key, Supplier<T> viewModelSupplier) {
        Callable<EtaggedBytes> cachedValueSupplier = () -> {
            var json = objectMapper.writeValueAsBytes(viewModelSupplier.get());
            return new EtaggedBytes(json, null);
        };

        Cache cache = cacheManager.getCache(cacheName);
        assert cache != null;
        return Objects.requireNonNull(cache.get(key, cachedValueSupplier));
    }

    /** Allows the same viewModelSupplier to be used in methods where we conditionally cache some requests to the calling handler */
    public <T> void writeWithoutCache(
            HttpServletResponse response,
            Supplier<T> viewModelSupplier
    ) throws IOException {
        var json = objectMapper.writeValueAsBytes(viewModelSupplier.get());
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.getOutputStream().write(json);
    }
}