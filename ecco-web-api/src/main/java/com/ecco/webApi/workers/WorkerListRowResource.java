package com.ecco.webApi.workers;

import com.ecco.infrastructure.rest.hateoas.schema.JsonSchemaMetadata;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.springframework.hateoas.RepresentationModel;

import static lombok.AccessLevel.PROTECTED;

@NoArgsConstructor(access = PROTECTED)
@AllArgsConstructor
@Getter
public class WorkerListRowResource extends RepresentationModel<WorkerListRowResource> {

    @JsonSchemaMetadata(title = "staff id", order = 15)
    private Long workerId;

    @JsonSchemaMetadata(title = "firstName", order = 25)
    private String firstName;

    @JsonSchemaMetadata(title = "lastName", order = 30)
    private String lastName;

    @JsonSchemaMetadata(title = "has access", order = 40)
    private boolean hasAccess;

}
