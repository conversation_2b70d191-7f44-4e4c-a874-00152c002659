package com.ecco.webApi.workers;

import com.ecco.dom.hr.Worker;
import com.ecco.hr.dao.WorkerRepository;
import com.ecco.infrastructure.rest.hateoas.schema.*;
import com.ecco.webApi.viewModels.ResourceList;
import com.fasterxml.jackson.module.jsonSchema.JsonSchema;
import com.fasterxml.jackson.module.jsonSchema.types.LinkDescriptionObject;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Predicate;
import org.springframework.composed.web.rest.json.GetJson;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.querydsl.QSort;
import org.springframework.hateoas.IanaLinkRelations;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.WebRequest;

import java.util.Optional;

import static com.ecco.dom.hr.QWorker.worker;
import static com.ecco.infrastructure.rest.hateoas.ApiLinkTo.linkToApi;
import static org.springframework.hateoas.server.mvc.WebMvcLinkBuilder.methodOn;

/**
 * Web API to obtain a list of workers, paged and filtered by lastname.
 * This can then be used to build a rich client version of the workers list page.
 * The API is also self describing by means of a schema.
 */
@RestController
@RequestMapping("/staff")
public class WorkerListController extends SchemaProvidingController<WorkerListController> {
    private static final String REL_INSTANCES = "instances";


    private final WorkerRepository workerRepository;
    private final WorkerListRowResourceAssembler rowResourceAssembler;


    public WorkerListController(WorkerRepository workerRepository,
                                WorkerListRowResourceAssembler rowResourceAssembler) {
        super();
        this.workerRepository = workerRepository;
        this.rowResourceAssembler = rowResourceAssembler;
    }

    @Override
    public String getEntityTypeName() {
        return "staff";
    }

    @GetJson("/")
    public ResourceList<WorkerListRowResource> list(
            @RequestParam(name = "search", required = false)
            @JsonSchemaMetadata(order = 3)
            String search,
            @RequestParam(name = "page", defaultValue="0")
            Integer page,
            @RequestParam(name = "pageSize", defaultValue="15") // could do getPageSize("pageSize.workers"));
            @JsonSchemaProperty(readOnly = true)
            Integer pageSize) {

        PageRequest pr = workerPageAndSorting(page, pageSize);
        Predicate p = workerQuery(search);
        final Page<Worker> workers = workerRepository.findAll(p, pr);

        Page<WorkerListRowResource> resoucePage = workers.map(rowResourceAssembler::toModel);
        ResourceList<WorkerListRowResource> resourceList = new ResourceList<>(resoucePage.getContent());

        if (resoucePage.hasPrevious()) {
            resourceList.add(
                    linkToApi(methodOn(WorkerListController.class).list(search, page - 1, pageSize))
                        .withRel(IanaLinkRelations.PREV));
        }
        if (resoucePage.hasNext()) {
            resourceList.add(
                    linkToApi(methodOn(WorkerListController.class).list(search, page + 1, pageSize))
                        .withRel(IanaLinkRelations.NEXT));
        }
        resourceList.setNumPages(resoucePage.getTotalPages());
        resourceList.setPageSize(pageSize);
        resourceList.setNumItems(resoucePage.getTotalElements());
        addDescribedByLink(resourceList);
        return resourceList;
    }

    @Override
    public ResponseEntity<JsonSchema> describe(WebRequest request) {
        Object invocation = self().list("", 0, 0);

        JsonSchema listRequestParamSchema = getSchemaCreator().createForRequestParams(invocation);

        LinkDescriptionObject instancesLink = new SchemaProvidingLinkDescriptionObject()
                .setRel(REL_INSTANCES)
                .setMethod(RequestMethod.GET.toString())
                .setHref(linkToApi(invocation).toString()) // toUriComponentsBuilder().replaceQuery(null).build(false).toString())
                .setSchema(listRequestParamSchema);

        JsonSchema schema = getSchemaCreator().create(WorkerListRowResource.class,
                self().describe(request),
                Optional.of(instancesLink),
                Optional.empty());

        cacheForXSecs(request, 180);
        return ResponseEntity.ok(schema);
    }

    // NB also see ReportController.reportUsers
    private Predicate workerQuery(String search) {
        BooleanBuilder p = new BooleanBuilder();
        if (search != null) {
            p.and(
                    worker.contact.lastName.containsIgnoreCase(search)
                    .or(worker.contact.firstName.containsIgnoreCase(search))
            );
        }
        return p;
    }

    private PageRequest workerPageAndSorting(int page, int pageSize) {
        QSort sort = new QSort(worker.contact.lastName.asc().nullsLast());
        return PageRequest.of(page, pageSize, sort);
    }

}
