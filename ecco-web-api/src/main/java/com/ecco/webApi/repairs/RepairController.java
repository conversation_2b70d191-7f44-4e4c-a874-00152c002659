package com.ecco.webApi.repairs;

import com.ecco.config.repositories.ListDefinitionRepository;
import com.ecco.dom.repairs.RepairServiceRecipient;
import com.ecco.repositories.repairs.RepairRateRepository;
import com.ecco.repositories.repairs.RepairRepository;
import com.ecco.webApi.controllers.BaseWebApiController;
import com.ecco.webApi.controllers.CreateServiceRecipientCommandController;
import com.ecco.webApi.controllers.CreateServiceRecipientParams;
import com.ecco.webApi.viewModels.Result;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.composed.web.rest.json.GetJson;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Nonnull;
import java.io.IOException;
import java.util.List;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static org.springframework.hateoas.IanaLinkRelations.SELF;


@RestController
@RequestMapping("/repairs")
public class RepairController extends BaseWebApiController {

    private final RepairRepository repairRepository;
    private final RepairRateRepository repairRateRepository;
    private final RepairToViewModel repairToViewModel;
    private final RepairRateToViewModel repairRateToViewModel;
    private final ObjectMapper objectMapper;
    private final CreateServiceRecipientCommandController createServiceRecipientCommandController;

    public RepairController(RepairRepository repairRepository,
                            RepairRateRepository repairRateRepository,
                            ObjectMapper objectMapper,
                            CreateServiceRecipientCommandController createServiceRecipientCommandController,
                            ListDefinitionRepository listDefinitionRepository) {
        this.repairRepository = repairRepository;
        this.repairRateRepository = repairRateRepository;
        this.objectMapper = objectMapper;
        this.createServiceRecipientCommandController = createServiceRecipientCommandController;
        this.repairToViewModel = new RepairToViewModel(listDefinitionRepository, repairRateRepository);
        this.repairRateToViewModel = new RepairRateToViewModel();
    }

    // see ContractController.EXTRACT_ID_FN
    // see BuildingController.EXTRACT_ID_FN
    public static Pattern extractIdPattern = Pattern.compile("\\w+/(\\d+)/");
    public static final Function<String, Integer> EXTRACT_ID_FN = (href) -> {
        Matcher matches = extractIdPattern.matcher(href);
        matches.find();
        String id = matches.group(1);
        return Integer.parseInt(id);
    };

    @GetJson("/{repairId}/")
    public RepairViewModel getRepair(@PathVariable int repairId) {
        return repairToViewModel.apply(repairRepository.getById(repairId));
    }

    @GetJson("/service-recipients/{srId}/")
    public RepairViewModel getRepairBySrId(@PathVariable int srId) {
        return repairToViewModel.apply(repairRepository.findRepairByServiceRecipientId(srId).get());
    }

    @GetJson("/rates/")
    public List<RepairRateViewModel> getRepairRates() {
        return repairRateRepository.findAll().stream().map(repairRateToViewModel).toList();
    }

    @PreAuthorize("isFullyAuthenticated()")
    @Nonnull
    Result createImport(RepairViewModel repairViewModel) throws IOException {
        Assert.isNull(repairViewModel.repairId, "No id should be set on POST");

        CreateRepairCommandViewModel createVm = new CreateRepairCommandViewModel(repairViewModel);
        CreateServiceRecipientParams params = new CreateServiceRecipientParams(RepairServiceRecipient.PREFIX);
        String createVmStr = objectMapper.writeValueAsString(createVm);
        Result commandResult = createServiceRecipientCommandController.createServiceRecipientCommand(SecurityContextHolder.getContext().getAuthentication(), params, createVmStr);

        // return what was expected previously - before a command was used
        if (commandResult.isCommandSuccessful()) {
            // TODO we could avoid this if BaseCommandHandler.createCommandAndHandleInternal was to return the id of the entity, not command
            long rId = RepairController.EXTRACT_ID_FN.apply(commandResult.getLink(SELF.value()).getHref());
            return new Result(rId);
        }
        // if an error - return it
        return commandResult;
    }

}
