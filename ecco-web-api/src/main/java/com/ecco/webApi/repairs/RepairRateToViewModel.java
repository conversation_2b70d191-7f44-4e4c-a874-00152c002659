package com.ecco.webApi.repairs;

import com.ecco.dom.repairs.RepairRate;

import java.util.function.Function;

public class RepairRateToViewModel implements Function<RepairRate, RepairRateViewModel> {

    @Override
    public RepairRateViewModel apply(RepairRate input) {

        RepairRateViewModel result = new RepairRateViewModel();

        result.setId(input.getId());
        result.setArea(input.getArea());
        result.setCode(input.getCode());
        result.setRef(input.getRef());
        result.setDescription(input.getDescription());
        result.setUnit(input.getUnit());
        result.setRate(input.getRate());

        return result;
    }

}