package com.ecco.webApi.users;

import com.ecco.infrastructure.spring.data.BaseCommandRepository;
import com.ecco.users.commands.UserCommand;
import com.ecco.users.repository.UserCommandRepository;
import com.ecco.webApi.evidence.BaseCommandHandler;
import com.ecco.webApi.evidence.BaseCommandViewModel;
import com.fasterxml.jackson.databind.ObjectMapper;

public abstract class BaseUserCommandHandler<VM extends BaseCommandViewModel, ENTITY extends UserCommand, PARAMS>
        extends BaseCommandHandler<VM, Integer, ENTITY, PARAMS> {

    @SuppressWarnings("unchecked")
    public BaseUserCommandHandler(ObjectMapper objectMapper, UserCommandRepository userCommandRepository,
                                  Class<VM> vmClass) {
        super(objectMapper, (BaseCommandRepository<ENTITY, Integer>) userCommandRepository, vmClass);
    }
}