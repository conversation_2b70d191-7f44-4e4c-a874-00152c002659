package com.ecco.webApi.viewModels;

import com.ecco.webApi.contacts.IndividualViewModel;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.joda.time.DateTime;
import org.springframework.hateoas.RepresentationModel;

import java.util.Set;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.NON_NULL;

// Property accessors are unfortunately required for com.ecco.data.client.csv.CSVBeanReader.
// See ECCO-703
@Data
@EqualsAndHashCode(callSuper = true)
@JsonInclude(NON_NULL)
public class UserViewModel extends RepresentationModel<UserViewModel> {

    /**
     * The user ID.
     */
    public Long userId;

    /**
     * username
     */
    public String username;

    /**
     * Allow the creation of a user to have a password
     */
    public String newPassword;

    /**
     * enabled
     */
    public boolean enabled;

    public DateTime registered;

    /** Indicates that this user requires mfa */
    public boolean mfaRequired;

    public boolean mfaValidated;

    /**
     * The date time of the last log in (impersonate?)
     */
    public DateTime lastLoggedIn;

    /**
     * The contact of the user (name, address etc)
     */
    public IndividualViewModel individual;

    /**
     * The 'rights' or roles the user belongs to
     */
    public Set<String> groups;

    /**
     * The 'where' the user has rights
     * Currently its easier given the current file format to stick this here
     * We could use proper AclEntryViewModel, or a similar approach on ReferralAggregate.referralIndividuals
     */
    public long projectId;

}
