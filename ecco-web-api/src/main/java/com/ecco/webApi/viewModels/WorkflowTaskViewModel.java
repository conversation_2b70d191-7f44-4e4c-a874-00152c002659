package com.ecco.webApi.viewModels;

import java.time.Instant;
import java.time.LocalDateTime;

import org.jspecify.annotations.Nullable;
import org.springframework.hateoas.RepresentationModel;

/** Data-transfer object representing a task within a workflow. */
public class WorkflowTaskViewModel extends RepresentationModel<WorkflowTaskViewModel> {

    @Nullable
    public String assignedTo;

    /** The task is available to be actioned */
    public boolean isAvailable;

    /** The task has been actioned */
    public boolean isCompleted; // TODO: should we replace this with (endTime != null) ??

    /** <PERSON><PERSON> used to query for the WorkflowTaskDefinition containing settings for this task */
    public String taskDefinitionHandle;

    /** The name used for display */
    public String taskName;

    /** The opaque handle for this task */
    public String taskHandle;

    /** Due date for the task, if it has one. */
    @Nullable
    public LocalDateTime dueDate;

    /** Time that this task was completed or cancelled */
    @Nullable
    public Instant endTime;

}
