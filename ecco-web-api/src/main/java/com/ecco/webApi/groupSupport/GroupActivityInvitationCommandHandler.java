package com.ecco.webApi.groupSupport;

import com.ecco.dom.*;
import com.ecco.dom.groupsupport.*;
import com.ecco.evidence.EvidenceTask;
import com.ecco.groupsupport.repositories.GroupSupportActivityInvolvementRepository;
import com.ecco.groupsupport.repositories.GroupSupportCommandRepository;
import com.ecco.security.SecurityUtil;
import com.ecco.webApi.CommandResult;
import com.ecco.webApi.evidence.BaseCommandHandler;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.text.StrBuilder;
import org.jspecify.annotations.NonNull;
import org.joda.time.DateTime;
import org.jspecify.annotations.Nullable;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.io.Serializable;

@Component
public class GroupActivityInvitationCommandHandler extends BaseCommandHandler<GroupActivityInvitationCommandViewModel, Long, GroupSupportCommand, @Nullable Void> {

    @PersistenceContext
    private EntityManager entityManager;

    private final GroupSupportActivityInvolvementRepository involvementRepository;

    public GroupActivityInvitationCommandHandler(ObjectMapper objectMapper, GroupSupportCommandRepository commandRepository,
            GroupSupportActivityInvolvementRepository involvementRepository) {
        super(objectMapper, commandRepository, GroupActivityInvitationCommandViewModel.class);
        this.involvementRepository = involvementRepository;
    }

    @Override
    protected CommandResult handleInternal(@NonNull Authentication auth, @Nullable Void params, @NonNull GroupActivityInvitationCommandViewModel viewModel) {
        GroupActivity_Referral_MultiId multiId = new GroupActivity_Referral_MultiId();
        multiId.setActivity(entityManager.getReference(GroupSupportActivity.class, viewModel.activityId));
        multiId.setReferral(entityManager.getReference(Referral.class, viewModel.referralId));

        GroupActivity_Referral invitation = involvementRepository.findById(multiId).orElse(null);

        if (viewModel.invited != null) {
            if (viewModel.invited.to == true && invitation == null) {
                invitation = new GroupActivity_Referral();
                invitation.setMultiId(multiId);
            } else if (viewModel.invited.to == false && invitation != null) {
                involvementRepository.delete(invitation);
                invitation = null;
            }
        }

        if (invitation != null) {
            if (viewModel.attending != null) {
                invitation.setAttending(viewModel.attending.to);
            }

            if (viewModel.attended != null) {
                handleAttendedChange(viewModel, invitation);
            }

            involvementRepository.save(invitation);
        }
        return null;
    }

    private void handleAttendedChange(GroupActivityInvitationCommandViewModel viewModel, GroupActivity_Referral invitation) {

        // We only deal with attended being set one way
        if (viewModel.attended.to) {
            invitation.setAttended(viewModel.attended.to);
            Individual author = SecurityUtil.getAuthenticatedUser().getContact();
            EvidenceSupportWork work = createWorkObject(invitation.getGroupSupportActivity(),
                    invitation.getReferral(),
                    viewModel.evidenceNotes != null ? viewModel.evidenceNotes.to : null,
                    viewModel.evidenceType != null ? viewModel.evidenceType.to : null,
                    author);

            entityManager.persist(work);
            invitation.setSupportWorkUuid(work.getWorkId());
        }
        else if (Boolean.FALSE.equals(viewModel.attended.to)) {
            throw new IllegalArgumentException("Cannot unset attended once saved");
        }
    }

    @NonNull
    @Override
    protected GroupSupportCommand createCommand(Serializable targetId, @Nullable Void params,
                                                @NonNull String requestBody, @NonNull GroupActivityInvitationCommandViewModel viewModel, long userId) {
        return new GroupActivityInvitationCommand(viewModel.uuid, viewModel.timestamp, userId, viewModel.activityUuid, requestBody);
    }

    private EvidenceSupportWork createWorkObject(GroupSupportActivity activity,
                                                 Referral referral, String evidenceNote, Integer commentTypeId, Individual author) {

        StrBuilder comment = new StrBuilder()
                .append("group support: ")
                .append(activity.getGroupSupportActivityType().getName());

        if (activity.getDescription() != null) {
            comment.append(" (")
            .append(activity.getDescription())
            .append(")");
        }
        comment.appendNewLine();
        comment.append(evidenceNote);

        SupportEvidenceBuilder builder = new SupportEvidenceBuilder(referral.getServiceRecipientId());
                // removed reference to child, but now doesn't follow EvidenceCommandHandler logic
                //.setChild(childserviceRecipientId);

        builder.fromSource(EvidenceTask.GROUP_SUPPORT, EvidenceGroup.NEEDS)
                .withAuthor(author)
                .withCreatedDate(new DateTime())
                .withWorkDate(activity.getFromDate())
                .withComment(comment.toString(), activity.getMinutes(), commentTypeId);

        EvidenceSupportWork work = builder.build();

        // linked actions - TODO: Reinstate via cfg_list_definitions.parameters
        /*Set<Action> actions = activity.getGroupSupportActivityType().getLinkedActions();
        for (Action a : actions) {
            work.addAssociatedAction(a);
        }*/
        return work;
    }
}
