package com.ecco.webApi.groupSupport;

import com.ecco.dom.ReportCriteriaDto;
import com.ecco.webApi.controllers.BaseWebApiController;
import com.ecco.webApi.controllers.ReportUnsecuredDelegator;
import com.ecco.webApi.viewModels.ResourceList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.composed.web.rest.json.GetJson;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@PreAuthorize("hasRole('ROLE_USER')")
@RestController
@RequestMapping(produces = MediaType.APPLICATION_JSON_VALUE)
public class GroupAuxActivityController extends BaseWebApiController {

    private final GroupActivityCommandHandler groupActivityCommandHandler;
    private final GroupActivityAttendanceCommandHandler groupActivityAttendanceCommandHandler;

    private final ReportUnsecuredDelegator reportUnsecuredDelegator;

    @Autowired
    public GroupAuxActivityController(GroupActivityCommandHandler groupActivityCommandHandler,
                                      GroupActivityAttendanceCommandHandler groupActivityAttendanceCommandHandler,
                                      GroupActivityInvitationCommandHandler groupActivityInvitationCommandHandler,
                                      ReportUnsecuredDelegator reportUnsecuredDelegator) {
        this.groupActivityCommandHandler = groupActivityCommandHandler;
        this.groupActivityAttendanceCommandHandler = groupActivityAttendanceCommandHandler;
        this.reportUnsecuredDelegator = reportUnsecuredDelegator;
    }

    @GetJson("/activities-aux/")
    public ResourceList<GroupSupportActivitySummaryRowResource> findActivities(
            @RequestParam(value= "service", required = false) Long serviceId,
            @RequestParam(name = "page", defaultValue="0") Integer page,
            @RequestParam(name = "pageSize", defaultValue="15") Integer pageSize) {
        ReportCriteriaDto criteria = new ReportCriteriaDto();
        criteria.setServiceId(serviceId);
        var pageReq = PageRequest.of(page, pageSize, Sort.by("fromDate").descending());
        return reportUnsecuredDelegator.reportGroupAuxActivitySummaries(criteria, pageReq);
    }

}
