package com.ecco.webApi.groupSupport;

import com.ecco.dom.Referral;
import com.ecco.dom.agreements.DaysOfWeek;
import com.ecco.dom.groupsupport.GroupActivity_Referral;
import com.ecco.dom.groupsupport.GroupSupportAttendance;
import com.ecco.webApi.evidence.ReferralSummaryViewModel;
import java.util.function.Function;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;

/**
 * For group activities with end dates.
 * NOTE: This is a seemingly quirky transformer for a reason.
 *
 * For the supplied date range, generate a ClientAttendanceViewModel for that referral, where expected attendance
 * is recorded in dailyAttendances where the Referral indicates that day of the week, even before any
 * {@link GroupSupportAttendance} or {@link GroupActivity_Referral} have been saved.
 */
public class ReferralClientAttendanceWithEndDateViewModel implements Function<ReferralSummaryViewModel, ClientAttendanceViewModel> {

    // referralId to attendance date and data
    private final Map<Long, Map<LocalDate, GroupSupportAttendance>> attendancesByAttendedDateByReferralId = new HashMap<>();

    private final LocalDate startDate;

    private final LocalDate endDate;


    ReferralClientAttendanceWithEndDateViewModel(DateTime from, DateTime to, List<GroupSupportAttendance> attendances) {
        this.startDate = from.toLocalDate();
        this.endDate = to.toLocalDate();

        // populate the attendances into by referralId -> attendance date and attendance
        // where we later lookup by referral
        for (GroupSupportAttendance attendance : attendances) {
            Long referralId = attendance.getReferral().getId();
            Map<LocalDate, GroupSupportAttendance> attendancesByAttendedDate = attendancesByAttendedDateByReferralId.get(referralId);
            if (attendancesByAttendedDate == null) {
                attendancesByAttendedDate = new HashMap<>();
                attendancesByAttendedDateByReferralId.put(referralId, attendancesByAttendedDate);
            }
            attendancesByAttendedDate.put(attendance.getAttendedAt().toLocalDate(), attendance);
        }
    }

    @Override
    public ClientAttendanceViewModel apply(ReferralSummaryViewModel input) {
        ClientAttendanceViewModel result = new ClientAttendanceViewModel();

        result.dailyAttendances = new ArrayList<>();
        result.serviceRecipientId = input.serviceRecipientId;
        result.invited = true;

        // for the duration of the long-running activity, get the daily attendee data
        for (LocalDate day = startDate; !day.isAfter(endDate); day = day.plusDays(1)) {
            GroupSupportAttendance attendance = getAttendance(input, day);

            DailyAttendanceViewModel dailyAttendanceResult = createAttendance(input, day, attendance);
            if (dailyAttendanceResult != null) {
                result.dailyAttendances.add(dailyAttendanceResult);
            }
        }
        return result;
    }

    /** Creates an attendance entry if there is any data associated with this day, including daysAttending */
    static DailyAttendanceViewModel createAttendance(Referral referral, LocalDate day, GroupSupportAttendance attendance) {
        return createAttendance(day, referral.getReceivingServiceLocalDate(), referral.getExitedDate(), referral.getMeetingDays(), attendance);
    }
    static DailyAttendanceViewModel createAttendance(ReferralSummaryViewModel referral, LocalDate day, GroupSupportAttendance attendance) {
        return createAttendance(day, referral.receivingServiceDate, referral.exitedDate, referral.getMeetingDays(), attendance);
    }
    static DailyAttendanceViewModel createAttendance(LocalDate day, LocalDate receivingServiceDate,
                                                     LocalDate exitedDate, DaysOfWeek meetingDays,
                                                     GroupSupportAttendance attendance) {
        boolean attendingOnCurrentDay = isNormalAttendanceDayWithinServicePeriod(day, receivingServiceDate, exitedDate, meetingDays);
        if (!attendingOnCurrentDay && attendance == null) {
            return null;
        }
        DailyAttendanceViewModel dailyAttendanceResult = new DailyAttendanceViewModel();
        dailyAttendanceResult.attending = attendingOnCurrentDay;
        dailyAttendanceResult.date = day;
        if (attendance != null) {
            dailyAttendanceResult.attendedAllDay = attendance.isAttendedAllDay();
            dailyAttendanceResult.cancelled = attendance.isCancelled();
        }
        return dailyAttendanceResult;
    }

    static private boolean isNormalAttendanceDayWithinServicePeriod(LocalDate day, LocalDate receivingServiceDate,
                                                                    LocalDate exitedDate, DaysOfWeek meetingDays) {
        return (receivingServiceDate == null || !day.isBefore(receivingServiceDate))
                && (exitedDate == null || !day.isAfter(exitedDate))
                && meetingDays.isJodaCalendarDay(day.getDayOfWeek());
    }

    private GroupSupportAttendance getAttendance(ReferralSummaryViewModel referral, LocalDate day) {
        Map<LocalDate, GroupSupportAttendance> attendanceDayMap = attendancesByAttendedDateByReferralId.get(referral.referralId);
        if (attendanceDayMap != null) {
            return attendanceDayMap.get(day);
        }
        return null;
    }

}
