package com.ecco.webApi.calendar;

import com.ecco.calendar.cosmo.CosmoCalendarService;
import com.ecco.webApi.evidence.BaseCommandValid;
import com.ecco.webApi.evidence.BaseCommandViewModel;
import com.ecco.dto.ChangeViewModel;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.LocalDate;
import org.joda.time.LocalTime;

import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;
import java.util.Arrays;
import java.util.List;

@Builder(toBuilder = true)
@Getter
@Setter
@Slf4j
@NoArgsConstructor // only for Cglib/Hibernate etc
@AllArgsConstructor(access = AccessLevel.PROTECTED)
public class CalendarEntryCommandViewModel implements BaseCommandValid {

    /**
     * The operation to perform; either {@link BaseCommandViewModel#OPERATION_ADD},
     * {@link BaseCommandViewModel#OPERATION_UPDATE} or {@link BaseCommandViewModel#OPERATION_REMOVE}.
     */
    @NonNull
    public String operation;

    /** Below as per CalendarableEntity */
    public String eventUuid; // for an existing event
    protected ChangeViewModel<LocalDate> startDate;
    protected ChangeViewModel<Boolean> allDay;
    protected ChangeViewModel<LocalTime> startTime;
    private ChangeViewModel<String> title;
    private ChangeViewModel<LocalDate> endDate;
    private ChangeViewModel<LocalTime> endTime;
    /**
     * The timezone you want the entry to have. Whilst we can assume the time is always local time, we sometimes need to convert
     * it to an actual instant in time, and then assumptions can cause problems. For instance, if a country changes timezone then our
     * actual instant needs modifying. Depending on the system requirements, this could either be done on the day, or in advance. If in
     * advance then instants created before the change would need modifying (and assume new events after the announcement were in the new tz).
     * Regardless of system requirements, the problem is also highlighted coming out of British Summer Time when we 'fall back'
     * and have 1am twice. By specifying the timezone, we know which one we are talking about - something the system can never do.
     * Use the continent/region from https://docs.oracle.com/javase/8/docs/api/java/time/ZoneId.html#getAvailableZoneIds--
     * See https://stackoverflow.com/questions/40548309/are-joda-timezone-ids-the-same-as-java-time-zone-ids
    */
    private ChangeViewModel<String> tzId;
    private ChangeViewModel<Integer> eventCategoryId;

    private ChangeViewModel<String> repeatType; // "WK" | "MTH" | "QTR" | "BI" | "YR"
    private ChangeViewModel<Integer> repeatEveryDays; // see DaysOfWeek
    private ChangeViewModel<Integer> repeatEveryWeeks;
    private ChangeViewModel<LocalDate> repeatEndDate;

    // NB calendarIds and attendees do the same job, but attendees lets us do more
    // or it would if we had a reference to calendarId from Attendee (which would allow us to reference and hence edit Attendees)
    private ChangeViewModel<List<Long>> contactIds;
    private CalendarAttendeeCommandViewModel[] attendees;

    public CalendarEntryCommandViewModel(@NonNull String operation, @Nullable String eventUuid) {
        this.operation = operation;
        this.eventUuid = eventUuid;
    }

    public boolean recurringStarted() {
        // intervalType not handled in normal calendar events yet
        return (getRepeatEveryWeeks() != null && (getRepeatEveryWeeks().from == null || getRepeatEveryWeeks().from == 0)
                && getRepeatEveryWeeks().to != null && getRepeatEveryWeeks().to > 0);
    }
    public boolean recurringStopped() {
        // intervalType not handled in normal calendar events yet
        return (getRepeatEveryWeeks() != null && (getRepeatEveryWeeks().to == null || getRepeatEveryWeeks().to == 0));
    }
    public boolean recurringChanged() {
        return (getRepeatEveryWeeks() != null && getRepeatEveryWeeks().from != null &&
                (getRepeatEveryWeeks().from.compareTo(getRepeatEveryWeeks().to) != 0))
             || (getRepeatEndDate() != null && getRepeatEndDate().from != null &&
                (getRepeatEndDate().from.compareTo(getRepeatEndDate().to) != 0))
        ;
    }

    public boolean hasChanges() {
        return this.title != null || this.startDate != null || allDay != null || this.startTime != null ||
                this.endDate != null || this.endTime != null || this.contactIds != null ||
                this.eventCategoryId != null || this.repeatEveryDays != null || this.repeatEveryWeeks != null ||
                this.repeatEndDate != null ||
                this.attendees != null && this.attendees.length > 0;
    }

    public boolean valid() {

        boolean valid = BaseCommandViewModel.valid(getClass(), this);

        boolean hasAllDay = this.allDay != null && Boolean.TRUE.equals(this.allDay.to);
        boolean hasStartTime = this.startTime != null && this.startTime.to != null;
        boolean hasEndTime = this.endTime != null && this.endTime.to != null;

        if (StringUtils.equalsIgnoreCase(this.operation, BaseCommandViewModel.OPERATION_UPDATE) ||
                StringUtils.equalsIgnoreCase(this.operation, BaseCommandViewModel.OPERATION_REMOVE)) {
            if (this.eventUuid == null) {
                log.error("Required field: eventUuid");
                return false;
            }
        }

        if (StringUtils.equalsIgnoreCase(this.operation, BaseCommandViewModel.OPERATION_ADD)) {
            if (this.startDate == null || this.startDate.to == null) {
                log.error("Required field with 'add': startDate");
                valid = false;
            }
            if (!hasAllDay && !hasStartTime) {
                log.error("Required field with 'add': allDay or startTime");
                valid = false;
            }

            if (hasStartTime && hasEndTime && endDate != null) {
                if (startDate.to.toLocalDateTime(startTime.to).isAfter(endDate.to.toLocalDateTime(endTime.to))) {
                    log.error("endDateTime is before startDateTime");
                    valid = false;
                }
                // could also check repeatEvery... is set also
                if (repeatEndDate != null && endDate.to.isAfter(repeatEndDate.to)) {
                    // this is probably a mistake (they probably didn't mean the end date), so stop it
                    log.error("repeatEndDate is before endDate");
                    valid = false;
                }
            }

            if (hasStartTime && hasEndTime && repeatEndDate != null) {
                if (startDate.to.isAfter(repeatEndDate.to)) {
                    log.error("repeatEndDate is before startDate");
                    valid = false;
                }
            }

        }

        if (StringUtils.equalsIgnoreCase(this.operation, BaseCommandViewModel.OPERATION_UPDATE)) {
            // if not recurring and changed to recurring, that's not a clean way to do it - just create a new one
            boolean isRecurrence = CosmoCalendarService.isRecurrenceUid(eventUuid); // not ideal having direct cosmo reference, but don't want calendarService just for that
            if (!isRecurrence && this.recurringStarted()) {
                log.error("Not clear to define: editing an event to make it recurring (what do we do with the current event) - just create a new event");
                return false;
            }
            // if recurring and changed the schedule, do we update the current entry, or just the ones going forward? For now, just prevent
            if (isRecurrence && this.recurringChanged() && this.getRepeatEveryWeeks().to > 0) {
                log.error("Not clear to define: editing the frequency on a recurring entry (what do we do with the current event) - just create a new event");
                return false;
            }
            // if recurring changed on the repeatEnd only, that is probably okay
        }

        if (hasStartTime && !hasEndTime){
            log.error("Required field with 'add': startTime with endTime");
            valid = false;
        }

        if (this.attendees != null && this.attendees.length > 0) {
            boolean attendeesValid = Arrays.stream(this.attendees).allMatch(CalendarAttendeeCommandViewModel::valid);
            if (!attendeesValid) {
                return false;
            }
        }

        return valid;
    }

}
