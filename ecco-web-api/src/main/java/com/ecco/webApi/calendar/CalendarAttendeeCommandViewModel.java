package com.ecco.webApi.calendar;

import com.ecco.webApi.evidence.BaseCommandValid;
import com.ecco.webApi.evidence.BaseCommandViewModel;
import com.ecco.dto.ChangeViewModel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.jspecify.annotations.NonNull;

@Getter
@Setter
@Slf4j
@NoArgsConstructor // only for Cglib/Hibernate etc
public class CalendarAttendeeCommandViewModel implements BaseCommandValid {

    /**
     * The operation to perform; either {@link BaseCommandViewModel.OPERATION_ADD}, {@link BaseCommandViewModel.OPERATION_UPDATE} or
     * {@link BaseCommandViewModel.OPERATION_REMOVE}.
     */
    @NonNull
    public String operation;

    public String name;
    public String calendarIdUserReferenceUri; // cosmo representation - see CosmoHelper.syncAttendeesWithCalendars
    public String calendarId; // integration-representation (pointing to the user's collection)

    protected ChangeViewModel<Boolean> requiredChange;
    private ChangeViewModel<Integer> statusChange;

    public CalendarAttendeeCommandViewModel(@NonNull String operation, @NonNull String calendarId) {
        this.operation = operation;
        this.calendarId = calendarId;
    }

    public boolean hasChanges() {
        return this.requiredChange != null || this.statusChange != null;
    }

    public boolean valid() {
        boolean valid = BaseCommandViewModel.valid(getClass(), this);

        if (this.calendarId == null) {
            log.error("Required field: calendarId is required to reference the attendee (we can't get at calendarIdUserReferenceUri)");
            return false;
        }

        return valid;
    }

}
