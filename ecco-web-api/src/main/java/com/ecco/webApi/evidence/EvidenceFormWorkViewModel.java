package com.ecco.webApi.evidence;

import com.fasterxml.jackson.annotation.JsonRawValue;
import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.databind.JsonNode;

import java.util.UUID;

/**
 * Transfers the custom form along with the work item that recorded it.
 * This can be used as the snapshots view model and the work view model since they have a 1-1 relationship.
 */
public class EvidenceFormWorkViewModel extends BaseWorkViewModel {

    public String evidenceGroupKey;

    @JsonRawValue
    public String form;

    /**
     * The definition that the snapshot was created with
     */
    public UUID formDefinitionUuid;

    /**
     * This is for deserialisation because JsonRawValue only does serialisation.  There is a perf penalty from not
     * parsing straight into a string, but we only use this in tests (at the moment).
     * Other approaches can be found at
     * https://stackoverflow.com/questions/4783421/how-can-i-include-raw-json-in-an-object-using-jackson
     */
    @JsonSetter("form")
    public void setForm(JsonNode form) {
        this.form = form == null ? null : form.toString();
    }
}
