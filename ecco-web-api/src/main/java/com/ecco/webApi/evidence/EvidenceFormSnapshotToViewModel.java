package com.ecco.webApi.evidence;

import com.ecco.dom.EvidenceFormSnapshot;
import com.ecco.dom.EvidenceFormWork;
import com.ecco.service.TaskDefinitionService;
import lombok.RequiredArgsConstructor;
import org.jspecify.annotations.Nullable;

import java.util.function.Function;

@RequiredArgsConstructor
public class EvidenceFormSnapshotToViewModel implements Function<EvidenceFormSnapshot, EvidenceFormWorkViewModel> {

    private final TaskDefinitionService taskDefinitionService;

    @Override
    @Nullable
    public EvidenceFormWorkViewModel apply(@Nullable EvidenceFormSnapshot input) {

        EvidenceFormWorkViewModel viewModel = new EvidenceFormWorkViewModel();
        EvidenceFormWork work = input.getWork();
        viewModel.id = work.getId();
        viewModel.requestedDelete = work.isRequestedDelete();
        viewModel.taskName = taskDefinitionService.findOneById(work.getTaskDefId()).getName();
        viewModel.evidenceGroupKey = work.getEvidenceGroupKey();
        viewModel.serviceRecipientId = work.getServiceRecipientId();
        viewModel.serviceAllocationId = input.getServiceRecipient().getServiceAllocationId();
        viewModel.authorDisplayName = work.getAuthor().getDisplayName();
        viewModel.workDate = work.getWorkDate().toLocalDateTime();
        viewModel.createdDate = work.getCreated() == null ? null : input.getCreated().toLocalDateTime();
        // a 'snapshot' has to exist, even if the json doesn't
        viewModel.form = work.getSnapshot().getJson();
        viewModel.formDefinitionUuid = work.getSnapshot().getFormDefinitionUuid();
        viewModel.signatureId = work.getSignature() != null ? work.getSignature().getId() : null;
        if (work.getComment() != null) {
            viewModel.comment = work.getComment().getComment();
            viewModel.minsSpent = work.getComment().getMinutesSpent();
            viewModel.commentTypeId = work.getComment().getTypeDefId();
        }

        return viewModel;
    }

}
