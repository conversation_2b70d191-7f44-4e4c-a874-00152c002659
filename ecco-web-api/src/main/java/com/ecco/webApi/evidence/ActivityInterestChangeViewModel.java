package com.ecco.webApi.evidence;

import org.jspecify.annotations.NonNull;

/**
 * Data-transfer object representing a command to add or remove an association
 * between a service recipient and an Activity Type.
 */
public class ActivityInterestChangeViewModel extends BaseCommandViewModel {
    @NonNull
    public static final String OPERATION_ADD = "add";

    @NonNull
    public static final String OPERATION_REMOVE = "remove";

    /**
     * The operation to perform; either {@link #OPERATION_ADD}, or
     * {@link #OPERATION_REMOVE}.
     */
    @NonNull
    public String operation;

    /**
     * The ID of the Activity Type to be added or removed.
     */
    public int activityTypeId;
}
