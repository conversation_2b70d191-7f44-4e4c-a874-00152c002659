package com.ecco.webApi.evidence;

import com.ecco.serviceConfig.repositories.ServiceCategorisationRepository;
import com.ecco.servicerecipient.ServiceRecipientCaseStatusView;
import com.ecco.security.SecurityUtil;
import com.ecco.security.dom.User;
import com.ecco.security.repositories.IndividualRepository;
import com.ecco.serviceConfig.service.ServiceTypeService;
import com.ecco.calendar.core.util.DateTimeUtils;
import org.jspecify.annotations.NonNull;
import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.hateoas.server.LinkBuilder;
import org.springframework.hateoas.server.mvc.RepresentationModelAssemblerSupport;
import com.ecco.dao.ReferralSummary;
import com.ecco.infrastructure.web.UriUtils;

public class ReferralListRowResourceAssembler extends RepresentationModelAssemblerSupport<ReferralSummary, ReferralListRowResource> {

    private static final String REL_EDIT = "edit";
    private static final String REL_PARENT = "parent";
    private final MessageSourceAccessor messageSource;
    private final ServiceTypeService serviceTypeService;
    private final ServiceCategorisationRepository serviceCategorisationRepository;
    private final IndividualRepository individualRepository;

    public ReferralListRowResourceAssembler(MessageSourceAccessor messageSource, ServiceTypeService serviceTypeService,
                                            ServiceCategorisationRepository serviceCategorisationRepository,
                                            IndividualRepository individualRepository) {
        super(ReferralListController.class, ReferralListRowResource.class);
        this.messageSource = messageSource;
        this.serviceTypeService = serviceTypeService;
        this.serviceCategorisationRepository = serviceCategorisationRepository;
        this.individualRepository = individualRepository;
    }

    @NonNull
    @Override
    public ReferralListRowResource toModel(ReferralSummary referral) {

        ReferralListRowResource resource = new ReferralListRowResource(
                referral.serviceRecipientId,
                referral.referralId,
                referral.referralCode != null ? referral.referralCode : referral.referralId.toString(),
                referral.firstName + " " + referral.lastName, // TODO: pass them individually and allow display options at client side?
                referral.contactId,
                referral.supportWorkerId,
                referral.supportWorkerId == null ? null : individualRepository.findOne(referral.supportWorkerId).getDisplayName(),
                referral.interviewer1ContactId,
                referral.agencyName,
                referral.selfReferral,
                referral.serviceAllocationId,
                referral.receivedDate,
                referral.receivingServiceDate,
                referral.statusMessageKey,
                summariseStatus(referral),
                referral.nextDueSlaDate,
                referral.nextDueSlaTaskId);

        addEditLink(resource, referral);
        addParentLink(resource, referral);
        return resource;
    }

    private String summariseStatus(ReferralSummary referral) {
        User user = SecurityUtil.getAuthenticatedUser();

        String statusMessageKey = ServiceRecipientCaseStatusView.Support.getStatusMessageKey(referral);
        switch (statusMessageKey) {
            case "status.toStart":
            case "status.started":
                return messageSource.getMessage(statusMessageKey) + (referral.receivingServiceDate != null
                    ? " " + DateTimeUtils.getFormattedDate(referral.receivingServiceDate, user.getLocale()) : "");
            case "status.pending":
                // FALLTHRU: TODO should be return referral.getPendingStatusName();
            default:
                return messageSource.getMessage(statusMessageKey, statusMessageKey);
        }
    }

    private void addEditLink(ReferralListRowResource resource, ReferralSummary referral) {
        resource.add(linkToNavReferral(referral.referralId).withRel(REL_EDIT));
    }

    private void addParentLink(ReferralListRowResource resource, ReferralSummary referral) {
        if (referral.parentReferralId != null) {
            resource.add(linkToNavReferral(referral.parentReferralId).withRel(REL_PARENT));
        }
    }

    private LinkBuilder linkToNavReferral(Long referralId) {
        // eg. http://localhost:8080/ecco-war/nav/secure/referralFlow.html?referralId=100055&referralTasksFilterKey=
        return UriUtils.hostRelativeLinkBuilder()
                .slash("nav").slash("referrals")
                .slash(referralId.toString()+"/");
    }
}
