package com.ecco.webApi.evidence

import com.ecco.dom.servicerecipients.BaseServiceRecipientCommand
import com.ecco.security.repositories.UserRepository

/** Extracts the raw JSON body and enhances it by transforming an historically-incorrect property name on the DTO.  */
open class GoalCommandExtractCommandViewModelJson<T : BaseServiceRecipientCommand>(userRepository: UserRepository) :
    AbstractExtractCommandViewModelJson<T>(userRepository) {
    override fun apply(input: T): CharSequence {
        val result = StringBuilder()

        // do the normal checking and appending etc
        result.append(super.apply(input))

        // replace 'commentChange' with 'goalNameChange'
        for (fieldName in migrationMappings.keys) {
            val idx = result.indexOf(fieldName)
            if (idx != -1) {
                result.replace(idx, idx + fieldName.length, migrationMappings[fieldName])
                // NB this approach is no doubt slower
                // private final ObjectMapper objectMapper = new ObjectMapper();
                // node = objectMapper.readTree(super.apply(input).toString()).get("commentChange");
                // ((ObjectNode) node).put(fieldName, newFieldName) ...
            }
        }
        return result
    }

    companion object {
        private val migrationMappings: MutableMap<String, String> = HashMap()

        init {
            migrationMappings["\"commentChange\":"] = "\"goalNameChange\":"
        }
    }
}