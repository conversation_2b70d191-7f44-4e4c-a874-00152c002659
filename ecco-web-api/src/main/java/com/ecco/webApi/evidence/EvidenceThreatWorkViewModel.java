package com.ecco.webApi.evidence;

import java.util.List;
import java.util.UUID;

/**
 * Evidence of a piece of threat work done to support a client, and any corresponding
 * changes to the client's support plan.
 */
public class EvidenceThreatWorkViewModel extends BaseOutcomeBasedWorkViewModel {

    /**
     * Changes to actions on the client's risk assessment made as part of this
     * support work.
     */
    public List<EvidenceThreatActionViewModel> riskActions;

    public List<EvidenceRiskAreaViewModel> riskAreas;

    public List<UUID> handledSupportWorkIds;
}
