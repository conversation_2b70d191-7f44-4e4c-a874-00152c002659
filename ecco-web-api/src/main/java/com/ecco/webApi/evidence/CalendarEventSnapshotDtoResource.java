package com.ecco.webApi.evidence;

import com.ecco.rota.webApi.dto.DemandScheduleDto;
import com.ecco.webApi.contacts.IndividualViewModel;
import com.ecco.webApi.contacts.address.AddressViewModel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.jspecify.annotations.NonNull;
import org.springframework.hateoas.RepresentationModel;

import org.jspecify.annotations.Nullable;
import java.time.Instant;
import java.util.UUID;

import static lombok.AccessLevel.PACKAGE;

/**
 * The live status of an event (e.g. is someone attending, has associated work been started)
 */
@NoArgsConstructor(access = PACKAGE)
@AllArgsConstructor
@Getter
public final class CalendarEventSnapshotDtoResource extends RepresentationModel<CalendarEventSnapshotDtoResource> {

    // TODO check flow from controller to client dto

    @NonNull
    public String eventUid;

    @Nullable
    public Integer serviceRecipientId;

    @Nullable
    public Integer serviceAllocationId;

    @Nullable
    public IndividualViewModel demandContact;

    @Nullable
    public IndividualViewModel resourceContact;

    @NonNull
    public Instant plannedStartInstant;

    @NonNull
    public Instant plannedEndInstant;

    @NonNull
    public Long demandScheduleId;

    public DemandScheduleDto demandScheduleDto;

    public AddressViewModel plannedLocation;

    @Nullable
    public LocationViewModel location;

    @Nullable
    public Instant startInstant;

    @Nullable
    public Instant endInstant;

    @Nullable
    public Long contactId;

    @Nullable
    public UUID workUuid;
}
