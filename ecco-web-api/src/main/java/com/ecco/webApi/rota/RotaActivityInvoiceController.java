package com.ecco.webApi.rota;

import com.ecco.dom.EvidenceSupportWork;
import com.ecco.dom.agreements.ClientSalesInvoiceLine;
import com.ecco.dom.agreements.ClientSalesRotaInvoiceLine;
import com.ecco.infrastructure.annotations.WriteableTransaction;
import com.ecco.webApi.calendar.ServiceRecipientRotaDecorator;
import com.ecco.dao.ClientSalesInvoiceRepository;
import com.ecco.dao.EvidenceSupportWorkRepository;
import com.ecco.dom.agreements.ClientSalesInvoice;
import com.ecco.rota.service.RotaDelegator;
import com.ecco.rota.service.RotaService;
import com.ecco.rota.webApi.dto.ClientSalesRotaInvoiceDetailResource;
import com.ecco.rota.webApi.dto.Rota;
import com.ecco.rota.webApi.dto.RotaAppointmentViewModel;
import com.ecco.webApi.controllers.BaseWebApiController;
import com.ecco.webApi.controllers.NotFoundException;
import com.ecco.webApi.finance.ServiceRecipientCreateInvoiceCommandHandler;
import com.ecco.webApi.finance.ServiceRecipientCreateInvoiceParams;
import com.ecco.webApi.viewModels.Result;
import com.ecco.calendar.core.CalendarService;
import com.ecco.calendar.core.Entry;
import com.ecco.calendar.core.Recurrence;
import lombok.RequiredArgsConstructor;
import org.joda.time.Duration;
import org.jspecify.annotations.NonNull;
import org.springframework.composed.web.rest.json.GetJson;
import org.springframework.composed.web.rest.json.PostJson;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.format.annotation.DateTimeFormat.ISO;
import org.springframework.security.core.Authentication;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Predicate;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.stream.StreamSupport;

import static com.ecco.dao.RotaVisitPredicates.rotaVisits;
import static com.ecco.dao.RotaVisitPredicates.uninvoicedRotaVisit;
import static com.ecco.infrastructure.time.JodaToJDKAdapters.dateTimeToJdk;
import static com.ecco.infrastructure.time.JodaToJDKAdapters.localDateToJoda;
import static com.ecco.webApi.evidence.BaseCommandViewModel.OPERATION_REMOVE;
import static com.ecco.webApi.evidence.BaseCommandViewModel.OPERATION_UPDATE;
import static java.util.stream.Collectors.toList;

/**
 * Web API for managing invoices relating to agreements and rota appointments.
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/rota/invoice")
public class RotaActivityInvoiceController extends BaseWebApiController {

    private final ClientSalesRotaInvoiceDetailResourceAssembler clientSalesInvoiceDetailResourceAssembler;
    private final ClientSalesRotaInvoiceDetailResourceLineAssembler lineAssembler;
    private final ClientSalesInvoiceRepository clientSalesInvoiceRepository;
    private final EvidenceSupportWorkRepository supportWorkRepository;
    private final CalendarService calendarService;
    private final RotaService rotaService;
    private final RotaDelegator rotaDelegator;
    private final ServiceRecipientCreateInvoiceCommandHandler createInvoiceCommandHandler;

    // Queries...

    /**
     * List all invoices in the date range.
     */
    @GetJson("/")
    public List<ClientSalesRotaInvoiceDetailResource> findAllInvoices(
            @RequestParam @DateTimeFormat(iso = ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = ISO.DATE) LocalDate endDate
    ) {
        var filter = new InvoiceFilterSpecification(startDate, endDate);
        var results = clientSalesInvoiceRepository.findAll(filter.toPredicate());
        return StreamSupport.stream(results.spliterator(), false)
                .map(clientSalesInvoiceDetailResourceAssembler::toModel)
                .collect(toList()); // Would love to return Stream, but need to sort out why OpenEntityManagerInViewInterceptor doesn't seem to be working
    }

    /**
     * List all invoices for a service recipient.
     * @param serviceRecipientId the service recipient for whom we wish to list invoices.
     * @return list of the invoices for the service recipient
     */
    @GetJson("/serviceRecipient/{serviceRecipientId}/")
    public List<ClientSalesRotaInvoiceDetailResource> findAllClientSalesInvoices(@PathVariable Integer serviceRecipientId) {
        return clientSalesInvoiceRepository.streamByServiceRecipientId(serviceRecipientId)
            .map(clientSalesInvoiceDetailResourceAssembler::toModel)
            .collect(toList()); // Would love to .iterator() but it doesn't return anything (open session in view?)
    }

    /**
     * Get a single invoice in detail.
     * @param invoiceId the identity of the invoice.
     * @return single invoice in detail
     */
    @GetJson("/id/{invoiceId}/")
    public ClientSalesRotaInvoiceDetailResource fetchSingleInvoice(@PathVariable Integer invoiceId) {
        return clientSalesInvoiceRepository.findById(invoiceId)
                .map(clientSalesInvoiceDetailResourceAssembler::toModel)
                .orElseThrow(() -> new NotFoundException(invoiceId));
    }

    /**
     * Get a single invoices' lines. Note these are also included with {@link #fetchSingleInvoice(Integer)} but
     * this method exists to ensure that HATEOAS links returned are queryable.
     * @param invoiceId the identity of the invoice.
     * @return single invoice's lines
     */
    @GetJson("/id/{invoiceId}/lines/")
    public List<ClientSalesRotaInvoiceDetailResource.Line> fetchLinesForSingleInvoice(@PathVariable Integer invoiceId) {
        var invoice = clientSalesInvoiceRepository.findById(invoiceId)
                .orElseThrow(() -> new NotFoundException(invoiceId));
        return invoice.getLines()
                .stream()
                .map(l -> (ClientSalesRotaInvoiceLine) l)
                .map(lineAssembler.withInvoice(invoice))
                .collect(toList());
    }


    /**
     * List uninvoiced work items as pro forma invoice lines.
     * @param serviceRecipientId the service recipient for whom the lines should be returned.
     * @param endDate the endDate (inclusive) for search filter to apply.
     * @return list of pro forma invoice lines, ready to add to an invoice
     */
    @GetJson("/uninvoiced/serviceRecipient/{serviceRecipientId}/")
    public List<ClientSalesRotaInvoiceDetailResource.Line> findUninvoicedWork(
            @PathVariable Integer serviceRecipientId,
            @RequestParam @DateTimeFormat(iso = ISO.DATE) LocalDate endDate) {
        var filter = new WorkFilterSpecification(endDate);
        final Spliterator<EvidenceSupportWork> uninvoicedWork = supportWorkRepository.findAll(
                uninvoicedRotaVisit(serviceRecipientId).and(filter.toPredicate())
        ).spliterator();
        return StreamSupport.stream(uninvoicedWork, false)
                .map(lineAssembler::recordedWorkToLine)
                .collect(toList());

        // TODO: DEV-1433 include confirmed calendar events - not essential, but the API will be inconsistent
        //
    }


    /**
     * For WHOLE ROTA
     * List uninvoiced items as pro forma invoice lines. This has always included:
     *      - work done but not invoiced
     *      - unrecorded work, ie visits without work done
     *      TODO however, unrecorded work is time consuming to obtain and could be a separate (missed?) or paged call
     *          this is largely because we look up allocated visits, which can be every 15min across the month
     * @param startDate the endDate (inclusive) for search filter to apply.
     * @param endDate the endDate (inclusive) for search filter to apply.
     * @return list of pro forma invoice lines, ready to add to an invoice
     */
    @GetJson("/uninvoiced/")
    public List<ClientSalesRotaInvoiceDetailResource.Line> findAllUninvoicedWork(
            @RequestParam @DateTimeFormat(iso = ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = ISO.DATE) LocalDate endDate
    ) {

        var handler = rotaDelegator.selectHandler("workers:all", "referrals:all");
        var rota = rotaService.fetchRota(handler, localDateToJoda(startDate), localDateToJoda(endDate),
                 "workers:all", "referrals:all", true, false);

        // convert all 'visits' into invoice lines
        var linesFromAllocatedEventsInRange = rota.getResources().stream()
            .flatMap(resource ->
                resource.getAppointments().stream()
                    .map(appointment -> {
                        // ignore runs
                        var isRun = appointment.hasLink(ServiceRecipientRotaDecorator.REL_RUN_BREAKDOWN);
                        if (isRun) {
                            return null;
                            //throw new IllegalArgumentException("missing functionality for handling care run for whole-rota 'uninvoiced'");
                        }
                        return lineAssembler.appointmentToLine(appointment, resource);
                    })
                    .filter(Objects::nonNull)
            ).collect(toList());

        return findUnverified(startDate, endDate, linesFromAllocatedEventsInRange);
    }

    private List<ClientSalesRotaInvoiceDetailResource.Line> findUnverified(LocalDate startDate, LocalDate endDate, List<ClientSalesRotaInvoiceDetailResource.Line> linesFromAllocatedEventsInRange) {
        // load the work around the boundary of the range
        addEvidenceToInvoiceLine(startDate, endDate, linesFromAllocatedEventsInRange);

        // TODO /uninvoiced should be unverified API call
        var unverified = filterUnverifiedEvidence(linesFromAllocatedEventsInRange);

        // sort by planned date
        unverified.sort(Comparator.comparing(ClientSalesRotaInvoiceDetailResource.Line::getPlannedDate));

        return unverified;
    }

    private void addEvidenceToInvoiceLine(LocalDate startDate, LocalDate endDate, List<ClientSalesRotaInvoiceDetailResource.Line> linesFromAllocatedEventsInRange) {
        // load the work around the boundary of the range
        // and augment the lines with any work in the range
        // NB we ideally want to load work by planned date not work date but can't - instead we load a day aside to check nothing is missed.
        // TODO we could load the work with eventId in the range
        var workDateInRange = new WorkFilterSpecification(startDate.minusDays(1), endDate.plusDays(1));
        final var workInRange = supportWorkRepository.findAll(
                rotaVisits().and(workDateInRange.toPredicate())
        ).spliterator();

        StreamSupport.stream(workInRange, false)
            .forEach(w ->
                linesFromAllocatedEventsInRange.stream()
                    .filter(l -> l.getEventId().equalsIgnoreCase(w.getEventId()))
                    .forEach(l -> lineAssembler.withRecordedWork(l, w))
            );
    }

    /**
     * Filter/keep the unverified only.
     * Unverified are those that do not have a workUuid in fin_invoice_lines - which would be pending invoice or invoiced.
     */
    private List<ClientSalesRotaInvoiceDetailResource.Line> filterUnverifiedEvidence(List<ClientSalesRotaInvoiceDetailResource.Line> linesFromAllocatedEventsInRange) {
        // get the workUuid that we have evidence for
        var workUuidFromLines = linesFromAllocatedEventsInRange.stream()
                .map(ClientSalesRotaInvoiceDetailResource.Line::getWorkUuid)
                .collect(Collectors.toSet());

        // find the workUuid's that have been processed - ie verified or invoiced
        var batched = createBatch(workUuidFromLines, 20);
        var processedWorkUuids = batched.stream()
                .map(clientSalesInvoiceRepository::findMatchingInvoiceLineWorkUuid)
                .flatMap(Collection::stream)
                .collect(Collectors.toSet());

        // return the difference - those workUuidFromLines not in processedWorkUuids
        workUuidFromLines.removeAll(processedWorkUuids);
        return linesFromAllocatedEventsInRange.stream()
                .filter(l -> workUuidFromLines.contains(l.getWorkUuid()))
                .collect(toList());
    }

    public static <T> Collection<List<T>> createBatch(Collection<T> inputList, int batchSize) {
        // atomic is effectively final
        AtomicInteger counter = new AtomicInteger();

        return inputList
                .stream()
                .collect(Collectors.groupingBy(gr -> counter.getAndIncrement() / batchSize))
                .values();
    }

    /**
     * For BUILDING.
     * List uninvoiced items as pro forma invoice lines. This has always included:
     *      - work done but not invoiced
     *      - unrecorded work, ie visits without work done
     *      TODO however, unrecorded work is time consuming to obtain and could be a separate (missed?) or paged call
     *          this is largely because we look up allocated visits, which can be every 15min across the month
     *          NB collecting the run contents per allocated worker apt is very poor performance (ie runBreakdownFetch).
     * @param startDate the endDate (inclusive) for search filter to apply.
     * @param endDate the endDate (inclusive) for search filter to apply.
     * @return list of pro forma invoice lines, ready to add to an invoice
     */
    @GetJson("/uninvoiced/building/{buildingId}/")
    public List<ClientSalesRotaInvoiceDetailResource.Line> findAllUninvoicedWorkInBuilding(
            @RequestParam @DateTimeFormat(iso = ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = ISO.DATE) LocalDate endDate,
            @PathVariable Integer buildingId
    ) {

        // service recipients are allocated to runs, which are allocated to workers
        // when a visit is recorded its done so against the ???run???
        // so the appointment should be locked to the run and worker for correct data here

        // if we assume the appointment is locked, then we can call any rota for the answer
        // because if there is work done, that means the worker was allocated
        // however, we can also be directly allocated, not on a run, so we do still need the whole-rota approach
        // NB load resources only - which is quick and will show the allocated appointments

        // RUN BUILDER (demand) CLIENT (on building) ALLOCATIONS
        var resourceToFulfilDemand = "careruns:all";
        var serviceRecipientDemandFilter = "buildings:"+buildingId; // serviceRecipients on buildings are the demand
        var runHandler = rotaDelegator.selectHandler(resourceToFulfilDemand, serviceRecipientDemandFilter);
        var runRota = rotaService.fetchRota(runHandler, localDateToJoda(startDate), localDateToJoda(endDate),
                resourceToFulfilDemand, serviceRecipientDemandFilter, true, false);

        // STAFF ALLOCATIONS (to runs and direct client allocations)
        var handler = rotaDelegator.selectHandler("workers:all", "referrals:all");
        // TODO load just a building worth of staff
        var staffRota = rotaService.fetchRota(handler, localDateToJoda(startDate), localDateToJoda(endDate),
                "workers:all", "referrals:all", true, false);

        // convert all 'visits' into invoice lines
        // loop through staff allocations, finding the runs
        var linesFromAllocatedEventsInRange = staffRota.getResources().stream()
                .flatMap(resource -> resource.getAppointments().stream()
                        .flatMap(appointment -> {
                            // if 'shift breakdown' then its a run, and we collect the appointments inside
                            var isRun = appointment.hasLink(ServiceRecipientRotaDecorator.REL_RUN_BREAKDOWN);
                            if (isRun) {
                                // lookup run contents to add here
                                var apts = runBreakdownLookup(appointment, runRota, startDate, endDate);
                                return apts.stream().map(apt -> lineAssembler.appointmentToLine(apt, resource));
                            } else {
                                return Stream.of(lineAssembler.appointmentToLine(appointment, resource));
                            }
                        }))
                .collect(toList());

        return findUnverified(startDate, endDate, linesFromAllocatedEventsInRange);
    }

    /**
     * We need to get the run breakdown.
     * This works by looking up from pre-loaded report, rather than N+! as per runBreakdownFetch.
     */
    private List<RotaAppointmentViewModel> runBreakdownLookup(RotaAppointmentViewModel careRunActivity, Rota runRota,
                                                              LocalDate startDate, LocalDate endDate) {

        // get all care run resources, which hold allocated apts
        return runRota.getResources().stream()
                // ** MATCH ON SRID **
                // we have a resource and an activity which refer to the same thing
                // and we can match on srid (care run) because we then select the date ranges
                .filter(r -> r.getServiceRecipientId().equals(careRunActivity.getServiceRecipientId()))
                .flatMap(r -> r.getAppointments().stream())
                    // check the time is included, since the rota loads the whole day - so we can include apts outside the shift
                    // allocate (or just show) to the worker all the rota appointments
                .filter(it ->
                    (it.getStart().isAfter(careRunActivity.getStart()) || it.getStart().isEqual(careRunActivity.getStart()))
                        && it.getEnd().isBefore(careRunActivity.getEnd())
                )
                .collect(toList());
    }

    /**
     * We need to get the run breakdown.
     * This works as per ActivityView.tsx.
     * TODO we should replace this by calling a server-side hateoas link to a method.
     */
    private List<RotaAppointmentViewModel> runBreakdownFetch(RotaAppointmentViewModel careRunActivity, LocalDate startDate, LocalDate endDate) {
        var bldgId = getBuildingId(careRunActivity);
        var demandFilter = "careruns:"+careRunActivity.getServiceRecipientId();
        var serviceRecipientFilter = "buildings:"+bldgId;
        var handler = rotaDelegator.selectHandler(demandFilter, serviceRecipientFilter);
        var rota = rotaService.fetchRota(handler, localDateToJoda(startDate), localDateToJoda(endDate),
                demandFilter, serviceRecipientFilter, true, false);

        // TODO ?? this is getDemand when we only loadResource - see ActivityView
        return rota.getDemandAppointments().stream()
            .filter(it ->
                // check the time is included, since the rota loads the whole day - so we can include apts outside the shift
                // allocate (or just show) to the worker all the rota appointments
                it.getStatus().equals(Recurrence.Status.CONFIRMED)
                && (it.getStart().isAfter(careRunActivity.getStart()) || it.getStart().isEqual(careRunActivity.getStart()))
                && it.getEnd().isBefore(careRunActivity.getEnd())
            )
            .collect(toList());
    }
    // as per ActivityView.tsx
    private final Pattern careRunHrefToBuildingId = Pattern.compile(".*/buildings/(\\d+)"); // probably better supplying a hateoas 'id' as I think we've done before
    private Integer getBuildingId(RotaAppointmentViewModel careRunActivity) {
        var careRunRef = careRunActivity.getLink(ServiceRecipientRotaDecorator.REL_EDIT);
        var id = careRunRef.map(link -> careRunHrefToBuildingId.matcher(link.toString())).orElse(null);
        id.find();
        return Integer.parseInt(id.group(1));
    }

    /**
     * Create a draft invoice for a service recipient.
     * @param params -> serviceRecipientId the service recipient for whom the invoice is being generated
     * @param requestBody a command view model describing the details of the invoice
     * @return standard command result
     */
    @PostJson("/serviceRecipient/{serviceRecipientId}/")
    public Result createClientSalesInvoice(ServiceRecipientCreateInvoiceParams params,
                                           @NonNull Authentication authentication,
                                           @NonNull @RequestBody String requestBody) throws IOException {
        return createInvoiceCommandHandler.handleCommand(authentication, params, requestBody);
    }

    /**
     * Add a line to an invoice.
     * @param invoiceId the identity of the invoice.
     * @param command a command describing the details of the line
     * @return standard command result
     */
    @PostJson("/id/{invoiceId}/lines/")
    @WriteableTransaction
    public Result addLineToInvoice(@PathVariable Integer invoiceId, @RequestBody InvoiceLineCommandViewModel command) {
        final ClientSalesInvoice invoice = clientSalesInvoiceRepository.findById(invoiceId)
                .orElseThrow(() -> new NotFoundException(invoiceId));

        // NB "this is actually passing back information the server already calculated in getUninvoicedLines" (see ECCO-SNIPPET.md)
        final String description = command.description != null? command.description.to : null;
        final Integer rateCardId = command.rateCardId != null? command.rateCardId.to : null;
        final BigDecimal amount = command.amount != null? command.amount.to : BigDecimal.ZERO;
        final BigDecimal taxRate = command.taxRate != null? command.taxRate.to : null;

        UUID workUuid = command.workUuid != null ? command.workUuid.to : null;
        int actualMinutes = 0;
        ZonedDateTime actualDate = null;
        var eventId = command.eventId != null ? command.eventId.to : null;
        if (workUuid != null) {
            final EvidenceSupportWork work = supportWorkRepository.findById(workUuid).orElse(null);
            work.setInvoiceId(invoiceId);
            actualMinutes = work.getComment().getMinutesSpent();
            actualDate = dateTimeToJdk(work.getWorkDate());
            eventId = work.getEventId();
            supportWorkRepository.save(work);
        }

        int plannedMinutes = 0;
        ZonedDateTime plannedDate = null;
        // this is OPTION 3 from below
        String plannedResourceCalendarId = command.plannedResourceCalendarId != null ? command.plannedResourceCalendarId.to : null;
        if (eventId != null) { // Rota visit is linked to an actual appointment - or a planned one without work
            final Entry entry = calendarService.findEntry(eventId);
            Duration duration = new Duration(entry.getStart(), entry.getEnd());
            plannedMinutes = Math.toIntExact(duration.getStandardMinutes());
            plannedDate = dateTimeToJdk(entry.getStart());

            // **************************************
            // get the staff assigned on the care run
            // - if it is indeed a care run
            // **************************************
            // the server side decorators provide the information on careruns for the client to absorb and handle
            // often finding the 'other' attendee eg EventCard.tsx, AdditionalStaff.tsx, ActivityView.tsx (as per runBreakdownFetch)
            // the server side controller, here in runBreakdownLookup, handles care run breakdowns to find the appointments inside
            // which comes from loading a rota - which provides the information on careruns
            // here, though, we are after one single appointment on a care run (maybe), which means we could reuse the same logic
            // but this only provides us with the links to then call and find the appointments inside
                // OPTION 1
                // var combinedEntry = eventService.getEntry(String uuid)
                // var eventResource = eventResourceAssember.toModel(combinedEntry);
                // ServiceRecipientEventDecorator.decorate(eventResource, combinedEntry);
            // what we are actually after is the worker attending the run
            // which we should be able to find via the 'other' attendee (as per other examples noted above)
            // so find the non-client attendee (worker or care run) and load its event for its attendees (worker or none)
            // BUT we still need to load the other attendee's events at the same date and time because the two aren't related
            // so just like runBreakdownLookup - we need to load and "** MATCH ON SRID **"
                // OPTION 2
                // var calendarIdRunMaybe = entry.getAttendees().stream().filter(a -> !a.getCalendarId().equals(entry.getOwnerCalendarId())).findFirst()
                // var careRunEventMaybe = calendarService.findEntries(calendarId, intervalOfEntry)
                // MATCH ON SRID
                // var workerAttendee - careRunEventMaybe filter calendarId not equal to the entry.getOwnerCalendarId
            // HOWEVER, the simplest thing is to just provide the plannedResourceSrId
            // even though we don't want to trust what is from the client ideally
                // OPTION 3
                // receive the plannedResourceCalendarId from the client
        }

        // TODO: ECCO-2176 fix the link from the calendar entry back to appointment schedule
        /*
            final AppointmentSchedule appointmentSchedule = new EntityUriMapperImpl().entityClassForUri(entry.getManagedByUri());
            final String type = appointmentSchedule.getAppointmentType().getName();
            // TODO we should be making adjustments to correct any normal scheduled charges not change the actual charge
            // TODO so need to determine whether we accept the client's command.amount.to when work is involved or use server-side
            // TODO although it could just be a case of the server sending the uninvoicedLines to the client to pass back
            // TODO from DEV-4 (ECCO-2147) and DEV-5 (ECCO-2148), all data is obtained at this stage
            final String amount = appointmentSchedule.getCharge();
         */
        // all args constructor for persisting - can ignore plannedResourceName
        ClientSalesRotaInvoiceDetailResource.Line resource = new ClientSalesRotaInvoiceDetailResource.Line(invoice.getServiceRecipientId(),
                null, workUuid, eventId, actualDate, actualMinutes, plannedDate, plannedMinutes,
                plannedResourceCalendarId, null,
                null, description, rateCardId, amount, taxRate, invoiceId, false
        );

        final ClientSalesRotaInvoiceLine line = lineAssembler.fromResource(resource);
        invoice.addLine(line);
        clientSalesInvoiceRepository.save(invoice);

        clientSalesInvoiceRepository.flush(); // required since earlier flush  (Where, Adam?)
        return new Result((String) null, line.getId().toString());
    }

    /**
     * Add a line to an invoice.
     * @param invoiceId the identity of the invoice.
     * @param command a command describing the details of the line
     */
    @PostJson("/id/{invoiceId}/lines/{lineUuid}/")
    public void modifyInvoiceLine(
            @PathVariable Integer invoiceId,
            @PathVariable UUID lineUuid,
            @RequestBody InvoiceLineCommandViewModel command) {

        // FIXME: Extract to a command handler

        Assert.isNull(command.workUuid, "workUuid cannot be amended");

        var invoice = clientSalesInvoiceRepository.findById(invoiceId).orElseThrow();

        Predicate<ClientSalesInvoiceLine> isSpecifiedLine = line -> line.getId().equals(lineUuid);
        var line = invoice.getLines().stream()
                .map(l -> (ClientSalesRotaInvoiceLine) l)
                .filter(isSpecifiedLine).findFirst().orElseThrow();

        if (command.operation.equals(OPERATION_REMOVE)) {
            if (line.getWorkUuid() != null) {
                supportWorkRepository.setInvoiceId(line.getWorkUuid(), null);
            }
            invoice.getLines()
                    .removeIf(isSpecifiedLine);
        }
        else {
            Assert.state(command.operation.equals(OPERATION_UPDATE), "Illegal value for operation");

            if (command.description != null) {
                line.setDescription(command.description.to);
            }
            if (command.amount != null) {
                line.setNetAmount(command.amount.to);
            }
            if (command.taxRate != null) {
                line.setTaxRate(command.taxRate.to);
            }
        }
        clientSalesInvoiceRepository.save(invoice);
    }
}
