package com.ecco.webApi.rota;

import com.ecco.calendar.CombinedEntry;
import com.ecco.dom.EvidenceSupportWork;
import com.ecco.dom.agreements.ClientSalesInvoice;
import com.ecco.dom.agreements.ClientSalesRotaInvoiceLine;
import com.ecco.dom.agreements.DemandSchedule;
import com.ecco.dom.contracts.RateCard;
import com.ecco.dom.contracts.RateCardEntry;
import com.ecco.infrastructure.time.JodaToJDKAdapters;
import com.ecco.dom.contracts.RateCardCalculation;
import com.ecco.rota.service.RotaService;
import com.ecco.rota.webApi.dto.ClientSalesRotaInvoiceDetailResource;
import com.ecco.rota.webApi.dto.RotaAppointmentViewModel;
import com.ecco.rota.webApi.dto.RotaResourceViewModel;
import com.ecco.service.EventService;
import com.ecco.webApi.GuavaResourceAssemblerSupport;
import lombok.extern.slf4j.Slf4j;

import org.jspecify.annotations.NonNull;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.Instant;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.List;

import java.util.Optional;
import java.util.function.Function;

import com.google.common.collect.Range;

import static com.ecco.infrastructure.time.JodaToJDKAdapters.UTC;


/**
 * @since 14/10/2016
 */
@Slf4j
public class ClientSalesRotaInvoiceDetailResourceLineAssembler extends GuavaResourceAssemblerSupport<ClientSalesRotaInvoiceLine, ClientSalesRotaInvoiceDetailResource.Line> {
    private final RotaService rotaService;
    private final EventService eventService;
    private final RateCardCalculation rateCardCalculation;

    public ClientSalesRotaInvoiceDetailResourceLineAssembler(RotaService rotaService,
                                                             EventService eventService,
                                                             RateCardCalculation rateCardCalculation) {
        super(RotaActivityInvoiceController.class, ClientSalesRotaInvoiceDetailResource.Line.class);
        this.rotaService = rotaService;
        this.eventService = eventService;
        this.rateCardCalculation = rateCardCalculation;
    }

    // NB used by getters to get a dto
    public ClientSalesRotaInvoiceDetailResource.@NonNull Line toModel(ClientSalesRotaInvoiceLine clientSalesInvoiceLine) {
        // load the name from the persisted invoice line
        var plannedResourceName = eventService.getNameFromCalendarId(clientSalesInvoiceLine.getPlannedResourceCalendarId());
        return new ClientSalesRotaInvoiceDetailResource.Line(null, clientSalesInvoiceLine.getId(), clientSalesInvoiceLine.getWorkUuid(),
                clientSalesInvoiceLine.getEventId(),
                clientSalesInvoiceLine.getActualDate(), clientSalesInvoiceLine.getActualMinutes(),
                clientSalesInvoiceLine.getPlannedDate(), clientSalesInvoiceLine.getPlannedMinutes(),
                clientSalesInvoiceLine.getPlannedResourceCalendarId(),
                plannedResourceName,
                clientSalesInvoiceLine.getType(), clientSalesInvoiceLine.getDescription(),
                clientSalesInvoiceLine.getRateCardId(), clientSalesInvoiceLine.getNetAmount(), clientSalesInvoiceLine.getTaxRate(),
                null, false);
    }

    public Function<ClientSalesRotaInvoiceLine, ClientSalesRotaInvoiceDetailResource.Line> withInvoice(ClientSalesInvoice invoice) {
        return line -> toResourceWithInvoice(line, invoice);
    }

    private ClientSalesRotaInvoiceDetailResource.Line toResourceWithInvoice(
            ClientSalesRotaInvoiceLine clientSalesInvoiceLine,
            ClientSalesInvoice invoice) {
        var line = toModel(clientSalesInvoiceLine);
        line.setInvoiceId(invoice.getId());
        line.setServiceRecipientId(invoice.getServiceRecipientId());
        line.setLocked(invoice.isPosted());
        return line;
    }

    /**
     * 'uninvoiced' lines are created here from RotaActivityInvoiceController.findUninvoicedWork
     * DEV-4 comments that this does the 'actuals' to avoid loading data that can just be got when adding the line to the invoice
     *
     * To provide the invoice lines with an actual charge, we need to get the work's eventId data (which was being avoided in DEV-4 and DEV-5)
     * we can load all the RateCard's in a cache to avoid n+1 loading, and get together the rate cards from loading the event
     * which gets the 'managed by://AppointmentSchedule/id' so we can do a projected load for rateCardName and rateCardId's
     * and the event will also have the outcome (ListDefEntry) for DNA / Rescheduled / Attended etc
     * then we loop calculations on categoryTypeId, outcome, and other factors according to what needs invoicing.
     * If the server-side struggles, we could just populate the charges from the client - since they aren't persisted
     * OR we could specify the demandschedule id on the events/eventsrecurring table giving a direct lookup avoiding the standard calendaring system
     */
    public ClientSalesRotaInvoiceDetailResource.Line recordedWorkToLine(EvidenceSupportWork work) {
        // used as a getter
        ClientSalesRotaInvoiceDetailResource.Line line = new ClientSalesRotaInvoiceDetailResource.Line(
                work.getServiceRecipientId(),
                work.getEventId(),
                work.getId(), JodaToJDKAdapters.dateTimeToJdk(work.getWorkDate()), // FIXME: Does this need BST adjust?
                work.getComment().getMinutesSpent(), work.getAuthor().getCalendarId(), work.getAuthor().getDisplayName());

        if (work.getEventId() != null) {

            // find the rate card
            CombinedEntry event = eventService.getEntry(work.getEventId());
            DemandSchedule schedule = rotaService.getAppointmentSchedule(event.getIcalEntry());
            List<RateCard> ratesAvailable = schedule.allowableRateCards();
            // TODO we may need an invoice line per charge item?
            var rateCard = rateCardCalculation.getRateCardInDate(JodaToJDKAdapters.instantFromJoda(work.getWorkDate()), ratesAvailable);

            // charge
            rateCard.ifPresent(card -> {
                line.setRateCardId(card.getId());
                line.setNetAmount(
                    chargeVisit(JodaToJDKAdapters.instantFromJoda(work.getWorkDate()), work.getEventId(), work.getComment().getMinutesSpent(), event, schedule, rateCard)
                );
                // chargeMileage...
                // charge...
            });
        }

        return line;
    }
    public void withRecordedWork(ClientSalesRotaInvoiceDetailResource.Line line, EvidenceSupportWork work) {
        // used as a getter
        line.withWork(work.getServiceRecipientId(),
                work.getEventId(),
                work.getId(), JodaToJDKAdapters.dateTimeToJdk(work.getWorkDate()), // FIXME: Does this need BST adjust?
                work.getComment().getMinutesSpent(), work.getAuthor().getCalendarId(), work.getAuthor().getDisplayName());
    }

    public ClientSalesRotaInvoiceDetailResource.Line appointmentToLine(
            RotaAppointmentViewModel appointment,
            RotaResourceViewModel resource
    ) {
        // FIXME: Duration will not be correct spanning 1am on BST/GMT switch - see finance calc where we do atZone(LONDON)
        var start = appointment.getStart();
        var end = appointment.getEnd();
        var duration = start != null && end != null
                ? Duration.between(start.atZone(UTC), end.atZone(UTC)).toMinutes()
                : appointment.getAllDay()
                    ? 24 * 60 // FIXME: This may not be correct - may relate to contract
                    : 0;
        var line = new ClientSalesRotaInvoiceDetailResource.Line(
                appointment.getServiceRecipientId(),
                appointment.getRef(), // eventId is set here on the line
                appointment.getStart(),
                (int)duration,
                resource.getCalendarId(),
                resource.getName()
        );

        CombinedEntry event = eventService.getEntry(appointment.getRef());
        DemandSchedule schedule = rotaService.getAppointmentSchedule(event.getIcalEntry());
        if (schedule != null) {

            // find the rate card
            // TODO we may need an invoice line per charge item?
            List<RateCard> ratesAvailable = schedule.allowableRateCards();
            var rateCard = rateCardCalculation.getRateCardInDate(appointment.getStart().toInstant(ZoneOffset.UTC), ratesAvailable);

            // charge
            rateCard.ifPresent(card -> {
                line.setRateCardId(card.getId());
                line.setNetAmount(
                    chargeVisit(appointment.getStart().toInstant(ZoneOffset.UTC), appointment.getRef(), (int)duration, event, schedule, rateCard)
                );
                // chargeMileage...
                // charge...
            });
        }
        return line;
    }

    private BigDecimal chargeVisit(Instant instant, String ref, int minsSpent, CombinedEntry event, DemandSchedule schedule, Optional<RateCard> rateCard) {
        if (rateCard.isEmpty()) {
            log.debug("Returning zero charge for iCal event {} - no rate cards avail", ref);
            return BigDecimal.ZERO;
        }
        List<RateCardEntry> entries = rateCardCalculation.determineRateCardEntries(rateCard.get(), schedule.getCategoryId(),
                event.getEventEntry() != null ? event.getEventEntry().getEventStatusRateId() : null,
                null);
        if (entries == null || entries.size() == 0) {
            return BigDecimal.ZERO;
        }

        // Create a ZonedDateTime range from the instant and duration in minutes
        ZonedDateTime start = instant.atZone(UTC);
        ZonedDateTime end = start.plusMinutes(minsSpent);
        Range<ZonedDateTime> dateTimeRange = Range.closedOpen(start, end);

        return rateCardCalculation.calculateCharge(entries, dateTimeRange);
    }

    /**
     * Construct an invoice line to be persisted.
     * Uses the same ClientSalesInvoiceDetailResource.Line that is used for getter dto's (eg appointmentToLine)
     */
    public ClientSalesRotaInvoiceLine fromResource(ClientSalesRotaInvoiceDetailResource.Line resource) {
        return new ClientSalesRotaInvoiceLine(resource.getLineUuid(), resource.getWorkUuid(),
                resource.getEventId(), resource.getPlannedResourceCalendarId(),
                resource.getPlannedDate(), resource.getPlannedMinutes(),
                resource.getWorkDate(), resource.getWorkMinutesSpent(),
                resource.getType(), resource.getDescription(),
                resource.getRateCardId(), resource.getNetAmount(), resource.getTaxRate());
    }
}
