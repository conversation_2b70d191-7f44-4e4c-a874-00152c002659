package com.ecco.webApi.finance;

import com.ecco.dom.contracts.Contract;
import com.ecco.dom.contracts.RateCard;
import com.ecco.dom.contracts.commands.RateCardCommand;
import com.ecco.repositories.contracts.ContractRepository;
import com.ecco.repositories.contracts.RateCardCommandRepository;
import com.ecco.repositories.contracts.RateCardRepository;
import com.ecco.webApi.CommandResult;
import com.ecco.webApi.evidence.BaseCommandHandler;
import com.ecco.webApi.evidence.BaseCommandViewModel;
import com.ecco.webApi.rota.ContractController;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;

import static com.ecco.infrastructure.rest.hateoas.ApiLinkTo.linkToApi;
import static org.springframework.hateoas.server.mvc.WebMvcLinkBuilder.methodOn;

@Component
public class RateCardCommandHandler extends BaseCommandHandler<RateCardCommandDto, Integer, RateCardCommand, @Nullable Void> {

    private final RateCardRepository rateCardRepository;
    private final ContractRepository contractRepository;
    private final RateCardMapper rateCardMapper;

    @Autowired
    public RateCardCommandHandler(RateCardCommandRepository rateCardCommandRepository,
                                  ObjectMapper objectMapper,
                                  RateCardRepository rateCardRepository,
                                  ContractRepository contractRepository,
                                  RateCardMapper rateCardMapper) {
        super(objectMapper, rateCardCommandRepository, RateCardCommandDto.class);
        this.rateCardRepository = rateCardRepository;
        this.contractRepository = contractRepository;
        this.rateCardMapper = rateCardMapper;
    }

    @Override
    protected CommandResult handleInternal(@NonNull Authentication auth, @Nullable Void params,
                                           @NonNull RateCardCommandDto dto) {

        Integer rateCardId = dto.rateCardId;

        switch (dto.operation) {
            case BaseCommandViewModel.OPERATION_ADD:
                rateCardId = addRateCard(dto);
                break;
            case BaseCommandViewModel.OPERATION_UPDATE:
                updateRateCard(dto);
                break;
            case BaseCommandViewModel.OPERATION_REMOVE:
                deleteRateCard(dto);
                break;
            default:
                throw new IllegalArgumentException("cannot handle operation: " + dto.operation);
        }

        return CommandResult.ofLink(linkToApi(methodOn(ContractController.class).getRateCard(rateCardId)).withSelfRel());
    }

    private int addRateCard(RateCardCommandDto dto) {
        RateCard r = new RateCard();
        applyChanges(r, dto);
        return rateCardRepository.save(r).getId();
    }

    private void updateRateCard(RateCardCommandDto dto) {
        applyChanges(rateCardRepository.findById(dto.rateCardId).orElseThrow(), dto);
    }

    private void deleteRateCard(RateCardCommandDto dto) {
        rateCardRepository.deleteById(dto.rateCardId);
    }

    private void applyChanges(RateCard r, RateCardCommandDto dto) {
        rateCardMapper.updateRateCardFromDto(dto, r);

        // Handle contracts special case
        if (dto.contractsChange != null) {
            var contractsFrom = new HashSet<>(dto.contractsChange.from != null ? dto.contractsChange.from : new ArrayList<>());
            var contractsTo = new HashSet<>(dto.contractsChange.to != null ? dto.contractsChange.to : new ArrayList<>());
            List<Integer> contractsFromNotContractsTo = contractsFrom.stream()
                    .filter(f -> !contractsTo.contains(f))
                    .toList();
            List<Integer> contractsToNotContractsFrom = contractsTo.stream()
                    .filter(f -> !contractsFrom.contains(f))
                    .toList();
            for (Integer id : contractsFromNotContractsTo) {
                Contract c = contractRepository.findOne(id);
                c.getRateCards().remove(r);
            }
            for (Integer id : contractsToNotContractsFrom) {
                Contract c = contractRepository.findOne(id);
                c.getRateCards().add(r);
            }
        }
    }

    @NonNull
    @Override
    protected RateCardCommand createCommand(Serializable targetId, @Nullable Void params, @NonNull String requestBody,
                                            @NonNull RateCardCommandDto dto, long userId) {
        return new RateCardCommand(dto.uuid, dto.timestamp, userId, requestBody, dto.serviceRecipientId);
    }

}
