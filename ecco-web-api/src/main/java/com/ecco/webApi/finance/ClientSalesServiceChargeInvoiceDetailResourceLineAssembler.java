package com.ecco.webApi.finance;

import com.ecco.dom.contracts.RateCard;
import com.ecco.finance.webApi.dto.ClientSalesServiceChargeInvoiceDetailResource;
import com.ecco.dom.contracts.RateCardCalculation;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.util.Optional;
import java.util.UUID;


@Slf4j
public class ClientSalesServiceChargeInvoiceDetailResourceLineAssembler {

    private final RateCardCalculation rateCardCalculation;

    public ClientSalesServiceChargeInvoiceDetailResourceLineAssembler(RateCardCalculation rateCardCalculation) {
        this.rateCardCalculation = rateCardCalculation;
    }

    public ClientSalesServiceChargeInvoiceDetailResource.Line toResource(int srId, String chargeDescription, LocalDate from, LocalDate to) {
        int rateCardId = -1;
        BigDecimal charge = new BigDecimal(10);
        return new ClientSalesServiceChargeInvoiceDetailResource.Line(srId, UUID.randomUUID(), null, chargeDescription, rateCardId, charge, new BigDecimal(20), null, false, null, null, null);
    }

    private BigDecimal serviceCharge(Instant instant, String ref, Optional<RateCard> rateCard) {
        if (rateCard.isEmpty()) {
            log.debug("Returning zero charge - no rate cards avail", ref);
            return BigDecimal.ZERO;
        }
        /*List<RateCardEntry> entries = rateCardCalculation.determineRateCardEntries(instant,
                rateCard.get(), schedule.getCategoryId(),
                event.getEventEntry() != null ? event.getEventEntry().getEventStatusRateId() : null,
                null);
        if (entries == null || entries.size() == 0) {
            return BigDecimal.ZERO;
        }
        return rateCardCalculation.calculateCharge(entries, minsSpent);*/
        return null;
    }

}
