package com.ecco.upload.webapi;

import org.springframework.hateoas.RepresentationModel;

/**
 * DTO representing an uploaded file (attachment).
 *
 * @since 07/07/2014
 */
public class UploadedFileResource extends RepresentationModel<UploadedFileResource> implements Comparable<UploadedFileResource> {
    public long fileId;
    public String filename;
    public long size;
    public long bytesId;
    public String type;

    @Override
    public int compareTo(UploadedFileResource o) {
        return Long.compare(this.fileId, o.fileId);
    }
}
