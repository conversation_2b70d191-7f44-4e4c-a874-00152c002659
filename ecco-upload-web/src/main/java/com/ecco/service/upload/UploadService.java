package com.ecco.service.upload;

import com.ecco.infrastructure.annotations.WriteableTransaction;
import com.ecco.web.upload.UploadConfig;
import com.ecco.dom.upload.UploadedBytes;
import com.ecco.dom.upload.UploadedFile;

@WriteableTransaction
public interface UploadService {

    UploadedFile processFile(UploadConfig config);

    boolean deleteFile(long fileId, UploadConfig config);

    UploadedFile getSimpleFile(long fileId);

    UploadedFile getFile(long fileId, UploadConfig config);

    UploadedBytes getBytes(long byteId);

}
/*
// PROCESS THE FILE
// if multipart holds a file on disk, its name is determined by DiskFileItem, and is random...unless a 'tempFile' parameter is set
// String tempFileName = "upload_" + UID + "_" + getUniqueId() + ".tmp";
// the folder is java.io.tmpdir unless 'repository' is set
// we use the format of dateStr + "_" + usersId + "_" + submitNumber + "_" + jSession;
// for emails, we write to disk for the email to pick up
// writing to a disk requires using 'transferTo' which needs a path
// alternatively we could set the 'keepInMem' value to 0
// but there is no real harm in this step - the file is moved to this location
// get the filename to save with
// we could just keep the filename commons gives it
//String fileName = StringUtils.getUploadFileName(multipartFile, currentUserDao.getUserId(), request.getSession().getId());
//String fileName = "tmp_" + multipartFile.getName() + "_" + request.getSession().getId();
// rather than save a new file through a service and delete the commons file, we simply use existing functionality
// the factory has been configured in a post process to set the temp dir through a property - or as javax.servlet.context.tempdir
// which for us is C:\Program Files\Apache Software Foundation\Tomcat 5.5\work\Catalina\localhost\med
// however, the commons file upload uses the real temp dir
// wherever the temp file actually is, we move it to the location we specified
//File parentDirectory = multipartResolver.getFileItemFactory().getRepository();
*
String pathFileName = System.getProperty("java.io.tmpdir") + File.separator + fileName;
File newFile = new File(pathFileName);
multipartFile.transferTo(newFile);
*
//service.saveFile(multipartFile.getInputStream(), multipartFile.getOriginalFilename());
*
File parentDirectory = multipartResolver.getFileItemFactory().getRepository();
File newFile = new File(parentDirectory, fileName);
multipartFile.transferTo(newFile);
*

//upload.setContent(multipartFile.getBytes());
//upload.setContentSize(new Long(multipartFile.getSize()).intValue());
//upload.setContentType(multipartFile.getContentType());
//upload.setFilename(multipartFile.getOriginalFilename());
// save in db...
*/
